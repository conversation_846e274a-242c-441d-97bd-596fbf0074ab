msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-07-31 13:12+0200\n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: hr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n>=2 && n<=4 ? 1 : 2);\n"
"Mime-Version: 1.0\n"
"X-Generator: Poedit 3.6\n"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:7
msgid "error"
msgstr "<i>Ispričavamo se ali imamo problema s našim AI pomoćnikom. Znamo da je frustrirajuće kada stvari ne rade kako se očekuje.<br> Pokušajte ponovo nakon nekog vremena.</i>"

#. placeholder {0}: data.word_count
#: src/components/monitoring/FeedList/FeedListItem/FeedListItem.js:305
msgid "(full text; {0} words)"
msgstr "(cijeli tekst; {0} riječi)"

#: src/components/staff/admin/workspace/Workspace.js:781
msgid "(TVR) Allow automatic transcripts in monitoring"
msgstr "(TVR) Dopustite automatske transkripte u praćenju"

#: src/components/staff/admin/workspace/Workspace.js:790
msgid "(TVR) Allow reruns in monitoring"
msgstr "(TVR) Dopusti reprize u praćenju"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+access%7D+other+%7B%23+accesses%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:85
msgid "# access"
msgid_plural "# accesses"
msgstr[0] "# pristup"
msgstr[1] "# pristupa"
msgstr[2] "# pristupa"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(item.article_count)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+article%7D+other+%7B%23+articles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:43
#: src/helpers/charts/formatters.js:55
#: src/components/reports/history/HistoryTable.js:199
#: src/components/analytics/AnalyticsContent.js:122
msgid "# article"
msgid_plural "# articles"
msgstr[0] "# članaka"
msgstr[1] "# članci"
msgstr[2] "# članaka"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+attached+article%7D+other+%7B%23+attached+articles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:50
msgid "# attached article"
msgid_plural "# attached articles"
msgstr[0] "# priloženi članak"
msgstr[1] "# priložena članka"
msgstr[2] "# priloženi članci"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+author%7D+other+%7B%23+authors%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:57
msgid "# author"
msgid_plural "# authors"
msgstr[0] "# autor"
msgstr[1] "# autori"
msgstr[2] "# autora"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: inspector.data.versions_count
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+change%7D+other+%7B%23+changes%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:78
#: src/components/monitoring/Inspector/InspectorMonitora/StatusBar/ArticleHistoryAction.js:22
msgid "# change"
msgid_plural "# changes"
msgstr[0] "# promjena"
msgstr[1] "# promjene"
msgstr[2] "# promjena"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+comment%7D+other+%7B%23+comments%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:89
msgid "# comment"
msgid_plural "# comments"
msgstr[0] "# komentar"
msgstr[1] "# komentara"
msgstr[2] "# komentara"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+dislike%7D+other+%7B%23+dislikes%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:109
msgid "# dislike"
msgid_plural "# dislikes"
msgstr[0] "# dislajk"
msgstr[1] "# dislajka"
msgstr[2] "# dislajkova"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+email%7D+other+%7B%23+emails%7D%7D&pluralize_on=0
#: src/components/emailing/helpers/emailing.plurals.js:3
msgid "# email"
msgid_plural "# emails"
msgstr[0] "# email"
msgstr[1] "# emaila"
msgstr[2] "# emaila"

#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(data.social_shares)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+interaction%7D+other+%7B%23+interactions%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:76
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:78
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:119
msgid "# interaction"
msgid_plural "# interactions"
msgstr[0] "# interakcija"
msgstr[1] "# interakcije"
msgstr[2] "# interakcija"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+like%7D+other+%7B%23+likes%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:69
msgid "# like"
msgid_plural "# likes"
msgstr[0] "# lajk"
msgstr[1] "# lajka"
msgstr[2] "# lajkova"

#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(inspector.data.article_mentions_count)
#. placeholder {0}: parseInt(data.article_mentions_count)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+mention%7D+other+%7B%23+mentions%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:90
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:103
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:81
#: src/components/monitoring/Inspector/InspectorMonitora/StatusBar/ArticleMentionsActions.js:25
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:342
msgid "# mention"
msgid_plural "# mentions"
msgstr[0] "# spominjanje"
msgstr[1] "# spominjanja"
msgstr[2] "# spominjanja"

#. placeholder {0}: parseInt(jobs.length - 1)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+more%7D+other+%7B%23+more%7D%7D&pluralize_on=0
#: src/helpers/getAuthorJobs.js:16
msgid "# more"
msgid_plural "# more"
msgstr[0] "# više"
msgstr[1] "# više"
msgstr[2] "# više"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: parseInt(value)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+output%7D+other+%7B%23+outputs%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:71
#: src/helpers/charts/formatters.js:69
msgid "# output"
msgid_plural "# outputs"
msgstr[0] "# izlaz"
msgstr[1] "# izlaza"
msgstr[2] "# izlaza"

#. placeholder {0}: parseInt(value, 10)
#. placeholder {0}: payload.page_count
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+page%7D+other+%7B%23+pages%7D%7D&pluralize_on=0
#: src/components/monitoring/WorkspaceArticles/Limits.js:51
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:145
msgid "# page"
msgid_plural "# pages"
msgstr[0] "# stranica"
msgstr[1] "# stranice"
msgstr[2] "# stranica"

#. placeholder {0}: parseInt(value)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+person%7D+other+%7B%23+people%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:83
msgid "# person"
msgid_plural "# people"
msgstr[0] "# osoba"
msgstr[1] "# osobe"
msgstr[2] "# osoba"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+retweet%7D+other+%7B%23+retweets%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:79
msgid "# retweet"
msgid_plural "# retweets"
msgstr[0] "# retweet"
msgstr[1] "# retweeta"
msgstr[2] "# retweeta"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+share%7D+other+%7B%23+shares%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:119
msgid "# share"
msgid_plural "# shares"
msgstr[0] "# dijeljenje"
msgstr[1] "# dijeljenja"
msgstr[2] "# dijeljenja"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+view%7D+other+%7B%23+views%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:99
msgid "# view"
msgid_plural "# views"
msgstr[0] "# pregled"
msgstr[1] "# pregleda"
msgstr[2] "# pregleda"

#. placeholder {0}: account.workspace.limits.media_archive_depth_limit
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+year%7D+other+%7B%23+years%7D%7D&pluralize_on=0
#: src/components/tariff/TariffLimits/TariffLimits.js:263
msgid "# year"
msgid_plural "# years"
msgstr[0] "# godina"
msgstr[1] "# godine"
msgstr[2] "# godina"

#. placeholder {0}: item.recipients.length - shortEmailList.length
#. placeholder {0}: items.length - MAX_ITEMS
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%2B%23+more%7D+other+%7B%2B%23+more%7D%7D&pluralize_on=0
#: src/components/reports/history/HistoryTable.js:373
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:73
msgid "+# more"
msgid_plural "+# more"
msgstr[0] "+# više"
msgstr[1] "+# više"
msgstr[2] "+# više"

#. placeholder {0}: data.identical_articles.length
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%2B%23+other%7D+other+%7B%2B%23+other%7D%7D&pluralize_on=0
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:128
msgid "+# other"
msgid_plural "+# other"
msgstr[0] "+# drugi"
msgstr[1] "+# drugi"
msgstr[2] "+# drugih"

#. placeholder {0}: 1
#. placeholder {0}: self.selector.selected.size
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7BArticle+Removed%7D+other+%7BArticles+Removed%7D%7D&pluralize_on=0
#: src/store/models/monitoring/Inspector/Inspector.ts:497
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:659
#: src/store/models/emailing/campaignDetail/CampaignDetailStore/CampaignDetailStore.js:108
msgid "Article Removed"
msgid_plural "Articles Removed"
msgstr[0] "Članak uklonjen"
msgstr[1] "Članci uklonjeni"
msgstr[2] "Članci uklonjeni"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7Barticle%7D+other+%7Barticles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:64
msgid "article"
msgid_plural "articles"
msgstr[0] "članak"
msgstr[1] "članka"
msgstr[2] "članaka"

#. placeholder {0}: 1
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7BItem+Removed%7D+other+%7BItems+Removed%7D%7D&pluralize_on=0
#: src/store/models/tvr/tvr.js:274
msgid "Item Removed"
msgid_plural "Items Removed"
msgstr[0] "Stavka uklonjena"
msgstr[1] "Stavke uklonjene"
msgstr[2] "Stavke uklonjene"

#: src/components/forms/dashboard/Search/SearchDeclensions.js:51
msgid "{appName} will search"
msgstr "{appName} će pretraživati"

#: src/components/tariff/MonitoredMedia/MonitoredMedia.js:206
msgid "{countriesWithActiveMedia} countries with monitoring enabled"
msgstr "{countriesWithActiveMedia} zemlje s omogućenim medijskim praćenjem"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:107
msgid "{i} (current)"
msgstr "{i} (trenutna)"

#. js-lingui:icu=%7BolderCount%2C+plural%2C+one+%7B%2B+%23+older%7D+other+%7B%2B+%23+older%7D%7D&pluralize_on=olderCount
#: src/components/feed/InspectorToolbar/ToolbarPagination.js:32
msgid "+ # older"
msgid_plural "+ # older"
msgstr[0] "+ # stariji"
msgstr[1] "+ # stariji"
msgstr[2] "+ # starijih"

#. js-lingui:icu=%7BprocessedCount%2C+plural%2C+one+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+article+has+been+updated.%7D+other+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+articles+have+been+updated.%7D%7D&pluralize_on=processedCount
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:517
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:754
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:800
msgid "You have reached the limit for this action. {processedCount} article has been updated."
msgid_plural "You have reached the limit for this action. {processedCount} articles have been updated."
msgstr[0] "Dosegli ste limit za ovu radnju. {processedCount} članak je ažuriran."
msgstr[1] "Dosegli ste limit za ovu radnju. {processedCount} članka su ažurirana."
msgstr[2] "Dosegli ste limit za ovu radnju. {processedCount} članaka je ažurirano."

#. js-lingui:icu=%7BprocessedCount%2C+plural%2C+one+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+author+has+been+updated.%7D+other+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+authors+have+been+updated.%7D%7D&pluralize_on=processedCount
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:553
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:590
#: src/store/models/authors/AuthorsStore.js:528
#: src/store/models/authors/AuthorsStore.js:581
#: src/store/models/authors/AuthorsStore.js:657
#: src/store/models/authors/AuthorsStore.js:709
msgid "You have reached the limit for this action. {processedCount} author has been updated."
msgid_plural "You have reached the limit for this action. {processedCount} authors have been updated."
msgstr[0] "Dosegli ste limit za ovu radnju. {processedCount} autor je ažuriran."
msgstr[1] "Dosegli ste limit za ovu radnju. {processedCount} autora su ažurirana."
msgstr[2] "Dosegli ste limit za ovu radnju. {processedCount} autora je ažurirano."

#. js-lingui:icu=%7Bvalue%2C+plural%2C+one+%7B%23+post%7D+other+%7B%23+posts%7D%7D&pluralize_on=value
#: src/helpers/charts/formatters.js:97
msgid "# post"
msgid_plural "# posts"
msgstr[0] "# objava"
msgstr[1] "# objave"
msgstr[2] "# objava"

#. js-lingui:icu=%7Bvalue%2C+plural%2C+one+%7B%23+visit%7D+other+%7B%23+visits%7D%7D&pluralize_on=value
#: src/helpers/charts/formatters.js:62
msgid "# visit"
msgid_plural "# visits"
msgstr[0] "# posjet"
msgstr[1] "# posjeta"
msgstr[2] "# posjeta"

#: src/pages/newsroom/index.js:62
msgid "<0>Accurate</0> data is part of analytics."
msgstr "<0>Točni</0> podaci su dio analitike."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:88
msgid "help.engagementRate"
msgstr "<0>Engagement rate je metrika koja se koristi za procjenu prosječnog broja interakcija koje objava dobiva po jednom pratitelju. Pomaže u relativnom uspoređivanju spominjanja iz različitih kanala i izvora. Pomoću engagement rate možete: </0><1><2>utvrditi koje objave imaju najbolje i najgore rezultate, </2><3>usporediti stupanj angažmana koji generirate na različitim društvenim mrežama, </3><4>usporediti svoje rezultate s konkurencijom, </4><5>procijeniti utjecajne osobe. </5></1>"

#: src/pages/authors/index.js:69
msgid "<0>Export</0> detailed lists of authors"
msgstr "<0>Izvoz</0> detaljnih popisa autora"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:21
msgid "<0>Immediate notifications</0> about mentions on <1>TV and radio</1>. Be in the swim of things. Non-stop."
msgstr "<0>Trenutačne obavijesti</0> o spominjanjima na <1>TV i radiju</1>. Budite u tijeku događanja. Non-stop."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:72
msgid "help.influenceScore"
msgstr "<0>Rezultat utjecaja je broj (od 1 do 10) izračunat za svaku spomenutu stavku na društvenim medijima. Njegova vrijednost je primarno temeljena na dva parametra:</0><1><2>kako je vjerojatno da će se spomenuta stavka vidjeti,</2><3>koliko puta je spomenuta stavka prikazana, podijeljena ili retweetana.</3></1><4>Vjerujemo da će vam ova vrijednost pomoći otkriti spomenute stavke, autore i web stranice koje su najpopularnije i najutjecajnije. Na ovaj način možete dobiti dodatnu metriku za analizu vaših marketinških kampanja.</4>"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:38
msgid "<0>Non-stop</0> monitoring of selected TVs and radios"
msgstr "<0>Non-stop</0> praćenje odabranih TV i radijskih postaja"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesSearch.tsx:18
msgid "help.search.wordSearch.description"
msgstr "<0>Brzi pregled</0><1><2><3><4><5>Uneseni izraz</5><6>Što {appName} radi</6><7>Traži članke koji sadrže</7></4></3><8><9><10>Pingvin</10><11>riječ se mijenja po padežima, dijakritički znakovi i veličina slova nisu bitni</11><12>Pingvin, PINGVIN, pingvin, Pingvina, pingvini, pingvin</12></9><13><14>\"Pingvin\"</14><15>riječ se ne mijenja po padežima, dijakritički znakovi su bitni, veličina slova nije bitna</15><16>Pingvin, PINGVIN, pingvin</16></13><17><18>\"!Pingvin\"</18><19>riječ se ne mijenja po padežima, dijakritički znakovi i veličina slova su bitni</19><20>Pingvin</20></17></8></2></1><21>S promjenom po padežima</21><22>Ako unesemo u polje za pretragu: <23>Pingvin</23></22><24>{appName} će pronaći sve članke koji sadrže riječ <25>Pingvin</25> u bilo kojem obliku. Dakle, pronaći će se članci koji sadrže riječ <26>Pingvina</26> ({appName} mijenja riječ po padežima), <27>PINGVIN</27> (veličina slova nije bitna) ili <28>pingvin</28> (dijakritički znakovi nisu bitni).</24><29>Preporučujemo ovakav način pretrage za sve riječi koje se uobičajeno mijenjaju po padežima. To su tipično općenite riječi (pingvin), vlastita imena (Ivan) ili strani nazivi (facebook).</29><30>Točno podudaranje</30><31>Ako unesemo u polje za pretragu: <32>\"Pingvin\"</32> (stavimo riječ u navodnike)</31><33>{appName} će pronaći sve članke koji sadrže riječ <34>Pingvin</34>, ali samo u unesenom obliku (tj. riječ se ne mijenja po padežima). Dakle, pronaći će se članci koji sadrže riječ <35>Pingvin</35> ili<36>PINGVIN</36> (veličina slova nije bitna).</33><37>{appName} neće pronaći one članke koji sadrže samo riječ u promijenjenom obliku <38>Pingvina</38> ili riječ <39>pingvin</39> bez dijakritičkih znakova.</37><40>Preporučujemo ovakav način pretrage za nazive tvrtki i proizvoda (\"McDonald's\"), web domene (\"{appName}.hr\"), točno podudaranje riječi (\"najbolji\") ili akronime (\"SAD\").</40><41>Točno podudaranje uključujući veličinu slova</41><42>Ako unesemo u polje za pretragu: <43>\"!Pingvin\"</43> (stavimo riječ u navodnike i iza prvog navodnika stavimo uskličnik)</42><44>{appName} će pronaći sve članke koji sadrže riječ <45>Pingvin</45>, ali samo u unesenom obliku, uključujući i veličinu slova. Ovo je najstroža varijanta.</44><46>{appName} neće pronaći one članke koji sadrže npr. samo riječ <47>pingvin</47> napisanu malim slovima.</46><48><49>Preporučujemo ovakav način pretrage za nazive tvrtki i proizvoda (\"!Google\") ili akronime (\"!WHO\").</49></48>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesOperators.tsx:18
msgid "help.search.operators.description"
msgstr "<0>Brzi pregled</0><1><2><3><4>Uneseni izraz</4><5>{appName} pretražuje</5></3></2><6><7><8>pingvin AND tuljan</8><9>Članci koji sadrže obje riječi <10>pingvin</10> i <11>tuljan</11>.</9></7><12><13>pingvin tuljan</13><14>Članci koji sadrže obje riječi <15>pingvin</15> i <16>tuljan</16>. Razmak između riječi ponaša se kao da je tamo operator AND.</14></12><17><18>pingvin OR tuljan</18><19>Članci koji sadrže barem jednu od riječi <20>pingvin</20> ili <21>tuljan</21>.</19></17><22><23>pingvin -tuljan</23><24>Članci koji sadrže riječ <25>pingvin</25>, ali ne sadrže riječ <26>tuljan</26>.</24></22></6></1><27>AND</27><28>Ako želimo pretražiti članke koji sadrže nekoliko riječi ili fraza istovremeno, unesemo sve tražene riječi i odvojimo ih ili razmakom ili riječju <29>AND</29> (pisano velikim slovima).</28><30>Ako unesemo u polje za pretragu: <31>pingvin tuljan Praški+zoo \"!CEZ\"</31></30><32>To je isto kao da smo napisali: <33>pingvin AND tuljan AND Praški+zoo AND \"!CEZ\"</33></32><34>OR</34><35>Ako želimo pretražiti članke koji sadrže barem jednu od unesenih riječi ili fraza, unesemo sve tražene riječi i odvojimo ih riječju <36>OR</36> (pisano velikim slovima).</35><37>Primjer: <38>pingvin OR tuljan OR Praški+zoo OR \"!CEZ\"</38></37><39>NOT</39><40>Ako želimo iz rezultata pretrage ukloniti članke koji sadrže neke riječi ili fraze, napišemo popis zabranjenih riječi i fraza iza traženog izraza i ispred svake od njih stavimo znak minus.</40><41>Primjer: <42>pingvin -tuljan -Praški+zoo -\"!CEZ\"</42></41><43>Zagrade</43><44>Operatore pretrage možemo kombinirati prema potrebi. Kod složenijih izraza često trebamo odrediti i redoslijed u kojem želimo da se operatori pretrage procjenjuju. Za tu svrhu koristimo zagrade, koje funkcioniraju slično kao u matematici.</44><45>Ako unesemo u polje za pretragu: <46>\"!Billa\" AND (trgovina OR lanac OR prodavaonica OR supermarket OR hipermarket)</46></45><47>{appName} pretražuje sve članke koji sadrže riječ <48>Billa</48> (samo u unesenom obliku, uključujući veličinu slova) u kombinaciji s barem jednom od riječi <49>trgovina</49>, <50>lanac</50>, …</47><51>Na kraju, primjer kako možete sastaviti složene izraze u aplikaciji koristeći operatore pretrage: <52>naselje (kuća OR zgrada OR građevina) AND (balkon OR (plastični+prozori -\"!Veka\")) AND Zagreb+Trešnjevka~5 -(Maksimir OR Dubrava)</52></51>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesPhrase.tsx:18
msgid "help.search.phrase.description"
msgstr "<0>Brzi pregled</0><1><2><3><4>Uneseni izraz</4><5>Traži članke koji sadrže</5></3></2><6><7><8>Pametni+Ivan</8><9>Pametni Ivan, PAMETNI IVAN, pametni ivan, Pametnog Ivana, pametni ivan, pametni-ivan</9></7><10><11>\"Pametni Ivan\"</11><12>Pametni Ivan, PAMETNI IVAN, pametni ivan</12></10><13><14>\"Pametni-Ivan\"</14><15>Pametni-Ivan, PAMETNI-IVAN, pametni-ivan</15></13><16><17>\"!Pametni Ivan\"</17><18>Pametni Ivan</18></16></6></1><19>S padežima</19><20>Ako unesemo u polje za pretragu: <21>Pametni+Ivan</21> (riječi odvojimo plus znakom, tako da između njih nema razmaka)</20><22>{appName} će pretražiti sve članke koji sadrže izraz <23>Pametni Ivan</23> (tj. ove riječi jednu za drugom u ovom redoslijedu) u bilo kojem obliku. Dakle, pretraživat će se članci koji sadrže izraz <24>Pametnog Ivana</24> ({appName} sklanja riječi), <25>PAMETNI IVAN</25> (veličina slova nije bitna), <26>pametni ivan</26> (dijakritički znakovi nisu bitni) ili <27>pametni-ivan</27> (između riječi može biti i separator, npr. zarez ili crtica).</22><28>Preporučujemo ovakav način pretrage za imena osoba (Ivan+Novi), nazive tvrtki i organizacija (Ministarstvo+za+lokalni+razvoj) ili izraze (praćenje+medija).</28><29>Točno podudaranje</29><30>Ako unesemo u polje za pretragu: <31>\"Pametni Ivan\"</31> (stavimo cijeli izraz u navodnike)</30><32>{appName} će pretražiti sve članke koji sadrže izraz <33>Pametni Ivan</33>, ali samo u unesenom obliku. Dakle, pretraživat će se članci koji sadrže <34>Pametni Ivan</34> ili <35>PAMETNI IVAN</35> (veličina slova nije bitna).</32><36>{appName} neće pretražiti one članke koji sadrže samo sklonjeni izraz <37>Pametnog Ivana</37>, izraz <38>pametni ivan</38> bez dijakritičkih znakova ili izraz <39>pametni-ivan</39> sa separatorom.</36><40>Preporučujemo ovakav način pretrage za nazive tvrtki i proizvoda (\"{appName} Media\"), akronime (\"MFF UK\") ili točno podudaranje izraza (\"biti ili ne biti\").</40><41>Točno podudaranje sa separatorom</41><42>Pri traženju točnog podudaranja potrebno je unijeti u navodnike i separatore koji se mogu pojaviti između riječi - crtice, podvlake, znakovi @, itd. To se odnosi na sljedeće znakove: & @ _ + - ' #</42><43>Ako unesemo u polje za pretragu: <44>\"Pametni-Ivan\"</44></43><45>{appName} će pretražiti sve članke koji sadrže izraz <46>Pametni-Ivan</46> sa separatorom.</45><47>{appName} neće pretražiti one članke koji sadrže samo izraz <48>Pametni Ivan</48> bez separatora ili <49>Pametni&Ivan</49> s drugim separatorom nego što smo unijeli.</47><50>Tipični izrazi kod kojih ne zaboravite na separator su \"Ernst & Young\", \"info@{appName}.cz\", \"Mi+Te\" ili \"X-Men\".</50><51>Točno podudaranje uključujući veličinu slova</51><52>Ako unesemo u polje za pretragu: <53>\"!Pametni Ivan\"</53> (stavimo cijeli izraz u navodnike i iza prvog navodnika uskličnik)</52><54>{appName} će pretražiti sve članke koji sadrže izraz <55>Pametni Ivan</55>, ali samo u unesenom obliku, uključujući i veličinu slova. Ovo je najstroža varijanta.</54><56>{appName} neće pretražiti one članke koji sadrže npr. samo izraz <57>pametni ivan</57> napisan malim slovima.</56><58>Preporučujemo ovakav način pretrage za nazive tvrtki i proizvoda (\"!Zlatna žlica\") ili akronime (\"!AV RH\").</58>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesDistance.tsx:18
msgid "help.search.distance.description"
msgstr "<0>Brzi pregled</0><1><2><3><4>Uneseni izraz</4><5>Traži članke koji sadrže</5></3></2><6><7><8>tuljan+pingvin~5</8><9>pingvin je pomilovao tuljana</9></7><10><11>tukan+tuljan+pingvin~10</11><12>tukani su htjeli pojesti pingvina, ali tuljan je intervenirao</12></10><13><14>\"tuljan tukan\"~5</14><15>\"Tuljan!\" rekao je tukan i odletio.</15></13><16><17>\"tukan tuljan pingvin\"~5</17><18>Na slici s lijeva: pingvin, tuljan, tukan.</18></16></6></1><19>Sa sklonidbom</19><20>Ako unesemo u polje za pretragu: <21>tuljan+pingvin~5</21> (riječi odvojimo plus znakom i iza njih napišemo tildu i broj)</20><22>{appName} će pronaći sve članke koji sadrže riječi <23>tuljan</23> i <24>pingvin</24> u bilo kojem redoslijedu i na udaljenosti najviše 5 riječi jedna od druge. Unesene riječi se automatski sklanjaju i veličina slova ili dijakritički znakovi nisu bitni.</22><25>Preporučujemo ovakav način pretrage za riječi koje su povezane i u tekstu članka će biti blizu jedna drugoj (firma+Facebook~7).</25><26>Tačno podudaranje</26><27>Ako unesemo u polje za pretragu: <28>\"tuljan pingvin\"~5</28> (riječi stavimo u navodnike i iza drugih navodnika napišemo tildu i broj)</27><29>{appName} će pronaći sve članke koji sadrže riječi <30>tuljan</30> i <31>pingvin</31> u bilo kojem redoslijedu i na udaljenosti najviše 5 riječi jedna od druge. Obje unesene riječi se pretražuju samo u unesenom obliku, tj. ne sklanjaju se i dijakritički znakovi su bitni.</29>"

#: src/pages/newsroom/index.js:35
msgid "<0>Share press releases</0> and other external and internal communication with <1>Newsroom</1> and have an accurate overview of traffic directly in the application."
msgstr "<0>Dijelite priopćenja za tisak</0> i ostalu vanjsku i unutarnju komunikaciju putem <1>Newsrooma</1> i imajte točan pregled prometa izravno u aplikaciji."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:36
msgid "help.ave"
msgstr "<0>Koeficijent AVE (Advertising Value Equivalent) predstavlja financijsku procjenu medijskih aktivnosti. To je ekvivalent koliko bi koštao prostor dobiven sadržajem prema vrijednosti reklamnog prostora prema cjeniku određenog medija.</0><1>Za strojni izračun AVE koristimo ove varijable:</1><2><3>jedinična cijena oglasa u određenom mediju (npr: cijena po standardnoj stranici u tisku / 1s emitirane vijesti na TV ili radiju)</3><4>veličina članka u tisku / duljina reportaže na TV ili radiju</4><5>opseg informacija posvećenih temi unutar priloga</5></2>"

#. js-lingui-explicit-id
#: src/components/layout/Header/MessageDirty/MessageDirty.js:12
msgid "message.dirty.description"
msgstr "<0>Prikazani podaci možda ne odgovaraju vašim trenutnim postavkama jer je jedna ili više tema promijenjeno.</0><1>Učitajte stranicu ponovno za nekoliko minuta.</1>"

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:105
msgid "help.socialInteractions"
msgstr "<0>Broj socijalnih interakcija (like, share, komentar, pregled, retweet) kod spomena.</0>"

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:59
msgid "help.socialInteractionsOnline"
msgstr "<0>Broj socijalnih interakcija (like, share, komentar) na online člancima na Facebooku.</0><1>Statistika se ažurira jednom u 24 sata.</1>"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:11
msgid "missingBasics"
msgstr "<div>Hvala što ste poslali svoju objavu. Proveli smo početni pregled kako bismo osigurali da ispunjava naše osnovne zahtjeve.</div><br> <strong>Evo što smo pronašli:</strong>"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:15
msgid "responseInfo"
msgstr ""
"<div>Ovaj postupak može potrajati nekoliko trenutaka dok izvršavamo sljedeće korake:</div>\n"
" <ol>\n"
" <li><strong>Početno skeniranje:</strong> Brzi pregled ukupne strukture i formata vašeg sadržaja.</li>\n"
" <li><strong>Dubinska analiza:</strong> Pažljivo ispitivanje pojedinosti, jezika i konteksta vašeg podneska.</li>\n"
" <li><strong>Procjena kvalitete:</strong> Ocjenjivanje različitih aspekata kao što su jasnoća, koherentnost i relevantnost.</li>\n"
" <li><strong>Otkrivanje pogrešaka:</strong> Identificiranje potencijalnih problema, nedosljednosti ili područja za poboljšanje.</li>\n"
" <li><strong>Prijedlozi za optimizaciju:</strong> Priprema preporuka za poboljšanje vašeg sadržaja ako je potrebno.</li>\n"
" </ol>\n"
" <div>Naša umjetna inteligencija marljivo radi kako bi vam pružila točne i korisne povratne informacije. Cijenimo vaše strpljenje tijekom ove sveobuhvatne analize. Rezultati će uskoro biti dostupni.</div>"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:124
#: src/components/newsroom/components/PostsList/PostsList.js:98
msgid "<No title yet>"
msgstr "<Bez naslova>"

#: src/store/models/admin/customer/CustomerStore.js:220
msgid "<user already exists>"
msgstr "<korisnik već postoji>"

#: src/components/tariff/TariffLimits/TariffLimits.js:26
#: src/components/staff/admin/workspace/Workspace.js:359
msgid "30-day article limit"
msgstr "30-dnevni limit na broj članaka"

#: src/components/tariff/UsageTracker/UsageTracker.js:13
msgid "30-day limit"
msgstr "30-dnevni limit"

#: src/components/tariff/TariffLimits/TariffLimits.js:63
#: src/components/staff/admin/workspace/Workspace.js:378
msgid "30-day limit on exported articles"
msgstr "30-dnevni limit na broj izvezenih članaka"

#: src/components/tariff/TariffLimits/TariffLimits.js:224
#: src/components/staff/admin/workspace/Workspace.js:559
msgid "30-day limit on exported social media mentions"
msgstr "30-dnevni limit na broj izvezenih spominjanja na društvenim mrežama"

#: src/components/tariff/TariffLimits/TariffLimits.js:241
#: src/components/staff/admin/workspace/Workspace.js:514
msgid "30-day limit on licensed article downloads"
msgstr "30-dnevno ograničenje za preuzimanje licenciranih članaka"

#: src/components/staff/admin/workspace/Workspace.js:629
msgid "30-day limit on OCR pages"
msgstr "30-dnevni limit na broj stranica za OCR"

#: src/components/tariff/TariffLimits/TariffLimits.js:203
#: src/components/staff/admin/workspace/Workspace.js:540
msgid "30-day limit on social media mentions"
msgstr "30-dnevni limit na broj spominjanja na društvenim mrežama"

#: src/components/staff/admin/workspace/Workspace.js:650
msgid "30-day limit on transcribed seconds"
msgstr "30-dnevni limit na broj sekundi transkripcije"

#: src/components/staff/admin/workspace/Workspace.js:399
msgid "30-day limit on translated articles with Google Translate"
msgstr "30-dnevni limit na broj članaka prevedenih putem Google Translate"

#: src/components/medialist/forms/FormEditAuthor.js:755
msgid "About Author"
msgstr "O autoru"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:132
msgid "Above avg."
msgstr "Iznad prosjeka"

#: src/components/tariff/Permissions/Permissions.js:45
msgid "Access"
msgstr "Pristup"

#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:52
msgid "Access comprehensive articles via {appName}’s media monitoring, covering online, traditional, and social media content."
msgstr "Pristupite sveobuhvatnim člancima putem praćenja medija aplikacije {appName}, koje pokriva sadržaj online, tradicionalnih i društvenih medija."

#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:49
msgid "Access Full Articles via Media Monitoring"
msgstr "Pristupite cjelovitim člancima putem praćenja medija"

#: src/components/dashboards/PageExpiredSharedDashboard/PageExpiredSharedDashboard.js:37
msgid "Access to this dashboard has expired."
msgstr "Pristup ovoj nadzornoj ploči je istekao."

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:22
msgid "Account info"
msgstr "Podaci o računu"

#: src/components/misc/Changelog/ChangelogTableRow.js:114
msgid "Account manager"
msgstr "Voditelj računa"

#: src/components/staff/admin/customer/bio/CustomerBio.js:112
msgid "Account managers"
msgstr "Voditelji računa"

#: src/components/settings/SettingsHeader/SettingsHeader.js:8
msgid "Account settings"
msgstr "Postavke računa"

#: src/components/settings/SettingsTheme/SettingsTheme.js:11
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:87
msgid "Account theme"
msgstr "Izgled računa"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:57
msgid "Activate"
msgstr "Aktiviraj"

#: src/components/staff/admin/user/User.js:235
msgid "Activated"
msgstr "Aktiviran"

#: src/components/emailing/content/EmailingSettingsContent.js:76
#: src/components/emailing/content/EmailingCampaignsContent.tsx:32
msgid "Activated senders without verification:"
msgstr "Aktivirani pošiljatelji bez napredne provjere:"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:173
#: src/components/staff/admin/user/User.js:138
#: src/components/staff/admin/customer/users/UsersTable.js:116
#: src/components/forms/dashboard/Search/SearchUsers.js:99
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:151
msgid "Active"
msgstr "Aktivan"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:80
msgid "Active Article Language"
msgstr "Aktivni jezik članaka"

#: src/components/staff/admin/workspace/ToggleActiveMedia.js:29
msgid "Active only"
msgstr "Samo aktivno"

#: src/components/reports/Content/ReportsList/FormToggleActive/FormToggleActive.js:34
msgid "Active report"
msgstr "Aktivno izvješće"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:93
msgid "Active Source Country"
msgstr "Dozvoljene zemlje u člancima"

#: src/components/medialist/content/MedialistAuthorCreate.js:16
msgid "Activity Overview"
msgstr "Pregled aktivnosti"

#: src/components/topics/Content/TopicsList/FormAddKeyword/FormAddKeyword.tsx:54
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:591
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:61
#: src/components/medialist/forms/modules/FormArray.js:198
#: src/components/medialist/forms/modules/FormArray.js:220
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:38
#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:190
#: src/components/emailing/forms/FormEmailRecipients.js:120
#: src/components/ReusableFeed/FormAddArticle.tsx:42
msgid "Add"
msgstr "Dodaj"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:178
msgid "Add a sender to activate Emailing."
msgstr "Dodajte prvog pošiljatelja za aktivaciju e-pošte."

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:92
msgid "Add all to selection"
msgstr "Dodaj sve u odabir"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:96
msgid "Add annotation"
msgstr "Dodaj anotaciju"

#: src/helpers/modal/withModalAddArticle/withModalAddArticle.tsx:17
#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:95
#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:156
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:90
#: src/components/emailing/content/mediaCoverage/AddArticleToMediaCoverage.tsx:10
msgid "Add article"
msgstr "Dodaj članak"

#: src/components/emailing/content/mediaCoverage/AddArticleToMediaCoverage.tsx:13
msgid "Add article media coverage"
msgstr "Dodaj članak u medijsko praćenje"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:237
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:219
msgid "Add article to topic"
msgstr "Dodaj članak na temu"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:537
#: src/components/newsroom/content/post/AttachmentsList.js:89
msgid "Add Attachment"
msgstr "Dodaj privitak"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:129
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:100
msgid "Add authors to list"
msgstr "Dodaj autore na popis"

#: src/components/newsroom/forms/FormNewsroomPost/CategoriesSelector.js:71
msgid "Add Category"
msgstr "Dodaj kategoriju"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:118
msgid "Add content"
msgstr "Dodaj sadržaj"

#: src/components/dashboards/DashboardSelector/CreateDashboard.js:20
msgid "Add Dashboard"
msgstr "Dodaj nadzornu ploču"

#: src/components/reports/Content/ReportsList/AddDay.js:25
msgid "Add day"
msgstr "Dodaj dan"

#: src/components/staff/admin/workspace/Workspace.js:827
msgid "Add domains separated by a comma (domain1.com, domain2.com)"
msgstr "Dodajte domene odvojene zarezom (domain1.com, domain2.com)"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:79
msgid "Add Gallery"
msgstr "Dodaj galeriju"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:66
msgid "Add Image"
msgstr "Dodaj sliku"

#: src/components/topics/Content/TopicsList/FormAddKeyword/FormAddKeyword.tsx:45
msgid "Add Keyword"
msgstr "Dodaj ključnu riječ"

#: src/components/newsroom/forms/FormNewsroomSettings/LanguageSection.tsx:91
msgid "Add language variant"
msgstr "Dodaj varijantu jezika"

#: src/components/newsroom/content/posts/ChooseTemplates.tsx:109
msgid "Add main message"
msgstr "Dodajte glavnu poruku"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:57
msgid "Add manually"
msgstr "Dodaj ručno"

#: src/components/newsroom/components/AiTools/AiGenerateContent.tsx:117
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:137
msgid "Add missing data"
msgstr "Dodajte nedate podatke"

#: src/components/emailing/components/EmailRecipientsList/RecipientsButton.tsx:37
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:164
msgid "Add Missing Info"
msgstr "Dodaj nedostajuće informacije"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:64
msgid "Add new keypoint"
msgstr "Dodajte novu ključnu točku"

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:111
msgid "Add new mediatypes"
msgstr "Dodaj nove vrste medija"

#: src/components/newsroom/content/modules/CustomQuotes.tsx:84
msgid "Add new quote"
msgstr "Dodajte novu citat"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:52
msgid "Add new sender"
msgstr "Dodaj novog pošiljatelja"

#: src/components/topics/Content/TopicsList/TopicsList.js:63
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:44
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:70
msgid "Add New Topic"
msgstr "Dodaj novu temu"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:28
#: src/components/medialist/forms/FormEditAuthor.js:710
msgid "Add note"
msgstr "Dodaj bilješku"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:37
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:57
msgid "Add note to article"
msgstr "Dodaj bilješku članku"

#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:55
msgid "Add recipient"
msgstr "Dodaj primatelja"

#: src/components/emailing/modules/PreviewEmail/RecipientsIsEmpty.tsx:32
#: src/components/emailing/content/tabs/AddRecipients.tsx:78
msgid "Add recipients"
msgstr "Dodaj primatelje"

#: src/components/emailing/content/Signature.tsx:113
#: src/components/emailing/content/Signature.tsx:116
msgid "Add signature"
msgstr "Dodaj potpis"

#: src/components/emailing/forms/FormEmailRecipients.js:112
msgid "Add single authors, author’s lists or emails"
msgstr "Dodavanje pojedinačnih autora, popisa autora ili e-mailova"

#: src/components/newsroom/content/modules/CustomQuotes.tsx:35
msgid "Add specific quotes you want to include in your article, along with the name of the person being quoted. We will use these quotes exactly as provided.\""
msgstr "Dodajte određene citate koje želite uključiti u svoj članak, zajedno s imenom osobe koju citirate. Koristit ćemo ove citate točno onako kako su navedeni.”"

#: src/components/reports/Content/ReportsList/AddTime.js:27
msgid "Add time"
msgstr "Dodaj vrijeme"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/ModalAddDashboardFooter.tsx:35
msgid "Add to Dashboard"
msgstr "Dodaj na nadzornu ploču"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:179
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:210
msgid "Add to export"
msgstr "Dodaj u eksport"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:38
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:94
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:150
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:207
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:130
msgid "Add to filters"
msgstr "Dodaj u filtre"

#: src/components/medialist/forms/FormEditAuthor.js:670
#: src/components/medialist/content/withAddToBasketPopup.js:44
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorBaskets/AddAuthorToBasket.js:26
msgid "Add to list"
msgstr "Dodaj na popis"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:263
msgid "Add to next report"
msgstr "Dodaj u sljedeći izvještaj"

#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:282
msgid "Add to report"
msgstr "Dodaj u izvještaj"

#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:91
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Keywords.js:52
msgid "Add to search"
msgstr "Dodaj u pretragu"

#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:58
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:60
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:895
msgid "Add Topic"
msgstr "Dodaj temu"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:41
#: src/components/settings/SettingsUserManagement/AddUsers.tsx:23
msgid "Add users"
msgstr "Dodaj korisnike"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:42
msgid "Add users to workspace"
msgstr "Dodaj korisnike u radni prostor"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:101
msgid "Add Video"
msgstr "Dodaj video"

#: src/components/dashboards/Content.js:89
#: src/components/dashboards/Content.js:90
msgid "Add Widget"
msgstr "Dodaj widget"

#: src/store/models/ExportStore.js:316
#: src/store/models/monitoring/Inspector/Inspector.ts:449
msgid "Added to export."
msgstr "Dodano u izvoz."

#: src/store/models/monitoring/Inspector/Inspector.ts:422
msgid "Added to next report."
msgstr "Dodano u sljedeći izvještaj."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:29
msgid "Additional settings"
msgstr "Dodatne postavke"

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorAddress/AuthorAddress.js:8
msgid "Address"
msgstr "Adresa"

#: src/constants/analytics.js:143
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:42
#: src/components/misc/ActionsBar/View/ViewMenu.js:237
msgid "Adjusted Reach"
msgstr "Prilagođeni Doseg"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:160
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:162
#: src/components/staff/admin/workspace/Workspace.js:162
#: src/components/staff/admin/user/getUserAttributes.js:9
#: src/components/staff/admin/user/User.js:88
#: src/components/reports/history/HistoryTable.js:452
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:60
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:62
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:406
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:413
#: src/components/medialist/forms/FormEditAuthor.js:396
#: src/components/medialist/forms/FormEditAuthor.js:542
#: src/components/layout/Header/UserMenu/UserMenu.tsx:200
#: src/app/components/monitoring-navigation.tsx:314
msgid "Admin"
msgstr "Admin"

#: src/components/reports/Content/ReportsList/ReportsForm.js:331
#: src/components/forms/dashboard/ExportResend/ExportResend.js:133
msgid "Advanced attachment settings"
msgstr "Napredne postavke privitka"

#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:15
msgid "Advanced export settings"
msgstr "Napredne postavke izvoza"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:79
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:174
msgid "Advanced settings"
msgstr "Napredne postavke"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:58
msgid "Advanced template settings"
msgstr "Napredne postavke predloška"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:255
msgid "Advertising Value Equivalency"
msgstr "Ekvivalentnost vrijednosti oglašavanja"

#: src/constants/analytics.js:101
#: src/constants/analytics.js:621
#: src/constants/analytics.js:755
#: src/components/layout/AuthWrapper/constants/features.slides.js:191
msgid "Advertising Value Equivalent (AVE)"
msgstr "Ekvivalent vrijednosti oglašavanja (AVE)"

#: src/constants/analytics.js:99
msgid "Advertising Value Equivalent (AVE) by sentiment"
msgstr "Ekvivalent vrijednosti oglašavanja (AVE) prema sentimentu"

#: src/components/staff/admin/workspace/Workspace.js:920
#: src/components/settings/SettingsTariff/SettingsTariff.js:37
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:48
msgid "Agency media"
msgstr "Agencijski mediji"

#: src/components/newsroom/components/AiTools/AiCheckPostResult.tsx:88
msgid "AI check"
msgstr "AI provjera"

#: src/components/newsroom/components/AiTools/AiCheckLoadingInfo.tsx:28
msgid "AI Checkup information"
msgstr "Informacije o AI provjeri"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:218
msgid "Align Center"
msgstr "Poravnaj na sredinu"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:230
msgid "Align Justify"
msgstr "Poravnaj po bloku"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:212
msgid "Align Left"
msgstr "Poravnaj lijevo"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:224
msgid "Align Right"
msgstr "Poravnaj desno"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:83
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:37
#: src/components/reports/history/HistoryTable.js:86
#: src/components/reports/history/HistoryTable.js:96
#: src/components/reports/history/HistoryTable.js:331
#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:123
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:144
#: src/components/misc/portable/PortableExport/CounterTitle.js:8
#: src/components/misc/ActionsBar/Selector/Selector.js:51
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:146
#: src/components/analytics/AnalyticsContent.js:156
#: src/components/analytics/AnalyticsContent.js:166
msgid "All"
msgstr "Sve"

#: src/store/models/ExportStore.js:318
msgid "All articles are already in export."
msgstr "Svi članci su već u izvozu."

#: src/components/exportList/Content/Content.tsx:97
#: src/app/components/monitoring-navigation.tsx:130
msgid "All articles will be removed from export."
msgstr "Svi će članci biti uklonjeni iz izvoza."

#: src/components/misc/portable/PortableExport/CounterTitle.js:10
msgid "All except"
msgstr "Sve osim"

#: src/components/layout/AuthWrapper/constants/features.slides.js:400
msgid "All features of the browser app are accessible on a mobile device. The app keeps you informed even when you are drinking a morning cup of coffee."
msgstr "Sve značajke web aplikacije dostupne su na mobilnom uređaju. Aplikacija vas drži informiranim čak i dok pijete jutarnju šalicu kave."

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
msgid "all mediatypes for"
msgstr "sve vrste medija za"

#: src/store/models/Megalist/MegalistFilter.js:49
#: src/components/topics/Content/TopicsList/MegalistToolbar/MediatypeFilterPopup.js:11
msgid "All Sources"
msgstr "Svi izvori"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitorsReports.js:40
msgid "All topics"
msgstr "Sve teme"

#: src/components/medialist/forms/FormEditAuthor.js:576
msgid "All unsaved changes will be lost. Do you really want to cancel the changes?"
msgstr "Sve nespremljene promjene bit će izgubljene. Želite li zaista otkazati promjene?"

#: src/components/staff/admin/workspace/Workspace.js:743
msgid "Allow adjusted reach (PL)"
msgstr "Dopusti prilagođeni doseg (PL)"

#: src/components/staff/admin/workspace/Workspace.js:716
msgid "Allow automatic sentiment"
msgstr "Dopusti automatsko otkrivanje sentimenta"

#: src/components/staff/admin/workspace/Workspace.js:725
msgid "Allow automatic summarization"
msgstr "Dopusti automatsko sažimanje"

#: src/components/staff/admin/workspace/Workspace.js:734
msgid "Allow AVE"
msgstr "Dopusti AVE"

#: src/components/staff/admin/workspace/Workspace.js:752
msgid "Allow AVE Coefficient (for media analysis)"
msgstr "Dopusti AVE koeficijent (za medijske analize)"

#: src/components/staff/admin/workspace/Workspace.js:707
msgid "Allow custom logo"
msgstr "Dopusti prilagođeni logo"

#: src/components/staff/admin/workspace/Workspace.js:598
msgid "Allow english social media"
msgstr "Dopusti objave sa soc. mreža na engleskom"

#: src/components/staff/admin/workspace/Workspace.js:772
msgid "Allow forcing articles to email reports"
msgstr "Dopusti funkciju \"Pošalji članak u sljedećem izvješću\""

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:93
msgid "Allow search engines to index this blog (including inclusion of articles in media monitoring and analysis of online mentions)"
msgstr "Omogući tražilicama indeksiranje ovog bloga (uključujući uključivanje članaka u medijski monitoring i analizu online spominjanja)"

#: src/components/staff/admin/workspace/Workspace.js:609
msgid "Allow users to create own articles"
msgstr "Dopustite korisnicima stvaranje vlastitih članaka"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:31
msgid "Allowing detailed tracking of distribution campaigns."
msgstr "Omogućuje detaljno praćenje distribucijskih kampanja."

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:63
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:86
#: src/components/staff/admin/customer/expenses/ExpenseTable.js:81
#: src/components/staff/admin/customer/expenses/DetailExpenseModal.js:38
msgid "Amount"
msgstr "Iznos"

#: src/components/emailing/content/EmailingSettingsContent.js:32
msgid "An email sender record with this address already exists. Please check your existing records or try again."
msgstr "Zapis pošiljatelja e-pošte s ovom adresom već postoji. Provjerite svoje postojeće zapise ili pokušajte ponovo."

#: src/pages/_error.js:36
msgid "An error {statusCode} occurred on server"
msgstr "Na serveru se dogodila greška {statusCode}"

#: src/pages/_error.js:37
msgid "An error occurred on client"
msgstr "Došlo je do pogreške na klijentu"

#: src/components/emailing/content/EmailingSettingsContent.js:17
msgid "An error occurred while authorizing our application to use the external service."
msgstr "Došlo je do pogreške prilikom autorizacije naše aplikacije za korištenje vanjske usluge."

#: src/components/staff/admin/user/getUserAttributes.js:19
msgid "Analyst"
msgstr "Analitičar"

#: src/store/models/dashboards/DashboardPreview.js:87
#: src/pages/analytcs.js:16
#: src/components/widgets/modules/stats/WidgetStats.js:67
#: src/components/widgets/modules/analytics/WidgetAnalytics.js:29
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:102
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:27
#: src/components/layout/AuthWrapper/constants/features.slides.js:165
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:16
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:16
#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:36
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:36
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormSubtype/FormSubtype.js:56
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:20
#: src/components/analytics/AnalyticsContent.js:105
#: src/app/components/monitoring-navigation.tsx:81
msgid "Analytics"
msgstr "Analitika"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:140
msgid "AND"
msgstr "I"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderVideo/HeaderVideo.js:43
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderSocial/HeaderSocial.js:25
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:37
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:69
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationHeader.js:25
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationHeader.js:29
#: src/components/feed/InspectorToolbar/InspectorToolbar.js:134
msgid "Annotation"
msgstr "Anotacija"

#: src/pages/user/yoy-analysis.js:34
msgid "Annual Media Analysis"
msgstr "Godišnja medijska analiza"

#: src/components/notifications/Content.js:28
msgid "App"
msgstr "Aplikacija"

#: src/components/staff/admin/workspace/Workspace.js:932
#: src/components/settings/SettingsTariff/SettingsTariff.js:45
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:55
msgid "Application permissions"
msgstr "Dozvole aplikacija"

#: src/components/settings/SettingsApplication/SettingsApplication.js:19
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:98
msgid "Application settings"
msgstr "Postavke aplikacije"

#: src/components/newsroom/components/AiTools/AiGenerateContent.tsx:138
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:161
msgid "Apply"
msgstr "Primijeniti"

#: src/components/medialist/content/FeedMedialist/FeedMedialistItem.js:229
msgid "archive"
msgstr "arhiva"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:22
msgid "Archive"
msgstr "Arhiva"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:129
msgid "Are you ready to send the email?"
msgstr "Jeste li spremni poslati e-poštu?"

#. placeholder {0}: item.filename
#. placeholder {0}: file.name
#: src/components/newsroom/content/post/AttachmentsList.js:66
#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:42
msgid "Are you sure you want to delete {0}?"
msgstr "Jeste li sigurni da želite izbrisati {0}?"

#: src/components/emailing/content/SignaturePopup.tsx:36
msgid "Are you sure you want to delete signature?"
msgstr "Jeste li sigurni da želite izbrisati potpis?"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:397
msgid "Are you sure you want to delete this blog post?"
msgstr "Jeste li sigurni da želite izbrisati ovu objavu na blogu?"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:87
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:100
msgid "Are you sure you want to delete this email?"
msgstr "Jeste li sigurni da želite izbrisati ovaj email?"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:122
msgid "Are you sure you want to delete this sender?"
msgstr "Jeste li sigurni da želite izbrisati ovog pošiljatelja?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:662
msgid "Are you sure you want to delete your Newsroom? This action will delete all articles and settings."
msgstr "Jeste li sigurni da želite izbrisati svoj Newsroom? Ova će radnja izbrisati sve članke i postavke."

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:299
msgid "Are you sure you want to publish the changes?"
msgstr "Jeste li sigurni da želite objaviti promjene?"

#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:110
msgid "Are you sure you want to remove all recipients from this report?"
msgstr "Jeste li sigurni da želite ukloniti sve primatelje iz ovog izvješća?"

#: src/components/staff/admin/workspace/UsersTable/RemoveUsers.tsx:27
msgid "Are you sure you want to remove these users from the workspace?"
msgstr "Jeste li sigurni da želite ukloniti ove korisnike iz radnog prostora?"

#: src/components/newsroom/content/posts/NewsroomPosts.js:206
msgid "Are you sure you want to remove this article?"
msgstr "Jeste li sigurni da želite ukloniti ovaj članak?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:679
msgid "Are you sure you want to remove this Newsroom?"
msgstr "Jeste li sigurni da želite ukloniti ovaj Newsroom?"

#: src/components/staff/admin/workspace/UsersTable/RemoveUser.js:30
#: src/components/staff/admin/user/WorkspacesTable.js:143
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:400
msgid "Are you sure you want to remove this user from the workspace?"
msgstr "Jeste li sigurni da želite ukloniti ovog korisnika iz radnog prostora?"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:300
msgid "Are you sure you want to set this post to draft?"
msgstr "Jeste li sigurni da želite postaviti ovaj post kao skicu?"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:205
msgid "area"
msgstr "površina"

#: src/components/OurChart/OurChartAdvanced.js:148
msgid "Area"
msgstr "Površina"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:72
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:56
#: src/components/misc/MntrEditor/extensions/ExtensionArticle.tsx:33
msgid "Article"
msgstr "Članak"

#. placeholder {0}: item.title
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:364
msgid "Article '<0>{0}</0>' will be removed."
msgstr "Članak '<0>{0}</0>' će biti uklonjen."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:575
msgid "Article '<0>{title}</0>' will be removed."
msgstr "Članak '<0>{title}</0>' će biti uklonjen."

#: src/components/misc/ActionsBar/View/ViewMenu.js:140
msgid "Article Area"
msgstr "Područje članka"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:53
msgid "Article can still be attached later"
msgstr "Članak se može priložiti i kasnije"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:34
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:48
msgid "Article clipping"
msgstr "Izrezak članka"

#: src/store/models/OurChart.js:531
#: src/store/models/OurChart.js:563
#: src/store/models/OurChart.js:788
#: src/constants/stats.ts:11
#: src/constants/analytics.js:26
#: src/constants/analytics.js:1097
#: src/components/widgets/modules/stats/WidgetStats.js:203
#: src/components/widgets/modules/stats/WidgetStats.js:216
#: src/components/widgets/modules/stats/WidgetStats.js:229
#: src/components/monitoring/FeedChart/FeedChart.js:28
#: src/components/medialist/content/MedialistInspector/AuthorChart/AuthorChart.js:65
msgid "Article count"
msgstr "Broj članaka"

#: src/components/topics/Content/TopicsList/KeywordStatsTable.js:23
msgid "Article count for the last 30 days"
msgstr "Broj članaka u posljednjih 30 dana"

#: src/constants/analytics.js:192
msgid "Article count vs. GRP vs. AVE"
msgstr "Broj članaka vs. GRP vs. AVE"

#: src/store/models/monitoring/Inspector/Inspector.ts:778
msgid "Article has been copied to the clipboard."
msgstr "Članak je kopiran u međuspremnik."

#: src/store/models/monitoring/WorkspaceArticles.js:219
msgid "Article has been removed."
msgstr "Članak je uklonjen."

#: src/store/models/monitoring/WorkspaceArticles.js:193
msgid "Article has been updated."
msgstr "Članak je ažuriran."

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:215
msgid "Article has no annotations assigned. Select the text to add."
msgstr "Članak nema dodijeljenih anotacija. Odaberite tekst koji želite dodati."

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:63
msgid "Article history"
msgstr "Povijest članka"

#: src/store/models/monitoring/Inspector/Inspector.ts:451
msgid "Article is already in export."
msgstr "Članak je već u izvozu."

#: src/components/article/Content.js:17
msgid "Article link has expired"
msgstr "Poveznica na članak je istekla"

#: src/components/layout/MntrActiveFilters/modules/ArticleMentions.js:12
msgid "Article mentions"
msgstr "Spominjanja članka"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:146
msgid "Article numbering"
msgstr "Numeracija članaka"

#: src/store/models/admin/customer/CustomerStore.js:303
msgid "Article recreation started successfully."
msgstr "Ponovno stvaranje članka je uspješno započelo."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:181
msgid "Article screenshot"
msgstr "Snimka zaslona članka"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:37
#: src/components/layout/MntrActiveFilters/modules/EmptyTags.js:23
msgid "Article Tags"
msgstr "Oznake članaka"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:441
msgid "Article Text"
msgstr "Tekst članka"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:83
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:129
msgid "Article transcript"
msgstr "Transkript članka"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:101
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:381
msgid "Article Type"
msgstr "Vrsta članka"

#: src/components/ReusableFeed/FormAddArticle.tsx:30
msgid "Article URL"
msgstr "URL članka"

#: src/store/models/monitoring/Inspector/Inspector.ts:751
#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:58
#: src/components/monitoring/FeedList/FeedListItem/FeedListItem.js:138
msgid "Article URL has been copied to the clipboard. Without a login, it will be accessible for 30 days."
msgstr "URL adresa članka je kopirana u međuspremnik. Bez prijave bit će dostupna 30 dana."

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:70
msgid "Article view"
msgstr "Prikaz članka"

#: src/store/models/monitoring/Inspector/Inspector.ts:551
msgid "Article was reported"
msgstr "Članak je prijavljen"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:278
msgid "Article was successfully published on your Newsroom page"
msgstr "Članak je uspješno objavljen na vašoj stranici Newsroom"

#: src/store/models/dashboards/DashboardPreview.js:75
#: src/components/trash/Content.js:55
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:95
#: src/components/newsroom/content/posts/NewsroomPosts.js:80
#: src/components/medialist/constants/medialist.tabNavigation.js:27
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:20
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:18
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:109
#: src/components/layout/MntrActiveFilters/modules/MedialistArticles.js:18
#: src/components/forms/dashboard/Search/SearchNewsroom.js:31
#: src/components/exportList/History/HistoryTable/HistoryTable.js:63
#: src/components/exportList/Content/HeadingExport/HeadingExport.js:36
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:30
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:30
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:58
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:17
#: src/app/components/monitoring-navigation.tsx:71
msgid "Articles"
msgstr "Članci"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:525
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:808
msgid "Articles updated successfully."
msgstr "Članci su uspješno ažurirani."

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:38
msgid "Assess competitors and trends to refine your strategy."
msgstr "Analizirajte prednosti i slabosti konkurencije, pratite trendove i usavršavajte svoju strategiju."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:28
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:64
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:260
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:361
#: src/components/monitoring/FeedActionsBar/withAddTagPopup/AddTagPopupContent.js:16
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:50
msgid "Assign tag"
msgstr "Dodijeli oznaku"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:34
msgid "Assign tag to article"
msgstr "Dodijeli oznaku članku"

#: src/components/medialist/forms/FormEditAuthor.js:626
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorTags/AddAuthorTag.js:19
msgid "Assign tag to author"
msgstr "Dodijeli oznaku autoru"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:25
msgid "Assistant creates a draft of the email content based on your specific needs"
msgstr "Asistent izrađuje nacrt sadržaja e-pošte na temelju vaših specifičnih potreba"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:192
msgid "Attached articles"
msgstr "Priloženi članci"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:238
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:52
msgid "Attachment"
msgstr "Privitak"

#: src/components/reports/history/HistoryTable.js:173
#: src/components/newsroom/content/post/AttachmentsList.js:81
msgid "Attachments"
msgstr "Privitci"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:68
msgid "Audio"
msgstr "Audio"

#: src/constants/analytics.js:1094
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:485
#: src/components/newsroom/content/modules/CustomQuotes.tsx:69
#: src/components/monitoring/Inspector/InspectorMonitora/AuthorsList/AuthorsList.js:21
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:258
msgid "Author"
msgstr "Autor"

#: src/components/medialist/constants/medialist.tabNavigation.js:12
msgid "Author Detail"
msgstr "Detalji autora"

#: src/components/medialist/content/AuthorBasketsMenu.js:26
#: src/components/medialist/content/AuthorBasketSelectorButton.js:8
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:19
msgid "Author Lists"
msgstr "Popisi autora"

#: src/pages/authors/index.js:89
msgid "Author tags"
msgstr "Oznake autora"

#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:874
msgid "Author Tags"
msgstr "Oznake autora"

#: src/components/medialist/forms/FormEditAuthor.js:233
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:122
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:67
msgid "Author type"
msgstr "Vrsta autora"

#: src/store/models/authors/AuthorsStore.js:1079
msgid "Author was deleted."
msgstr "Autor je izbrisan."

#: src/store/models/authors/AuthorsStore.js:1168
msgid "Author was reported."
msgstr "Autor je prijavljen."

#: src/components/medialist/forms/FormEditAuthor.js:316
#: src/components/medialist/forms/FormEditAuthor.js:481
msgid "Author will be deleted."
msgstr "Autor će biti izbrisan."

#: src/components/layout/MntrFiltersBar/forms/FormAuthor/FormAuthor.tsx:18
msgid "Author's name"
msgstr "Ime autora"

#: src/components/medialist/forms/FormEditAuthor.js:997
msgid "Author's shortname"
msgstr "Autorovo kratko ime"

#: src/components/medialist/forms/FormEditAuthor.js:834
#: src/components/medialist/forms/FormEditAuthor.js:992
msgid "Author's shortnames"
msgstr "Autorove kratice"

#: src/components/monitoring/Inspector/InspectorMonitora/AuthorsList/AuthorsList.js:21
#: src/components/medialist/content/MedialistDashboard.js:82
#: src/components/medialist/content/MedialistDashboard.js:115
#: src/components/layout/Sidebar/modules/AuthorsNavigation/AuthorsNavigation.js:20
#: src/components/forms/dashboard/Search/SearchAuthors.js:39
#: src/components/emailing/forms/FormEmailRecipients.js:131
#: src/components/emailing/components/EmailRecipientsList/RenderAllRecipients.tsx:40
msgid "Authors"
msgstr "Autori"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:560
#: src/store/models/authors/AuthorsStore.js:535
msgid "Authors added."
msgstr "Autori su dodani."

#: src/components/emailing/content/tabs/AddRecipients.tsx:69
msgid "Authors lists"
msgstr "Popisi autora"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:598
#: src/store/models/authors/AuthorsStore.js:603
msgid "Authors removed."
msgstr "Autori su uklonjeni."

#: src/store/models/authors/AuthorsStore.js:664
msgid "Authors updated successfully."
msgstr "Autori su uspješno ažurirani."

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:94
msgid "Authors with types “agency”, “publisher” or “editorial office” can’t use merge tags  *|LAST_NAME|*,  *|VOKATIV_L|*. If you want to apply these merge tags to the author, change their type to “author” or “blogger” and add the last name."
msgstr "Autori s tipom „agencija“, „izdavač“ ili „uredništvo“ ne mogu koristiti merge oznake *|LAST_NAME|*, *|VOKATIV_L|*. Ako želite primijeniti ove merge oznake na autora, promijenite njihov tip u „autor“ ili „bloger“ i dodajte prezime."

#: src/components/staff/admin/user/User.js:62
msgid "Autologin link"
msgstr "Autologin poveznica"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:178
msgid "Automatic summary"
msgstr "Automatski sažetak"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:270
#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:74
msgid "Automatic transcript"
msgstr "Automatski transkript"

#: src/components/tariff/TariffLimits/TariffLimits.js:99
msgid "Automatic translations 30-day limit"
msgstr "30-dnevni limit na broj prevedenih članaka"

#: src/constants/stats.ts:6
#: src/constants/analytics.js:994
#: src/components/widgets/modules/stats/WidgetStats.js:154
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:387
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:252
#: src/components/misc/ActionsBar/View/ViewMenu.js:203
msgid "AVE"
msgstr "AVE"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:237
msgid "AVE and sentiment"
msgstr "AVE i sentiment"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:39
msgid "AVE Coefficient"
msgstr "AVE Koeficijent"

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:23
msgid "formatNumber.B"
msgstr "mlrd."

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:165
#: src/components/notifications/AppNotifications/AppNotifications.js:18
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:197
#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepTitleAndCommunicationPlan.tsx:106
#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:82
#: src/components/newsroom/content/posts/ChooseTemplates.tsx:98
#: src/components/newsroom/components/NewsroomHeading/NewsroomHeading.js:20
#: src/components/misc/ActionsBar/View/ViewMenu.js:40
#: src/components/misc/ActionsBar/View/ViewMenu.js:260
#: src/components/medialist/content/MedialistActionsBar/FormTransformContacts.tsx:30
#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:73
#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:97
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:49
#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:31
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:145
#: src/components/layout/Header/AppNotifications/AppNotifications.js:110
#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:163
#: src/components/emailing/content/CreateEmailContent.js:296
msgid "Back"
msgstr "Natrag"

#: src/components/page/auth/ResetPassword/ResetPasswordFooter.js:29
msgid "Back to Log In"
msgstr "Povratak na prijavu"

#: src/components/page/auth/ResetPassword/ResetPasswordFooter.js:19
msgid "Back to settings"
msgstr "Natrag na postavke"

#: src/pages/_error.js:61
#: src/pages/404.js:29
#: src/pages/user/yoy-analysis.js:79
#: src/pages/user/reactivate-24.js:79
#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:28
#: src/components/page/auth/UserInactive/UserInactive.js:25
#: src/components/page/auth/Expired/Expired.js:119
#: src/components/layout/ErrorCustom/ErrorCustom.js:13
#: src/app/not-found-content.tsx:35
msgid "Back to the main page"
msgstr "Povratak na glavnu stranicu"

#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:68
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:107
msgid "Background Color"
msgstr "Boja pozadine"

#: src/constants/analytics.js:529
#: src/constants/analytics.js:547
#: src/constants/analytics.js:565
#: src/constants/analytics.js:584
#: src/constants/analytics.js:602
#: src/constants/analytics.js:620
#: src/constants/analytics.js:638
#: src/constants/analytics.js:658
#: src/components/OurChart/OurChartAdvanced.js:141
msgid "Bar"
msgstr "Stupčasti"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:46
msgid "Basic settings"
msgstr "Osnovne postavke"

#: src/components/layout/MntrActiveFilters/modules/Paywalled.js:6
msgid "Behind paywall"
msgstr "Iza paywalla"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:141
msgid "Below avg."
msgstr "Ispod prosjeka"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:81
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:220
msgid "best"
msgstr "najbolje"

#: src/constants/analytics/primeScoreCharts.ts:95
msgid "Best PRIMe mediatypes"
msgstr "Najbolji PRIMe medijatipovi"

#: src/components/staff/admin/customer/bio/CustomerBio.js:106
msgid "Billing email"
msgstr "Računski email"

#: src/components/medialist/forms/FormEditAuthor.js:855
#: src/components/medialist/forms/FormEditAuthor.js:1023
msgid "Bio"
msgstr "Bio"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:273
msgid "Blockquote"
msgstr "Citiranje"

#: src/store/models/newsroom/blogs/posts/NewsroomPostsStoreArrItem.ts:106
msgid "Blog post was successfully deleted."
msgstr "Blog post je uspješno izbrisan."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:123
msgid "Bold"
msgstr "Podebljano"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:158
msgid "Brackets"
msgstr "Zagrade"

#: src/pages/brand-tracking.tsx:29
#: src/components/layout/Sidebar/SidebarNavigation.tsx:169
#: src/app/components/monitoring-navigation.tsx:301
msgid "Brand Tracking"
msgstr "Praćenje brenda"

#: src/components/monitoring/Inspector/InspectorMonitora/KeywordsPagination/KeywordsPagination.js:256
msgid "Browse keywords"
msgstr "Pregledaj ključne riječi"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:257
msgid "Bullet list"
msgstr "Popis s grafičkim oznakama"

#: src/components/newsroom/components/PostsList/PostsList.js:162
msgid "By"
msgstr "Od"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:48
msgid "by source"
msgstr "prema izvoru"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:152
msgid "By submitting the form, you agree to our <0>terms</0>."
msgstr "Slanjem obrasca slažete se s našim <0>uvjetima</0>."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:61
msgid "Call to Action (CTA):"
msgstr "Poziv na akciju (CTA):"

#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:136
msgid "Campaign"
msgstr "Kampanja"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:54
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:92
msgid "Campaign will be removed"
msgstr "Kampanja će biti uklonjena"

#: src/components/forms/dashboard/Search/SearchEmailingCampaigns.js:26
#: src/components/emailing/sidebar/EmailingSidebarDashboard.js:17
#: src/components/emailing/content/EmailingCampaignsContent.tsx:49
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:44
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:57
msgid "Campaigns"
msgstr "Kampanje"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:139
msgid "Can unsubscribe"
msgstr "Može otkazati pretplatu"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:76
msgid "Can't find an article in your feed? Enter a link to the article you are looking for and select a topic."
msgstr "Ne možete pronaći članak u svom feedu? Unesite poveznicu na članak koji tražite i odaberite temu."

#: src/components/reports/Content/ReportsList/ReportsForm.js:350
#: src/components/misc/VideoPlayer/getCropAction.js:7
#: src/components/misc/MntrForm/MntrForm.tsx:516
#: src/components/medialist/forms/FormEditAuthor.js:559
#: src/components/medialist/forms/FormEditAuthor.js:571
#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:86
msgid "Cancel"
msgstr "Otkaži"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:273
msgid "Cancel choice"
msgstr "Odustani"

#: src/components/misc/Changelog/ChangelogTableRow.js:247
msgid "Cancel revert"
msgstr "Otkaži povratak"

#: src/components/misc/UploadWatcher/UploadWatcher.js:40
msgid "Cancel Upload"
msgstr "Otkaži prijenos"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:35
msgid "Cannot delete articles, run manual sentiment, create/edit topics or reports, change account settings, delete TV/radio stories, or edit CRM info."
msgstr "Ne može brisati članke, pokretati ručnu analizu sentimenta, stvarati/uređivati teme ili izvješća, mijenjati postavke računa, brisati TV/radio sadržaje ili uređivati CRM podatke."

#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/MediaDetail.js:120
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:279
msgid "Captured on the screen"
msgstr "Uhvaćeno na zaslonu"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:67
msgid "Categories"
msgstr "Kategorije"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:29
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNewsroomCategory.js:24
#: src/components/layout/MntrActiveFilters/modules/NewsroomCategory.js:21
msgid "Category"
msgstr "Kategorija"

#. placeholder {0}: item.name
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:146
msgid "Category <0>{0}</0> will be removed."
msgstr "Kategorija <0>{0}</0> će biti uklonjena."

#: src/components/newsroom/forms/FormCreateCategory/FormCreateCategory.js:25
msgid "Category name"
msgstr "Naziv kategorije"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:47
msgid "Change email"
msgstr "Promijeni email"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:27
#: src/components/settings/SettingsUserManagement/UpdateRole.tsx:20
msgid "Change role"
msgstr "Promijeni ulogu"

#: src/components/misc/Changelog/ChangelogTable.js:36
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:394
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChangeType.js:24
msgid "Change Type"
msgstr "Promijeni Tip"

#: src/pages/topics/[topicId]/changelog.js:13
#: src/pages/staff/admin/workspaces/[workspaceId]/changelog.js:12
#: src/pages/reports/[reportId]/changelog.js:13
#: src/components/staff/admin/workspace/Workspace.js:147
msgid "Changelog"
msgstr "Povijest promjena"

#: src/store/models/admin/customer/CustomerStore.js:163
#: src/store/models/admin/customer/CustomerStore.js:177
#: src/store/models/admin/customer/CustomerStore.js:186
#: src/store/models/admin/customer/CustomerStore.js:231
#: src/store/models/admin/customer/CustomerStore.js:258
#: src/store/models/admin/customer/CustomerStore.js:286
msgid "Changes successfully saved."
msgstr "Promjene su uspješno spremljene."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:27
msgid "Channel"
msgstr "Kanal"

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:241
msgid "Channels"
msgstr "Kanali"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:169
msgid "Chart"
msgstr "Grafikon"

#: src/components/OurChart/OurChartAdvanced.js:128
msgid "Chart Settings"
msgstr "Postavke grafikona"

#: src/components/OurChart/OurChartAdvanced.js:137
msgid "Chart Type"
msgstr "Vrsta grafikona"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:238
msgid "Check"
msgstr "Provjeriti"

#: src/components/emailing/content/tabs/AddRecipients.tsx:80
msgid "Choose authors list or tag:"
msgstr "Odaberite popis autora ili oznaku:"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:35
msgid "Choose how to add/edit your signature"
msgstr "Odaberite kako dodati/urediti svoj potpis"

#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:44
msgid "Clear Color"
msgstr "Očisti boju"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:139
msgid "Clear formatting"
msgstr "Očisti formatiranje"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/EntityInfoBox.js:220
msgid "click to open the detail"
msgstr "kliknite za otvaranje detalja"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:342
msgid "Click to see options"
msgstr "Kliknite za prikaz opcija"

#: src/components/forms/inspector/FormMediaEditor.js:124
msgid "Clip duration"
msgstr "Trajanje klipa"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:127
#: src/components/reports/history/Compose.js:36
#: src/components/misc/portable/PortableResend/PortableResend.js:59
#: src/components/misc/portable/PortableResend/PortableResend.js:99
#: src/components/misc/portable/PortableExport/PortableExport.js:55
#: src/components/misc/portable/PortableExport/PortableExport.js:95
#: src/components/misc/MntrHint/MntrHint.js:77
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:111
#: src/components/misc/Mntr/ButtonGroup.tsx:51
msgid "Close"
msgstr "Zatvori"

#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:129
msgid "Collapse"
msgstr "Smanji"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:53
msgid "Color palette"
msgstr "Paleta boja"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:208
msgid "Colors"
msgstr "Boje"

#: src/constants/analytics/primeScoreCharts.ts:115
#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:60
msgid "Column"
msgstr "Stupac"

#: src/components/newsroom/content/posts/NewsroomPosts.js:126
msgid "Compact"
msgstr "Kompaktan"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:66
msgid "Company"
msgstr "Tvrtka"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:93
msgid "Company (Name or CRN)"
msgstr "Tvrtka (ime ili matični broj)"

#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:79
msgid "Company detail"
msgstr "Detalji tvrtke"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:72
#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:26
msgid "Company info"
msgstr "Podaci o tvrtki"

#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:893
msgid "Compare Topic"
msgstr "Usporedi temu"

#: src/pages/sign-up-completion.tsx:30
#: src/pages/staff/sign-up-completion.js:26
msgid "Completion"
msgstr "Završetak registracije"

#: src/components/emailing/forms/FormSenderSettings.js:260
msgid "Configuring DKIM (DomainKeys Identified Mail) enhances the integrity and authenticity of your emails, reducing the likelihood of them being marked as spam:"
msgstr "Konfiguracija DKIM-a (DomainKeys Identified Mail) poboljšava integritet i autentičnost vaših e-mailova, smanjujući vjerojatnost da će biti označeni kao spam:"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:51
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:319
msgid "Confirm"
msgstr "Potvrdi"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:93
msgid "Confirm new password"
msgstr "Potvrdite novu lozinku"

#: src/helpers/store/apiClient.js:240
msgid "Connection with the server was lost. Please try again."
msgstr "Veza sa serverom je izgubljena. Molimo pokušajte ponovno."

#: src/components/layout/Sidebar/SidebarNavigation.tsx:263
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:160
msgid "Contact"
msgstr "Kontakt"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:123
msgid "Contact information"
msgstr "Kontakt informacije"

#: src/components/medialist/forms/FormEditAuthor.js:761
#: src/components/medialist/content/MedialistDashboard.js:88
#: src/components/medialist/content/MedialistDashboard.js:121
#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:56
msgid "Contacts"
msgstr "Kontakti"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:178
msgid "Contacts cannot be imported from this file"
msgstr "Kontakti se ne mogu uvesti iz ove datoteke"

#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:19
msgid "Contacts import in progress"
msgstr "Uvoz kontakata u tijeku"

#: src/pages/user/reset-password/new.tsx:48
#: src/pages/user/reset-password/index.tsx:29
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:167
#: src/components/page/auth/SignUp/SignUp.js:58
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:610
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:246
#: src/components/newsroom/content/newsroom/NewsroomCreateBlog.js:19
#: src/components/misc/Wizard/WizardChoice.tsx:150
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:143
#: src/components/medialist/forms/FormEditAuthor.js:300
#: src/components/medialist/forms/FormEditAuthor.js:318
#: src/components/medialist/forms/FormEditAuthor.js:464
#: src/components/medialist/forms/FormEditAuthor.js:483
#: src/components/medialist/forms/FormEditAuthor.js:578
#: src/components/emailing/forms/FormAddCampaign.tsx:20
#: src/components/emailing/content/CreateEmailContent.js:354
msgid "Continue"
msgstr "Nastavi"

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:58
msgid "Continue to import"
msgstr "Nastavi s uvozom"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:229
#: src/components/medialist/forms/FormEditAuthor.js:116
#: src/components/medialist/forms/FormEditAuthor.js:120
#: src/components/medialist/forms/FormEditAuthor.js:872
#: src/components/medialist/forms/FormEditAuthor.js:1048
#: src/components/emailing/forms/FormSenderSettings.js:69
msgid "Copied to the clipboard."
msgstr "Kopirano u međuspremnik."

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:64
msgid "Copy article to clipboard"
msgstr "Kopiraj članak u međuspremnik"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:12
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:13
#: src/components/exportList/History/HistoryTable/HistoryTable.js:115
msgid "Copy link to clipboard"
msgstr "Kopiraj poveznicu u međuspremnik"

#: src/components/staff/admin/workspace/UsersTable/CopyPassword.js:27
msgid "Copy password"
msgstr "Kopiraj lozinku"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:117
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:76
msgid "Copy public URL to clipboard"
msgstr "Kopiraj javni URL u međuspremnik"

#: src/components/reports/history/HistoryTable.js:436
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:96
msgid "Copy recipients to clipboard"
msgstr "Kopiraj primatelje u međuspremnik"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:34
#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:66
msgid "Copy share link"
msgstr "Kopiraj poveznicu za dijeljenje"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:70
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:82
msgid "Copy to another campaign"
msgstr "Kopiraj u drugu kampanju"

#: src/helpers/modal/withModalEmailPreview.js:102
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:585
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:644
#: src/components/medialist/forms/FormEditAuthor.js:773
#: src/components/medialist/forms/FormEditAuthor.js:801
#: src/components/medialist/forms/FormEditAuthor.js:869
#: src/components/medialist/forms/FormEditAuthor.js:1045
#: src/components/emailing/forms/FormSenderSettings.js:65
msgid "Copy to clipboard"
msgstr "Kopiraj u međuspremnik"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:112
msgid "Copy to Dashboard"
msgstr "Kopiraj na nadzornu ploču"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:238
#: src/components/misc/ActionsBar/View/ViewMenu.js:187
msgid "Cost per Point (CCP) - how much does one second of advertising cost for each GRP point (AVE = CPP * GRP * duration)"
msgstr "Cost per Point (CCP) - koliko košta jedna sekunda oglašavanja za svaku GRP točku (AVE = CPP * GRP * trajanje)"

#: src/constants/analytics.js:864
msgid "Countries"
msgstr "Države"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:124
msgid "countries with enabled mediatype"
msgstr "zemlje s omogućenim mediatipom"

#: src/constants/analytics.js:851
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:74
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:360
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:76
#: src/components/medialist/forms/FormEditAuthor.js:246
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:271
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:291
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:80
msgid "Country"
msgstr "Država"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:63
msgid "Cover page"
msgstr "Naslovnica"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:233
#: src/components/misc/ActionsBar/View/ViewMenu.js:182
msgid "CPP"
msgstr "CPP"

#: src/components/medialist/forms/FormEditAuthor.js:601
msgid "Create"
msgstr "Stvoriti"

#: src/components/page/auth/Login/Login.tsx:75
msgid "Create an account for free"
msgstr "Kreirajte račun besplatno"

#: src/components/emailing/content/NewEmailWizardButton.tsx:15
msgid "Create an email"
msgstr "Kreiraj e-mail"

#: src/components/medialist/content/withAddToBasketPopup.js:52
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorBaskets/AddAuthorToBasket.js:59
msgid "Create and add to new list"
msgstr "Kreiraj i dodaj na novi popis"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:68
#: src/components/monitoring/FeedActionsBar/withAddTagPopup/AddTagPopupContent.js:49
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorTags/AddAuthorTag.js:51
msgid "Create and assign new tag"
msgstr "Kreiraj i dodijeli novu oznaku"

#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:48
#: src/app/components/monitoring-navigation.tsx:213
msgid "Create article"
msgstr "Kreiraj članak"

#: src/pages/workspace-articles.js:64
#: src/components/monitoring/WorkspaceArticles/withWorkspaceArticleModal.js:9
msgid "Create Article"
msgstr "Kreiraj članak"

#: src/components/medialist/content/MedialistDashboard.js:144
#: src/components/medialist/content/AuthorBasketsMenu.js:51
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:249
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:250
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:168
msgid "Create author"
msgstr "Kreiraj autora"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:76
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:77
msgid "Create Folder"
msgstr "Kreiraj Mapu"

#: src/components/medialist/content/AuthorBasketsMenu.js:151
msgid "Create new list"
msgstr "Kreiraj novi popis"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:69
msgid "Create new Newsroom"
msgstr "Kreiraj novi Newsroom"

#: src/components/newsroom/content/newsroom/NewsroomCreateBlog.js:18
msgid "Create Newsroom"
msgstr "Kreiraj Newsroom"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:240
msgid "Create Own Article"
msgstr "Kreiraj vlastiti članak"

#: src/components/newsroom/content/posts/NewPostWizardButton.tsx:16
msgid "Create post"
msgstr "Kreiraj objavu"

#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:69
msgid "Create Report"
msgstr "Kreiraj izvještaj"

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:24
#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:26
msgid "Create workspace"
msgstr "Kreiraj radni prostor"

#: src/pages/authors/index.js:61
msgid "Create your own <0>lists</0> and <1>tags</1>"
msgstr "Kreirajte vlastite <0>liste</0> i <1>oznake</1>"

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:44
msgid "Created"
msgstr "Stvoreno"

#: src/store/models/dashboards/DashboardPreview.js:134
#: src/pages/crisis-communication.js:10
#: src/pages/crisis-communication-story/[articleId].js:10
#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:19
#: src/components/layout/Sidebar/SidebarNavigation.tsx:129
#: src/components/layout/Sidebar/SidebarNavigation.tsx:255
#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:26
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:37
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:23
#: src/app/components/monitoring-navigation.tsx:258
msgid "Crisis communication"
msgstr "Krizna komunikacija"

#: src/store/models/monitoring/Inspector/EntityKnowledgeBaseStore/EntityKnowledgeBaseStore.js:23
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:55
msgid "CRN"
msgstr "MBS"

#. placeholder {0}: option.reg_no
#. placeholder {0}: data.reg_no
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:117
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorAddress/AuthorAddress.js:14
msgid "CRN: {0}"
msgstr "MBS: {0}"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:124
#: src/components/misc/MntrEditor/modals/withModalCTAButton.js:8
#: src/components/misc/MntrEditor/extensions/ExtensionCTAButton.js:23
msgid "CTA Button"
msgstr "CTA gumb"

#: src/components/staff/admin/user/User.js:286
#: src/components/settings/SettingsApplication/SettingsApplication.js:42
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:408
msgid "Currency"
msgstr "Valuta"

#: src/components/settings/SettingsApplication/SettingsApplication.js:33
msgid "Currency in which to calculate AVE."
msgstr "Valuta u kojoj se izračunava AVE."

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:81
msgid "Current password"
msgstr "Trenutna lozinka"

#: src/components/newsroom/modals/withModalCustomAiRewrite.tsx:11
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:645
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:65
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:49
#: src/components/emailing/content/CreateEmailContent.js:548
msgid "Custom"
msgstr "Prilagođeno"

#: src/components/emailing/content/promo/PromoEmailing.js:22
msgid "Custom branding"
msgstr "Prilagođeni branding"

#: src/components/settings/SettingsTheme/SettingsThemePreview/LogoColorPicker/LogoColorPicker.js:70
#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:65
msgid "Custom color"
msgstr "Prilagođena boja"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:171
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:135
#: src/components/misc/MntrEditor/modals/withModalHtmlCode.js:17
#: src/components/misc/MntrEditor/extensions/ExtensionHtmlCode.js:23
msgid "Custom HTML Code"
msgstr "Prilagođeni HTML kod"

#: src/components/newsroom/modals/withModalCustomAiRewrite.tsx:24
msgid "Custom insruction"
msgstr "Prilagođena uputa"

#: src/components/settings/SettingsLogo/SettingsLogo.js:63
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:78
msgid "Custom logo"
msgstr "Prilagođeni logo"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:176
msgid "Custom meta/script/style for <head> section"
msgstr "Prilagođeni meta/skripta/stil za sekciju <head>"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:83
msgid "Custom selection"
msgstr "Prilagođeni odabir"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:353
msgid "Custom Slug"
msgstr "Prilagođeni URL slug"

#: src/components/reports/Content/ReportsList/ReportsForm.js:306
msgid "Custom subject (optional)"
msgstr "Prilagođena tema (opcionalno)"

#: src/pages/staff/admin/customers/index.js:12
#: src/components/staff/admin/customers/Customers.js:20
#: src/components/layout/Sidebar/SidebarNavigation.tsx:188
#: src/components/layout/Header/UserMenu/UserMenu.tsx:219
#: src/components/forms/dashboard/Search/SearchCustomers.js:54
#: src/app/components/monitoring-navigation.tsx:317
msgid "Customers"
msgstr "Kupci"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:313
msgid "Customization"
msgstr "Prilagodba"

#: src/components/misc/VideoPlayer/getCropAction.js:7
msgid "Cut clip"
msgstr "Izreži klip"

#: src/components/forms/inspector/FormMediaEditor.js:117
msgid "Cut from"
msgstr "Rez od"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:433
msgid "Cut media"
msgstr "Izreži medij"

#: src/components/forms/inspector/FormMediaEditor.js:120
msgid "Cut to"
msgstr "Rez do"

#: src/pages/staff/admin/workspaces/[workspaceId]/daily-access.js:12
#: src/pages/staff/admin/users/[userId]/daily-access.js:12
#: src/components/staff/admin/workspace/Workspace.js:153
#: src/components/staff/admin/user/User.js:77
msgid "Daily Access"
msgstr "Dnevni pristup"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:54
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:70
#: src/components/misc/ActionsBar/View/ViewMenu.js:166
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:106
msgid "Daily listenership"
msgstr "Dnevna slušanost"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:71
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:45
msgid "daily users"
msgstr "korisnika dnevno"

#: src/components/misc/ActionsBar/View/ViewMenu.js:66
msgid "Daily users"
msgstr "Dnevni posjetitelji"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:151
msgid "Dark"
msgstr "Tamno"

#: src/components/settings/SettingsTheme/SettingsThemePreview/SettingsThemePreview.js:193
msgid "Dark mode preview"
msgstr "Pregled tamnog načina"

#: src/pages/dashboard/index.js:18
#: src/pages/dashboard/shared/[dashboardKey].js:15
#: src/app/components/monitoring-navigation.tsx:91
msgid "Dashboard"
msgstr "Nadzorna ploča"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:19
msgid "Dashboard sharing"
msgstr "Dijeljenje nadzorne ploče"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:56
msgid "Dashboard will be removed."
msgstr "Nadzorna ploča će biti uklonjena."

#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:47
msgid "Dashboard will be shared in read-only form (non-interactive) with currently displayed data. Link expiration is 30 days."
msgstr "Nadzorna ploča će biti podijeljena u obliku samo za čitanje (neinteraktivno) sa trenutno prikazanim podacima. Link ističe za 30 dana."

#: src/components/tariff/TariffLimits/TariffLimits.js:150
#: src/components/staff/admin/workspace/Workspace.js:460
msgid "Dashboards limit"
msgstr "Broj nadzornih ploča"

#: src/components/staff/admin/DailyAccess/Table.js:21
#: src/components/reports/history/HistoryTable.js:146
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:180
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:81
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:263
#: src/components/exportList/History/HistoryTable/HistoryTable.js:48
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:177
msgid "Date"
msgstr "Datum"

#: src/components/misc/Changelog/ChangelogTable.js:30
msgid "Date & User"
msgstr "Datum i korisnik"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:238
msgid "Date and time must be in the future"
msgstr "Datum i vrijeme moraju biti u budućnosti"

#. placeholder {0}: format(effectiveMinDate, DATE_FORMAT)
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:35
msgid "Date cannot be earlier than {0}"
msgstr "Datum ne može biti raniji od {0}"

#. placeholder {0}: format(effectiveMaxDate, DATE_FORMAT)
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:38
msgid "Date cannot be later than {0}"
msgstr "Datum ne može biti kasniji od {0}"

#: src/constants/analytics.js:827
msgid "Day of the week"
msgstr "Dan u tjednu"

#: src/helpers/charts/makeGranularityMenu.js:10
#: src/helpers/charts/getGranularityLabel.js:16
msgid "Days"
msgstr "Dnevni"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:289
msgid "DD.MM.YYYY"
msgstr "DD.MM.YYYY"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:32
#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:51
msgid "Deduplicate articles"
msgstr "Dedupliciraj članke"

#: src/components/staff/admin/workspace/Workspace.js:763
msgid "Deduplicate feed articles"
msgstr "Dedupliciraj članke u feedu"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:36
msgid "Deduplication will remove same or similar articles from the report according to your settings. It will not remove any article from the feed."
msgstr "Deduplikacija će ukloniti iste ili slične članke iz izvješća prema vašim postavkama. Nijedan članak neće biti uklonjen iz feeda."

#: src/components/newsroom/content/posts/NewsroomPosts.js:133
msgid "Default"
msgstr "Zadano"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:62
msgid "Define the action you want recipients to take."
msgstr "Definirajte radnju koju želite da primatelji poduzmu."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:37
msgid "Define the subject line to set the focus and tone."
msgstr "Definirajte predmet kako biste postavili fokus i ton."

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:137
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:78
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:160
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:63
#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:87
#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:60
#: src/components/newsroom/content/posts/NewsroomPosts.js:201
#: src/components/newsroom/content/post/AttachmentsList.js:52
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:101
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:357
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:388
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:37
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:35
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:279
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:75
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:37
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:35
#: src/components/medialist/forms/modules/FormArray.js:119
#: src/components/medialist/forms/modules/FormArray.js:165
#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:33
#: src/components/emailing/content/SignaturePopup.tsx:31
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:85
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:96
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:117
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:53
msgid "Delete"
msgstr "Izbriši"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:348
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:360
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:385
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:313
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:388
msgid "Delete Article"
msgstr "Izbriši članak"

#: src/components/newsroom/content/post/AttachmentsList.js:56
msgid "Delete attachment"
msgstr "Izbriši privitak"

#: src/components/medialist/forms/FormEditAuthor.js:311
#: src/components/medialist/forms/FormEditAuthor.js:317
#: src/components/medialist/forms/FormEditAuthor.js:476
#: src/components/medialist/forms/FormEditAuthor.js:482
msgid "Delete author"
msgstr "Izbriši autora"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:393
msgid "Delete blog post"
msgstr "Izbriši blog post"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:133
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:142
msgid "Delete category"
msgstr "Izbriši kategoriju"

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:37
msgid "Delete file"
msgstr "Izbriši datoteku"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:286
msgid "Delete Folder"
msgstr "Izbriši mapu"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:375
msgid "Delete from media coverage"
msgstr "Ukloni iz medijskog praćenja"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:131
#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:140
msgid "Delete Item"
msgstr "Izbriši stavku"

#: src/components/medialist/content/AuthorBasketsMenu.js:126
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:108
msgid "Delete list"
msgstr "Izbriši popis"

#: src/components/medialist/content/AuthorBasketsMenu.js:134
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:123
msgid "Delete list?"
msgstr "Izbrisati popis?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:659
msgid "Delete Newsroom"
msgstr "Izbriši Newsroom"

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:147
msgid "Delete recipient"
msgstr "Izbriši primatelja"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:121
msgid "Delete Sender"
msgstr "Izbriši pošiljatelja"

#: src/components/emailing/content/SignaturePopup.tsx:34
msgid "Delete signature"
msgstr "Izbriši potpis"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:302
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:311
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:192
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:201
msgid "Delete tag"
msgstr "Izbriši oznaku"

#: src/pages/trash.js:16
msgid "Deleted Articles"
msgstr "Izbrisani članci"

#. placeholder {0}: feed.totalCount
#: src/components/trash/Content.js:41
msgid "Deleted Articles ({0})"
msgstr "Izbrisani članci ({0})"

#: src/components/trash/Content.js:49
msgid "Deleted articles will appear here when deleted in the Articles section."
msgstr "Ovdje će se pojaviti izbrisani članci kada se izbrišu u odjeljku Članci."

#: src/components/reports/history/RecipientsTableRow.js:40
#: src/components/reports/history/HistoryTable.js:84
#: src/components/reports/history/HistoryTable.js:118
#: src/components/reports/history/HistoryTable.js:328
msgid "Delivered"
msgstr "Dostavljeno"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:204
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:230
msgid "Delivery rate"
msgstr "Stopa dostave"

#: src/components/reports/history/HistoryTable.js:162
msgid "Delivery stats"
msgstr "Statistika dostave"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:20
msgid "Demo"
msgstr "Demo"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:111
msgid "Demographic Data"
msgstr "Demografski podaci"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:47
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:96
#: src/components/feed/InspectorToolbar/InspectorToolbar.js:145
msgid "Demographics"
msgstr "Demografija"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:42
msgid "Describe who will receive the email (demographics, interests)."
msgstr "Opišite tko će primiti e-mail (demografski podaci, interesi)."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:59
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:373
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:223
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:43
msgid "Description"
msgstr "Opis"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:44
#: src/components/layout/MntrFiltersBar/modules/MenuFilterToggleAllButtons.js:76
msgid "Deselect all"
msgstr "Poništi sve odabire"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:23
msgid "Designed for PR professionals, generates a press release structure."
msgstr "Dizajnirano za PR profesionalce, generira strukturu press objave."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:80
msgid "Detailed instructions for email text creation"
msgstr "Detaljne upute za izradu teksta e-pošte"

#: src/pages/newsroom/index.js:53
msgid "Detailed traffic<0/> <1>analytics</1>"
msgstr "Detaljna<0/> <1>analiza prometa</1>"

#: src/constants/analytics/primeScoreCharts.ts:31
msgid "Development of PRIMe by rating"
msgstr "Razvoj PRIMe prema ocjenjivanju"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:149
msgid "Deviation from the average"
msgstr "Odstupanje od prosjeka. Što je veća vrijednost, rezultati su zanimljiviji."

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:89
msgid "Disable"
msgstr "Onemogući"

#: src/components/staff/admin/workspace/Workspace.js:225
#: src/components/staff/admin/workspace/Workspace.js:958
#: src/components/staff/admin/user/User.js:167
#: src/components/staff/admin/user/User.js:336
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:279
msgid "Discard"
msgstr "Odbaci promjene"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:227
msgid "Discard changes"
msgstr "Odbaci promjene"

#: src/components/monitoring/Inspector/InspectorMonitora/DiscussionThreadBar/DiscussionThreadBar.js:20
#: src/components/layout/MntrActiveFilters/modules/DiscussionThread.js:12
msgid "Discussion thread"
msgstr "Raspravna nit"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:98
msgid "Display empty categories"
msgstr "Prikaži prazne kategorije"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:205
msgid "Display the article"
msgstr "Prikaži članak"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:154
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:49
#: src/components/misc/ActionsBar/View/ViewMenu.js:116
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:75
msgid "Distribution amount"
msgstr "Naklada"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:149
msgid "DNS settings are invalid."
msgstr "DNS postavke nisu valjane."

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:147
msgid "DNS settings are valid."
msgstr "DNS postavke su valjane."

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:108
msgid "Do not add new media to the medium"
msgstr "Ne dodavajte nove medije mediju"

#: src/components/medialist/forms/FormEditAuthor.js:299
#: src/components/medialist/forms/FormEditAuthor.js:316
#: src/components/medialist/forms/FormEditAuthor.js:463
#: src/components/medialist/forms/FormEditAuthor.js:481
msgid "Do you really want to continue?"
msgstr "Želite li zaista nastaviti?"

#. placeholder {0}: menuItem.name
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:294
msgid "Do you really want to delete '<0>{0}</0>'?"
msgstr "Želite li zaista izbrisati '<0>{0}</0>'?"

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:200
msgid "Do you want to add author addresses from this list?"
msgstr "Želite li dodati adrese autora s ovog popisa?"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:84
msgid "Do you want to start with AI assistant?"
msgstr "Želite li započeti s AI asistentom?"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:124
msgid "Do you wish to reactivate this recipient?"
msgstr "Želite li ponovno aktivirati ovog primatelja?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:55
#: src/components/emailing/forms/FormSenderSettings.js:267
msgid "Domain"
msgstr "Domena"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:489
msgid "Domain change"
msgstr "Promjena domene"

#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:130
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:24
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:23
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:184
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:30
#: src/components/forms/dashboard/Export/ExportForm.js:100
#: src/components/exportList/History/HistoryTable/HistoryTable.js:96
#: src/components/exportList/Content/Content.tsx:78
#: src/components/OurChart/OurChartAdvanced.js:181
msgid "Download"
msgstr "Preuzmi"

#: src/components/forms/inspector/FormMediaEditor.js:141
msgid "Download clip"
msgstr "Preuzmi isječak"

#: src/components/staff/admin/workspace/Workspace.js:126
msgid "Download settings (.xlsx)"
msgstr "Preuzmi postavke (.xlsx)"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:82
msgid "Download template"
msgstr "Preuzmi predložak"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewImage/PreviewImage.js:60
msgid "Drag 'n' drop image or click to select files"
msgstr "Povucite i ispustite sliku ili kliknite za odabir datoteka"

#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryAdapter.js:59
msgid "Drag 'n' drop some images here, or click to select files"
msgstr "Povucite i ispustite neke slike ovdje, ili kliknite za odabir datoteka"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:66
msgid "Due date"
msgstr "Datum dospijeća"

#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:36
#: src/components/medialist/content/AuthorBasketsMenu.js:117
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:101
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:59
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:71
msgid "Duplicate"
msgstr "Dupliciraj"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:74
msgid "Duplicate widget"
msgstr "Dupliciraj widget"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:214
#: src/components/misc/ActionsBar/View/ViewMenu.js:174
msgid "Duration"
msgstr "Trajanje"

#: src/components/layout/AuthWrapper/constants/features.slides.js:307
msgid "Dynamic platform for creating, curating, and sharing captivating content."
msgstr "Stvarajte i dijelite vizualno privlačne tiskovne izvještaje zahvaljujući našoj modernoj platformi."

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:89
#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:124
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:51
#: src/components/misc/MntrEditor/extensions/ExtensionImageGallery.js:53
#: src/components/emailing/content/SignaturePopup.tsx:22
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:65
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:46
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:34
msgid "Edit"
msgstr "Uredi"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:443
msgid "Edit article"
msgstr "Uredi članak"

#: src/components/newsroom/content/posts/NewsroomPosts.js:194
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:169
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:170
msgid "Edit Article"
msgstr "Uredi članak"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:34
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:37
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:77
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:80
msgid "Edit Campaign"
msgstr "Uredi kampanju"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/modalEditCategory.js:10
#: src/components/layout/Sidebar/modules/NewsroomNavigation/modalEditCategory.js:24
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:113
msgid "Edit category"
msgstr "Uredi kategoriju"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:37
msgid "Edit dashboard"
msgstr "Uredi nadzornu ploču"

#: src/components/topics/Content/TopicsList/Keyword/Keyword.js:62
msgid "Edit keyword"
msgstr "Uredi ključnu riječ"

#: src/components/medialist/content/AuthorBasketsMenu.js:102
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:19
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:87
msgid "Edit list"
msgstr "Uredi popis"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:63
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:135
#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:60
msgid "Edit mediatypes"
msgstr "Uredi medijatipe"

#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:56
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:106
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:131
#: src/components/medialist/forms/FormEditAuthor.js:710
msgid "Edit note"
msgstr "Uredi bilješku"

#: src/components/medialist/forms/FormEditAuthor.js:282
#: src/components/medialist/forms/FormEditAuthor.js:448
msgid "Edit profile"
msgstr "Uredi profil"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:67
msgid "Edit recipient"
msgstr "Uredi primatelja"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:48
msgid "Edit Sender"
msgstr "Uredi pošiljatelja"

#: src/components/newsroom/content/posts/NewsroomPosts.js:300
#: src/components/newsroom/content/dashboard/NewsroomBlogSettings.js:23
msgid "Edit settings"
msgstr "Uredi postavke"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:26
msgid "Edit signature"
msgstr "Uredi potpis"

#: src/components/layout/Sidebar/modules/SidebarTags/modalEditTags.js:10
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:279
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:168
msgid "Edit tag"
msgstr "Uredi oznaku"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:32
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:58
msgid "Edit topic"
msgstr "Uredi temu"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:59
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:63
msgid "Edit widget"
msgstr "Uredi widget"

#: src/components/medialist/forms/modules/FormArray.js:94
msgid "Editorial Office"
msgstr "Uredništvo"

#: src/components/medialist/forms/FormEditAuthor.js:848
#: src/components/medialist/forms/FormEditAuthor.js:1012
msgid "Editorial offices and positions"
msgstr "Uredništva i pozicije"

#: src/components/medialist/content/MedialistAuthorCreate.js:24
msgid "Eg. sent press releases, profile edits, published articles related to your press releases."
msgstr "Npr. poslane tiskovne objave, uređivanja profila, objavljeni članci povezani s vašim tiskovnim objavama."

#: src/pages/user/reset-password/index.tsx:20
#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:116
#: src/components/staff/admin/user/User.js:246
#: src/components/staff/admin/customer/users/UsersTable.js:68
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:61
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:224
#: src/components/reports/history/RecipientsTableHeader.js:30
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:133
#: src/components/page/auth/SignUp/SignUp.js:37
#: src/components/page/auth/Login/Login.tsx:40
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:128
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:74
#: src/components/medialist/forms/FormEditAuthor.js:793
#: src/components/medialist/forms/FormEditAuthor.js:911
#: src/components/medialist/forms/FormEditAuthor.js:916
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:33
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:100
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:111
#: src/components/emailing/content/EmailDetailEmailContent.js:37
#: src/components/emailing/content/CreateEmailContent.js:262
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:50
msgid "Email"
msgstr "E-pošta"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:26
msgid "Email address was verified"
msgstr "E-mail adresa je provjerena"

#. placeholder {0}: campaign.name
#: src/components/emailing/content/CampaignAutocompleteList.tsx:41
msgid "Email copied to {0}"
msgstr "Email kopiran u {0}"

#: src/store/models/ExportStore.js:126
msgid "Email has been successfully sent."
msgstr "Email je uspješno poslan."

#: src/components/emailing/content/CreateEmailContent.js:77
msgid "Email is locked and cannot be edited. If you want to edit the email, return it to the draft state."
msgstr "Email je zaključan i ne može se uređivati. Ako želite urediti email, vratite ga u stanje nacrta."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:45
msgid "Email is missing"
msgstr "Nedostaje e-mail"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:12
msgid "Email is required"
msgstr "Email je obavezan"

#: src/components/emailing/content/CreateEmailContent.js:72
msgid "Email is sending"
msgstr "E-mail se šalje"

#: src/helpers/modal/withModalEmailPreview.js:120
msgid "Email preview"
msgstr "Pregled e-pošte"

#: src/components/reports/Content/ReportsList/ReportsHeading/ReportsHeading.js:10
msgid "Email reports ({counter})"
msgstr "Email izvještaji ({counter})"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:142
msgid "Email subject"
msgstr "Predmet e-pošte"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:36
msgid "Email Subject:"
msgstr "Predmet e-pošte:"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:62
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:74
msgid "Email successfully duplicated"
msgstr "Email je uspješno dupliciran"

#: src/components/emailing/content/CreateEmailContent.js:134
msgid "Email was saved"
msgstr "Email je spremljen"

#: src/components/emailing/content/CreateEmailContent.js:153
msgid "Email was sent"
msgstr "Email je poslan"

#: src/components/emailing/content/CreateEmailContent.js:124
msgid "Email was set to draft"
msgstr "Email je postavljen u nacrt"

#: src/components/emailing/content/CreateEmailContent.js:74
msgid "Email will be sent at: {scheduledDate}"
msgstr "Email će biti poslan u: {scheduledDate}"

#: src/components/staff/admin/user/User.js:115
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:384
msgid "Email with further instructions has been sent."
msgstr "Email s daljnjim uputama je poslan."

#: src/pages/emailing/settings.tsx:16
#: src/pages/emailing/index.tsx:14
#: src/pages/emailing/campaign/[campaignId]/recipients.tsx:16
#: src/pages/emailing/campaign/[campaignId]/media-coverage.tsx:18
#: src/pages/emailing/campaign/[campaignId]/index.tsx:19
#: src/pages/emailing/campaign/[campaignId]/email/create.tsx:15
#: src/pages/emailing/campaign/[campaignId]/email/edit/[emailId]/index.tsx:12
#: src/pages/emailing/campaign/[campaignId]/email/[emailId]/recipients.tsx:16
#: src/pages/emailing/campaign/[campaignId]/email/[emailId]/index.tsx:16
#: src/components/layout/Sidebar/SidebarNavigation.tsx:158
#: src/components/layout/AuthWrapper/constants/features.slides.js:259
#: src/components/emailing/content/promo/PromoEmailing.js:17
#: src/components/emailing/components/FunnelStats/FunnelStats.tsx:79
#: src/app/components/monitoring-navigation.tsx:290
msgid "Emailing"
msgstr "Slanje e-pošte"

#: src/components/reports/history/Compose.js:71
#: src/components/forms/dashboard/Search/SearchEmailingEmailMessages.js:27
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:106
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:54
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:158
msgid "Emails"
msgstr "E-mailovi"

#: src/components/medialist/forms/modules/MainEmailHelperText.js:6
msgid "Emails from <0>Emailing</0> will be sent to this address."
msgstr "Emailovi s <0>Emailinga</0> bit će poslani na ovu adresu."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:488
msgid "Embed"
msgstr "Ugradi"

#: src/components/misc/ActionsBar/View/ViewMenu.js:281
msgid "Emoji reactions"
msgstr "Emoji reakcije"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:83
msgid "Empty Body"
msgstr "Prazan tekst članka"

#: src/app/components/monitoring-navigation.tsx:123
msgid "Empty export"
msgstr "Obriši članke za eksport"

#: src/app/components/monitoring-navigation.tsx:129
msgid "Empty export?"
msgstr "Isprazniti izvoz?"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:74
msgid "Empty Perex"
msgstr "Prazan članak perex"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:65
msgid "Empty Title"
msgstr "Prazan naslov"

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:89
msgid "Enable"
msgstr "Omogući"

#: src/components/notifications/Permissions.js:58
msgid "Enable notifications"
msgstr "Omogući obavijesti"

#: src/components/tariff/AgencyMedia/AgencyMedia.js:69
#: src/components/misc/ActionsBar/View/ViewMenu.js:52
#: src/components/misc/ActionsBar/View/ViewMenu.js:272
msgid "Enabled"
msgstr "Omogućeno"

#: src/components/emailing/forms/FormSenderSettings.js:112
msgid "Encryption method"
msgstr "Metoda šifriranja"

#: src/components/settings/SettingsTheme/ThemePicker.tsx:99
msgid "Enforce primary color as header"
msgstr "Nametni primarnu boju kao zaglavlje"

#: src/constants/analytics.js:315
#: src/constants/analytics.js:424
#: src/constants/analytics.js:489
#: src/constants/analytics.js:511
#: src/constants/analytics.js:949
#: src/constants/analytics.js:964
#: src/components/monitoring/FeedList/FeedListItem/MetaData/modules/MetaDataEngagement/MetaDataEngagement.js:19
#: src/components/misc/ActionsBar/View/ViewMenu.js:305
msgid "Engagement rate"
msgstr "Stopa angažmana"

#: src/constants/analytics.js:336
msgid "Engagement rate by mention type"
msgstr "Stopa angažmana prema tipu spomena"

#: src/constants/analytics.js:334
#: src/constants/analytics.js:443
#: src/constants/analytics.js:509
msgid "Engagement rate by sentiment"
msgstr "Stopa angažmana prema sentimentu"

#: src/constants/analytics.js:445
msgid "Engagement rate by social network"
msgstr "Stopa angažmana po društvenoj mreži"

#: src/components/analytics/SocialMedia.js:25
msgid "Engagement summary"
msgstr "Sažetak angažmana"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:62
#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:18
msgid "Enter a word or phrase"
msgstr "Unesite riječ ili frazu"

#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:53
msgid "Enter text here..."
msgstr "Unesite tekst ovdje..."

#: src/components/emailing/forms/FormSenderSettings.js:83
msgid "Enter the hostname of your SMTP server"
msgstr "Unesite naziv hosta vašeg SMTP servera"

#: src/components/emailing/forms/FormSenderSettings.js:85
msgid "Enter the hostname of your SMTP server. This is usually in the format of \"smtp.yourdomain.com\"."
msgstr "Unesite naziv hosta vašeg SMTP servera. Obično je u formatu „smtp.vašadomena.com“."

#: src/components/emailing/forms/FormSenderSettings.js:98
msgid "Enter the password to login to SMTP server"
msgstr "Unesite lozinku za prijavu na SMTP poslužitelj"

#: src/components/emailing/forms/FormSenderSettings.js:100
msgid "Enter the password to login to SMTP server. This is usually the same password you use for your email."
msgstr "Unesite lozinku za prijavu na SMTP poslužitelj. To je obično ista lozinka koju koristite za svoj e-mail."

#: src/components/emailing/forms/FormSenderSettings.js:90
msgid "Enter the username to login to SMTP server"
msgstr "Unesite korisničko ime za prijavu na SMTP poslužitelj"

#. placeholder {0}: initialValues.email
#: src/components/emailing/forms/FormSenderSettings.js:92
msgid "Enter the username to login to SMTP server. If left blank, \"{0}\" is used by default."
msgstr "Unesite korisničko ime za prijavu na SMTP poslužitelj. Ako ostane prazno, koristit će se \"{0}\"."

#: src/pages/user/reset-password/index.tsx:17
msgid "Enter your email. We'll send you instructions on how to reset your password."
msgstr "Unesite svoju e-mail adresu. Poslat ćemo vam upute kako resetirati lozinku."

#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:61
msgid "Error detail"
msgstr "Detalji pogreške"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:163
msgid "Estimated number of distributed copies (print and digital)."
msgstr "Procijenjeni broj distribuiranih primjeraka (tiskanih i digitalnih)."

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:17
msgid "Eternal"
msgstr "Vječan"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:32
msgid "Everything enabled"
msgstr "Sve omogućeno"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:24
msgid "Everything went well."
msgstr "Sve je prošlo dobro."

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:44
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:78
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:118
msgid "Exact match"
msgstr "Točno podudaranje"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:84
msgid "Exact match with separator"
msgstr "Točno podudaranje sa separatorom"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:50
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:90
msgid "Exact match, including letter size"
msgstr "Točno podudaranje, uključujući veličinu slova"

#: src/pages/newsroom/index.js:72
msgid "Example Newsroom"
msgstr "Primjer Newsrooma"

#: src/components/misc/VideoPlayer/Controls.js:143
msgid "Exit fullscreen"
msgstr "Izađi iz cijelog zaslona"

#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:83
msgid "Expense type"
msgstr "Vrsta troška"

#: src/pages/staff/admin/customers/[customerId]/expenses.js:12
#: src/components/staff/admin/customer/expenses/Expenses.js:26
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:50
msgid "Expenses"
msgstr "Troškovi"

#: src/components/page/auth/Expired/Expired.js:24
msgid "expired"
msgstr "istekao"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:12
msgid "Expired"
msgstr "Isteklo"

#: src/components/staff/admin/workspace/Workspace.js:316
msgid "Expires at"
msgstr "Istječe na"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:30
msgid "Expires on: {formattedDate}"
msgstr "Istječe: {formattedDate}"

#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:219
#: src/components/exportList/History/History.js:28
#: src/components/exportList/Content/Content.tsx:58
#: src/components/exportList/Content/Content.tsx:58
#: src/app/components/monitoring-navigation.tsx:102
msgid "Export"
msgstr "Eksport"

#: src/components/misc/portable/PortableExport/PortableExport.js:64
#: src/components/misc/portable/PortableExport/PortableExport.js:104
msgid "Export all articles"
msgstr "Eksport svih članaka"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:142
msgid "Export article"
msgstr "Eksport članka"

#: src/components/misc/portable/PortableExport/PortableExport.js:62
#: src/components/misc/portable/PortableExport/PortableExport.js:102
msgid "Export articles"
msgstr "Eksportaj članke"

#: src/components/staff/admin/workspace/Workspace.js:329
msgid "Export basket mode"
msgstr "Način izvoza košarice"

#: src/pages/export/history.js:12
#: src/components/exportList/History/History.js:35
msgid "Export History"
msgstr "Povijest izvoza"

#: src/store/models/ExportStore.js:311
#: src/store/models/monitoring/Inspector/Inspector.ts:447
msgid "Export is full."
msgstr "Izvozna košarica je puna. Ne može se dodati više članaka."

#: src/components/exportList/Content/HeadingExport/HeadingExport.js:21
msgid "Export list is empty"
msgstr "Lista za eksport je prazna"

#: src/components/medialist/forms/FormEditAuthor.js:362
#: src/components/medialist/forms/FormEditAuthor.js:502
msgid "Export XLSX"
msgstr "Izvoz XLSX"

#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:17
#: src/app/components/monitoring-navigation.tsx:117
msgid "Exports to download"
msgstr "Preuzmi članke"

#: src/pages/external-analytics.tsx:29
#: src/components/layout/Sidebar/SidebarNavigation.tsx:179
msgid "External analytics"
msgstr "Vanjska analitika"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:41
msgid "External Communication Manager"
msgstr "Menadžer vanjske komunikacije"

#: src/components/misc/MntrEditor/forms/FormEmbedSocialUrl/FormEmbedFacebookUrl.tsx:29
msgid "Facebook Post URL"
msgstr "URL objave na Facebooku"

#: src/components/emailing/content/EmailingSettingsContent.js:30
msgid "Failed to fetch your email address from the service provider. Please try again or contact support if the issue persists."
msgstr "Dohvaćanje vaše adrese e-pošte od davatelja usluge nije uspjelo. Pokušajte ponovo ili kontaktirajte podršku ako se problem nastavi pojavljivati."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:41
msgid "Feature has been requested."
msgstr "Funkcionalnost je zatražena."

#: src/components/forms/dashboard/Export/ExportForm.js:66
msgid "File format"
msgstr "Format datoteke"

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:24
msgid "Files"
msgstr "Datoteke"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:207
msgid "Fill from URL"
msgstr "Popuni iz URL-a"

#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:105
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:122
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:136
#: src/components/layout/MntrFiltersBar/forms/FormFilterItems/FormFilterItems.js:38
#: src/components/layout/MntrFiltersBar/forms/FormFilterAuthorTitles/FormFilterAuthorTitles.js:13
#: src/components/layout/MntrFiltersBar/forms/FormChannelSearch/FormChannelSearch.js:31
#: src/components/emailing/content/CampaignAutocomplete.tsx:25
msgid "Filter"
msgstr "Filtriraj"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:104
msgid "Filter by absolute score"
msgstr "Filtriraj prema apsolutnom rezultatu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorActivity.js:27
msgid "Filter by activity"
msgstr "Filtriraj prema aktivnosti"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterArticleType.js:26
msgid "Filter by article type"
msgstr "Filtriraj prema tipu članka"

#: src/components/layout/MntrFiltersBar/forms/FormAuthor/FormAuthor.tsx:13
msgid "Filter by author"
msgstr "Filtriraj prema autoru"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTypeMultiselect.js:32
msgid "Filter by author type"
msgstr "Filtriraj prema tipu autora"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/withModalMedialistArticlesFilter.tsx:21
msgid "Filter by author's articles"
msgstr "Filtriraj prema člancima autora"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterContactInformationMultiselect.js:32
msgid "Filter by contact"
msgstr "Filtriraj prema kontaktu"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:98
#: src/components/layout/MntrFiltersBar/modules/MenuFilterCountryMultiselect.js:29
msgid "Filter by country"
msgstr "Filtriraj prema zemlji"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:239
msgid "Filter by date"
msgstr "Filtriraj po datumu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorFocusAreasMultiselect.js:32
msgid "Filter by focus area"
msgstr "Filtriraj prema području fokusa"

#: src/components/layout/MntrFiltersBar/forms/FormFilterAuthorTitles/FormFilterAuthorTitles.js:32
msgid "Filter by job position"
msgstr "Filtriraj prema radnom mjestu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterLanguageTVR.js:24
#: src/components/layout/MntrFiltersBar/modules/MenuFilterLanguageMultiselect.js:26
msgid "Filter by language"
msgstr "Filtriraj prema jeziku"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSourceTVR.js:24
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:115
msgid "Filter by media"
msgstr "Filtriraj prema mediju"

#: src/components/emailing/forms/FormFilter.js:35
msgid "Filter by name"
msgstr "Filtriraj po imenu"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:50
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:80
msgid "Filter by rank"
msgstr "Filtriraj prema rangu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:187
msgid "Filter by reach"
msgstr "Filtriraj prema dosegu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:141
msgid "Filter by relevance"
msgstr "Filtriraj prema relevantnosti"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSentimentMultiselect.js:27
msgid "Filter by sentiment"
msgstr "Filtriraj prema sentimentu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:59
msgid "Filter sources"
msgstr "Filtriraj izvore"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:120
#: src/components/layout/MntrFiltersBar/modals/withModalPageNumbers.js:15
msgid "Filter specific pages"
msgstr "Filtriraj specifične stranice"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:354
msgid "Filter tags"
msgstr "Filtriraj oznake"

#: src/components/layout/Sidebar/modules/SidebarTopics/SidebarTopicsFolders.js:76
msgid "Filter topics"
msgstr "Filtriraj teme"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:56
msgid "Final version"
msgstr "Konačna verzija"

#. placeholder {0}: account.enums.analytics.export_charts_file_format.find( ({ id }) => id === fileFormatId, ).text
#: src/components/misc/Capture/Capture.js:304
msgid "finalizing {0} file for download"
msgstr "finalizacija {0} datoteke za preuzimanje"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:67
#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:59
msgid "Find out more"
msgstr "Saznajte više"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:228
msgid "Find similar articles"
msgstr "Pronađi slične članke"

#. js-lingui-explicit-id
#: src/components/misc/ActionsBar/Selector/Selector.js:45
msgid "selector.first"
msgstr "Prvih"

#: src/components/page/auth/SignUp/SignUp.js:20
msgid "First Name"
msgstr "Ime"

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:21
msgid "First Step"
msgstr "Prvi korak"

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:141
msgid "Focus area"
msgstr "Fokus područje"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:301
#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:53
msgid "Font Size"
msgstr "Veličina fonta"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:30
msgid "For each functionality choose one of three levels:"
msgstr "Za svaku funkcionalnost odaberite jednu od tri razine:"

#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:68
msgid "For example"
msgstr "Na primjer"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:81
msgid "For example, \"1,4-6\" will filter out 1,4,5,6"
msgstr "Npr.: „1,4-6“ će filtrirati stranice 1,4,5,6"

#: src/components/dashboards/PageExpiredSharedDashboard/PageExpiredSharedDashboard.js:40
msgid "For renewal, contact account admin."
msgstr "Za obnovu, kontaktirajte administratora računa."

#. placeholder {0}: self.filters.topic_monitors[0].text
#: src/store/models/OurChart.js:853
msgid "for topic: {0}"
msgstr "za temu: {0}"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:39
msgid "Forbidden:"
msgstr "Zabranjeno:"

#: src/components/layout/AuthWrapper/constants/features.slides.js:146
msgid "Foreign Media"
msgstr "Strani mediji"

#: src/components/page/auth/Login/Login.tsx:86
msgid "Forgot password?"
msgstr "Zaboravili ste lozinku?"

#: src/components/exportList/History/HistoryTable/HistoryTable.js:51
msgid "Format"
msgstr "Format"

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:18
msgid "Formatting is finished successfully. The prepared file is downloaded automatically. Edit it manually if needed and upload it to the medialist in the next step."
msgstr "Formatiranje je uspješno završeno. Pripremljena datoteka se automatski preuzima. Ako je potrebno, uredite je ručno i prenesite u medijalist u sljedećem koraku."

#: src/components/reports/Content/ReportsList/ReportsForm.js:111
msgid "Frequency of report dispatch"
msgstr "Frekvencija slanja izvještaja"

#: src/components/reports/Content/ReportsList/ReportsForm.js:279
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:56
msgid "From"
msgstr "Od"

#: src/components/forms/dashboard/ExportResend/ExportResend.js:90
msgid "From email"
msgstr "Email pošiljatelja"

#: src/components/misc/ActionsBar/View/ViewMenu.js:101
msgid "Frontpage promo"
msgstr "Promocija na naslovnici"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:32
msgid "Full access:"
msgstr "Puni pristup:"

#: src/components/misc/ActionsBar/View/ViewMenu.js:148
msgid "Full page ad price"
msgstr "Cijena oglasa na cijeloj stranici"

#: src/components/misc/VideoPlayer/Controls.js:143
msgid "Fullscreen"
msgstr "Cijeli zaslon"

#: src/components/tariff/Permissions/Permissions.js:40
msgid "Functionality"
msgstr "Funkcionalnost"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:93
msgid "Generate structure"
msgstr "Generiraj strukturu"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:149
msgid "Generate text"
msgstr "Generiraj tekst"

#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:40
msgid "Generating link"
msgstr "Stvaranje veze"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:115
msgid "Get a comprehensive view of the topics that matter to you."
msgstr "Dobijte sveobuhvatan pregled tema koje su vam važne."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:102
msgid "Get a more complete view of the topics that interest you"
msgstr "Dobijte još potpuniji pregled tema koje vas zanimaju"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:34
msgid "Get access to our media archive."
msgstr "Dobijte pristup našem medijskom arhivu."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:71
msgid "Go back to Emailing"
msgstr "Povratak na Emailing"

#: src/components/staff/admin/workspace/Workspace.js:403
msgid "Google Translate price: 1 article = 2.5 Kč = 0.09 €"
msgstr "Google Translate cijena: 1 članak = 2.5 Kč = 0.09 €"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:268
msgid "Gross Rating Point"
msgstr "Bruto ocjenjivačka točka"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:107
msgid "Group articles"
msgstr "Grupiraj članke"

#: src/constants/stats.ts:16
#: src/constants/analytics.js:1010
#: src/components/widgets/modules/stats/WidgetStats.js:150
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:265
#: src/components/misc/ActionsBar/View/ViewMenu.js:211
msgid "GRP"
msgstr "GRP"

#: src/components/monitoring/Inspector/InspectorMonitora/HashTagsList/HashTagsList.js:24
msgid "Hashtags"
msgstr "Hashtagovi"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:33
msgid "Hasn't started yet"
msgstr "Još nije počeo"

#: src/pages/sign-up-completion.tsx:42
#: src/components/page/auth/SignUp/SignUp.js:81
msgid "Have an account? Sign in"
msgstr "Imate račun? Prijavite se"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:11
msgid "Head of External and Internal Communication"
msgstr "Voditelj vanjske i unutarnje komunikacije"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:225
msgid "Header"
msgstr "Zaglavlje"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:166
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:172
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:178
msgid "Heading"
msgstr "Naslov"

#: src/pages/user/reactivate-24.js:37
msgid "Hello!<0/><1/>Thank you for your interest in trying Mediaboard with all the features. Please confirm by clicking on the button below. We will contact you as soon as possible.<2/><3/>Kind regards,<4/><5/>{appName} team"
msgstr "Pozdrav!<0/><1/>Hvala vam na interesu za isprobavanje Mediaboarda sa svim značajkama. Molimo potvrdite klikom na gumb ispod. Kontaktirat ćemo vas što je prije moguće.<2/><3/>Srdačan pozdrav,<4/><5/>{appName} tim"

#: src/pages/user/yoy-analysis.js:37
msgid "Hello!<0/><1/>Thank you for your interest! Please confirm by clicking on the button below. We will contact you as soon as possible.<2/><3/>Kind regards,<4/><5/>{appName} team"
msgstr "Pozdrav!<0/><1/>Hvala na vašem interesu! Molimo potvrdite klikom na gumb ispod. Kontaktirat ćemo vas što je prije moguće.<2/><3/>Srdačan pozdrav,<4/><5/>tim {appName}"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:30
msgid "Hello!<0/><1/>Thanks for your interest in our Emailing platform. To activate it you need to verify your email first. To do so just check, that the email you have entered is correct and click the activation button.<2/><3/><4/><5/>Best regards,<6/><7/>{appName} team"
msgstr "Pozdrav!<0/><1/>Hvala na vašem interesu za našu platformu za slanje e-pošte. Da biste je aktivirali, prvo morate potvrditi svoju e-poštu. Da biste to učinili, samo provjerite je li e-pošta koju ste unijeli ispravna i kliknite na gumb za aktivaciju.<2/><3/><4/><5/>S poštovanjem,<6/><7/>{appName} tim"

#: src/helpers/modal/withModalHelp.tsx:17
#: src/components/layout/Sidebar/SidebarNavigation.tsx:215
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:20
#: src/components/OurChart/OurChartAdvanced.js:268
msgid "Help"
msgstr "Pomoć"

#: src/components/medialist/content/MedialistAuthorCreate.js:20
msgid "Here you will see all the activity related to this author."
msgstr "Ovdje ćete vidjeti sve aktivnosti povezane s ovim autorom."

#: src/components/emailing/components/FunnelStats/StatBlock.tsx:121
msgid "Here you will see newsroom analytics affected by the campaign"
msgstr "Ovdje ćete vidjeti analitiku redakcije koju je kampanja utjecala"

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:159
msgid "hide"
msgstr "sakrij"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:246
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:210
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:249
msgid "Hide"
msgstr "Sakrij"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:557
msgid "Hide header and footer"
msgstr "Sakrij zaglavlje i podnožje"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:63
msgid "hide stats"
msgstr "sakrij statistiku"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:230
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:241
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:144
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:153
msgid "Hide tag"
msgstr "Sakrij oznaku"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:229
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:190
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:202
msgid "Hide topic"
msgstr "Sakrij temu"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:210
msgid "Hide topics in folder"
msgstr "Sakrij teme u mapi"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:65
msgid "Highlight Only"
msgstr "Samo istakni"

#: src/components/tariff/Permissions/Permissions.js:51
msgid "Hints"
msgstr "Savjeti"

#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:28
#: src/components/exportList/Content/Content.tsx:72
msgid "History"
msgstr "Povijest"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:84
msgid "Homepage url"
msgstr "URL početne stranice"

#: src/components/emailing/forms/FormSenderSettings.js:82
msgid "Host"
msgstr "Domaćin"

#: src/components/emailing/forms/FormSenderSettings.js:123
msgid "Hostname"
msgstr "Naziv poslužitelja"

#: src/components/staff/admin/workspace/Workspace.js:504
msgid "How many years back is the user allowed to search in media archive."
msgstr "Koliko godina unatrag korisnik može pretraživati u medijskom arhivu."

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:88
msgid "How permissions work"
msgstr "Kako funkcioniraju dopuštenja"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:135
msgid "How to help the AI generate a more satisfying and detailed email"
msgstr "Kako pomoći umjetnoj inteligenciji generirati zadovoljavajući i detaljniji e-mail"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:218
msgid "How to use {appName}"
msgstr "Kako koristiti {appName}"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:111
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:91
msgid "HTML"
msgstr "HTML"

#: src/pages/user/yoy-analysis.js:59
#: src/pages/user/reactivate-24.js:59
msgid "I am interested"
msgstr "Zanima me"

#: src/components/staff/admin/user/WorkspacesTable.js:74
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:74
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:80
msgid "ID"
msgstr "ID"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:43
msgid "Ideal for those with their own content ready."
msgstr "Idealno za one koji imaju spreman sadržaj."

#: src/components/staff/admin/workspace/Workspace.js:382
msgid "If set to 0, then: no export basket, no exporting or email sending from feed or export basket."
msgstr "Ako je postavljeno na 0, onda: nema košarice za izvoz, ne može se izvoziti ni slati e-mail iz feeda."

#: src/helpers/modal/withModalReportArticle.tsx:23
msgid "If the article has a bad transcript or screenshot, please report the problem and our staff will look into it and fix the issue."
msgstr "Ako članak ima loš prijepis ili snimku zaslona, molimo prijavite problem i naše osoblje će ga istražiti i riješiti problem."

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:92
msgid "If this was a mistake or if you'd like to re-subscribe at any time, please contact us at"
msgstr "Ako je došlo do pogreške ili se želite ponovno pretplatiti u bilo kojem trenutku, kontaktirajte nas na"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:64
msgid "If this was a mistake or you'd prefer to stay available for our emails, no further action is needed."
msgstr "Ako je došlo do pogreške ili želite ostati dostupni za naše e-mailove, nije potrebno poduzimati daljnje korake."

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:39
msgid "If you don't remember your current password, you can <0>reset it</0> or contact us at <1>{salesEmail}</1>."
msgstr "Ako se ne sjećate svoje trenutne lozinke, možete je <0>ponovno postaviti</0> ili nas kontaktirajte na <1>{salesEmail}</1>."

#: src/components/page/auth/Expired/Expired.js:63
msgid "If you liked our service and would like to purchase the account, send us an email to <0>{salesEmail}</0>"
msgstr "Ako vam se svidjela naša usluga i želite kupiti račun, pošaljite nam e-mail na <0>{salesEmail}</0>"

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:20
msgid "If you would like to purchase a workspace account, send us an email to <0>{salesEmail}</0>"
msgstr "Ako želite kupiti račun za radni prostor, pošaljite nam e-mail na <0>{salesEmail}</0>"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:126
msgid "If you'd like to re-subscribe, please contact us at"
msgstr "Ako se želite ponovno pretplatiti, kontaktirajte nas na adresi"

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:56
msgid "If you'd like to unsubscribe from emails, sent via mediaboard.com, simply click the button below. Your email <0>{0}</0> will no longer receive new emails form us."
msgstr "Ako se želite odjaviti s primanja e-mailova poslanih putem mediaboard.com, jednostavno kliknite na gumb ispod. Vaša e-mail adresa <0>{0}</0> više neće primati nove e-mailove od nas."

#: src/components/reports/history/Content.js:38
msgid "If your report wasn't delivered, make sure to check your spam folder and your promotions inbox."
msgstr "Nije li vam dostavljen izvještaj putem e-maila? Provjerite svoju mapu za neželjenu poštu i mapu Promocije (ako koristite Gmail)."

#: src/store/models/dashboards/DashboardPreview.js:147
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:49
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:47
#: src/components/misc/MntrEditor/extensions/ExtensionImageGallery.js:49
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:70
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:70
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewImage/PreviewImage.js:29
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:38
msgid "Image"
msgstr "Slika"

#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:61
msgid "Images"
msgstr "Slike"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:70
msgid "Import"
msgstr "Uvoz"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:181
#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:84
#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:13
#: src/components/medialist/content/MedialistActionsBar/ContactsImportTitle.tsx:7
msgid "Import contacts"
msgstr "Uvezi kontakte"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:54
msgid "Import options"
msgstr "Opcije uvoza"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:42
msgid "Import to"
msgstr "Uvezi u"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:75
msgid "Import your already formatted contact list or manually completed template."
msgstr "Uvezite svoj već formatirani popis kontakata ili ručno ispunjeni predložak."

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:106
msgid "Import your contacts to medialist"
msgstr "Uvezite svoje kontakte u medijalist"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:92
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:37
msgid "Imprint"
msgstr "Impresum"

#. placeholder {0}: formatDate(lowerDate, 'd. M. yyyy')
#. placeholder {1}: formatDate(upperDate, 'd. M. yyyy')
#: src/helpers/getTitleWithDateFromTo.js:5
msgid "in period from {0} to {1}"
msgstr "u razdoblju od {0} do {1}"

#: src/components/newsroom/content/posts/NewsroomPosts.js:297
msgid "In the settings you can edit basic information about the Newsroom, appearance, web address, etc."
msgstr "U postavkama možete urediti osnovne informacije o Newsroomu, izgledu, web adresi itd."

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:13
msgid "In three years with Mediaboard, our experience has been exceptional. Their professionalism, wide range of services, and top-notch quarterly and annual analyses are highly valuable. We recommend Mediaboard for quality and reliability."
msgstr "Tijekom tri godine suradnje s Mediaboardom, naše iskustvo je bilo iznimno. Njihova profesionalnost, širok spektar usluga i vrhunske tromjesečne i godišnje analize su nam izuzetno vrijedne. Mediaboard preporučujemo zbog visoke kvalitete i pouzdanosti."

#: src/components/settings/SettingsApplication/SettingsApplication.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:106
msgid "In-app currency"
msgstr "Valuta korištena u aplikaciji"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:173
#: src/components/staff/admin/user/User.js:138
#: src/components/staff/admin/customer/users/UsersTable.js:116
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoStaticItem.js:14
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoItem.js:31
#: src/components/forms/dashboard/Search/SearchUsers.js:99
msgid "Inactive"
msgstr "Neaktivan"

#: src/components/reports/Content/ReportsList/FormToggleActive/FormToggleActive.js:36
msgid "Inactive report"
msgstr "Neaktivan izvještaj"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:22
msgid "Include all the key points you want to specifically mention in your article. These should be the essential details, arguments, or highlights that support and enhance the main content.\""
msgstr "Uključite sve ključne točke koje želite posebno spomenuti u svom članku. To bi trebali biti bitni detalji, argumenti ili naglasci koji podržavaju i poboljšavaju glavni sadržaj.”"

#: src/components/tariff/TariffLimits/TariffLimits.js:51
#: src/components/tariff/TariffLimits/TariffLimits.js:88
#: src/components/tariff/TariffLimits/TariffLimits.js:123
#: src/components/tariff/TariffLimits/TariffLimits.js:143
#: src/components/tariff/TariffLimits/TariffLimits.js:159
#: src/components/tariff/TariffLimits/TariffLimits.js:176
#: src/components/tariff/TariffLimits/TariffLimits.js:195
#: src/components/tariff/TariffLimits/TariffLimits.js:212
#: src/components/tariff/TariffLimits/TariffLimits.js:233
#: src/components/tariff/TariffLimits/TariffLimits.js:250
#: src/components/tariff/TariffLimits/TariffLimits.js:283
#: src/components/tariff/TariffLimits/SingleValueLimit/SingleValueLimit.js:20
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:114
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:85
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:102
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:110
msgid "Increase limit"
msgstr "Povećaj limit"

#: src/components/tariff/TariffLimits/TariffLimits.js:50
#: src/components/tariff/TariffLimits/TariffLimits.js:87
#: src/components/tariff/TariffLimits/TariffLimits.js:122
#: src/components/tariff/TariffLimits/TariffLimits.js:142
#: src/components/tariff/TariffLimits/TariffLimits.js:158
#: src/components/tariff/TariffLimits/TariffLimits.js:175
#: src/components/tariff/TariffLimits/TariffLimits.js:194
#: src/components/tariff/TariffLimits/TariffLimits.js:211
#: src/components/tariff/TariffLimits/TariffLimits.js:232
#: src/components/tariff/TariffLimits/TariffLimits.js:249
#: src/components/tariff/TariffLimits/TariffLimits.js:282
#: src/components/tariff/TariffLimits/SingleValueLimit/SingleValueLimit.js:18
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:84
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:101
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:109
msgid "Increase limit?"
msgstr "Povećati limit?"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:52
msgid "Indicate the desired tone (formal, casual) and style (informative, promotional)."
msgstr "Naznačite željeni ton (formalni, neformalni) i stil (informativni, promotivni)."

#: src/components/monitoring/FeedList/FeedListItem/MetaData/modules/MetaDataScore/MetaDataScore.js:38
msgid "influence score"
msgstr "skor utjecaja"

#: src/constants/stats.ts:21
#: src/constants/analytics.js:262
#: src/constants/analytics.js:371
#: src/constants/analytics.js:451
#: src/constants/analytics.js:483
#: src/constants/analytics.js:644
#: src/constants/analytics.js:659
#: src/constants/analytics.js:929
#: src/constants/analytics.js:944
#: src/components/widgets/modules/stats/WidgetStats.js:162
#: src/components/misc/ActionsBar/View/ViewMenu.js:297
msgid "Influence score"
msgstr "Rezultat utjecaja"

#: src/constants/analytics.js:283
#: src/constants/analytics.js:530
msgid "Influence score by mention type"
msgstr "Rezultat utjecaja (Influence Score) prema tipu spomena"

#: src/constants/analytics.js:281
#: src/constants/analytics.js:390
#: src/constants/analytics.js:481
msgid "Influence score by sentiment"
msgstr "Skor utjecaja (Influence Score) prema sentimentu"

#: src/constants/analytics.js:392
msgid "Influence score by social network"
msgstr "Skor utjecaja (Influence Score) prema soc. mreži"

#: src/components/emailing/content/sender/EmailingSenderContent.js:17
msgid "Initial Emailing settings"
msgstr "Početne postavke e-pošte"

#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:124
msgid "Insert"
msgstr "Umetni"

#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:155
msgid "Insert button label to view preview"
msgstr "Unesite oznaku gumba za pregled"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:125
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:169
msgid "Insert HTML code to view preview"
msgstr "Unesite HTML kod za pregled pregleda"

#: src/components/emailing/content/CreateEmailContent.js:408
msgid "Insert internal name of email"
msgstr "Unesite interni naziv e-pošte"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:359
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:363
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:385
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:409
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:413
msgid "Insert link"
msgstr "Umetni poveznicu"

#: src/components/emailing/content/CreateEmailContent.js:449
msgid "Insert subject"
msgstr "Unesite predmet"

#: src/components/medialist/forms/FormEditAuthor.js:736
#: src/components/medialist/forms/FormEditAuthor.js:1031
msgid "Insert text..."
msgstr "Unesite tekst..."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:121
msgid "Instructions"
msgstr "Upute"

#: src/constants/analytics.js:416
msgid "Interactions by sentiment"
msgstr "Interakcije prema sentimentu"

#: src/constants/analytics.js:418
msgid "Interactions by social network"
msgstr "Interakcije po društvenoj mreži"

#: src/constants/analytics.js:227
#: src/constants/analytics.js:639
#: src/constants/analytics.js:774
#: src/components/layout/AuthWrapper/constants/features.slides.js:199
msgid "Interactions on social networks"
msgstr "Interakcije na društvenim mrežama"

#: src/constants/analytics.js:225
msgid "Interactions on social networks by sentiment"
msgstr "Interakcije na društvenim mrežama prema sentimentu"

#: src/components/emailing/content/CreateEmailContent.js:405
msgid "Internal name of email"
msgstr "Interni naziv e-pošte"

#: src/components/emailing/forms/FormEmailRecipients.js:37
msgid "Invalid"
msgstr "Neispravno"

#: src/components/article/Content.js:13
msgid "Invalid article link"
msgstr "Neispravan link na članak"

#. placeholder {0}: format( startOfYear(today), DATE_FORMAT, )
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:83
msgid "Invalid date format. Expected format is {0}"
msgstr "Neispravan format datuma. Očekivani format je {0}"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:16
#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:10
#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:33
msgid "Invalid email format"
msgstr "Neispravan format e-pošte"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:118
msgid "Invalid page number"
msgstr "Neispravan broj stranice"

#: src/components/forms/inspector/FormMediaEditor.js:76
#: src/components/forms/inspector/FormMediaEditor.js:79
msgid "Invalid time format. Enter hh:mm:ss"
msgstr "Neispravan format vremena. Unesite hh:mm:ss"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:57
msgid "Invoice no."
msgstr "Broj računa"

#: src/pages/staff/admin/customers/[customerId]/invoices.js:12
#: src/components/staff/admin/customer/invoices/Invoices.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:42
msgid "Invoices"
msgstr "Računi"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:56
msgid "Irrelevant"
msgstr "Irrelevantno"

#: src/components/misc/Changelog/ChangelogTableRow.js:156
msgid "Irreversible"
msgstr "Nepovratno"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:104
msgid "Is overdue"
msgstr "Kasni"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:296
msgid "Is there a problem with the article?"
msgstr "Postoji li problem s člankom?"

#. placeholder {0}: data.publication.issue
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:225
msgid "Issue: {0}"
msgstr "Izdanje: {0}"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:72
msgid "Issued via"
msgstr "Izdano preko"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:368
msgid "It appears above the description on the search results page."
msgstr "Prikazuje se iznad opisa na stranici s rezultatima pretrage."

#: src/pages/_error.js:49
msgid "It looks like you're trying to access a malformed URL. Please review it and try again."
msgstr "Izgleda da pokušavate pristupiti neispravnom URL-u. Molimo pregledajte ga i pokušajte ponovno."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:131
msgid "Italic"
msgstr "Kurziv"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:144
msgid "Item '<0>{title}</0>' will be removed."
msgstr "Stavka '<0>{title}</0>' će biti uklonjena."

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:179
msgid "Job position"
msgstr "Radno mjesto"

#: src/components/medialist/forms/modules/FormArray.js:107
msgid "Job Position"
msgstr "Radno mjesto"

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:21
msgid "formatNumber.k"
msgstr "tis."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:68
msgid "Keep original"
msgstr "Zadrži original"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:20
msgid "Key points list"
msgstr "Popis ključnih točaka"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:56
msgid "Key Points:"
msgstr "Ključne točke:"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:40
msgid "Keyword"
msgstr "Ključna riječ"

#: src/helpers/modal/withModalTvrTopics.tsx:53
#: src/constants/analytics.js:1054
#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/KeywordsListMedia/KeywordsListMedia.js:22
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:53
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:119
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:255
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:337
msgid "Keywords"
msgstr "Ključne riječi"

#: src/components/misc/ResendSettings/SaveResendSettings/FormSaveResendSettings.js:21
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:122
#: src/components/misc/ExportSettings/SaveExportSettings/FormSaveExportSettings.js:21
#: src/components/emailing/forms/FormAddCampaign.tsx:15
#: src/components/dashboards/DashboardSelector/FormEditDashboard.js:26
msgid "Label"
msgstr "Oznaka"

#: src/constants/analytics.js:870
#: src/components/staff/admin/user/User.js:278
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:274
#: src/components/misc/ActionsBar/View/ViewMenu.js:333
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:310
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:329
#: src/components/layout/Header/UserMenu/UserMenu.tsx:119
msgid "Language"
msgstr "Jezik"

#: src/constants/analytics.js:883
msgid "Languages"
msgstr "Jezici"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:106
msgid "Languages & connected newsrooms"
msgstr "Jezici i povezani newsroomi"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:236
msgid "Last access"
msgstr "Zadnji pristup"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:122
#: src/components/staff/admin/customer/users/UsersTable.js:74
msgid "Last login"
msgstr "Posljednja prijava"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:147
msgid "Last month"
msgstr "Prošli mjesec"

#: src/components/page/auth/SignUp/SignUp.js:29
msgid "Last Name"
msgstr "Prezime"

#: src/components/newsroom/components/PostsList/PostsList.js:166
#: src/components/newsroom/components/PostsList/PostsList.js:181
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:152
msgid "Last update"
msgstr "Zadnje ažuriranje"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:128
msgid "Last week"
msgstr "Prošli tjedan"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:160
msgid "Last year"
msgstr "Prošle godine"

#: src/components/emailing/forms/FormSenderSettings.js:106
msgid "Leave blank to use the default port"
msgstr "Ostavite prazno za korištenje zadanih portova"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:141
msgid "Light"
msgstr "Svijetli"

#: src/components/settings/SettingsTheme/SettingsThemePreview/SettingsThemePreview.js:193
msgid "Light mode preview"
msgstr "Pregled svijetlog načina"

#: src/components/medialist/forms/FormEditAuthor.js:504
msgid "limit"
msgstr "limita"

#: src/store/models/monitoring/MedialistMapItem/MedialistMapItem.js:260
#: src/store/models/monitoring/MedialistMapItem/MedialistMapItem.js:285
#: src/store/models/authors/AuthorsStore.js:410
#: src/store/models/authors/AuthorsStore.js:435
msgid "Limit exceeded. Sucessfully exported {generated} of {requested} requested authors."
msgstr "Premašen je limit. Uspješno izvezeno {generated} od {requested} traženih autora."

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:652
msgid "Limit reached. You have selected too many articles."
msgstr "Dosegnut je limit. Odabrali ste previše članaka."

#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:35
msgid "Limits"
msgstr "Ograničenja"

#: src/components/OurChart/OurChartAdvanced.js:162
msgid "Line"
msgstr "Linija"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:76
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:325
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:131
msgid "Link"
msgstr "Poveznica"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:40
#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:69
#: src/components/staff/admin/user/User.js:67
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:224
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:15
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:16
#: src/components/exportList/History/HistoryTable/HistoryTable.js:111
msgid "Link has been copied to the clipboard."
msgstr "Poveznica je kopirana u međuspremnik."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:116
msgid "Link to other language"
msgstr "Poveznica na drugi jezik"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:95
msgid "Link to the article"
msgstr "Poveznica na članak"

#: src/components/emailing/content/EmailingSettingsContent.js:31
msgid "Linking with Google account timed out. Please, try again."
msgstr "Isteklo je vrijeme za povezivanje s Google računom. Molimo pokušajte ponovo."

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:240
#: src/components/monitoring/Inspector/InspectorMonitora/Links/Links.js:14
msgid "Links"
msgstr "Poveznice"

#: src/components/medialist/content/AuthorBasketsMenu.js:135
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:124
msgid "List {label} will be removed."
msgstr "Popis {label} će biti uklonjen."

#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:25
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:31
#: src/components/forms/baskets/FormNewBasket.js:26
msgid "List name"
msgstr "Naziv popisa"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:26
msgid "List of tags"
msgstr "Popis oznaka"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:99
msgid "List of topics"
msgstr "Popis tema"

#: src/components/medialist/forms/FormEditAuthor.js:660
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:97
msgid "Lists"
msgstr "Popisi"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:46
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:20
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:20
msgid "Load"
msgstr "Učitaj"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:54
msgid "Load from"
msgstr "Učitaj iz"

#: src/components/monitoring/FeedList/LoadMore/LoadMore.js:27
msgid "Load more"
msgstr "Učitaj više"

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:140
msgid "Load more..."
msgstr "Učitaj više..."

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:46
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:48
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:48
msgid "Load settings"
msgstr "Učitaj postavke"

#: src/components/tvr/Content/Content.js:82
#: src/components/trash/Content.js:39
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:70
#: src/components/reports/history/HistoryTable.js:491
#: src/components/notifications/Permissions.js:84
#: src/components/notifications/AppNotifications/AppNotifications.js:21
#: src/components/newsroom/content/dashboard/ChartVisits.js:127
#: src/components/monitoring/WorkspaceArticles/Intro.js:20
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:57
#: src/components/monitoring/FeedList/LoadMore/LoadMore.js:27
#: src/components/monitoring/FeedChart/FeedChart.js:47
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:47
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:55
#: src/components/misc/MntrEditor/forms/FormMediaUpload/UploadProgress.js:31
#: src/components/medialist/content/MedialistHeading.js:14
#: src/components/medialist/content/MedialistInspector/AuthorChart/AuthorChart.js:84
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:31
#: src/components/exportList/Content/HeadingExport/HeadingExport.js:25
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:40
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:48
#: src/components/dashboards/DashboardSelector/DashboardSelector.js:21
#: src/components/analytics/AnalyticsContent.js:38
#: src/components/OurChart/OurChartAdvanced.js:316
msgid "Loading..."
msgstr "Učitavam..."

#: src/components/layout/Header/UserMenu/UserMenu.tsx:228
msgid "Log back in"
msgstr "Prijavite se ponovno"

#: src/components/page/auth/Login/Login.tsx:55
#: src/components/page/auth/Login/Login.tsx:69
#: src/components/page/auth/Expired/Expired.js:104
msgid "Log In"
msgstr "Prijavi se"

#: src/components/staff/admin/user/User.js:192
#: src/components/staff/admin/customer/users/UsersTable.js:129
msgid "Login as this user"
msgstr "Prijavi se kao ovaj korisnik"

#: src/components/staff/admin/workspace/Workspace.js:249
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:137
msgid "Login into this workspace"
msgstr "Prijavite se u ovaj radni prostor"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:187
msgid "Login link"
msgstr "Poveznica za prijavu"

#: src/components/page/auth/Login/Login.tsx:63
msgid "Login to {appName}"
msgstr "Prijava u aplikaciju {appName}"

#: src/components/page/auth/Login/Login.tsx:64
msgid "Login to {appName}, the next generation media monitoring tool."
msgstr "Prijavite se u {appName}, alat za praćenje medija nove generacije. Pratite, mjerite i analizirajte svoju komunikaciju."

#: src/components/page/auth/Expired/Expired.js:75
msgid "Login to different workspace"
msgstr "Prijavite se na drugi radni prostor"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:187
msgid "Logout"
msgstr "Odjava"

#: src/helpers/auth.js:47
msgid "Logout performed in another window."
msgstr "Odjava izvršena u drugom prozoru."

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:22
msgid "formatNumber.M"
msgstr "mil."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:155
msgid "Magazine cover pages"
msgstr "Naslovnice časopisa"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:59
msgid "Main message"
msgstr "Glavna poruka"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:48
msgid "Main message & key points"
msgstr "Glavna poruka i ključne točke"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:46
msgid "Main Objective:"
msgstr "Glavni cilj:"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:61
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:65
msgid "Mainstream sources"
msgstr "Mainstream izvori"

#: src/pages/_error.js:44
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:422
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:156
#: src/components/misc/MntrEditor/forms/FormEmbedSocialUrl/FormEmbedFacebookUrl.tsx:44
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:88
msgid "Malformed URL"
msgstr "Neispravan URL"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:104
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:107
msgid "Manage hidden tags"
msgstr "Upravljaj skrivenim oznakama"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:95
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:98
msgid "Manage hidden topics"
msgstr "Upravljaj skrivenim temama"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:231
msgid "Management summaries"
msgstr "Upravljački sažeci"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:49
msgid "Manual writing"
msgstr "Ručno pisanje"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:36
msgid "Manually add the information for your signature, which will appear in every email or customize it using your own HTML."
msgstr "Ručno dodajte informacije za svoj potpis, koji će se pojavljivati u svakom e-mailu, ili ga prilagodite koristeći vlastiti HTML."

#: src/components/emailing/content/promo/PromoEmailing.js:27
msgid "Mass mailing"
msgstr "Masovno slanje pošte"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:488
msgid "Max. file size:"
msgstr "Max. veličina datoteke:"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:225
msgid "Media analysis"
msgstr "Analiza medija"

#: src/components/tariff/TariffLimits/TariffLimits.js:261
#: src/components/staff/admin/workspace/Workspace.js:500
msgid "Media archive depth limit"
msgstr "Dubina medijskog arhiva"

#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:120
msgid "Media Coverage"
msgstr "Medijsko pokrivanje"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:330
#: src/components/misc/ActionsBar/View/ViewMenu.js:49
#: src/components/misc/ActionsBar/View/ViewMenu.js:344
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:37
msgid "Media data"
msgstr "Medijske metrike"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:92
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:168
msgid "Media data (GRP, OTS, AVE, PRIMe)"
msgstr "Medijski podaci (GRP, OTS, AVE, PRIMe)"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:16
msgid "Media Monitoring"
msgstr "Praćenje medija"

#: src/constants/analytics.js:79
#: src/constants/analytics.js:585
#: src/constants/analytics.js:717
#: src/components/layout/AuthWrapper/constants/features.slides.js:183
msgid "Media reach (GRP)"
msgstr "Medijski doseg (GRP)"

#: src/constants/analytics.js:77
msgid "Media reach (GRP) by sentiment"
msgstr "Medijski doseg (GRP) prema sentimentu"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:222
msgid "Media services"
msgstr "Medijske usluge"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:43
msgid "Mediaboard transformed our communication at Coca-Cola HBC! A daily essential for top-notch media monitoring, with a user-friendly interface and insightful analytics. Their exceptional customer support makes it a joy to work with Mediaboard."
msgstr "Mediaboard je transformirao našu komunikaciju u Coca-Cola HBC! Neophodan je svakodnevno za vrhunsko praćenje medija, s korisničkim sučeljem i prodornom analitikom. Njihova iznimna korisnička podrška čini rad s Mediaboardom pravim užitkom."

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:29
msgid "Mediakit"
msgstr "Mediakit"

#: src/store/models/dashboards/DashboardPreview.js:99
#: src/pages/authors/index.js:32
#: src/pages/authors/index.js:41
#: src/pages/authors/create.js:10
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:110
#: src/components/layout/Sidebar/SidebarNavigation.tsx:137
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:36
#: src/components/layout/AuthWrapper/constants/features.slides.js:214
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:44
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:44
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:29
#: src/app/components/monitoring-navigation.tsx:269
msgid "Medialist"
msgstr "Adrema"

#: src/constants/analytics.js:793
#: src/components/topics/Content/TopicsList/MegalistModal.js:52
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:70
#: src/components/topics/Content/TopicsList/MegalistToolbar/MediatypeFilterPopup.js:8
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:247
msgid "Mediatype"
msgstr "Medijski tip"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:91
msgid "mediatype for all countries"
msgstr "mediatip za sve zemlje"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:67
msgid "Mention any personalization details (name, company)."
msgstr "Navedite sve personalizacijske podatke (ime, tvrtka)."

#: src/constants/analytics.js:233
#: src/constants/analytics.js:342
#: src/constants/analytics.js:889
#: src/constants/analytics.js:909
#: src/constants/analytics.js:1312
#: src/components/widgets/modules/stats/WidgetStats.js:241
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:51
msgid "Mentions"
msgstr "Spominjanja"

#: src/constants/analytics.js:254
#: src/constants/analytics.js:363
msgid "Mentions by sentiment"
msgstr "Spominjanja prema sentimentu"

#: src/constants/analytics.js:365
#: src/constants/analytics.js:923
#: src/constants/analytics.js:1325
msgid "Mentions by social network"
msgstr "Spominjanja po društvenoj mreži"

#: src/constants/analytics.js:256
#: src/constants/analytics.js:903
msgid "Mentions by type"
msgstr "Spominjanja po tipu"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:586
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:96
msgid "Merge Tags"
msgstr "Spoji oznake"

#: src/pages/staff/admin/customers/[customerId]/merged-customers.js:12
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomers.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:60
msgid "Merged customers"
msgstr "Spojeni kupci"

#: src/components/staff/admin/customer/bio/CustomerBio.js:79
msgid "Merged to"
msgstr "Spojeno u"

#: src/components/misc/ActionsBar/View/ViewMenu.js:199
msgid "Metrics"
msgstr "Metrike"

#: src/components/misc/portable/PortableResend/PortableResend.js:93
#: src/components/misc/portable/PortableExport/PortableExport.js:88
msgid "Minimize"
msgstr "Minimiziraj"

#: src/components/tariff/MonitoredMedia/MissedArticles/MissedArticles.js:9
msgid "Missed articles"
msgstr "Propušteni članci"

#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:54
#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:74
msgid "Missing article"
msgstr "Nedostaje članak"

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:39
msgid "Missing data"
msgstr "Nedostaju podaci"

#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:46
msgid "Missing recipient info"
msgstr "Nedostaju podaci o primatelju"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:298
#: src/components/layout/AuthWrapper/constants/features.slides.js:399
msgid "Mobile Apps"
msgstr "Mobilne aplikacije"

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:41
msgid "Modified"
msgstr "Izmijenjeno"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoItem.js:87
msgid "Monitor a wide range of social media platforms including Facebook, LinkedIn, Instagram, TikTok, X.com, and YouTube."
msgstr "Pratite širok spektar društvenih mreža uključujući Facebook, LinkedIn, Instagram, TikTok, X.com i YouTube."

#: src/components/layout/AuthWrapper/constants/features.slides.js:23
msgid "Monitor newspapers, magazines, radios, TV stations or the entire online world. Reach out to media, react, track, analyze, and build your brand."
msgstr "Pratite događanja u novinama, časopisima, na radiju, televizijskim postajama i cijelom online svijetu. Obratite se medijima, reagirajte, pratite, analizirajte i izgradite svoj brend."

#: src/components/topics/Content/TopicsList/MediaCard.js:21
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:127
#: src/components/staff/admin/workspace/Workspace.js:877
#: src/components/settings/SettingsTariff/SettingsTariff.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:41
msgid "Monitored media"
msgstr "Praćeni mediji"

#: src/components/notifications/Content.js:34
#: src/components/monitoring/Monitoring.js:109
#: src/components/monitoring/Monitoring.js:110
#: src/components/monitoring/Monitoring.js:165
#: src/components/layout/Sidebar/SidebarNavigation.tsx:119
#: src/components/layout/AuthWrapper/constants/features.slides.js:22
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:10
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:10
#: src/app/components/monitoring-navigation.tsx:65
msgid "Monitoring"
msgstr "Medijsko praćenje"

#: src/components/staff/admin/customer/expenses/ExpenseTable.js:77
msgid "Month"
msgstr "Mjesec"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:107
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:65
msgid "monthly sessions"
msgstr "mjesečne sesije"

#: src/components/misc/ActionsBar/View/ViewMenu.js:82
msgid "Monthly sessions"
msgstr "Mjesečne sesije"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:87
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:55
msgid "monthly users"
msgstr "korisnika mjesečno"

#: src/components/misc/ActionsBar/View/ViewMenu.js:74
msgid "Monthly users"
msgstr "Mjesečni posjetitelji"

#: src/helpers/charts/makeGranularityMenu.js:26
#: src/helpers/charts/getGranularityLabel.js:9
msgid "Months"
msgstr "Mjesečni"

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:159
msgid "more"
msgstr "više"

#: src/components/OurChart/OurChartAdvanced.js:247
msgid "More"
msgstr "Više"

#: src/constants/analytics.js:1338
msgid "Most common terms"
msgstr "Najčešći pojmovi"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:96
msgid "Move article"
msgstr "Premjesti članak"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:119
msgid "Move to Dashboard"
msgstr "Premjesti na nadzornu ploču"

#: src/pages/workspace-articles.js:51
#: src/components/monitoring/WorkspaceArticles/Intro.js:23
#: src/app/components/monitoring-navigation.tsx:204
msgid "My Articles"
msgstr "Moji članci"

#: src/components/medialist/content/AuthorInfoDetail.js:72
msgid "My author"
msgstr "Moj autor"

#: src/components/medialist/content/OwnAuthorsListSelectorButton.js:9
#: src/components/medialist/content/AuthorBasketsMenu.js:41
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:158
msgid "My authors"
msgstr "Moji autori"

#: src/components/topics/Content/TopicsList/FormSaveMegalist/FormSaveMegalist.js:8
#: src/components/staff/admin/workspace/Workspace.js:288
#: src/components/staff/admin/user/WorkspacesTable.js:71
#: src/components/staff/admin/user/User.js:256
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:71
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:52
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:62
#: src/components/medialist/forms/FormEditAuthor.js:211
#: src/components/medialist/forms/FormEditAuthor.js:212
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:28
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:53
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:45
msgid "Name"
msgstr "Ime"

#. js-lingui-explicit-id
#: src/components/dashboards/DashboardSelector/FormCreateDashboard.js:25
msgid "name.nazev"
msgstr "Naziv"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:57
msgid "Name for expression (optional)"
msgstr "Naziv za izraz (neobavezno)"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:341
msgid "Need help? Ask AI assistant. Select a sentence or paragraph"
msgstr "Trebate pomoć? Pitajte AI asistenta. Odaberite rečenicu ili odlomak."

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:147
msgid "Need help? Check our <0>tutorial</0> or contact us."
msgstr "Trebate li pomoć? Pogledajte naš <0>tutorial</0> ili nas kontaktirajte."

#: src/components/medialist/content/MedialistDashboard.js:94
#: src/components/medialist/content/MedialistDashboard.js:127
msgid "New"
msgstr "Novo"

#. js-lingui-explicit-id
#: src/components/layout/Header/AppNotifications/AppNotifications.js:133
msgid "new.notifications"
msgstr "Nove"

#: src/components/emailing/modules/withModalAddCampaign.tsx:20
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:91
#: src/components/emailing/content/EmailingCampaignsContent.tsx:59
msgid "New Campaign"
msgstr "Nova kampanja"

#: src/components/newsroom/forms/FormCreateCategory/FormCreateCategory.js:29
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:72
msgid "New Category"
msgstr "Nova kategorija"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:29
#: src/components/emailing/content/NewEmailWizardButton.tsx:13
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:72
msgid "New Email"
msgstr "Nova e-pošta"

#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:38
#: src/components/forms/baskets/FormNewBasket.js:10
msgid "New list"
msgstr "Novi popis"

#: src/pages/user/reset-password/success.tsx:7
#: src/pages/user/reset-password/new.tsx:54
#: src/components/staff/admin/user/User.js:267
#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:87
msgid "New password"
msgstr "Nova lozinka"

#: src/store/models/admin/customer/CustomerStore.js:227
msgid "New passwords have been copied to the clipboard."
msgstr "Nove lozinke su kopirane u međuspremnik."

#: src/components/newsroom/content/posts/NewPostWizardButton.tsx:13
msgid "New post"
msgstr "Nova objava"

#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:71
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:70
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:103
msgid "New report"
msgstr "Novi izvještaj"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:78
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:264
#: src/components/forms/tags/FormNewTag/FormNewTag.js:10
msgid "New Tag"
msgstr "Nova oznaka"

#: src/components/topics/Content/TopicsList/TopicsList.js:36
msgid "New topic"
msgstr "Nova tema"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:55
msgid "New value"
msgstr "Nova vrijednost"

#: src/pages/newsroom/index.js:24
#: src/pages/newsroom/index.js:33
#: src/pages/newsroom/create.js:17
#: src/pages/newsroom/[blogId]/settings.js:15
#: src/pages/newsroom/[blogId]/index.js:16
#: src/pages/newsroom/[blogId]/post/[postId].js:10
#: src/components/newsroom/forms/FormNewsroomSettings/LanguageSection.tsx:61
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:112
#: src/components/layout/Sidebar/SidebarNavigation.tsx:147
#: src/components/layout/AuthWrapper/constants/features.slides.js:306
#: src/components/emailing/components/FunnelStats/FunnelStats.tsx:80
#: src/app/components/monitoring-navigation.tsx:279
msgid "Newsroom"
msgstr "Novinska soba"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:85
msgid "Newsroom Articles"
msgstr "Članci u redakciji"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:254
msgid "Newsroom is a blogging platform that allows you to easily share your external and internal communication (e.g. press releases, announcements, etc.). You can read more about the Newsroom <0>on our website</0>."
msgstr "Newsroom je blog platforma koja vam omogućuje jednostavno dijeljenje vaše vanjske i unutarnje komunikacije (npr. priopćenja za tisak, obavijesti, itd.). Više o Newsroomu možete pročitati <0>na našoj web stranici</0>."

#: src/components/staff/admin/workspace/Workspace.js:481
msgid "Newsroom limit"
msgstr "Limit za broj Newsrooma"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:305
msgid "Newsroom settings"
msgstr "Postavke Newsrooma"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:69
msgid "Next page"
msgstr "Sljedeća strana"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:40
msgid "No access to monitoring feeds, archive search, analytics, topic or report settings, crisis communications, medialist, or user settings."
msgstr "Nema pristupa feedovima za nadzor, pretraživanju arhive, analitici, postavkama tema ili izvješća, kriznoj komunikaciji, medijskoj listi ili korisničkim postavkama."

#: src/components/emailing/content/mediaCoverage/EmptyFeedMessage.tsx:8
msgid "No articles yet"
msgstr "Još nema članaka"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:81
msgid "No campaigns yet"
msgstr "Još nemate kampanje"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:189
msgid "No categories yet."
msgstr "Još nemate kategorije."

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:109
msgid "No companies found"
msgstr "Nema pronađenih tvrtki"

#: src/components/staff/admin/user/WorkspacesTable.js:164
#: src/components/staff/admin/customer/workspaces/Workspaces.js:53
#: src/components/staff/admin/customer/users/UsersTable.js:148
#: src/components/staff/admin/customer/users/Users.js:53
#: src/components/staff/admin/customer/invoices/Invoices.js:49
#: src/components/staff/admin/customer/expenses/Expenses.js:46
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomers.js:49
msgid "No data"
msgstr "Nema podataka"

#: src/helpers/charts/highcharts.js:20
msgid "No data to display"
msgstr "Nema podataka za prikaz"

#: src/components/trash/Content.js:43
msgid "No Deleted Articles"
msgstr "Nema izbrisanih članaka"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:47
msgid "No detailed data to track."
msgstr "Nema detaljnih podataka za praćenje."

#: src/components/reports/Content/ReportsList/ReportsHeading/ReportsHeading.js:9
msgid "No email reports created"
msgstr "Niste stvorili nijedno izvješće putem e-pošte"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:145
msgid "No emails found"
msgstr "Nisu pronađene e-pošte"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:127
msgid "No emails yet"
msgstr "Još nemate e-pošte"

#: src/components/newsroom/content/posts/NewsroomPosts.js:291
msgid "No posts yet"
msgstr "Još nemate objava."

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:66
msgid "No recipients found"
msgstr "Nisu pronađeni primatelji"

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:50
msgid "No recipients yet"
msgstr "Još nema primatelja"

#: src/components/reports/Content/ReportsList/ReportsList.js:85
msgid "No reports are assigned to the topic."
msgstr "Nema dodijeljenih izvještaja za temu."

#: src/store/models/Megalist/MegalistFilter.js:42
#: src/helpers/withTranslatePopup/TranslatePopupContent.js:89
#: src/helpers/withMenuPopup/MntrMenuPopupContent.js:58
#: src/components/widgets/modules/tvr/WidgetTvr.js:78
#: src/components/widgets/modules/medialist/WidgetMedialist.js:127
#: src/components/widgets/modules/feed/WidgetFeedSimple.js:75
#: src/components/staff/admin/customers/Customers.js:37
#: src/components/misc/MntrMultiSelect/MultiSelect.js:22
#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:16
#: src/components/medialist/content/MedialistHeading.js:15
#: src/components/medialist/content/MedialistInspector/Feed/Feed.js:25
#: src/components/medialist/content/FeedMedialist/FeedMedialistEmpty/FeedMedialistEmpty.js:8
#: src/components/layout/Sidebar/modules/SidebarTopics/SidebarTopicsFolders.js:118
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:371
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitorsReports.js:64
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitors.js:86
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:280
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:34
#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTitle.js:99
#: src/components/forms/dashboard/Search/SearchEmailingEmailMessages.js:32
#: src/components/forms/dashboard/Search/SearchEmailingCampaigns.js:31
#: src/components/emailing/content/CampaignAutocompleteList.tsx:23
msgid "No results found"
msgstr "Nema pronađenih rezultata"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:177
msgid "No senders"
msgstr "Nema pošiljatelja"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:129
msgid "No subject"
msgstr "Bez predmeta"

#: src/components/emailing/helpers/displayEmailingTitle.js:18
#: src/components/emailing/helpers/displayEmailingTitle.js:21
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:149
#: src/components/emailing/components/RecipientsFeed/EmailMessagesList.js:13
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:123
msgid "No title"
msgstr "Bez naslova"

#: src/components/topics/Content/TopicsHeading/TopicsHeading.js:9
msgid "No topics created"
msgstr "Niste stvorili nijednu temu"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:209
msgid "No users"
msgstr "Nema korisnika"

#: src/components/staff/admin/workspace/Workspace.js:862
msgid "No users assigned to this workspace"
msgstr "Nema korisnika dodijeljenih ovom radnom prostoru"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:187
msgid "No users found"
msgstr "Nema pronađenih korisnika"

#: src/components/newsroom/content/dashboard/ChartVisits.js:59
msgid "No visits yet"
msgstr "Još niste imali posjeta."

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:14
msgid "No workspace"
msgstr "Nema radnog prostora"

#: src/components/staff/admin/user/User.js:311
msgid "No workspaces assigned to this user"
msgstr "Nijedan radni prostor nije dodijeljen ovom korisniku"

#. js-lingui-explicit-id
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:542
msgid "filetypes.none"
msgstr "nijedan"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:90
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:43
#: src/components/misc/ActionsBar/Selector/Selector.js:58
msgid "None"
msgstr "Ništa"

#. js-lingui-explicit-id
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:43
msgid "attachment.none"
msgstr "Nijedan"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:152
msgid "NOT"
msgstr "NE"

#: src/components/reports/history/RecipientsTableRow.js:49
#: src/components/reports/history/HistoryTable.js:82
#: src/components/reports/history/HistoryTable.js:111
#: src/components/reports/history/HistoryTable.js:325
msgid "Not delivered"
msgstr "Nije dostavljeno"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:151
msgid "Not verified"
msgstr "Nije provjereno"

#: src/store/models/dashboards/DashboardPreview.js:156
#: src/components/staff/admin/workspace/Workspace.js:339
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:36
#: src/components/medialist/forms/FormEditAuthor.js:703
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:63
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:63
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:35
msgid "Note"
msgstr "Bilješka"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:128
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:368
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:83
msgid "Notes"
msgstr "Bilješke"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:30
msgid "Notification about mention <0>within 3 minutes</0>"
msgstr "Obavijest o spomenu <0>unutar 3 minute</0>"

#: src/components/layout/Header/AppNotifications/AppNotifications.js:99
#: src/components/layout/Header/AppNotifications/AppNotifications.js:107
msgid "Notification Settings"
msgstr "Postavke obavijesti"

#: src/components/notifications/ContentTvr.js:39
#: src/components/notifications/ContentTopics.js:24
#: src/components/notifications/AppNotifications/AppNotifications.js:21
#: src/components/layout/Header/AppNotifications/AppNotifications.js:152
msgid "Notifications"
msgstr "Obavijesti"

#: src/constants/analytics.js:55
#: src/constants/analytics.js:566
#: src/constants/analytics.js:678
msgid "Number of articles"
msgstr "Broj članaka"

#: src/constants/analytics/primeScoreCharts.ts:57
msgid "Number of articles by PRIMe relevant vs irrelevant"
msgstr "Broj članaka prema PRIMe relevantni vs. irelevantni"

#: src/constants/analytics.js:53
msgid "Number of articles by sentiment"
msgstr "Broj članaka prema sentimentu"

#: src/components/monitoring/Inspector/InspectorMonitora/SocialParentText/SocialParentHeader.js:94
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:41
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:132
#: src/components/misc/ActionsBar/View/ViewMenu.js:289
msgid "Number of followers"
msgstr "Broj pratitelja"

#: src/store/models/OurChart.js:188
msgid "Number of mentions"
msgstr "Broj spominjanja"

#: src/components/tvr/Content/Content.js:61
msgid "Number of outputs"
msgstr "Broj izlaza"

#: src/components/reports/Content/ReportsList/ReportsTopMentionsMode.js:21
msgid "Number of TOP stories"
msgstr "Broj TOP priča"

#: src/components/misc/Changelog/ChangelogTable.js:33
msgid "Object"
msgstr "Objekt"

#: src/components/monitoring/WorkspaceArticles/Limits.js:57
msgid "OCR"
msgstr "OCR"

#: src/components/monitoring/FeedList/FeedListItem/FeedListOlderDivider/FeedListOlderDivider.js:24
msgid "Older articles"
msgstr "Stariji članci"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:142
msgid "on frontpage"
msgstr "na naslovnici"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:91
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:125
msgid "on this continent"
msgstr "na ovom kontinentu"

#: src/components/misc/ActionsBar/View/ViewMenu.js:62
#: src/components/layout/AuthWrapper/constants/features.slides.js:47
msgid "Online"
msgstr "Online"

#: src/constants/analytics.js:1287
msgid "Online categories"
msgstr "Kategorije - Online"

#: src/store/models/Megalist/MegalistFilter.js:34
msgid "Only Selected"
msgstr "Samo odabrano"

#: src/store/models/Megalist/MegalistFilter.js:38
msgid "Only Unselected"
msgstr "Samo neodabrano"

#: src/components/medialist/forms/FormEditAuthor.js:892
msgid "Only you can see all the data you entered and the changes made."
msgstr "Sve unesene podatke i izvršene promjene možete vidjeti samo vi i vaši kolege."

#: src/components/newsroom/content/posts/NewsroomPosts.js:185
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:159
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderVideo/HeaderVideo.js:52
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderSocial/HeaderSocial.js:34
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:105
#: src/components/monitoring/Inspector/InspectorEntityKnowledgeBase/InspectorKnowledgeBaseHeader.js:12
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:51
#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthor.js:55
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:20
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:54
msgid "Open"
msgstr "Otvori"

#: src/components/staff/admin/customers/Customer.js:187
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:102
msgid "Open customer detail"
msgstr "Otvori detalje kupca"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:102
msgid "Open In Feed"
msgstr "Otvori u feedu"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/EmbedFacebook/EmbedFacebook.tsx:49
msgid "Open on Facebook"
msgstr "Otvorite na Facebooku"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:209
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:225
msgid "Open rate"
msgstr "Stopa otvaranja"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:199
#: src/components/staff/admin/customer/users/UsersTable.js:138
msgid "Open user detail"
msgstr "Otvori detalje korisnika"

#: src/components/staff/admin/user/WorkspacesTable.js:154
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:146
msgid "Open workspace detail"
msgstr "Otvori detalje radnog prostora"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:281
msgid "Opportunity to see"
msgstr "Prilika za vidjeti"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:190
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:320
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:360
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:371
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:387
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:421
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:441
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:510
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:230
#: src/components/emailing/forms/FormSenderSettings.js:89
#: src/components/emailing/forms/FormSenderSettings.js:105
msgid "optional"
msgstr "neobavezno"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:146
msgid "OR"
msgstr "ILI"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:70
msgid "Or use an external service"
msgstr "Ili upotrijebite vanjsku uslugu"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:91
msgid "Order articles"
msgstr "Poredaj članke"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:265
msgid "Ordered list"
msgstr "Numerirani popis"

#: src/helpers/withTranslatePopup/TranslatePopupContent.js:70
msgid "Original"
msgstr "Original"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:45
msgid "Original value"
msgstr "Izvorna vrijednost"

#: src/constants/analytics.js:1125
#: src/constants/analytics.js:1205
#: src/constants/analytics.js:1227
#: src/constants/analytics.js:1247
#: src/constants/analytics.js:1267
#: src/constants/analytics.js:1286
#: src/constants/analytics.js:1305
#: src/constants/analytics.js:1324
#: src/constants/analytics.js:1337
#: src/constants/analytics/primeScoreCharts.ts:135
#: src/components/notifications/ContentTvr.js:118
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:57
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:57
#: src/app/components/monitoring-navigation.tsx:252
msgid "Other"
msgstr "Ostalo"

#: src/store/models/Megalist/Megalist.js:48
msgid "Other Regions"
msgstr "Ostale regije"

#: src/components/staff/admin/workspace/Workspace.js:671
msgid "Other settings"
msgstr "Druge postavke"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:155
msgid "Others"
msgstr "Ostali"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:278
#: src/components/misc/ActionsBar/View/ViewMenu.js:219
msgid "OTS"
msgstr "OTS"

#: src/pages/_error.js:55
msgid "Our team has been notified. We're sorry for the inconvenience."
msgstr "Naš tim je obaviješten. Ispričavamo se za neugodnosti."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:57
msgid "Outline the main content or details you want included."
msgstr "Opišite glavni sadržaj ili pojedinosti koje želite uključiti."

#: src/components/medialist/constants/medialist.tabNavigation.js:20
msgid "Overview"
msgstr "Pregled"

#: src/components/staff/admin/workspace/Workspace.js:603
msgid "Own content"
msgstr "Vlastiti sadržaj"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:86
msgid "Own selection"
msgstr "Vlastiti izbor"

#. js-lingui-explicit-id
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:92
msgid "page.shortened"
msgstr "str."

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:64
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:76
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:72
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:220
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupPagesContent.js:27
#: src/components/misc/Pagination/Pagination.js:26
#: src/components/misc/Pagination/Pagination.js:39
#: src/components/misc/Pagination/Pagination.js:45
#: src/components/misc/Pagination/Pagination.js:76
#: src/components/misc/Pagination/Pagination.js:84
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:118
msgid "Page"
msgstr "Stranica"

#: src/components/tvr/Inspector/InspectorMedia/PaginationMedia/PaginationMedia.js:30
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPagination.js:48
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:258
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupPagesContent.js:18
#: src/components/layout/MntrActiveFilters/modules/PageNumbers.js:22
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:123
msgid "Pages"
msgstr "Stranice"

#. placeholder {0}: data.publication.pages.length
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:238
msgid "Pages ({0} total)"
msgstr "Stranice ({0} ukupno)"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:60
msgid "Paid"
msgstr "Plaćeno"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:160
msgid "Paragraph"
msgstr "Paragraf"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:423
msgid "Parse PDF"
msgstr "Parsiraj PDF"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:140
msgid "Partner Code (optional)"
msgstr "Partnerski kod (neobavezno)"

#: src/pages/user/reset-password/new.tsx:32
#: src/components/page/auth/SignUp/SignUp.js:42
#: src/components/page/auth/Login/Login.tsx:46
#: src/components/emailing/forms/FormSenderSettings.js:96
msgid "Password"
msgstr "Lozinka"

#: src/pages/user/reset-password/new.tsx:39
msgid "Password again"
msgstr "Lozinka ponovno"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:119
msgid "Password change"
msgstr "Promjena lozinke"

#: src/components/staff/admin/workspace/UsersTable/CopyPassword.js:34
msgid "Password copied to the clipboard."
msgstr "Lozinka je kopirana u međuspremnik."

#: src/components/misc/VideoPlayer/CropControls.js:127
#: src/components/misc/VideoPlayer/Controls.js:98
msgid "Pause"
msgstr "Pauza"

#: src/components/reports/history/RecipientsTableRow.js:58
msgid "Pending"
msgstr "Na čekanju"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:30
msgid "people"
msgstr "ljudi"

#: src/helpers/formatNumber.js:29
msgid "per month"
msgstr "po mjesecu"

#: src/components/OurChart/OurChartAdvanced.js:155
msgid "Percent Share"
msgstr "Postotni udio"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:137
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:562
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:564
#: src/components/misc/ActionsBar/View/ViewMenu.js:326
msgid "Perex"
msgstr "Sažetak"

#: src/components/exportList/History/HistoryTable/HistoryTable.js:60
msgid "Period"
msgstr "Za razdoblje"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:39
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:44
msgid "Periodicity"
msgstr "Periodičnost"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:60
msgid "Permissions"
msgstr "Dopuštenja"

#: src/components/medialist/forms/FormEditAuthor.js:841
#: src/components/medialist/forms/FormEditAuthor.js:1002
#: src/components/medialist/forms/FormEditAuthor.js:1007
msgid "Personal Website"
msgstr "Osobna web stranica"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:66
msgid "Personalization:"
msgstr "Personalizacija:"

#: src/components/page/auth/SignUp/SignUp.js:49
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:132
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:80
#: src/components/medialist/forms/FormEditAuthor.js:766
#: src/components/medialist/forms/FormEditAuthor.js:900
#: src/components/medialist/forms/FormEditAuthor.js:906
msgid "Phone"
msgstr "Telefon"

#: src/constants/analytics.js:677
#: src/constants/analytics.js:697
#: src/constants/analytics.js:716
#: src/constants/analytics.js:735
#: src/constants/analytics.js:754
#: src/constants/analytics.js:773
#: src/constants/analytics.js:792
#: src/constants/analytics.js:811
#: src/constants/analytics.js:826
#: src/constants/analytics.js:844
#: src/constants/analytics.js:863
#: src/constants/analytics.js:882
#: src/constants/analytics.js:902
#: src/constants/analytics.js:922
#: src/constants/analytics.js:943
#: src/constants/analytics.js:963
#: src/constants/analytics.js:978
#: src/constants/analytics.js:992
#: src/constants/analytics.js:1008
#: src/constants/analytics.js:1023
#: src/constants/analytics.js:1038
#: src/constants/analytics/primeScoreCharts.ts:94
msgid "Pie"
msgstr "Pita"

#: src/helpers/formatNumber.js:39
msgid "pieces"
msgstr "komada"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:71
msgid "Plain"
msgstr "Jednostavan"

#: src/components/staff/admin/workspace/Workspace.js:309
msgid "Plan"
msgstr "Plan"

#: src/components/emailing/content/promo/PromoEmailing.js:18
msgid "Platform for email communication with journalists."
msgstr "Platforma za e-mail komunikaciju s novinarima."

#: src/components/misc/VideoPlayer/CropControls.js:127
#: src/components/misc/VideoPlayer/Controls.js:98
msgid "Play"
msgstr "Reproduciraj"

#: src/components/emailing/content/sender/EmailingSenderContent.js:34
msgid "Please add a sender address that will be used for sending emails."
msgstr "Molimo dodajte adresu pošiljatelja koja će se koristiti za slanje e-pošte."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:545
msgid "Please copy and insert this code into your website. Modify the width and height values of the iframe according to your requirements. Additionally, it’s possible to hide the header and footer if necessary."
msgstr "Kopirajte i umetnite ovaj kod u izvorni kod vaše web stranice. Širinu i visinu iframe-a možete prilagoditi prema potrebi. Također, moguće je sakriti zaglavlje i podnožje ako je potrebno."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:95
msgid "Please remove some recipients."
msgstr "Molimo uklonite neke primatelje."

#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:168
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:81
msgid "Please select Image"
msgstr "Molim, odaberite sliku"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepTitleAndCommunicationPlan.tsx:60
msgid "Please select the title and review the communication plan. If it does not meet your expectations, restart the process."
msgstr "Molimo odaberite naslov i pregledajte komunikacijski plan. Ako ne ispunjava vaša očekivanja, ponovno pokrenite proces."

#: src/components/emailing/forms/FormSenderSettings.js:105
msgid "Port"
msgstr "Port"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:133
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:100
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:68
msgid "Position"
msgstr "Pozicija"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostPreview.tsx:80
msgid "Post preview"
msgstr "Pregled članka"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:351
msgid "Post settings"
msgstr "Postavke objave"

#: src/constants/analytics.js:1132
#: src/constants/analytics.js:1157
#: src/constants/analytics.js:1212
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:37
msgid "Posts"
msgstr "Objave"

#: src/components/topics/Content/TopicsList/KeywordExtraQueryAdvanced/KeywordExtraQueryAdvanced.tsx:26
msgid "Predefined queries"
msgstr "Predefinirani upiti"

#: src/components/misc/Capture/Capture.js:283
msgid "Preparing export..."
msgstr "Pripremamo izvoz..."

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:86
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:28
#: src/components/reports/history/HistoryTable.js:406
#: src/components/reports/Content/ReportsList/ReportPreview.js:18
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:20
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:115
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:140
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:140
#: src/components/forms/dashboard/ExportResend/ExportResend.js:163
#: src/components/emailing/content/CreateEmailContent.js:278
msgid "Preview"
msgstr "Pregled"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:142
msgid "Preview & Publish"
msgstr "Pregled i objava"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:110
msgid "Preview images"
msgstr "Pregled slika"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:66
msgid "Previous page"
msgstr "Prethodna stranica"

#: src/components/staff/SignUp.js:17
#: src/components/staff/admin/workspace/Workspace.js:296
#: src/components/staff/admin/DailyAccess/Table.js:30
msgid "Primary app"
msgstr "Primarna aplikacija"

#: src/constants/analytics/primeScoreCharts.ts:122
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:420
#: src/components/layout/MntrActiveFilters/modules/PrimeFilter.tsx:25
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:46
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:33
#: src/components/analytics/AnalyticsContent.js:152
#: src/components/analytics/AnalyticsContent.js:179
msgid "PRIMe"
msgstr "PRIMe"

#: src/constants/analytics/primeScoreCharts.ts:116
msgid "PRIMe in mediatype"
msgstr "PRIMe u medijatipu"

#: src/components/widgets/modules/stats/WidgetStats.js:190
msgid "PRIMe negative total value"
msgstr "PRIMe negativna ukupna vrijednost"

#: src/components/widgets/modules/stats/WidgetStats.js:183
msgid "PRIMe positive total value"
msgstr "PRIMe pozitivna ukupna vrijednost"

#: src/constants/analytics/primeScoreCharts.ts:76
msgid "PRIMe scale"
msgstr "PRIMe ljestvica"

#: src/constants/analytics/primeScoreCharts.ts:9
#: src/constants/analytics/primeScoreCharts.ts:37
#: src/constants/analytics/primeScoreCharts.ts:69
#: src/constants/analytics/primeScoreCharts.ts:82
#: src/constants/analytics/primeScoreCharts.ts:102
msgid "PRIMe score"
msgstr "PRIMe rezultat"

#: src/components/widgets/modules/stats/WidgetStats.js:176
msgid "PRIMe total average"
msgstr "PRIMe ukupni prosjek"

#: src/components/widgets/modules/stats/WidgetStats.js:169
msgid "PRIMe total value"
msgstr "PRIMe ukupna vrijednost"

#: src/components/misc/ActionsBar/View/ViewMenu.js:112
#: src/components/layout/AuthWrapper/constants/features.slides.js:57
#: src/components/OurChart/OurChartAdvanced.js:260
msgid "Print"
msgstr "Tisak"

#: src/constants/analytics.js:1306
msgid "Print categories"
msgstr "Kategorije - Ispis"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:89
msgid "Professional"
msgstr "Profesionalni"

#: src/constants/analytics.js:1154
msgid "Profile"
msgstr "Profil"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:113
#: src/components/misc/MntrEditor/modals/withModalPromoBox.js:8
#: src/components/misc/MntrEditor/extensions/ExtensionPromoBox.js:33
msgid "Promo Box"
msgstr "Promo kutija"

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:66
msgid "Provide additional feedback..."
msgstr "Dajte dodatne povratne informacije..."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:33
msgid "Provide information such as:"
msgstr "Navedite informacije kao što su:"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:74
msgid "Providing information about your company allows the AI assistant to generate more accurate and tailored content for your newsroom articles. This ensures the text aligns closely with your brand's identity and messaging"
msgstr "Pružanje informacija o vašoj tvrtki omogućuje pomoćniku umjetne inteligencije da generira točniji i prilagođeniji sadržaj za članke vaše redakcije. To osigurava blisku usklađenost teksta s identitetom i porukama vaše marke"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:291
msgid "Publication Date"
msgstr "Datum objave"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:95
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:142
msgid "Publish"
msgstr "Objavi"

#: src/components/newsroom/forms/FormNewsroomPost/PostStatus.js:49
msgid "Publish date set to"
msgstr "Datum objave postavljen na"

#: src/components/newsroom/forms/FormNewsroomPost/PostStatus.js:73
msgid "Publish date set to {scheduledFormatted}"
msgstr "Datum objave postavljen na {scheduledFormatted}"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:154
msgid "Publish now"
msgstr "Objavi odmah"

#: src/pages/newsroom/index.js:45
msgid "Publish press releases <0>easily and quickly</0>"
msgstr "Objavite priopćenja za tisak <0>jednostavno i brzo</0>"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:155
msgid "Publish this post immediately"
msgstr "Odmah objavi ovu objavu"

#: src/components/newsroom/components/PostsList/PostsList.js:184
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:30
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:66
msgid "Published"
msgstr "Objavljeno"

#: src/components/staff/admin/user/getUserAttributes.js:14
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:35
#: src/components/layout/Sidebar/SidebarNavigation.tsx:195
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:261
#: src/components/layout/MntrActiveFilters/modules/Publisher.js:13
msgid "Publisher"
msgstr "Izdavač"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:30
msgid "Publisher copyright fees"
msgstr "Autorske naknade za izdavače"

#: src/constants/analytics.js:979
#: src/constants/analytics.js:993
#: src/constants/analytics.js:1009
#: src/constants/analytics.js:1024
#: src/constants/analytics.js:1039
msgid "Publishers"
msgstr "Izdavači"

#: src/components/layout/Header/AppNotifications/AppNotifications.js:201
msgid "Push Notifications"
msgstr "Push obavijesti"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:32
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:66
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:106
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:134
msgid "Quick Overview"
msgstr "Brzi pregled"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:30
msgid "Quickly protect your brand reputation and stakeholder trust."
msgstr "Brzo zaštitite reputaciju svoje marke i povjerenje dionika."

#: src/components/newsroom/content/modules/CustomQuotes.tsx:64
msgid "Quote"
msgstr "Citat"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:74
#: src/components/newsroom/content/modules/CustomQuotes.tsx:33
msgid "Quotes"
msgstr "Citati"

#: src/components/notifications/ContentTvrRequest.js:74
#: src/components/notifications/ContentTvr.js:81
#: src/components/misc/ActionsBar/View/ViewMenu.js:162
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:62
#: src/components/layout/AuthWrapper/constants/features.slides.js:75
msgid "Radio"
msgstr "Radio"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:92
msgid "Rank is primarily based on the reach and the importance of the news source."
msgstr "Rang se prvenstveno temelji na dosegu i važnosti izvora vijesti."

#: src/components/newsroom/components/AiTools/AiCheckPostResult.tsx:92
msgid "Re-run check"
msgstr "Ponovo pokrenite AI provjeru"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:30
msgid "reach"
msgstr "dosegnuti"

#: src/constants/stats.ts:26
#: src/constants/analytics.js:107
#: src/constants/analytics.js:121
#: src/constants/analytics.js:123
#: src/constants/analytics.js:129
#: src/constants/analytics.js:590
#: src/constants/analytics.js:603
#: src/constants/analytics.js:736
#: src/constants/analytics.js:1025
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:421
#: src/components/misc/ActionsBar/View/ViewMenu.js:227
#: src/components/analytics/TraditionalMedia.js:34
#: src/components/analytics/TraditionalMedia.js:40
msgid "Reach"
msgstr "Doseg"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:98
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:112
msgid "Reactivate"
msgstr "Ponovno aktiviraj"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:101
msgid "Reactivate recipient"
msgstr "Ponovno aktiviraj primatelja"

#: src/components/reports/history/RecipientsTableRow.js:31
msgid "Read"
msgstr "Pročitano"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:34
msgid "Read only:"
msgstr "Samo za čitanje:"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:189
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:63
#: src/components/misc/ActionsBar/View/ViewMenu.js:132
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:95
msgid "Readership"
msgstr "Čitateljstvo"

#: src/helpers/modal/withModalReportProblem.tsx:32
#: src/helpers/modal/withModalReportArticle.tsx:46
#: src/components/reports/history/RecipientsTableHeader.js:38
msgid "Reason"
msgstr "Razlog"

#: src/components/medialist/content/MedialistDashboard.js:179
msgid "Recently edited authors"
msgstr "Nedavno uređeni autori"

#: src/components/medialist/content/MedialistDashboard.js:158
msgid "Recently viewed authors"
msgstr "Nedavno pregledani autori"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:70
msgid "Recipient"
msgstr "Primatelj"

#: src/store/models/reports/recipients/Recipients.js:27
msgid "Recipient added."
msgstr "Primatelj je dodan."

#: src/components/forms/dashboard/ExportResend/ExportResend.js:79
msgid "Recipient emails"
msgstr "E-mailovi primatelja"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:83
msgid "Recipient has no email address"
msgstr "Primatelj nema adresu e-pošte"

#: src/store/models/reports/recipients/Recipients.js:56
msgid "Recipient removed."
msgstr "Primatelj je uklonjen."

#: src/store/models/reports/recipients/Recipients.js:44
msgid "Recipient updated."
msgstr "Primatelj je ažuriran."

#: src/helpers/modal/withModalTvrTopics.tsx:77
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:246
#: src/components/reports/history/HistoryTable.js:169
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:59
#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:55
#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:63
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:101
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:112
#: src/components/emailing/content/CreateEmailContent.js:269
#: src/components/emailing/content/tabs/RecipientsTab.tsx:23
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:163
msgid "Recipients"
msgstr "Primatelji"

#: src/store/models/reports/recipients/Recipients.js:27
msgid "Recipients added."
msgstr "Primatelji su dodani."

#: src/components/reports/history/HistoryTable.js:52
msgid "Recipients from: {formattedCreated}"
msgstr "Primatelji iz: {formattedCreated}"

#: src/components/reports/history/HistoryTable.js:432
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:50
msgid "Recipients have been copied to the clipboard."
msgstr "Primatelji su kopirani u međuspremnik."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:45
msgid "Recipients limit"
msgstr "Limit primatelja"

#: src/store/models/reports/recipients/Recipients.js:68
msgid "Recipients removed."
msgstr "Primatelji su uklonjeni."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:25
msgid "Recipients with missing information"
msgstr "Primatelji s nedostajućim informacijama"

#: src/components/forms/dashboard/Export/RecommendedLimit.js:32
msgid "Recomended limit"
msgstr "Preporučeni limit"

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:59
msgid "Recommended file types: XLSX, CSV"
msgstr "Preporučeni tipovi datoteka: XLSX, CSV"

#: src/components/forms/adapters/MntrFileAdapter/MntrFileAdapter.js:70
msgid "Recommended resolution"
msgstr "Preporučena rezolucija"

#: src/components/staff/admin/workspace/Workspace.js:136
msgid "Recreate articles"
msgstr "Ponovno stvorite članke"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:566
msgid "Redo"
msgstr "Ponovi"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:31
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:110
msgid "Regenerate content"
msgstr "Ponovno generiraj sadržaj"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:31
msgid "Regenerate until the email text aligns perfectly with your requirements"
msgstr "Regenerirajte dok tekst e-pošte savršeno ne odgovara vašim zahtjevima."

#: src/components/staff/admin/customers/Customers.js:27
msgid "Register new user"
msgstr "Registriraj novog korisnika"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:55
msgid "Relevant"
msgstr "Relevantno"

#: src/helpers/modal/withModalRemove.tsx:37
#: src/helpers/modal/withModalRemove.tsx:51
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:142
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:47
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:85
#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:7
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:676
#: src/components/newsroom/content/modules/CustomQuotes.tsx:58
#: src/components/newsroom/content/modules/CustomKeypoints.tsx:49
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:570
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:74
#: src/components/emailing/modules/withModalRemoveRecipients.tsx:24
#: src/components/emailing/modules/withModalRemoveRecipients.tsx:39
#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:88
#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:68
msgid "Remove"
msgstr "Ukloni"

#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:105
#: src/components/exportList/Content/Content.tsx:95
#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:56
msgid "Remove All"
msgstr "Ukloni sve"

#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:34
msgid "Remove all from report"
msgstr "Ukloni sve iz izvješća"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:97
msgid "Remove all from selection"
msgstr "Ukloni sve iz odabira"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:155
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:126
msgid "Remove authors from list"
msgstr "Ukloni autore s popisa"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:50
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:88
msgid "Remove Campaign"
msgstr "Ukloni kampanju"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:57
msgid "Remove from article"
msgstr "Ukloni iz članka"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:338
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:285
msgid "Remove from Export"
msgstr "Ukloni iz izvoza"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:40
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:99
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:173
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:257
msgid "Remove from filters"
msgstr "Ukloni iz filtera"

#: src/components/medialist/content/withRemoveFromBasketPopup.js:34
msgid "Remove from list"
msgstr "Ukloni s popisa"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:273
msgid "Remove from next report"
msgstr "Ukloni iz sljedećeg izvješća"

#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:282
msgid "Remove from report"
msgstr "Ukloni iz izvješća"

#: src/components/staff/admin/user/WorkspacesTable.js:138
msgid "Remove from workspace"
msgstr "Ukloni iz radnog prostora"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:200
#: src/components/settings/SettingsLogo/SettingsLogo.js:145
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:335
msgid "Remove Image"
msgstr "Ukloni sliku"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:359
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:457
msgid "Remove Link"
msgstr "Ukloni poveznicu"

#: src/components/medialist/forms/modules/FormFieldUploadPhoto.js:53
msgid "Remove Photo"
msgstr "Ukloni fotografiju"

#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:69
msgid "Remove Recipients"
msgstr "Ukloni primatelje"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:73
msgid "Remove report"
msgstr "Ukloni izvještaj"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:99
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:274
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:374
#: src/components/monitoring/FeedActionsBar/withRemoveTagPopup/RemoveTagPopupContent.js:11
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:77
msgid "Remove tag"
msgstr "Ukloni oznaku"

#: src/components/staff/admin/workspace/UsersTable/RemoveUser.js:25
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:398
msgid "Remove user"
msgstr "Ukloni korisnika"

#: src/components/staff/admin/workspace/UsersTable/RemoveUsers.tsx:16
msgid "Remove users"
msgstr "Ukloni korisnike"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:132
msgid "Remove widget"
msgstr "Ukloni widget"

#: src/store/models/monitoring/Inspector/Inspector.ts:428
msgid "Removed from next report."
msgstr "Uklonjeno iz sljedećeg izvješća."

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:257
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:261
msgid "Rename"
msgstr "Preimenuj"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:94
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:36
#: src/components/reports/Content/ReportsList/ReportPreview.js:26
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:28
msgid "Report preview"
msgstr "Pregled izvješća"

#: src/helpers/modal/withModalReportProblem.tsx:45
#: src/helpers/modal/withModalReportArticle.tsx:70
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:104
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:26
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:289
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:295
#: src/components/medialist/forms/FormEditAuthor.js:381
#: src/components/medialist/forms/FormEditAuthor.js:387
#: src/components/medialist/forms/FormEditAuthor.js:528
#: src/components/medialist/forms/FormEditAuthor.js:534
msgid "Report problem"
msgstr "Prijavi problem"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:76
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:51
msgid "Report will be removed."
msgstr "Izvještaj će biti uklonjen."

#: src/pages/reports/index.js:15
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:118
#: src/components/reports/ReportChangelog.js:18
#: src/components/reports/history/Content.js:31
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:45
#: src/app/components/monitoring-navigation.tsx:165
msgid "Reports"
msgstr "Izvještaji"

#: src/components/layout/AuthWrapper/constants/features.slides.js:353
msgid "Reports and exports"
msgstr "Izvještaji i izvozi"

#: src/pages/reports/history.js:12
#: src/components/reports/history/Content.js:35
#: src/components/reports/Content/ReportsList/ReportsList.js:37
#: src/app/components/monitoring-navigation.tsx:179
msgid "Reports History"
msgstr "Povijest izvještaja"

#. js-lingui-explicit-id
#: src/helpers/modal/withModalRequestFeature.tsx:24
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:57
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:244
msgid "featureRequest.Request"
msgstr "Isprobaj"

#: src/helpers/modal/withModalRequestFeature.tsx:50
#: src/components/misc/MntrButton/modules/ButtonRequestFeature.js:36
msgid "Request Access?"
msgstr "Zatražiti pristup?"

#: src/helpers/modal/withModalTvrTopics.tsx:41
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:509
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:510
msgid "Request change"
msgstr "Zatraži promjenu"

#: src/components/notifications/ContentTvrRequest.js:32
#: src/components/notifications/ContentTvr.js:120
msgid "Request Channels"
msgstr "Zahtjev za kanale"

#: src/components/analytics/AnalyticsContent.js:250
msgid "Request social media?"
msgstr "Zatražiti pokretanje društvenih medija?"

#: src/components/analytics/AnalyticsContent.js:217
msgid "Request traditional media?"
msgstr "Zatražiti pokretanje tradicionalnih medija?"

#: src/helpers/store/apiClient.js:153
msgid "Request was cancelled."
msgstr "Zahtjev je otkazan."

#. js-lingui-explicit-id
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:104
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:78
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:49
#: src/components/misc/PromoBox/PromoBox.js:142
#: src/components/misc/MntrButton/modules/ButtonRequestFeature.js:25
#: src/components/analytics/AnalyticsContent.js:199
#: src/components/analytics/AnalyticsContent.js:232
msgid "featureRequest.Requested"
msgstr "Zahtjevano"

#: src/components/staff/admin/DailyAccess/Table.js:33
msgid "Requests"
msgstr "Zahtjevi"

#: src/components/reports/history/HistoryTable.js:65
#: src/components/reports/history/HistoryTable.js:446
msgid "Resend"
msgstr "Ponovno pošalji"

#: src/components/reports/history/Compose.js:42
msgid "Resend email report"
msgstr "Ponovno pošalji izvještaj e-pošte"

#: src/components/reports/history/Compose.js:44
msgid "Resend email report from: {formattedCreated}"
msgstr "Ponovno pošalji izvještaj e-pošte od: {formattedCreated}"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:102
msgid "Resend verification email"
msgstr "Ponovno pošalji verifikacijski email"

#: src/store/models/reports/history/History.js:92
msgid "Resending email report. Check back later."
msgstr "Ponovno šaljem izvještaj putem e-pošte. Provjerite kasnije."

#: src/components/reports/history/HistoryTable.js:215
msgid "Resent report"
msgstr "Ponovno poslano izvješće"

#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:33
#: src/components/medialist/forms/FormEditAuthor.js:591
msgid "Reset"
msgstr "Ponovno postavi"

#: src/components/medialist/forms/FormEditAuthor.js:295
#: src/components/medialist/forms/FormEditAuthor.js:459
msgid "Reset author profile"
msgstr "Resetiraj profil autora"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:153
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:72
msgid "Reset filter"
msgstr "Poništi filter"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:61
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:241
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:258
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:277
msgid "Reset filters"
msgstr "Poništi filtre"

#: src/pages/user/reset-password/index.tsx:36
msgid "Reset Password"
msgstr "Resetiraj Lozinku"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:69
msgid "Reset selection"
msgstr "Poništi odabir"

#: src/helpers/modal/withModalResetAuthor.tsx:25
#: src/helpers/modal/withModalResetAuthor.tsx:39
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:87
msgid "Restore"
msgstr "Vratiti"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:403
msgid "Restore articles"
msgstr "Obnovi članke"

#: src/components/settings/SettingsTheme/ThemePicker.tsx:122
msgid "Restore default"
msgstr "Vrati na zadano"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:131
#: src/components/misc/Changelog/ChangelogTable.js:40
msgid "Revert"
msgstr "Povrati"

#: src/components/misc/Changelog/ChangelogTableRow.js:192
msgid "Revert actions"
msgstr "Poništi radnje"

#: src/components/misc/Changelog/ChangelogTableRow.js:209
msgid "Revert now"
msgstr "Vrati odmah"

#: src/components/misc/Changelog/ChangelogTableRow.js:163
msgid "Reverted on:"
msgstr "Vraćeno na:"

#: src/components/newsroom/components/AiTools/AiGenerateCommunicationPlan.tsx:34
msgid "Review communication plan"
msgstr "Pregledaj komunikacijski plan"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:204
msgid "Roadmap"
msgstr "Plan razvoja"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:65
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:230
msgid "Role"
msgstr "Uloga"

#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:59
msgid "Row"
msgstr "Redak"

#: src/components/medialist/forms/FormEditAuthor.js:862
#: src/components/medialist/forms/FormEditAuthor.js:1038
msgid "Salutation"
msgstr "Pozdrav"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:142
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:229
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:103
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:44
#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:116
#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:215
#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:101
#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:70
#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:122
#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:47
#: src/components/staff/admin/workspace/Workspace.js:234
#: src/components/staff/admin/workspace/Workspace.js:951
#: src/components/staff/admin/user/User.js:176
#: src/components/staff/admin/user/User.js:329
#: src/components/settings/SettingsTheme/ThemePicker.tsx:139
#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:103
#: src/components/settings/SettingsLogo/SettingsLogo.js:164
#: src/components/settings/SettingsApplication/SettingsApplication.js:50
#: src/components/reports/Content/ReportsList/ReportsForm.js:342
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:36
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:150
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:224
#: src/components/newsroom/content/dashboard/NewsroomBlogSettings.js:21
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:591
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:73
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:82
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:21
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:467
#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:86
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:242
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:116
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:191
#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:35
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:170
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:21
#: src/components/misc/Capture/Capture.js:238
#: src/components/medialist/forms/FormEditAuthor.js:601
#: src/components/medialist/forms/FormEditAuthor.js:741
#: src/components/layout/Sidebar/modules/SidebarTopics/FormFolder.tsx:25
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:55
#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:91
#: src/components/forms/tags/FormEditTag/FormEditTag.js:48
#: src/components/emailing/forms/FormSenderSettings.js:193
#: src/components/emailing/content/CreateEmailContent.js:321
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:58
#: src/components/dashboards/DashboardSelector/FormEditDashboard.js:35
msgid "Save"
msgstr "Spremi"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:147
msgid "Save & Publish"
msgstr "Spremi i objavi"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:151
msgid "Save & Schedule"
msgstr "Spremi i zakaži"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:188
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:61
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:61
msgid "Save as"
msgstr "Spremi kao"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/ModalAddDashboardFooter.tsx:35
msgid "Save changes"
msgstr "Spremi promjene"

#: src/components/misc/Capture/Capture.js:245
#: src/components/OurChart/OurChartAdvanced.js:187
msgid "Save in format"
msgstr "Spremi u formatu"

#: src/helpers/modal/withModalEmailPreview.js:94
msgid "Save report"
msgstr "Spremi izvještaj"

#: src/components/topics/Content/TopicsList/FormSaveMegalist/FormSaveMegalist.js:20
msgid "Save selection"
msgstr "Spremi odabir"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:142
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:51
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:51
msgid "Save settings"
msgstr "Spremi postavke"

#: src/constants/analytics/primeScoreCharts.ts:75
msgid "Scatter"
msgstr "Scatter"

#: src/components/misc/Changelog/ChangelogTableRow.js:212
msgid "Schedule revert"
msgstr "Planiraj povratak"

#: src/components/newsroom/components/PostsList/PostsList.js:188
msgid "Scheduled"
msgstr "Zakazano"

#: src/components/misc/Changelog/ChangelogTableRow.js:172
msgid "Scheduled on:"
msgstr "Zakazano za:"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:99
msgid "Scheduled to send at {scheduledDateFormatted}, are you sure you want to delete this email?"
msgstr "Zakazano slanje na {scheduledDateFormatted}, jeste li sigurni da želite izbrisati ovaj email?"

#: src/components/emailing/content/CreateEmailContent.js:150
msgid "Scheduled to send email"
msgstr "Planirano slanje e-pošte"

#: src/components/misc/ActionsBar/View/ViewMenu.js:247
msgid "Scope of mention"
msgstr "Opseg spomena"

#: src/components/tvr/Inspector/InspectorMedia/PaginationMedia/PaginationMedia.js:46
msgid "Screens"
msgstr "Zasloni"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:33
msgid "Screenshot"
msgstr "Snimka zaslona"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:106
#: src/components/tariff/MonitoredMedia/MonitoredMedia.js:174
#: src/components/monitoring/Inspector/InspectorMonitora/HashTagsList/HashTagsList.js:55
#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:59
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Keywords.js:38
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:139
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/MedialistArticlesFilterSearchQuery.js:55
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/MedialistArticlesFilterSearchQuery.js:84
#: src/components/layout/MntrFiltersBar/forms/FormNote/FormNote.js:33
#: src/components/layout/Header/SearchSuggest/SearchSuggest.js:51
#: src/components/forms/dashboard/Search/SearchForm.js:71
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery.js:56
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery.js:85
msgid "Search"
msgstr "Pretraži"

#: src/components/forms/dashboard/Search/SearchForm.js:75
msgid "Search authors"
msgstr "Pretraži autore"

#: src/pages/authors/index.js:53
msgid "Search authors by <0>many filters</0>"
msgstr "Pretražite autore pomoću <0>mnogih filtera</0>"

#: src/components/forms/dashboard/Search/SearchForm.js:79
msgid "Search changelog"
msgstr "Pretraži dnevnik promjena"

#: src/components/forms/dashboard/Search/SearchForm.js:43
#: src/components/forms/dashboard/Search/SearchAdmin.js:39
msgid "Search customers"
msgstr "Pretraži kupce"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:360
msgid "Search engine metadata"
msgstr "Metapodaci za pretraživače"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:58
#: src/components/help/search/Content/RulesPhrase.tsx:16
msgid "Search for phrases"
msgstr "Pretraživanje fraza"

#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:28
#: src/components/layout/Header/HeaderWithObserver.tsx:201
#: src/components/layout/Header/HeaderWithObserver.tsx:240
#: src/app/(authorized)/help/search/page.tsx:17
msgid "Search help"
msgstr "Pomoć za pretragu"

#: src/components/layout/Header/SearchSuggest/SearchSuggest.js:70
msgid "Search History"
msgstr "Povijest pretrage"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:62
msgid "Search in"
msgstr "Pretraži u"

#: src/components/forms/dashboard/Search/SearchForm.js:52
msgid "Search in {topicName}"
msgstr "Pretraži u {topicName}"

#: src/components/forms/dashboard/Search/SearchForm.js:33
msgid "Search in archive"
msgstr "Pretraži u arhivi"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:44
msgid "Search in author"
msgstr "Pretraži pod autorom"

#: src/components/forms/dashboard/Search/SearchForm.js:63
msgid "Search in Emailing"
msgstr "Pretraži u Emailingu"

#: src/components/forms/dashboard/Search/SearchForm.js:59
msgid "Search in Newsroom"
msgstr "Pretraži u Newsroomu"

#: src/components/layout/MntrFiltersBar/forms/FormNote/FormNote.js:31
msgid "Search in Notes"
msgstr "Pretraži u bilješkama"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:102
#: src/components/forms/dashboard/Search/SearchForm.js:54
msgid "Search in topic"
msgstr "Pretraži u temi"

#: src/components/forms/dashboard/Search/SearchForm.js:34
#: src/components/forms/dashboard/Search/SearchForm.js:67
#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:36
msgid "Search in topics"
msgstr "Pretraži u temama"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTitle.js:90
msgid "Search job position"
msgstr "Pretraži radno mjesto"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:112
#: src/components/topics/Content/TopicsList/Keyword/KeywordExtraQuery.js:37
#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:47
msgid "Search keywords in conjunction with the phrase"
msgstr "Traži ključne riječi u vezi s frazom"

#: src/components/staff/admin/workspace/Workspace.js:172
#: src/components/staff/admin/user/User.js:100
msgid "Search log"
msgstr "Zapisnik pretraživanja"

#: src/components/medialist/forms/FormEditAuthor.js:371
#: src/components/medialist/forms/FormEditAuthor.js:514
#: src/components/medialist/content/FeedMedialist/FeedMedialistItem.js:181
msgid "Search on Google"
msgstr "Pretraži na Googleu"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:126
#: src/components/help/search/Content/RulesOperators.tsx:16
msgid "Search operators"
msgstr "Pretraživački operatori"

#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:41
msgid "Search query"
msgstr "Pretraživački upit"

#: src/store/models/Megalist/MegalistFilter.js:46
msgid "Search Results"
msgstr "Rezultati pretrage"

#: src/components/layout/MntrFiltersBar/forms/FormSearchSources/FormSearchSources.js:33
msgid "Search source or publisher"
msgstr "Pretraži izvor ili izdavača"

#. js-lingui-explicit-id
#: src/components/topics/Content/TopicsList/MegalistSearch.js:42
msgid "megalist.search"
msgstr "Pretraži izvor ili izdavača"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:122
msgid "Search users"
msgstr "Pretraži korisnike"

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:25
msgid "Second Step"
msgstr "Drugi korak"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:311
msgid "Section"
msgstr "Sekcija"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:55
#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:79
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:30
#: src/components/misc/ActionsBar/Selector/Selector.js:29
#: src/components/misc/ActionsBar/Selector/Selector.js:70
msgid "Select"
msgstr "Odaberi"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:106
msgid "Select a method"
msgstr "Odaberite metodu"

#: src/helpers/modal/withModalAddArticle/ArticleBoxesPreview.tsx:31
msgid "Select a preview card for the newsroom article to include in the email"
msgstr "Odaberite preglednu karticu za članak iz newsrooma koji će biti uključen u e-mail"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:33
#: src/components/layout/MntrFiltersBar/modules/MenuFilterToggleAllButtons.js:59
msgid "Select all"
msgstr "Odaberi sve"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:120
msgid "Select article"
msgstr "Odaberite članak"

#: src/components/misc/portable/PortableResend/PortableResend.js:118
#: src/components/misc/portable/PortableExport/PortableExport.js:113
msgid "Select articles to export."
msgstr "Odaberite članke za izvoz."

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:65
msgid "Select at least one mediatype"
msgstr "Odaberite barem jedan mediatip"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterContent.tsx:67
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:52
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewSocialEngagement/PreviewSocialEngagement.js:32
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:51
#: src/components/analytics/AnalyticsContent.js:106
msgid "Select at least one topic"
msgstr "Odaberite barem jednu temu"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:73
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:85
msgid "Select campaign"
msgstr "Odaberite kampanju"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:41
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:48
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:54
msgid "Select category"
msgstr "Odaberite kategoriju"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:350
#: src/components/misc/ColorPicker/ColorPickerSelector.js:100
#: src/components/misc/ColorPicker/ColorPicker.js:61
#: src/components/misc/ColorPicker/ColorPicker.js:67
msgid "Select color"
msgstr "Odaberite boju"

#: src/components/newsroom/forms/FormNewsroomPost/CoverImageUpload.js:85
msgid "Select Cover Image"
msgstr "Odaberite sliku naslovnice"

#: src/components/topics/Content/TopicsList/KeywordExtraQueryAdvanced/KeywordExtraQueryAdvanced.tsx:25
msgid "Select from our list of predefined queries"
msgstr "Odaberite s našeg popisa predefiniranih upita"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:130
#: src/components/settings/SettingsLogo/SettingsLogo.js:129
#: src/components/forms/adapters/MntrFileAdapter/MntrFileAdapter.js:58
msgid "Select Image"
msgstr "Odaberite sliku"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:320
msgid "Select Logo"
msgstr "Odaberite logo"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:108
msgid "Select newsroom"
msgstr "Odaberite novinsku sobu"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:75
msgid "Select pages"
msgstr "Odaberite stranice"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:96
msgid "Select sources"
msgstr "Odaberite izvore"

#: src/components/emailing/forms/FormSenderSettings.js:116
msgid "Select the encryption method used by your SMTP server."
msgstr "Odaberite metodu šifriranja koju koristi vaš SMTP poslužitelj."

#: src/components/newsroom/components/AiTools/AiGenerateTitles.tsx:45
msgid "Select the title"
msgstr "Odaberite naslov"

#: src/components/widgets/modules/stats/WidgetStats.js:79
#: src/components/widgets/modules/socialEngagement/WidgetSocialEngagement.js:41
#: src/components/widgets/modules/analytics/WidgetAnalytics.js:52
#: src/components/monitoring/Monitoring.js:166
msgid "Select Topic"
msgstr "Odaberite temu"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:85
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormSubtype/FormSubtype.js:81
msgid "Select type"
msgstr "Odaberite vrstu"

#: src/components/emailing/forms/FormSenderSettings.js:207
msgid "Select verification method"
msgstr "Odaberite metodu provjere"

#: src/helpers/charts/makeGranularityMenu.js:6
msgid "Select view"
msgstr "Način prikaza"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:106
msgid "Select workspace"
msgstr "Odaberite radni prostor"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:79
msgid "Selected"
msgstr "Odabrano"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:122
msgid "Selected articles will be removed."
msgstr "Odabrani članci će biti uklonjeni."

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:96
msgid "Selected merge tags can not be applied to the author"
msgstr "Odabrane oznake za spajanje ne mogu se primijeniti na autora"

#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:188
msgid "Selected sources"
msgstr "Odabrani izvori"

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:69
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:133
msgid "Selected: {selectedLength}/{MAX_SELECTED_LIMIT}"
msgstr "Odabrano: {selectedLength} od {MAX_SELECTED_LIMIT}"

#: src/store/models/Megalist/Megalist.js:376
msgid "Selection \"{name}\" was removed."
msgstr "Izbor \"{name}\" je uklonjen."

#: src/store/models/Megalist/Megalist.js:335
#: src/store/models/Megalist/Megalist.js:354
msgid "Selection saved as \"{name}\"."
msgstr "Izbor je spremljen kao \"{name}\"."

#: src/components/reports/history/Compose.js:84
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:198
#: src/components/forms/dashboard/ExportResend/ExportResend.js:179
#: src/components/exportList/Content/Content.tsx:86
#: src/components/emailing/content/CreateEmailContent.js:325
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:122
msgid "Send"
msgstr "Pošalji"

#: src/components/misc/portable/PortableResend/PortableResend.js:70
#: src/components/misc/portable/PortableResend/PortableResend.js:110
msgid "Send all articles to email"
msgstr "Pošalji sve članke na email"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:132
msgid "Send article"
msgstr "Pošalji članak"

#: src/components/misc/portable/PortableResend/PortableResend.js:66
#: src/components/misc/portable/PortableResend/PortableResend.js:106
msgid "Send article to email"
msgstr "Pošalji članak na email"

#: src/components/misc/portable/PortableResend/PortableResend.js:68
#: src/components/misc/portable/PortableResend/PortableResend.js:108
msgid "Send articles to email"
msgstr "Pošalji članke na email"

#: src/components/reports/Content/ReportsList/ReportsForm.js:121
msgid "Send empty reports"
msgstr "Pošalji prazna izvješća"

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:76
msgid "Send Feedback"
msgstr "Pošalji povratne informacije"

#: src/components/reports/Content/ReportsList/ReportsForm.js:270
msgid "Send in times (optional)"
msgstr "Pošalji u vremena (neobavezno)"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:150
msgid "Send now"
msgstr "Pošalji odmah"

#: src/components/reports/Content/ReportsList/ReportsForm.js:131
#: src/components/reports/Content/ReportsList/ReportsForm.js:218
msgid "Send on days"
msgstr "Pošalji na dane"

#: src/components/reports/Content/ReportsList/ReportsForm.js:155
msgid "Send on holidays"
msgstr "Pošalji tijekom praznika"

#: src/components/reports/Content/ReportsList/ReportsForm.js:166
msgid "Send on times"
msgstr "Vrijeme slanja"

#: src/components/emailing/content/promo/PromoEmailing.js:28
msgid "Send press releases to journalists with one click."
msgstr "Pošaljite priopćenja za tisak novinarima jednim klikom."

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:151
msgid "Send this email immediately"
msgstr "Pošalji ovaj email odmah"

#: src/components/reports/history/RecipientsTableHeader.js:41
msgid "Send this to your IT specialist"
msgstr "Pošaljite ovu poruku greške vašem IT odjelu"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:58
#: src/components/emailing/content/EmailDetailEmailContent.js:17
#: src/components/emailing/content/CreateEmailContent.js:418
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:135
msgid "Sender"
msgstr "Pošiljatelj"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:186
msgid "Senders"
msgstr "Pošiljatelji"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:58
msgid "Sending a generic email without an attached article provides no useful data for tracking"
msgstr "Slanje generičkog e-maila bez priloženog članka ne pruža korisne podatke za praćenje"

#: src/components/reports/history/HistoryTable.js:223
msgid "Sent automatically via scheduled report"
msgstr "Poslano automatski"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:69
msgid "Sent to"
msgstr "Poslano na"

#: src/constants/analytics.js:812
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:101
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Sentiment.js:30
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:349
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:61
#: src/components/OurChart/OurChartAdvanced.js:108
msgid "Sentiment"
msgstr "Sentiment"

#: src/components/emailing/forms/FormEmailRecipients.js:107
msgid "Separate emails with a space, comma, or semicolon"
msgstr "Odvojite e-mailove razmakom, zarezom ili točka-zarezom"

#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:50
msgid "Separate regional duplicates"
msgstr "Odvoji regionalne duplikate"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:57
#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:60
msgid "Separated by space, newline, comma, or semicolon."
msgstr "Odvojeno razmakom, novim redom, zarezom ili točka-zarezom."

#: src/components/newsroom/content/dashboard/ChartVisits.js:110
msgid "Sessions"
msgstr "Sesije"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:117
msgid "Set annotation"
msgstr "Postavi anotaciju"

#: src/components/layout/AuthWrapper/constants/features.slides.js:354
msgid "Set any number of reports that will be sent to any number of contacts. Everyone receives the correct info at the right time and in the format you choose."
msgstr "Postavite bilo koji broj izvještaja koji će biti poslani na bilo koji broj kontakata. Svi primaju ispravne informacije u pravo vrijeme i u formatu koji odaberete."

#: src/components/emailing/content/CreateEmailContent.js:372
msgid "Set as Draft"
msgstr "Postavi kao Nacrt"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:93
msgid "Set as primary"
msgstr "Postavi kao primarno"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:163
msgid "Set automatic publishing of this post"
msgstr "Postavi automatsko objavljivanje ove objave"

#: src/components/staff/admin/workspace/UsersTable/UpdatePermissions.tsx:36
#: src/components/staff/admin/workspace/UsersTable/UpdatePermissions.tsx:40
msgid "Set permissions"
msgstr "Postavi dozvole"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:96
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:162
msgid "Set publish date"
msgstr "Postavite datum objave"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:123
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:158
msgid "Set send date"
msgstr "Postavite datum slanja"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:229
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:233
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:339
msgid "Set sentiment"
msgstr "Postavi sentiment"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:159
msgid "Set this email to auto-send"
msgstr "Postavite ovu e-poštu na automatsko slanje"

#: src/pages/user/settings.js:19
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:387
#: src/components/newsroom/content/posts/NewsroomPosts.js:293
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:57
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:22
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:48
#: src/components/layout/Header/UserMenu/UserMenu.tsx:176
#: src/components/emailing/sidebar/EmailingSidebarDashboard.js:27
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:76
#: src/components/emailing/forms/FormSenderSettings.js:252
#: src/components/emailing/forms/FormSenderSettings.js:283
#: src/components/emailing/content/EmailingSettingsContent.js:54
#: src/components/emailing/content/EmailingCampaignsContent.tsx:40
msgid "Settings"
msgstr "Postavke"

#. placeholder {0}: model.name
#: src/store/models/ResendSettings.ts:38
#: src/store/models/ExportSettings.js:26
msgid "Settings \"{0}\" was applied."
msgstr "Primijenjene su postavke \"{0}\"."

#. placeholder {0}: exportSettings.name
#: src/store/models/ResendSettings.ts:114
#: src/store/models/ExportSettings.js:80
msgid "Settings \"{0}\" was removed."
msgstr "Postavke \"{0}\" su uklonjene."

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:21
msgid "Settings complete"
msgstr "Postavke su dovršene"

#. placeholder {0}: model.name
#. placeholder {0}: exportSettings.name
#: src/store/models/ResendSettings.ts:93
#: src/store/models/ResendSettings.ts:132
#: src/store/models/ExportSettings.js:62
#: src/store/models/ExportSettings.js:96
msgid "Settings saved as \"{0}\"."
msgstr "Postavke su spremljene kao \"{0}\"."

#: src/store/models/topics/TopicsStore.js:229
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:197
msgid "Settings saved."
msgstr "Postavke spremljene."

#: src/components/dashboards/Content.js:75
#: src/components/dashboards/Content.js:76
#: src/components/dashboards/DashboardSelector/DashboardSelector.js:69
msgid "Share"
msgstr "Podijeli"

#: src/constants/analytics.js:698
#: src/constants/analytics.js:979
#: src/constants/analytics.js:993
#: src/constants/analytics.js:1009
#: src/constants/analytics.js:1024
#: src/constants/analytics.js:1039
msgid "Share of voice"
msgstr "Udio glasa"

#: src/store/models/dashboards/Dashboards.js:523
msgid "Shared link will expire"
msgstr "Podijeljeni link će isteći"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:107
#: src/components/medialist/content/MedialistDashboard.js:135
msgid "Show"
msgstr "Prikaži"

#. placeholder {0}: format.formatAttachedArticles(item.mentioned_article_count)
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:191
msgid "Show {0}"
msgstr "Prikaži {0}"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:14
#: src/components/layout/Header/AppNotifications/AppNotifications.js:186
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:95
#: src/components/OurChart/OurChartAdvanced.js:116
msgid "Show All"
msgstr "Prikaži sve"

#. placeholder {0}: filteredRecipients.length - displayLimit
#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:130
msgid "Show all recipients (+{0})"
msgstr "Prikaži sve primatelje (+{0})"

#: src/components/medialist/forms/FormEditAuthor.js:772
#: src/components/medialist/forms/FormEditAuthor.js:800
msgid "Show and copy to clipboard"
msgstr "Prikaži i kopiraj u međuspremnik"

#: src/components/reports/history/HistoryTable.js:397
#: src/components/exportList/History/HistoryTable/HistoryTable.js:86
msgid "Show articles"
msgstr "Prikaži članke"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:123
msgid "Show articles in feed"
msgstr "Prikaži članke u feedu"

#: src/components/medialist/forms/FormEditAuthor.js:343
#: src/components/medialist/forms/FormEditAuthor.js:351
#: src/components/medialist/forms/FormEditAuthor.js:424
#: src/components/medialist/forms/FormEditAuthor.js:433
#: src/components/medialist/content/MedialistDashboard.js:99
msgid "Show authors"
msgstr "Prikaži autore"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:133
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:28
msgid "Show changes"
msgstr "Prikaži promjene"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:23
msgid "Show checked only"
msgstr "Prikaži samo označeno"

#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:21
msgid "Show history for this report"
msgstr "Prikaži povijest za ovaj izvještaj"

#: src/components/monitoring/Inspector/DemographicsData/modules/EntriesList/EntriesListContent.js:93
msgid "Show less"
msgstr "Prikaži manje"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:95
msgid "Show Less"
msgstr "Prikaži manje"

#: src/components/monitoring/Inspector/DemographicsData/modules/EntriesList/EntriesListContent.js:93
#: src/components/medialist/content/MedialistDashboard.js:162
#: src/components/medialist/content/MedialistDashboard.js:183
msgid "Show more"
msgstr "Prikaži više"

#: src/components/medialist/forms/FormEditAuthor.js:356
#: src/components/medialist/forms/FormEditAuthor.js:439
msgid "Show newsrooms"
msgstr "Prikaži redakcije"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:65
msgid "show stats"
msgstr "prikaži statistiku"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:32
msgid "Show unchecked only"
msgstr "Prikaži samo neoznačene"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:52
msgid "showing {counterFrom} out of {counterTo}"
msgstr "prikazano {counterFrom} od {counterTo}"

#: src/pages/sign-up.tsx:11
#: src/pages/sign-up-completion.tsx:30
#: src/pages/staff/sign-up.js:11
#: src/pages/staff/sign-up-completion.js:26
#: src/components/staff/SignUp.js:30
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:174
#: src/components/page/auth/SignUp/SignUp.js:74
msgid "Sign Up"
msgstr "Registriraj se"

#: src/components/emailing/content/Signature.tsx:104
msgid "Signature"
msgstr "Potpis"

#: src/components/layout/MntrActiveFilters/modules/SimilarArticle.js:12
msgid "Similar to"
msgstr "Slično kao"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:62
msgid "Similarity"
msgstr "Sličnost"

#: src/components/emailing/content/promo/PromoEmailing.js:23
msgid "Simple setting of the appearance of the email template."
msgstr "Jednostavno postavljanje izgleda e-mail predloška."

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:124
msgid "Skip"
msgstr "Preskoči"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:60
msgid "Skip error lines"
msgstr "Preskoči pogrešne retke"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:249
msgid "SMS alerts"
msgstr "SMS upozorenja"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:134
msgid "SMTP settings are invalid."
msgstr "SMTP postavke su nevažeće."

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:132
msgid "SMTP settings are valid."
msgstr "SMTP postavke su valjane."

#: src/components/misc/ActionsBar/View/ViewMenu.js:269
#: src/components/misc/ActionsBar/View/ViewMenu.js:353
msgid "Social data"
msgstr "Social metrike"

#: src/store/models/dashboards/DashboardPreview.js:110
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:51
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:51
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:32
msgid "Social Engagement"
msgstr "Socijalno angažiranje"

#: src/constants/analytics.js:198
#: src/constants/analytics.js:289
#: src/constants/analytics.js:398
#: src/constants/analytics.js:1040
#: src/components/misc/ActionsBar/View/ViewMenu.js:93
msgid "Social interactions"
msgstr "Socijalne interakcije"

#: src/constants/stats.ts:31
#: src/components/widgets/modules/stats/WidgetStats.js:143
msgid "Social Interactions"
msgstr "Socijalne interakcije"

#: src/constants/analytics.js:309
#: src/constants/analytics.js:548
msgid "Social interactions by mention type"
msgstr "Socijalne interakcije prema tipu spomena"

#: src/constants/analytics.js:307
msgid "Social interactions by sentiment"
msgstr "Socijalne interakcije prema sentimentu"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:179
#: src/components/staff/admin/workspace/Workspace.js:534
#: src/components/medialist/forms/FormEditAuthor.js:819
#: src/components/medialist/forms/FormEditAuthor.js:922
#: src/components/layout/AuthWrapper/constants/features.slides.js:87
#: src/components/exportList/ExportLimit/ExportLimit.js:28
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:29
#: src/components/analytics/AnalyticsContent.js:149
#: src/components/analytics/AnalyticsContent.js:228
msgid "Social Media"
msgstr "Društveni mediji"

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:37
msgid "Social Media in {appName}"
msgstr "Društveni mediji u aplikaciji {appName}"

#: src/components/tariff/TariffLimits/TariffLimits.js:186
msgid "Social media topics limit"
msgstr "Broj tema (soc. mediji)"

#: src/components/staff/admin/workspace/Workspace.js:578
msgid "Social media topics limit (Sentione price = 500 Kč per topic)"
msgstr "Ograničenje broja tema (soc. mediji) (Sentione cijena = 500 Kč/tema)"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:147
msgid "Social post"
msgstr "Objava na društvenoj mreži"

#: src/components/medialist/content/AuthorContactInformation.js:38
msgid "Social profiles"
msgstr "Društveni profili"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:56
#: src/components/misc/ActionsBar/View/ViewMenu.js:124
msgid "Sold amount"
msgstr "Prodana količina"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:173
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:85
msgid "Sold amount (print+digital)"
msgstr "Prodano (tisak+el.)"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:652
msgid "Some articles may not be deleted."
msgstr "Neki članci možda nisu bili izbrisani."

#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:155
msgid "Some data are missing in the generated content. Add them manually before proceeding."
msgstr "U generiranom sadržaju nedostaju neki podaci. Dodajte ih ručno prije nego što nastavite."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:23
msgid "Some recipients are missing information for merge tags or email. Please add the missing information or replace the recipients by clicking on them."
msgstr "Neki primatelji nemaju informacije za merge oznake ili e-mail. Molimo dodajte nedostajuće informacije ili zamijenite primatelje klikom na njih."

#: src/helpers/store/apiClient.js:249
msgid "Something failed while preparing a server request. Our team was notified."
msgstr "Došlo je do pogreške prilikom pripreme zahtjeva za server. Naš tim je obaviješten."

#: src/pages/_error.js:44
msgid "Something's gone wrong"
msgstr "Došlo je do pogreške"

#: src/components/misc/ActionsBar/Sort/SortExport.js:19
#: src/components/misc/ActionsBar/Sort/Sort.js:21
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:468
#: src/components/layout/MntrFiltersBar/modules/MenuFilterOrderBy.js:23
msgid "Sort"
msgstr "Sortiraj"

#: src/components/misc/ActionsBar/Sort/SortExport.js:25
#: src/components/misc/ActionsBar/Sort/Sort.js:38
msgid "Sort List"
msgstr "Sortiraj popis"

#: src/components/tariff/AgencyMedia/AgencyMedia.js:64
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:371
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:205
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:226
#: src/components/layout/MntrActiveFilters/modules/NewsSource.js:13
#: src/components/exportList/History/HistoryTable/HistoryTable.js:54
msgid "Source"
msgstr "Izvor"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:326
msgid "Source <0>{newsSourceName}</0> will be removed from the topic <1>{topicMonitorName}</1>."
msgstr "Izvor <0>{newsSourceName}</0> bit će uklonjen iz teme <1>{topicMonitorName}</1>."

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:37
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:456
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:510
msgid "Source File"
msgstr "Izvorna datoteka"

#: src/store/models/monitoring/Inspector/Inspector.ts:532
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:885
msgid "Source removed"
msgstr "Izvor uklonjen"

#: src/components/staff/admin/workspace/Workspace.js:892
msgid "Sources"
msgstr "Izvori"

#: src/components/staff/admin/workspace/Workspace.js:894
#: src/components/staff/admin/workspace/Workspace.js:901
msgid "Sources per client"
msgstr "Izvori po klijentu"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:233
msgid "Special tag"
msgstr "Posebna oznaka"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:47
msgid "Specify the primary goal (inform, persuade, invite, etc.)."
msgstr "Odredite primarni cilj (informirati, uvjeriti, pozvati itd.)."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:137
msgid "Spokesperson"
msgstr "Glasnogovornik"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:57
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:194
msgid "Start editing"
msgstr "Započni uređivanje"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:75
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:213
msgid "Start over"
msgstr "Započni ispočetka"

#: src/components/forms/inspector/FormMediaEditor.js:82
msgid "Start time must be lower than end time"
msgstr "Vrijeme početka mora biti manje od vremena završetka"

#: src/components/emailing/content/CreateEmailContent.js:584
msgid "Start typing or click + to add more content"
msgstr "Počnite tipkati ili kliknite na + za dodavanje više sadržaja"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:681
msgid "Start typing or insert image, video…"
msgstr "Počnite tipkati ili umetnite sliku, video…"

#: src/components/newsroom/content/posts/ChooseTemplates.tsx:76
msgid "Start with template"
msgstr "Započni s predloškom"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:119
#: src/components/staff/admin/user/WorkspacesTable.js:77
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:80
#: src/components/staff/admin/customer/users/UsersTable.js:71
msgid "State"
msgstr "Stanje"

#: src/store/models/dashboards/DashboardPreview.js:121
#: src/components/emailing/content/promo/PromoEmailing.js:32
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:23
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:23
#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:44
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:54
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:26
msgid "Statistics"
msgstr "Statistika"

#: src/components/reports/history/RecipientsTableHeader.js:33
#: src/components/newsroom/content/posts/NewsroomPosts.js:166
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:27
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:407
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNewsroomStatus.js:24
#: src/components/layout/MntrActiveFilters/modules/NewsroomStatus.js:21
msgid "Status"
msgstr "Status"

#: src/components/reports/history/HistoryTable.js:80
#: src/components/reports/history/HistoryTable.js:104
#: src/components/reports/history/HistoryTable.js:322
msgid "Status unknown"
msgstr "Status nepoznat"

#: src/components/layout/AuthWrapper/constants/features.slides.js:260
msgid "Streamline communication efforts and maximize your PR impact."
msgstr "Optimizirajte komunikacijske napore i maksimizirajte utjecaj vašeg PR-a."

#: src/components/reports/history/HistoryTable.js:149
#: src/components/reports/history/Compose.js:62
#: src/components/forms/dashboard/ExportResend/ExportResend.js:102
#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:92
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:86
#: src/components/emailing/content/EmailDetailEmailContent.js:27
#: src/components/emailing/content/CreateEmailContent.js:446
msgid "Subject"
msgstr "Predmet"

#: src/components/misc/MntrForm/MntrForm.tsx:525
msgid "Submit"
msgstr "Potvrdi"

#: src/constants/stats.ts:36
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:79
#: src/components/forms/dashboard/ExportResend/ExportResend.js:124
msgid "Summary"
msgstr "Sažetak"

#: src/components/staff/admin/customer/expenses/DetailExpenseModal.js:35
msgid "Supplier"
msgstr "Dobavljač"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:210
msgid "Support"
msgstr "Podrška"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:472
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:538
msgid "Supported file types:"
msgstr "Podržane vrste datoteka:"

#. placeholder {0}: item.label
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:245
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:157
msgid "Tag <0>{0}</0> will be hidden."
msgstr "Oznaka <0>{0}</0> bit će skrivena."

#. placeholder {0}: item.label
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:315
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:205
msgid "Tag <0>{0}</0> will be removed."
msgstr "Oznaka <0>{0}</0> će biti uklonjena."

#: src/components/forms/tags/FormNewTag/FormNewTag.js:26
#: src/components/forms/tags/FormEditTag/FormEditTag.js:25
#: src/components/forms/tags/FormEditTag/FormEditTag.js:28
msgid "Tag name"
msgstr "Naziv oznake"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:761
#: src/store/models/authors/AuthorsStore.js:716
msgid "Tag removed successfully."
msgstr "Oznaka je uspješno uklonjena."

#: src/constants/analytics.js:845
#: src/constants/analytics.js:1069
#: src/components/medialist/forms/FormEditAuthor.js:616
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:90
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:79
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:72
#: src/components/emailing/content/tabs/AddRecipients.tsx:70
msgid "Tags"
msgstr "Oznake"

#: src/components/medialist/forms/FormEditAuthor.js:610
msgid "Tags, lists and note"
msgstr "Oznake, popisi i bilješka"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:41
msgid "Target Audience:"
msgstr "Ciljana publika:"

#: src/components/settings/SettingsTariff/SettingsTariff.js:22
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:27
msgid "Tariff information"
msgstr "Informacije o tarifi"

#: src/components/reports/Content/ReportsList/ReportsForm.js:317
#: src/components/forms/dashboard/ExportResend/ExportResend.js:107
msgid "Template"
msgstr "Predložak"

#: src/components/emailing/forms/FormSenderSettings.js:167
msgid "Test DNS Settings"
msgstr "Testiraj DNS postavke"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:377
msgid "Text"
msgstr "Tekst"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:207
msgid "Text align"
msgstr "Poravnanje teksta"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:281
#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:76
#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:36
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:113
msgid "Text Color"
msgstr "Boja teksta"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:155
msgid "Text format"
msgstr "Format teksta"

#: src/components/page/auth/Expired/Expired.js:60
msgid "Thank you for trying out {appName}."
msgstr "Hvala što ste isprobali {appName}."

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:91
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:113
msgid "Thank you for your feedback!"
msgstr "Hvala vam na povratnoj informaciji!"

#: src/components/page/auth/UserInactive/UserInactive.js:17
msgid "Thank you for your interest in using {appName}."
msgstr "Hvala na vašem interesu za korištenje {appName}."

#: src/pages/user/yoy-analysis.js:67
#: src/pages/user/reactivate-24.js:67
msgid "Thank you for your interest. We will contact you soon.<0/><1/>Have a great day,<2/><3/>{appName} team"
msgstr "Hvala vam na vašem interesu. Kontaktirat ćemo vas uskoro.<0/><1/>Želimo vam ugodan dan,<2/><3/>tim {appName}"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:100
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:131
msgid "Thank you!"
msgstr "Hvala!"

#: src/store/models/monitoring/Inspector/Inspector.ts:910
msgid "The article already belongs to the topic."
msgstr "Članak već pripada temi."

#: src/store/models/monitoring/WorkspaceArticles.js:164
msgid "The article has been uploaded and is currently being processed. After that it will be added to your feed. You can see the processing status in My Articles."
msgstr "Članak je uspješno učitan i trenutno se obrađuje. Nakon toga bit će dodan u vaš feed. Status obrade možete vidjeti u odjeljku Moji članci."

#: src/store/models/monitoring/Inspector/Inspector.ts:907
msgid "The article was added to the topic. Please reload the feed to see the changes."
msgstr "Članak je dodan temi. Molimo ponovno učitajte feed kako biste vidjeli promjene. Ako ga ne vidite, provjerite postavke filtriranja."

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:392
msgid "The article will be removed from your media coverage view. If the article also exists in your feed, it will remain there and will not be deleted."
msgstr "Članak će biti uklonjen iz vašeg pregleda medijskog praćenja. Ako se članak također nalazi u vašem feedu, ostat će tamo i neće biti izbrisan."

#: src/components/medialist/forms/FormEditAuthor.js:299
#: src/components/medialist/forms/FormEditAuthor.js:463
msgid "The author's profile will be reset to its original values."
msgstr "Profil autora bit će vraćen na izvorne vrijednosti."

#: src/store/models/monitoring/Inspector/MediaEditor/MediaEditorStore.js:55
msgid "The clip is being prepared. It may take a while. When the clip is ready for download, you will receive a notification."
msgstr "Klip se priprema. To može potrajati. Kada klip bude spreman za preuzimanje, primit ćete obavijest."

#: src/components/emailing/forms/FormSenderSettings.js:239
msgid "The DNS Verification Is Unavailable"
msgstr "Provjera putem DNS-a nije dostupna"

#: src/components/emailing/forms/FormSenderSettings.js:240
msgid "The DNS verification settings for this email are not accessible. We suggest opting for SMTP (Simple Mail Transfer Protocol) as an alternative way of verification. If you need any additional information or help, our support team is here to assist you."
msgstr "Postavke DNS provjere za ovu e-poštu nisu dostupne. Kao alternativni način provjere predlažemo odabir SMTP-a (Simple Mail Transfer Protocol). Ako vam je potrebna dodatna informacija ili pomoć, naš tim za podršku je ovdje da vam pomogne."

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:37
msgid "The email content can be automatically adjusted to include personalized details for each recipient"
msgstr "Sadržaj e-pošte može se automatski prilagoditi kako bi uključivao personalizirane podatke za svakog primatelja."

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:113
msgid "The email is currently empty. Please add some content to the email."
msgstr "E-mail je trenutno prazan. Molimo dodajte neki sadržaj u e-mail."

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:28
msgid "The following summary was generated by a machine and may not accurately represent the original content."
msgstr "Sljedeći sažetak je generiran strojno i možda ne predstavlja točno originalni sadržaj."

#: src/store/models/ExportStore.js:251
msgid "The full article text cannot be downloaded as you have reached your limit. To adjust this limit, please contact support."
msgstr "Cjeloviti tekst članka ne može se preuzeti jer ste dosegli svoje ograničenje. Za prilagodbu ovog ograničenja kontaktirajte podršku."

#: src/components/tariff/TariffLimits/TariffLimits.js:31
msgid "The limit applies to the number of articles found in the last 30 days generated by set keywords. If you have reached the limit for the number of found articles, <0>edit keywords</0> or contact us to increase the limit."
msgstr "Limit se odnosi na broj članaka pronađenih u posljednjih 30 dana generiranih postavljenim ključnim riječima. Ako ste dostigli limit za broj pronađenih članaka, <0>uredite ključne riječi</0> ili nas kontaktirajte za povećanje limita."

#: src/components/tariff/TariffLimits/TariffLimits.js:68
msgid "The limit applies to the number of exported articles in the last 30 days (topics, archive or report attachments). If you have reached the limit for the number of exported articles, you must wait until the limit is restored or contact us to increase the limit."
msgstr "Limit se odnosi na broj izvezenih članaka u posljednjih 30 dana (unutar tema, arhive ili privitaka izvješća). Ako ste dostigli limit za broj izvezenih članaka, morate čekati dok se limit ne obnovi, s obzirom na vaše prethodno izvođenje. Ili nas kontaktirajte za povećanje limita."

#: src/components/tariff/TariffLimits/TariffLimits.js:104
msgid "The limit applies to the number of translated articles in the last 30 days (topics, archive, report or report attachments). If you are interested in increasing this limit, please contact us."
msgstr "Limit se odnosi na broj prevedenih članaka u posljednjih 30 dana (unutar tema, arhive, izvještaja ili priloga izvještaja). Ako ste zainteresirani za povećanje ovog limita, molimo kontaktirajte nas."

#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:20
msgid "The list of already exported articles can be downloaded without limitation."
msgstr "Popis već izvezenih članaka može se preuzeti bez ograničenja."

#: src/store/models/authors/Baskets/AuthorBasketDefinitionsStoreArrItem.ts:54
msgid "The list was successfully duplicated."
msgstr "Popis je uspješno dupliciran."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:84
msgid "The main content of your post appears to be empty. The body is where you elaborate on your ideas, present your arguments, or share your story. Please add substantial content to your post to engage your readers and convey your message effectively."
msgstr "Čini se da je glavni sadržaj vašeg posta prazan. Tijelo je mjesto gdje razrađujete svoje ideje, iznosite svoje argumente ili dijelite svoju priču. Dodajte značajan sadržaj svom postu kako biste zaokupili čitatelje i učinkovito prenijeli svoju poruku."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:20
msgid "help.grp"
msgstr "Medijski utjecaj ima za cilj bolje od apsolutnih brojeva članaka predstaviti stvarnu medijsku sliku promatranog subjekta kako dolazi do najšire skupine slušatelja, gledatelja i čitatelja medija. Temelji se prvenstveno na čitanosti (tisak), slušanosti (radio), gledanosti (TV) i mjesečnoj posjećenosti weba (online). Jedinica mjerenja medijskog utjecaja su GRP bodovi (Gross Rating Points), pri čemu jedan GRP bod odgovara jednom postotku populacije starije od petnaest godina (npr. za Hrvatsku skupinu od 45 000 pojedinaca). Radi se o čitateljima, slušateljima ili gledateljima koji su mogli biti oslovljeni objavljenim člankom. Čitatelj koji je mogao pročitati više od jednog članka, računa se više puta. OTS (Opportunity to See) zatim pokazuje koliko puta je pripadnik ciljane skupine u prosjeku imao priliku pročitati ili pogledati članak. U slučaju ciljane skupine svih stanovnika Hrvatske starijih od petnaest godina: OTS = GRP / 100."

#: src/pages/authors/index.js:43
msgid "The most <0>extensive</0> and the most <1>actual</1> medialist of journalists, publishers & other authors, in which you will find detailed information including contacts."
msgstr "<0>Najsveobuhvatniji</0> i <1>najaktualniji</1> popis novinara, izdavača i drugih autora, u kojem ćete pronaći detaljne informacije uključujući kontakte."

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:31
msgid "The primary sender is used as the default sender for emails. You can change this when you create an email."
msgstr "Primarni pošiljatelj koristi se kao zadani pošiljatelj za e-poštu. Ovo možete promijeniti kada kreirate e-poštu."

#. placeholder {0}: senderItem.unverified_recipients_limit
#. placeholder {1}: senderItem.verified_recipients_limit
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:68
msgid "The recipient limit is set to {0}. For a higher limit of {1} recipients, enable DNS or SMTP verification."
msgstr "Postavljen je limit na broj primatelja na {0}. Za viši limit od {1} primatelja, omogućite provjeru putem DNS-a ili SMTP-a."

#. placeholder {0}: appSettings.appName
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:136
msgid "The summary was created with the {0} application."
msgstr "Sažetak je stvoren s aplikacijom {0}."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:493
msgid "The user is responsible for the content uploaded to the {appName} application. By uploading files, you confirm that you own the rights to the file or that the file is licensed under <0>CC0</0>."
msgstr "Korisnik je odgovoran za sadržaj koji je prenesen na aplikaciju {appName}. Prijenosom datoteka potvrđujete da imate prava na datoteku ili da je datoteka licencirana pod <0>CC0</0>."

#: src/components/layout/Header/UserMenu/UserMenu.tsx:137
msgid "Theme"
msgstr "Tema"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:81
msgid "There are no keywords assigned to this topic"
msgstr "Nema ključnih riječi dodijeljenih ovom temi"

#: src/pages/404.js:18
#: src/app/not-found-content.tsx:27
msgid "There's nothing here..."
msgstr "Ovdje nema ničega..."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:107
msgid "These settings allow not only change language of the newsroom, but to link newsrooms together. Pair them in different languages for quick and seamless transitions."
msgstr "Ove postavke omogućuju ne samo promjenu jezika newsroomu, već i povezivanje newsroomu zajedno. Uparite ih na različitim jezicima za brze i nesmetane prijelaze."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:184
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:229
msgid "This field is required"
msgstr "Ovo polje je obavezno"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:222
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:305
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:418
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:75
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:78
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:83
msgid "This field is required."
msgstr "Ovo polje je obavezno."

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:380
msgid "This is a summary of the page's content. It appears below the headline on the search results page."
msgstr "Ovo je sažetak sadržaja stranice. Pojavljuje se ispod naslova na stranici s rezultatima pretrage."

#: src/components/emailing/forms/FormSenderSettings.js:109
msgid "This is the port number that your SMTP server uses to send email. If you're not sure, leave it blank to use the default port."
msgstr "Ovo je broj porta koji vaš SMTP server koristi za slanje e-pošte. Ako niste sigurni, ostavite prazno da biste koristili zadani port."

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:134
msgid "This list is empty"
msgstr "Ovaj popis je prazan"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:65
msgid "This month"
msgstr "Ovaj mjesec"

#: src/components/misc/Capture/Capture.js:300
msgid "this should take only a couple of seconds"
msgstr "ovo bi trebalo trajati samo nekoliko sekundi"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:269
msgid "This template was custom tailored for you. For further customization please contact our <0>support</0>."
msgstr "Ovaj predložak je prilagođen vašim potrebama. Za daljnje prilagodbe molimo kontaktirajte našu <0>podršku</0>."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:52
msgid "This week"
msgstr "Ovaj tjedan"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:78
msgid "This year"
msgstr "Ove godine"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:151
msgid "Threshold"
msgstr "Prag (%)"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:192
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:316
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:320
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:70
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:99
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:190
msgid "Time"
msgstr "Vrijeme"

#: src/components/forms/inspector/FormMediaEditor.js:85
#: src/components/forms/inspector/FormMediaEditor.js:88
msgid "Time must not exceed media length"
msgstr "Vrijeme ne smije premašiti duljinu medija"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:70
msgid "Timed"
msgstr "Vremenski ograničen"

#: src/constants/analytics.js:54
#: src/constants/analytics.js:78
#: src/constants/analytics.js:100
#: src/constants/analytics.js:122
#: src/constants/analytics.js:142
#: src/constants/analytics.js:191
#: src/constants/analytics.js:226
#: src/constants/analytics.js:255
#: src/constants/analytics.js:282
#: src/constants/analytics.js:308
#: src/constants/analytics.js:335
#: src/constants/analytics.js:364
#: src/constants/analytics.js:391
#: src/constants/analytics.js:417
#: src/constants/analytics.js:444
#: src/constants/analytics.js:482
#: src/constants/analytics.js:510
#: src/constants/analytics/primeScoreCharts.ts:30
#: src/constants/analytics/primeScoreCharts.ts:56
msgid "Timeline"
msgstr "Vremenska crta"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:51
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:506
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:507
#: src/components/newsroom/content/posts/NewsroomPosts.js:156
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:34
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:218
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormWidgetTitle/FormWidgetTitle.js:17
msgid "Title"
msgstr "Naslov"

#. js-lingui-explicit-id
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:362
msgid "metadata.title"
msgstr "Naslov"

#: src/components/reports/Content/ReportsList/ReportsForm.js:289
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:85
msgid "To"
msgstr "Do"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:33
msgid "To change your password, enter your current password and then the new password."
msgstr "Za promjenu lozinke unesite vašu trenutnu lozinku, a zatim novu lozinku."

#: src/components/emailing/forms/FormSenderSettings.js:286
msgid "To ensure the successful delivery of emails from our system, it's necessary to configure your SMTP server with the following details:"
msgstr "Da biste osigurali uspješnu dostavu e-pošte iz našeg sustava, potrebno je konfigurirati vaš SMTP poslužitelj s sljedećim detaljima:"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:27
msgid "To hide some tags from the list, uncheck these tags. The user can add hidden tags back to their feed again at any time if necessary."
msgstr "Kako biste sakrili neke oznake s popisa, poništite njihov odabir. Korisnik u bilo kojem trenutku može ponovno vratiti skrivene oznake u svoj pregled."

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:100
msgid "To hide some topics from the list, uncheck these topics. The user can add hidden topics back to their feed again at any time if necessary."
msgstr "Da biste sakrili neke teme s popisa, poništite njihov odabir. Korisnik može u bilo kojem trenutku ponovno dodati skrivene teme u svoj feed ako je potrebno."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:492
msgid "To set up your own domain (e.g. companyname.com), please contact our team. We will be happy to help you set up your domain. We have also written a detailed guide for you."
msgstr "Za postavljanje vlastite domene (npr. blog.firma.hr), molimo kontaktirajte naš tim. Rado ćemo vam pomoći s postavljanjem domene."

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:39
msgid "To view all mentions, it is necessary to activate social media monitoring."
msgstr "Za pregled svih spominjanja, potrebno je aktivirati praćenje društvenih medija."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/metaDataDate.js:19
msgid "today"
msgstr "danas"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:110
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:39
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:100
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:327
msgid "Today"
msgstr "Danas"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:51
msgid "Tone and Style:"
msgstr "Ton i stil:"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:88
msgid "Tone of voice"
msgstr "Ton glasa članka"

#: src/constants/analytics.js:1126
msgid "Top authors"
msgstr "Top autori"

#: src/constants/analytics.js:1228
msgid "Top hashtags"
msgstr "Top hashtagovi"

#: src/constants/analytics.js:1206
msgid "Top profiles"
msgstr "Top profili"

#: src/constants/analytics.js:1248
msgid "Top publishers"
msgstr "Najbolji izdavači"

#: src/constants/analytics.js:1268
msgid "Top sources"
msgstr "Najčešći izvori"

#: src/constants/analytics/primeScoreCharts.ts:136
msgid "Top sources by overall PRIMe"
msgstr "Izvori prema ukupnom PRIMe"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:200
msgid "Top stories"
msgstr "Top priče"

#: src/helpers/charts/tableTemplates.js:74
#: src/components/exportList/History/HistoryTable/HistoryTable.js:57
msgid "Topic"
msgstr "Tema"

#. placeholder {0}: item.data.name
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:206
msgid "Topic <0>{0}</0> will be hidden."
msgstr "Tema <0>{0}</0> bit će skrivena"

#. placeholder {0}: item.data.name
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:147
msgid "Topic <0>{0}</0> will be removed."
msgstr "Tema <0>{0}</0> će biti uklonjena."

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:147
msgid "Topic Name"
msgstr "Naziv teme"

#: src/pages/topics/index.js:24
#: src/components/topics/Content/TopicChangelog.js:18
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:113
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:253
#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:26
#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:80
#: src/components/notifications/ContentTopics.js:29
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:32
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:33
#: src/components/layout/MntrActiveFilters/modules/TvrTopics.js:10
#: src/components/layout/MntrActiveFilters/modules/EmptyTopics.js:21
#: src/app/components/monitoring-navigation.tsx:154
msgid "Topics"
msgstr "Teme"

#: src/components/topics/Content/TopicsHeading/TopicsHeading.js:10
msgid "Topics ({counter})"
msgstr "Teme ({counter})"

#. placeholder {0}: menuItem.topic_monitors .map((item) => { // @ts-expect-error TODO refactor topics to TS return item.label }) .join(', ')
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:233
msgid "Topics <0>{0}</0> will be hidden."
msgstr "Teme <0>{0}</0> bit će skrivene"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:142
msgid "Topics and keywords"
msgstr "Teme i ključne riječi"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:291
msgid "Topics in this folder will be displayed separately and won't be deleted."
msgstr "Teme u ovoj mapi bit će prikazane odvojeno i neće biti izbrisane."

#: src/components/tariff/TariffLimits/TariffLimits.js:167
#: src/components/staff/admin/workspace/Workspace.js:439
msgid "Topics limit"
msgstr "Broj tema"

#: src/components/monitoring/Inspector/InspectorMonitora/KeywordsPagination/KeywordsPagination.js:304
msgid "total"
msgstr "ukupno"

#: src/helpers/charts/tableTemplates.js:55
#: src/helpers/charts/tableTemplates.js:97
#: src/helpers/charts/tableTemplates.js:136
#: src/components/widgets/modules/stats/StatsBySource.js:120
#: src/components/tvr/Content/Content.js:92
msgid "Total"
msgstr "Ukupno"

#. placeholder {0}: humanizeNumber(data)
#: src/constants/analytics.js:215
msgid "Total {0} interactions"
msgstr "Ukupno {0} interakcija"

#. placeholder {0}: humanizeNumber(data)
#: src/constants/analytics.js:471
msgid "Total influence score: {0}"
msgstr "Ukupni rezultat utjecaja: {0}"

#. placeholder {0}: formatter( this.points.reduce((sum, { y }) => sum + y, 0), unit, )
#: src/components/OurChart/HighchartsRenderer.js:645
msgid "Total: {0}"
msgstr "Ukupno: {0}"

#: src/components/emailing/content/promo/PromoEmailing.js:33
msgid "Track delivery and opening statistics."
msgstr "Pratite statistike dostave i otvaranja."

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:20
msgid "Track online, traditional and social media with {appName} for a complete view of your brand and trends - never miss a beat."
msgstr "Pratite online, tradicionalne i društvene medije s {appName} za potpuni pregled vašeg brenda i trendova - ne propustite ništa."

#: src/components/layout/AuthWrapper/constants/features.slides.js:166
msgid "Tracking, analysis, and reporting are an integral part of PR. Use comprehensible charts that make data analysis easier. Compare your media output with your competition."
msgstr "Media monitoring, analiza i izvještavanje su integralni dio PR-a. Koristite razumljive grafikone koji olakšavaju analizu podataka. Usporedite svoje medijske rezultate s konkurencijom."

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:159
#: src/components/staff/admin/workspace/Workspace.js:353
#: src/components/layout/AuthWrapper/constants/features.slides.js:41
#: src/components/exportList/ExportLimit/ExportLimit.js:17
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:19
#: src/components/analytics/AnalyticsContent.js:146
#: src/components/analytics/AnalyticsContent.js:195
msgid "Traditional Media"
msgstr "Tradicionalni mediji"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:25
msgid "Traditional Media w/o percentage change"
msgstr "Tradicionalni mediji (bez postotne promjene)"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:532
msgid "Transcribe the source file"
msgstr "Prepišite izvornu datoteku"

#: src/components/monitoring/WorkspaceArticles/Limits.js:69
msgid "Transcribed seconds"
msgstr "Prepisanih sekundi"

#: src/components/monitoring/WorkspaceArticles/Limits.js:73
msgid "Transcript"
msgstr "Transkript"

#: src/components/medialist/content/MedialistActionsBar/FormTransformContacts.tsx:41
msgid "Transform"
msgstr "Transformirati"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:53
msgid "Transform & import"
msgstr "Transformiraj i uvezi"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:137
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:17
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformTitle.tsx:7
msgid "Transform contact list"
msgstr "Transformirati popis kontakata"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:134
msgid "Transformation failed"
msgstr "Transformacija nije uspjela"

#: src/helpers/withTranslatePopup/TranslatePopupContent.js:49
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:75
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:77
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:110
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:189
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:194
msgid "Translate"
msgstr "Prevedi"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:243
msgid "Translations"
msgstr "Prijevodi"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:569
msgid "Transparent background"
msgstr "Transparentna pozadina"

#: src/app/components/monitoring-navigation.tsx:242
msgid "Trash"
msgstr "Smeće"

#: src/constants/analytics.js:1053
#: src/constants/analytics.js:1068
msgid "Treemap"
msgstr "Treemap"

#: src/components/staff/admin/user/User.js:120
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:391
msgid "Trigger password reset"
msgstr "Pokreni resetiranje lozinke"

#: src/components/notifications/Permissions.js:74
msgid "Try again"
msgstr "Pokušaj ponovno"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:82
#: src/components/misc/PromoBox/PromoBox.js:144
msgid "Try for free"
msgstr "Isprobaj besplatno"

#: src/pages/user/reactivate-24.js:34
msgid "Try out Mediaboard"
msgstr "Isprobajte Mediaboard"

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:57
msgid "Try social media monitoring"
msgstr "Isprobajte praćenje društvenih medija"

#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:18
msgid "Turn off these notifications"
msgstr "Isključi ove obavijesti"

#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:27
msgid "Turn on these notifications"
msgstr "Uključi ove obavijesti"

#: src/components/notifications/ContentTvrRequest.js:41
#: src/components/notifications/ContentTvr.js:46
#: src/components/misc/ActionsBar/View/ViewMenu.js:162
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:41
#: src/components/layout/AuthWrapper/constants/features.slides.js:65
msgid "TV"
msgstr "TV"

#: src/components/emailing/forms/FormSenderSettings.js:123
msgid "TXT record"
msgstr "TXT zapis"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:58
msgid "Type a coefficient"
msgstr "Unesite koeficijent"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:38
msgid "Type your keypoint"
msgstr "Upišite svoju ključnu točku"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:56
msgid "Type your main message"
msgstr "Upišite svoju glavnu poruku"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:125
msgid "Type your subject or other instructions. Clearly outline the main message or information you want to convey.Provide instructions on how to structure the information, for example: use bullet points or numbered lists."
msgstr "Unesite predmet ili druge upute. Jasno izložite glavnu poruku ili informacije koje želite prenijeti. Dajte upute kako strukturirati informacije, na primjer: koristite oznake ili numerirane popise."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:175
msgid "Unable to extract data from the URL."
msgstr "Nije moguće izvući podatke iz navedenog URL-a."

#: src/components/emailing/content/EmailingSettingsContent.js:29
msgid "Unable to retrieve access token from the OAuth2 provider. This may be due to a network issue or provider outage. Please try again later."
msgstr "Nije moguće dohvatiti pristupni token od pružatelja OAuth2. To može biti zbog problema s mrežom ili ispada davatelja usluga. Pokušajte ponovno kasnije."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:558
msgid "Undo"
msgstr "Poništi"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:79
msgid "Unique visits"
msgstr "Jedinstveni posjeti"

#: src/components/newsroom/content/dashboard/ChartVisits.js:54
#: src/components/newsroom/content/dashboard/ChartVisits.js:96
#: src/components/newsroom/components/PostsList/PostsList.js:209
msgid "Unique Visits"
msgstr "Jedinstveni posjeti"

#: src/components/reports/history/RecipientsTableRow.js:68
msgid "Unknown"
msgstr "Nepoznato"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:194
msgid "Unlock licensed articles"
msgstr "Otključaj licencirane članke"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:148
msgid "Unpublish"
msgstr "Poništi objavljivanje"

#: src/components/medialist/forms/FormEditAuthor.js:577
msgid "Unsaved changes"
msgstr "Nespremljene promjene"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:152
msgid "Unschedule"
msgstr "Poništi raspored"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:149
msgid "Unsubscribe"
msgstr "Odjaviti se"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:47
msgid "Unsubscribe from emails"
msgstr "Odjava s e-mailova"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:311
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:322
msgid "Unsubscribe news source"
msgstr "Otkaži pretplatu na izvor vijesti"

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:138
msgid "Update recipient"
msgstr "Ažuriraj primatelja"

#: src/components/medialist/content/MedialistDashboard.js:75
#: src/components/medialist/content/MedialistDashboard.js:108
msgid "Updated"
msgstr "Ažurirano"

#: src/components/medialist/content/MedialistActionsBar/ContactsImportTitle.tsx:8
msgid "Upload either a manually completed template or a formatted contact list file. Once you import contacts, they will automatically appear in the Import. You can also add the contacts to one of the existing lists."
msgstr "Prenesite ručno ispunjeni predložak ili formatiranu datoteku s popisom kontakata. Nakon što uvezete kontakte, automatski će se pojaviti u Uvozu. Kontakte također možete dodati na jedan od postojećih popisa."

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:26
msgid "Upload File"
msgstr "Prenesi datoteku"

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:20
msgid "Upload Image"
msgstr "Prenesi sliku"

#: src/components/medialist/content/MedialistActionsBar/withModalUploadMedialist.tsx:8
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:240
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:241
msgid "Upload medialist"
msgstr "Prenesi popis medija"

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:23
msgid "Upload Video"
msgstr "Prenesi video"

#: src/components/settings/SettingsLogo/SettingsLogo.js:95
msgid "Upload your company logo, which will then be displayed in email reports, exports and in the application itself, instead of the {appName} logo."
msgstr "Prenesite logotip svoje tvrtke, koji će se zatim prikazivati u izvješćima e-pošte, izvozima i u samoj aplikaciji umjesto logotipa {appName}."

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformTitle.tsx:8
msgid "Upload your contact list and we'll transform it to fit our medialist for you."
msgstr "Prenesite svoj popis kontakata i mi ćemo ga transformirati kako bi odgovarao našem popisu medija za vas."

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:58
msgid "Upload your contact list, and we’ll format it to fit perfectly into our medialist for you."
msgstr "Prenesite svoj popis kontakata, a mi ćemo ga za vas oblikovati tako da savršeno odgovara našem popisu medija."

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:58
msgid "Upload your file"
msgstr "Prenesite svoju datoteku"

#: src/components/misc/UploadWatcher/UploadWatcher.js:18
msgid "Uploading has not finished. Please do not refresh or close this page."
msgstr "Prijenos još nije završen. Molimo, ne osvježavajte niti zatvarajte ovu stranicu."

#: src/components/misc/UploadWatcher/UploadWatcher.js:46
msgid "Uploading: {lastProgress}%"
msgstr "Prijenos: {lastProgress}%"

#: src/components/medialist/forms/modules/FormArray.js:131
msgid "Url"
msgstr "Url"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:190
#: src/components/ReusableFeed/FormAddArticle.tsx:31
msgid "URL"
msgstr "URL"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:38
msgid "Use another email address"
msgstr "Koristi drugu email adresu"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:76
msgid "Use Google account as sender"
msgstr "Koristite Google račun kao pošiljatelja"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:82
msgid "Use Microsoft 365 account as sender"
msgstr "Koristite Microsoft 365 račun kao pošiljatelja"

#: src/components/staff/admin/workspace/Workspace.js:369
#: src/components/staff/admin/workspace/Workspace.js:389
#: src/components/staff/admin/workspace/Workspace.js:410
#: src/components/staff/admin/workspace/Workspace.js:430
#: src/components/staff/admin/workspace/Workspace.js:449
#: src/components/staff/admin/workspace/Workspace.js:470
#: src/components/staff/admin/workspace/Workspace.js:491
#: src/components/staff/admin/workspace/Workspace.js:524
#: src/components/staff/admin/workspace/Workspace.js:550
#: src/components/staff/admin/workspace/Workspace.js:569
#: src/components/staff/admin/workspace/Workspace.js:588
#: src/components/staff/admin/workspace/Workspace.js:639
#: src/components/staff/admin/workspace/Workspace.js:660
#: src/components/staff/admin/workspace/Workspace.js:686
msgid "Used"
msgstr "Korišteno"

#: src/pages/staff/admin/users/[userId]/index.js:12
#: src/components/staff/admin/DailyAccess/Table.js:24
#: src/components/staff/admin/DailyAccess/Content.js:32
msgid "User"
msgstr "Korisnik"

#: src/components/tariff/TariffLimits/TariffLimits.js:274
#: src/components/staff/admin/workspace/Workspace.js:677
msgid "User accounts limit"
msgstr "Ograničenje korisničkih računa"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:53
#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:59
msgid "User emails"
msgstr "Korisnički e-mailovi"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:104
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:67
msgid "User management"
msgstr "Upravljanje korisnicima"

#: src/components/staff/admin/user/User.js:224
msgid "User settings"
msgstr "Korisničke postavke"

#: src/components/emailing/forms/FormSenderSettings.js:89
msgid "Username"
msgstr "Korisničko ime"

#: src/pages/staff/admin/customers/[customerId]/users.js:12
#: src/components/staff/admin/workspace/Workspace.js:858
#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:69
#: src/components/staff/admin/customers/Customer.js:173
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:77
#: src/components/staff/admin/customer/users/Users.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:34
#: src/components/forms/dashboard/Search/SearchUsers.js:36
msgid "Users"
msgstr "Korisnici"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:27
msgid "Utilizes company profiles for more tailored content."
msgstr "Koristi profile tvrtki za prilagođeniji sadržaj."

#: src/components/emailing/forms/FormSenderSettings.js:168
msgid "Validate"
msgstr "Potvrdi"

#: src/components/emailing/forms/FormSenderSettings.js:134
msgid "Value"
msgstr "Vrijednost"

#: src/components/staff/admin/customer/bio/CustomerBio.js:95
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:58
msgid "VAT"
msgstr "PDV"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:227
msgid "Verification"
msgstr "Verifikacija"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:101
msgid "Verification email sent."
msgstr "Verifikacijski email je poslan."

#. placeholder {0}: values.email
#: src/components/emailing/content/EmailingSettingsContent.js:93
msgid "Verification email was sent to {0}. Please check your inbox."
msgstr "Verifikacijski email je poslan na adresu {0}. Molimo provjerite svoj inbox."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:26
msgid "Verify your email"
msgstr "Potvrdite svoj email"

#: src/components/emailing/forms/FormSenderSettings.js:208
msgid "Verifying your email address with SMTP or DNS can improve email deliverability, protect against spoofing, improve sender reputation, and provide better analytics. It demonstrates legitimacy and helps email providers ensure that emails are not spam."
msgstr "Potvrđivanje vaše e-mail adrese putem SMTP-a ili DNS-a može poboljšati isporuku e-mailova, zaštititi od lažiranja, poboljšati reputaciju pošiljatelja i pružiti bolju analitiku. Dokazuje legitimitet i pomaže pružateljima e-mail usluga osigurati da e-mailovi nisu spam."

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:87
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:48
msgid "Video"
msgstr "Video"

#: src/components/newsroom/content/posts/NewsroomPosts.js:119
#: src/components/newsroom/content/posts/NewsroomPosts.js:123
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:23
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:32
#: src/components/misc/ActionsBar/View/ViewMenu.js:323
#: src/components/misc/ActionsBar/View/View.js:16
msgid "View"
msgstr "Prikaz"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:84
msgid "View changes"
msgstr "Pogledaj promjene"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:44
msgid "View full article"
msgstr "Pogledajte cijeli članak"

#: src/components/OurChart/OurChartAdvanced.js:253
msgid "View in full screen"
msgstr "Prikaz na cijelom zaslonu"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:58
msgid "View Newsroom"
msgstr "Pogledajte Newsroom"

#: src/components/feed/InspectorToolbar/InspectorToolbar.js:120
msgid "View preview"
msgstr "Pogledaj pregled"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:41
msgid "View Screenshot"
msgstr "Pogledaj screenshot"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:47
msgid "View Video"
msgstr "Prikaži video"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:41
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:47
msgid "View Web"
msgstr "Pogledaj web"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:35
msgid "Viewing this press publication incurs an additional fee as per the Table of Fees approved by the Minister of Culture and National Heritage."
msgstr "Pregledavanje ove tiskovne publikacije podliježe dodatnoj naknadi prema Cjeniku naknada odobrenom od strane Ministarstva kulture i nacionalne baštine."

#: src/components/monitoring/FeedList/FeedListItem/SocialInteractions/SocialInteractions.js:24
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:95
msgid "views"
msgstr "prikazi"

#: src/components/newsroom/content/posts/NewsroomPosts.js:163
msgid "Views"
msgstr "Pregledi"

#: src/components/misc/ActionsBar/Selector/Selector.js:34
msgid "Visible"
msgstr "Vidljivo"

#: src/components/newsroom/content/posts/NewsroomPosts.js:261
#: src/components/newsroom/content/dashboard/ChartVisits.js:86
#: src/components/newsroom/components/PostsList/PostsList.js:209
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:74
msgid "Visits"
msgstr "Posjete"

#: src/components/newsroom/content/dashboard/ChartVisits.js:49
msgid "Visits (last 30 days / total):"
msgstr "Posjete (zadnjih 30 dana / ukupno):"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:107
msgid "Warning"
msgstr "Upozorenje"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:46
msgid "Warning via <0>SMS</0>, <1>email</1> or <2>notification</2>"
msgstr "Upozorenje putem <0>SMS</0>, <1>e-pošte</1> ili <2>obavijesti</2>"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:57
msgid "We are the only ones in the Czech Republic to monitor <0>text mentions in the broadcast</0> for selected channels."
msgstr "Jedini smo u Hrvatskoj koji pratimo <0>tekstualne spomene u prijenosu</0> za odabrane kanale."

#: src/pages/user/reset-password/success.tsx:8
msgid "We have sent password reset link to your email."
msgstr "Poslali smo vam poveznicu za resetiranje lozinke na vašu e-mail adresu."

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:29
msgid "We have sent you an activation link. To activate Emailing and to confirm your email address please open the link."
msgstr "Poslali smo vam aktivacijski link. Da biste aktivirali Emailing i potvrdili svoju e-mail adresu, molimo otvorite link."

#: src/components/emailing/content/EmailingSettingsContent.js:28
msgid "We haven't been granted access to send emails on your behalf. Please try again and make sure to grant us access."
msgstr "Nije nam odobren pristup za slanje e-pošte u vaše ime. Pokušajte ponovno i svakako nam dopustite pristup."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:75
msgid "We noticed that your post lacks an introduction or perex. This section is crucial as it provides a brief overview of your post and entices readers to continue. Consider adding a short paragraph that summarizes your main points or sets the context for your post."
msgstr "Primijetili smo da vašem postu nedostaje uvod ili perex. Ovaj odjeljak je ključan jer pruža kratak pregled vašeg posta i potiče čitatelje da nastave. Razmislite o dodavanju kratkog odlomka koji sažima vaše glavne točke ili postavlja kontekst za vaš post."

#: src/components/emailing/forms/FormSenderSettings.js:226
msgid "We recommend that you verify your email address"
msgstr "Preporučujemo vam da provjerite svoju e-mail adresu"

#: src/components/page/auth/UserInactive/UserInactive.js:20
msgid "We will contact you shortly, once we setup your account."
msgstr "Kontaktirat ćemo vas uskoro, čim postavimo vaš račun."

#: src/pages/404.js:24
#: src/app/not-found-content.tsx:33
msgid "We're sorry, but the requested page was not found. It is possible that the page was either removed or moved somewhere else. Please make sure you entered the correct URL address."
msgstr "Žao nam je, ali tražena stranica nije pronađena. Moguće je da je stranica premještena ili uklonjena. Molimo vas da se uvjerite da ste unijeli ispravnu URL adresu."

#. placeholder {0}: topics.getTopicNameById(missingArticle.topicMonitorId)
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:170
msgid "We've added the article to the the topic \"{0}\" and adjusted its settings. The article will appear in your feed shortly."
msgstr "Dodali smo članak na temu \"{0}\" i prilagodili njegove postavke. Članak će se uskoro pojaviti u vašem feedu."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:110
msgid "We've discovered more content in the media landscape related to your topics and areas of interest."
msgstr "Otkrili smo više sadržaja u medijskom pejzažu povezanog s vašim temama i područjima interesa."

#: src/components/medialist/forms/FormEditAuthor.js:842
msgid "Website"
msgstr "Web stranica"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:141
msgid "Website URL"
msgstr "URL vaše web stranice"

#: src/helpers/charts/makeGranularityMenu.js:18
#: src/helpers/charts/getGranularityLabel.js:6
msgid "Weeks"
msgstr "Tjedni"

#: src/components/emailing/content/sender/EmailingSenderContent.js:48
msgid "What is Emailing used for?"
msgstr "Za što se Emailing koristi?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:250
msgid "What is Newsroom?"
msgstr "Što je Newsroom?"

#: src/components/emailing/content/sender/EmailingSenderContent.js:49
msgid "While our Emailing tool is designed to send press and PR messages to journalists, its functionality goes beyond that. You can use it for various types of communication, opening up possibilities beyond traditional media outreach."
msgstr "Naš alat za slanje e-pošte je dizajniran za slanje tiskovnih i PR poruka novinarima, ali njegova funkcionalnost ide dalje od toga. Možete ga koristiti za različite vrste komunikacije, otvarajući mogućnosti izvan tradicionalnog pristupa medijima."

#: src/components/staff/admin/workspace/Workspace.js:799
msgid "Whitelisted domains"
msgstr "Dopuštene domene"

#. placeholder {0}: targetDashboard.name
#: src/store/models/dashboards/DashboardItem/DashboardItem.js:323
msgid "Widget copied to \"{0}\"."
msgstr "Widget je kopiran u \"{0}\"."

#. placeholder {0}: targetDashboard.name
#: src/store/models/dashboards/DashboardItem/DashboardItem.js:314
msgid "Widget moved to \"{0}\"."
msgstr "Widget je premješten u \"{0}\"."

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:138
msgid "Widget will be removed"
msgstr "Widget će biti uklonjen."

#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:203
msgid "With contact"
msgstr "S kontaktom"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:38
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:72
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:112
msgid "With inflection"
msgstr "S padežima"

#: src/components/layout/AuthWrapper/constants/features.slides.js:215
msgid "With Medialist, you don't send your media output to randomly selected journalists. You only send it to those who are most likely to publish it."
msgstr "S Adremom ne šaljete svoje medijske sadržaje nasumično odabranim novinarima, već ih šaljete samo onima koji će ih najvjerojatnije objaviti."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:32
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:37
#: src/components/layout/MntrActiveFilters/modules/Note.js:11
msgid "With note"
msgstr "S bilješkom"

#. placeholder {0}: values.unverified_recipients_limit
#: src/components/emailing/forms/FormSenderSettings.js:227
msgid "Without a verified email address, your emails risk being marked as spam and rejected by providers, potentially damaging your reputation. You will also be limited to sending an email to only {0} recipients at a time."
msgstr "Bez verificirane e-mail adrese, postoji rizik da će vaši e-mailovi biti označeni kao spam i odbijeni od strane pružatelja usluga, što može potencijalno naštetiti vašem ugledu. Također ćete biti ograničeni na slanje e-maila samo {0} primateljima odjednom."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:29
msgid "Without limit"
msgstr "Bez ograničenja"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:49
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:54
#: src/components/layout/MntrActiveFilters/modules/Note.js:15
msgid "Without note"
msgstr "Bez bilješke"

#: src/components/staff/admin/customers/Customers.js:26
msgid "without sending registration email"
msgstr "bez slanja registracijskog emaila"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:154
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:166
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:86
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:92
#: src/components/layout/MntrActiveFilters/modules/Tags.js:46
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterTags.js:19
msgid "Without tags"
msgstr "Bez oznaka"

#: src/pages/404.js:14
#: src/app/not-found-content.tsx:23
msgid "Woop woop woop woop, page not found"
msgstr "Woop woop woop woop, stranica nije pronađena"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:24
#: src/components/help/search/Content/RulesSearch.tsx:16
msgid "Word Search"
msgstr "Pretraživanje riječi"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:98
#: src/components/help/search/Content/RulesDistance.tsx:16
msgid "Words to distance"
msgstr "Riječi do udaljenosti"

#: src/pages/staff/admin/workspaces/[workspaceId]/index.js:12
#: src/components/staff/admin/workspace/WorkspaceChangelog.js:21
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:77
#: src/components/staff/admin/DailyAccess/Table.js:27
#: src/components/staff/admin/DailyAccess/Content.js:32
#: src/components/page/auth/Expired/Expired.js:53
#: src/components/layout/Header/UserMenu/UserMenu.tsx:101
msgid "Workspace"
msgstr "Radni prostor"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:210
msgid "Workspace admin"
msgstr "Admin radnog prostora"

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:55
msgid "Workspace created."
msgstr "Radni prostor je stvoren."

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:37
msgid "Workspace name"
msgstr "Naziv radnog prostora"

#: src/components/staff/admin/workspace/Workspace.js:280
msgid "Workspace settings"
msgstr "Postavke radnog prostora"

#: src/pages/staff/admin/customers/[customerId]/workspaces.js:12
#: src/components/staff/admin/user/WorkspacesTable.js:61
#: src/components/staff/admin/user/User.js:307
#: src/components/staff/admin/customers/Customer.js:150
#: src/components/staff/admin/customer/workspaces/Workspaces.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:26
#: src/components/forms/dashboard/Search/SearchWorkspaces.js:43
msgid "Workspaces"
msgstr "Radni prostori"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:65
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:204
msgid "worst"
msgstr "najgore"

#: src/components/settings/SettingsLogo/SettingsLogo.js:76
msgid "Would you like to customize the appearance of the app, email reports and exports with your own logo? Contact us at <0>{salesEmail}</0>"
msgstr "Želite li prilagoditi izgled aplikacije, e-mail izvještaja i izvoza sa svojim vlastitim logom? Kontaktirajte nas na <0>{salesEmail}</0>"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:61
msgid "Write the main content of your article that you want to create. The main content is considered as the primary theme or topic of your article."
msgstr "Napišite glavnu sadržaj svog članka koji želite stvoriti. Glavni sadržaj smatra se primarnom temom ili temom vašeg članka."

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:19
#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:20
msgid "Write with AI assistant"
msgstr "Piši s AI asistentom"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:39
msgid "Write without AI assistant"
msgstr "Piši bez AI asistenta"

#: src/components/newsroom/components/AiTools/AiGenerateTitles.tsx:79
msgid "Write your own"
msgstr "Napišite sami"

#: src/components/tariff/TariffLimits/TariffLimits.js:132
#: src/components/staff/admin/workspace/Workspace.js:420
msgid "Yearly authors export limit"
msgstr "Godišnji limit izvoza autora"

#: src/helpers/charts/makeGranularityMenu.js:34
#: src/helpers/charts/getGranularityLabel.js:12
msgid "Years"
msgstr "Godišnji"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/metaDataDate.js:30
msgid "yesterday"
msgstr "jučer"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:114
#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/MediaDetail.js:72
#: src/components/tvr/Content/TvrStories/TvrStory/TvrStory.js:52
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:100
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:115
msgid "Yesterday"
msgstr "Jučer"

#: src/components/emailing/modules/withModalRemoveRecipients.tsx:60
msgid "You are about to remove the selected recipients. However, you can keep some of them by clicking on the recipients."
msgstr "Upravo ćete ukloniti odabrane primatelje. Ipak, neke od njih možete zadržati klikom na primatelje."

#: src/components/emailing/content/mediaCoverage/EmptyFeedMessage.tsx:9
msgid "You can add articles to media coverage"
msgstr "Možete dodati članke u medijsko praćenje"

#: src/components/exportList/Content/HeadingExport/HeadingExport.js:30
msgid "You can add items in Articles section."
msgstr "Možete dodati stavke u odjeljku Članci."

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:88
msgid "You can create your first campaign by clicking the button below."
msgstr "Svoju prvu kampanju možete stvoriti klikom na gumb ispod."

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:133
msgid "You can create your first email by clicking the button below."
msgstr "Svoj prvi email možete stvoriti klikom na gumb ispod."

#: src/components/monitoring/WorkspaceArticles/Intro.js:30
msgid "You can create your own articles here. They will be added to <0>your feed only</0>."
msgstr "Ovdje možete kreirati vlastite članke. Oni će biti dodani samo u <0>vaš feed</0>."

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:51
msgid "You can edit recipients in email settings."
msgstr "Primatelje možete urediti u postavkama e-pošte."

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:149
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:70
msgid "You can reset your filter by clicking the button below."
msgstr "Filter možete resetirati klikom na gumb ispod."

#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:20
msgid "You can safely close this window as the process will continue in the background. Once the import is complete, we will notify you. If any issues occur, you will receive a notification and an email detailing the errors."
msgstr "Ovaj prozor možete sigurno zatvoriti jer će proces nastaviti u pozadini. Kada uvoz bude dovršen, obavijestit ćemo vas. Ako dođe do bilo kakvih problema, primit ćete obavijest i e-mail s detaljima o pogreškama."

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:91
msgid "You can use an external link to the article or {appName} link."
msgstr "Možete koristiti vanjski link do članka ili link iz aplikacije {appName}."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:244
msgid "You don't have a Newsroom yet, but you can create a new one right now."
msgstr "Još nemate Newsroom, ali možete ga stvoriti upravo sada."

#: src/components/monitoring/WorkspaceArticles/Intro.js:26
msgid "You don't have any articles yet, but you can create one right now."
msgstr "Još niste stvorili nijedan članak, ali možete to pokušati odmah sada."

#: src/components/emailing/content/sender/EmailingSenderContent.js:20
msgid "You don't have Emailing set up yet. It only takes a few minutes to set it up."
msgstr "Još niste postavili Emailing. Postavljanje traje samo nekoliko minuta."

#: src/components/widgets/modules/stats/StatsBySource.js:35
#: src/components/widgets/components/PermissionErrorHint/PermissionErrorHint.js:13
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:36
msgid "You don't have permission to view"
msgstr "Nemate dozvolu za pregled"

#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:55
msgid "You have no topics created"
msgstr "Nemate kreirane teme"

#: src/components/notifications/AppNotifications/AppNotifications.js:25
#: src/components/layout/Header/AppNotifications/AppNotifications.js:173
msgid "You have not received any notifications yet."
msgstr "Još niste primili nikakve obavijesti."

#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:52
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:52
msgid "You have not saved any settings"
msgstr "Niste spremili nikakve postavke"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:106
msgid "You have reached 30-day limit on the number of translated articles."
msgstr "Dosegli ste 30-dnevni limit na broj prevedenih članaka."

#: src/components/tariff/TariffLimits/TariffLimits.js:59
#: src/components/exportList/ExportLimit/ExportLimit.js:19
msgid "You have reached 30-day limit. You cannot export any new articles."
msgstr "Dosegli ste 30-dnevni limit. Ne možete izvesti nove članke."

#: src/components/tariff/TariffLimits/TariffLimits.js:220
#: src/components/exportList/ExportLimit/ExportLimit.js:30
msgid "You have reached 30-day limit. You cannot export any new social media mentions."
msgstr "Dosegli ste 30-dnevni limit. Ne možete izvesti nove spomene sa društvenih mreža."

#: src/store/models/ExportStore.js:238
msgid "You have reached the 30-day limit on the number of exported articles. Exported file doesn't contain all the selected articles."
msgstr "Dosegli ste 30-dnevni limit na broj izvezenih članaka. Izvezena datoteka ne sadrži sve odabrane članke."

#: src/store/models/ExportStore.js:240
msgid "You have reached the 30-day limit on the number of exported social media mentions. Exported file doesn't contain all selected items."
msgstr "Dosegli ste 30-dnevni limit na broj izvezenih spominjanja na društvenim mrežama. Izvezena datoteka ne sadrži sve odabrane stavke."

#: src/components/monitoring/WorkspaceArticles/Limits.js:59
msgid "You have reached the 30-day limit on the number of OCR pages."
msgstr "Dosegli ste 30-dnevni limit na broj stranica za OCR."

#: src/components/monitoring/WorkspaceArticles/Limits.js:75
msgid "You have reached the 30-day limit on the number of transcribed seconds."
msgstr "Dosegli ste 30-dnevni limit na broj sekundi za prepis."

#: src/store/models/ExportStore.js:246
msgid "You have reached the 30-day limit on the number of translated articles. Exported file doesn't contain all the selected articles."
msgstr "Dosegli ste 30-dnevni limit na broj prevedenih članaka. Izvezena datoteka ne sadrži sve odabrane članke."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:92
msgid "You have reached the limit of recipients per email"
msgstr "Dostigli ste ograničenje broja primatelja po e-mailu"

#: src/components/layout/Header/MessageLimit/MessageLimit.js:19
msgid "You have reached the limit on found articles"
msgstr "Dosegli ste limit na broj pronađenih članaka"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:104
msgid "You have reached the limit on the number of dashboards."
msgstr "Dosegli ste limit na broj nadzornih ploča."

#: src/store/models/ExportStore.js:229
msgid "You have reached the limit on the number of exported articles. Exported file doesn't contain all the selected articles."
msgstr "Dosegli ste limit na broj izvezenih članaka. Izvezena datoteka ne sadrži sve odabrane članke."

#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:79
msgid "You have reached the limit on the number of Newsrooms."
msgstr "Dosegli ste limit na broj Newsrooma."

#: src/store/models/emailing/emailEdit/EmailEditStore/recipients/EmailRecipientsStore/EmailRecipientsStore.js:122
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:48
msgid "You have reached the limit on the number of recipients."
msgstr "Dosegli ste limit na broj primatelja."

#: src/components/emailing/content/EmailingSettingsContent.js:22
msgid "You have successfully authorized our application to use the external service."
msgstr "Uspješno ste autorizirali našu aplikaciju za korištenje vanjske usluge."

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:123
msgid "You have unsaved changes."
msgstr "Imate nespremljene promjene."

#: src/components/tariff/TariffLimits/TariffLimits.js:23
msgid "You may not see the latest articles."
msgstr "Možda ne vidite najnovije članke."

#: src/components/layout/Header/MessageLimit/MessageLimit.js:13
msgid "You may not see the latest articles. We recommend that you change your keyword settings or limit your watched media in the Topics section."
msgstr "Možda ne vidite najnovije članke. Preporučujemo da promijenite postavke ključnih riječi ili ograničite praćene medije u odjeljku Teme."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:470
msgid "You will be able to edit the link later to match your own domain."
msgstr "Kasnije ćete moći urediti poveznicu tako da odgovara vašoj domeni."

#: src/components/emailing/modules/PreviewEmail/RecipientsIsEmpty.tsx:27
#: src/components/emailing/components/EmailRecipientsList/RenderAllRecipients.tsx:34
msgid "You will see your recipients here"
msgstr "Ovdje ćete vidjeti svoje primatelje"

#: src/helpers/modal/withModalRequestFeature.tsx:40
#: src/components/misc/PromoBox/PromoBox.js:135
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:12
msgid "You'll be contacted by our Sales management."
msgstr "Naš predstavnik za prodaju će vas kontaktirati."

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:17
msgid "Your account does not have access to any workspace."
msgstr "Vaš račun nema pristup nijednom radnom prostoru."

#: src/components/page/auth/Expired/Expired.js:49
msgid "Your account has expired"
msgstr "Vaš račun je istekao"

#: src/components/page/auth/UserInactive/UserInactive.js:14
msgid "Your account is being prepared"
msgstr "Vaš račun se priprema"

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:118
msgid "Your email <0>{0}</0> is already unsubscribed from our email list. There is nothing you need to do to stop receiving emails from {host}"
msgstr "Vaša e-pošta <0>{0}</0> je već odjavljena s našeg popisa e-pošte. Ne trebate ništa učiniti da prestanete primati e-poštu od {host}"

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:85
msgid "Your email <0>{0}</0> successfully unsubscribed from our email list. You will no longer receive emails from us."
msgstr "Vaša e-pošta <0>{0}</0> je uspješno odjavljena s našeg popisa e-pošte. Više nećete primati e-poštu od nas."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:65
msgid "Your email has been verified. Now you can fully enjoy our platform."
msgstr "Vaša e-pošta je provjerena. Sada možete u potpunosti uživati u našoj platformi."

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:76
msgid "Your email successfully unsubscribed"
msgstr "Vaša e-pošta je uspješno odjavljena"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:109
msgid "Your email was already unsubscribed"
msgstr "Vaša e-pošta je već odjavljena"

#: src/components/emailing/content/EmailingSettingsContent.js:71
#: src/components/emailing/content/EmailingCampaignsContent.tsx:28
msgid "Your Emailing is not fully set up and verified"
msgstr "Vaš Emailing nije potpuno postavljen i verificiran"

#: src/components/emailing/content/EmailingSettingsContent.js:74
msgid "Your Emailing is not fully set up and verified. This can decrease the trust level and deliverability. You can fully set up and verify your Emailing in the settings. If you need help, please contact our support."
msgstr "Vaš Emailing nije potpuno postavljen i verificiran. To može smanjiti razinu povjerenja i dostavljivosti. Emailing možete potpuno postaviti i verificirati u postavkama. Ako vam je potrebna pomoć, molimo kontaktirajte našu podršku."

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:96
msgid "Your HTML code"
msgstr "Vaš HTML kod"

#: src/components/layout/Header/MessageDirty/MessageDirty.js:10
msgid "Your news feed is being updated"
msgstr "Vaša vijesti se ažuriraju"

#: src/store/models/account/user/UserStore.js:247
msgid "Your password has been changed successfully."
msgstr "Vaša lozinka je uspješno promijenjena."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:66
msgid "Your post is missing a title. A clear, concise title helps readers understand what your post is about at a glance. Please add a title that accurately represents your content."
msgstr "Vašem postu nedostaje naslov. Jasan, sažet naslov pomaže čitateljima da na prvi pogled razumiju o čemu se radi u vašem postu. Dodajte naslov koji točno predstavlja vaš sadržaj."
