msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-07-31 13:12+0200\n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Mime-Version: 1.0\n"
"X-Generator: Poedit 3.4.4\n"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:7
msgid "error"
msgstr "<i>We apologize but we experience some issues with our AI assistant. We know it's frustrating when things don't work as expected.<br> Please try again after some time.</i>"

#. placeholder {0}: data.word_count
#: src/components/monitoring/FeedList/FeedListItem/FeedListItem.js:305
msgid "(full text; {0} words)"
msgstr "(full text; {0} words)"

#: src/components/staff/admin/workspace/Workspace.js:781
msgid "(TVR) Allow automatic transcripts in monitoring"
msgstr "(TVR) Allow automatic transcripts in monitoring"

#: src/components/staff/admin/workspace/Workspace.js:790
msgid "(TVR) Allow reruns in monitoring"
msgstr "(TVR) Allow reruns in monitoring"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+access%7D+other+%7B%23+accesses%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:85
msgid "# access"
msgid_plural "# accesses"
msgstr[0] "# access"
msgstr[1] "# accesses"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(item.article_count)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+article%7D+other+%7B%23+articles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:43
#: src/helpers/charts/formatters.js:55
#: src/components/reports/history/HistoryTable.js:199
#: src/components/analytics/AnalyticsContent.js:122
msgid "# article"
msgid_plural "# articles"
msgstr[0] "# article"
msgstr[1] "# articles"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+attached+article%7D+other+%7B%23+attached+articles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:50
msgid "# attached article"
msgid_plural "# attached articles"
msgstr[0] "# attached article"
msgstr[1] "# attached articles"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+author%7D+other+%7B%23+authors%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:57
msgid "# author"
msgid_plural "# authors"
msgstr[0] "# author"
msgstr[1] "# authors"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: inspector.data.versions_count
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+change%7D+other+%7B%23+changes%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:78
#: src/components/monitoring/Inspector/InspectorMonitora/StatusBar/ArticleHistoryAction.js:22
msgid "# change"
msgid_plural "# changes"
msgstr[0] "# change"
msgstr[1] "# changes"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+comment%7D+other+%7B%23+comments%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:89
msgid "# comment"
msgid_plural "# comments"
msgstr[0] "# comment"
msgstr[1] "# comments"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+dislike%7D+other+%7B%23+dislikes%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:109
msgid "# dislike"
msgid_plural "# dislikes"
msgstr[0] "# dislike"
msgstr[1] "# dislikes"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+email%7D+other+%7B%23+emails%7D%7D&pluralize_on=0
#: src/components/emailing/helpers/emailing.plurals.js:3
msgid "# email"
msgid_plural "# emails"
msgstr[0] "# email"
msgstr[1] "# emails"

#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(data.social_shares)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+interaction%7D+other+%7B%23+interactions%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:76
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:78
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:119
msgid "# interaction"
msgid_plural "# interactions"
msgstr[0] "# interaction"
msgstr[1] "# interactions"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+like%7D+other+%7B%23+likes%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:69
msgid "# like"
msgid_plural "# likes"
msgstr[0] "# like"
msgstr[1] "# likes"

#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(inspector.data.article_mentions_count)
#. placeholder {0}: parseInt(data.article_mentions_count)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+mention%7D+other+%7B%23+mentions%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:90
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:103
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:81
#: src/components/monitoring/Inspector/InspectorMonitora/StatusBar/ArticleMentionsActions.js:25
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:342
msgid "# mention"
msgid_plural "# mentions"
msgstr[0] "# mention"
msgstr[1] "# mentions"

#. placeholder {0}: parseInt(jobs.length - 1)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+more%7D+other+%7B%23+more%7D%7D&pluralize_on=0
#: src/helpers/getAuthorJobs.js:16
msgid "# more"
msgid_plural "# more"
msgstr[0] "# more"
msgstr[1] "# more"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: parseInt(value)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+output%7D+other+%7B%23+outputs%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:71
#: src/helpers/charts/formatters.js:69
msgid "# output"
msgid_plural "# outputs"
msgstr[0] "# output"
msgstr[1] "# outputs"

#. placeholder {0}: parseInt(value, 10)
#. placeholder {0}: payload.page_count
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+page%7D+other+%7B%23+pages%7D%7D&pluralize_on=0
#: src/components/monitoring/WorkspaceArticles/Limits.js:51
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:145
msgid "# page"
msgid_plural "# pages"
msgstr[0] "# page"
msgstr[1] "# pages"

#. placeholder {0}: parseInt(value)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+person%7D+other+%7B%23+people%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:83
msgid "# person"
msgid_plural "# people"
msgstr[0] "# person"
msgstr[1] "# people"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+retweet%7D+other+%7B%23+retweets%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:79
msgid "# retweet"
msgid_plural "# retweets"
msgstr[0] "# retweet"
msgstr[1] "# retweets"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+share%7D+other+%7B%23+shares%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:119
msgid "# share"
msgid_plural "# shares"
msgstr[0] "# share"
msgstr[1] "# shares"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+view%7D+other+%7B%23+views%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:99
msgid "# view"
msgid_plural "# views"
msgstr[0] "# view"
msgstr[1] "# views"

#. placeholder {0}: account.workspace.limits.media_archive_depth_limit
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+year%7D+other+%7B%23+years%7D%7D&pluralize_on=0
#: src/components/tariff/TariffLimits/TariffLimits.js:263
msgid "# year"
msgid_plural "# years"
msgstr[0] "# year"
msgstr[1] "# years"

#. placeholder {0}: item.recipients.length - shortEmailList.length
#. placeholder {0}: items.length - MAX_ITEMS
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%2B%23+more%7D+other+%7B%2B%23+more%7D%7D&pluralize_on=0
#: src/components/reports/history/HistoryTable.js:373
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:73
msgid "+# more"
msgid_plural "+# more"
msgstr[0] "+# more"
msgstr[1] "+# more"

#. placeholder {0}: data.identical_articles.length
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%2B%23+other%7D+other+%7B%2B%23+other%7D%7D&pluralize_on=0
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:128
msgid "+# other"
msgid_plural "+# other"
msgstr[0] "+# other"
msgstr[1] "+# other"

#. placeholder {0}: 1
#. placeholder {0}: self.selector.selected.size
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7BArticle+Removed%7D+other+%7BArticles+Removed%7D%7D&pluralize_on=0
#: src/store/models/monitoring/Inspector/Inspector.ts:497
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:659
#: src/store/models/emailing/campaignDetail/CampaignDetailStore/CampaignDetailStore.js:108
msgid "Article Removed"
msgid_plural "Articles Removed"
msgstr[0] "Article Removed"
msgstr[1] "Articles Removed"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7Barticle%7D+other+%7Barticles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:64
msgid "article"
msgid_plural "articles"
msgstr[0] "article"
msgstr[1] "articles"

#. placeholder {0}: 1
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7BItem+Removed%7D+other+%7BItems+Removed%7D%7D&pluralize_on=0
#: src/store/models/tvr/tvr.js:274
msgid "Item Removed"
msgid_plural "Items Removed"
msgstr[0] "Item Removed"
msgstr[1] "Items Removed"

#: src/components/forms/dashboard/Search/SearchDeclensions.js:51
msgid "{appName} will search"
msgstr "{appName} will search"

#: src/components/tariff/MonitoredMedia/MonitoredMedia.js:206
msgid "{countriesWithActiveMedia} countries with monitoring enabled"
msgstr "{countriesWithActiveMedia} countries with monitoring enabled"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:107
msgid "{i} (current)"
msgstr "{i} (current)"

#. js-lingui:icu=%7BolderCount%2C+plural%2C+one+%7B%2B+%23+older%7D+other+%7B%2B+%23+older%7D%7D&pluralize_on=olderCount
#: src/components/feed/InspectorToolbar/ToolbarPagination.js:32
msgid "+ # older"
msgid_plural "+ # older"
msgstr[0] "+ # older"
msgstr[1] "+ # older"

#. js-lingui:icu=%7BprocessedCount%2C+plural%2C+one+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+article+has+been+updated.%7D+other+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+articles+have+been+updated.%7D%7D&pluralize_on=processedCount
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:517
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:754
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:800
msgid "You have reached the limit for this action. {processedCount} article has been updated."
msgid_plural "You have reached the limit for this action. {processedCount} articles have been updated."
msgstr[0] "You have reached the limit for this action. {processedCount} article has been updated."
msgstr[1] "You have reached the limit for this action. {processedCount} articles have been updated."

#. js-lingui:icu=%7BprocessedCount%2C+plural%2C+one+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+author+has+been+updated.%7D+other+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+authors+have+been+updated.%7D%7D&pluralize_on=processedCount
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:553
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:590
#: src/store/models/authors/AuthorsStore.js:528
#: src/store/models/authors/AuthorsStore.js:581
#: src/store/models/authors/AuthorsStore.js:657
#: src/store/models/authors/AuthorsStore.js:709
msgid "You have reached the limit for this action. {processedCount} author has been updated."
msgid_plural "You have reached the limit for this action. {processedCount} authors have been updated."
msgstr[0] "You have reached the limit for this action. {processedCount} author has been updated."
msgstr[1] "You have reached the limit for this action. {processedCount} authors have been updated."

#. js-lingui:icu=%7Bvalue%2C+plural%2C+one+%7B%23+post%7D+other+%7B%23+posts%7D%7D&pluralize_on=value
#: src/helpers/charts/formatters.js:97
msgid "# post"
msgid_plural "# posts"
msgstr[0] "# post"
msgstr[1] "# posts"

#. js-lingui:icu=%7Bvalue%2C+plural%2C+one+%7B%23+visit%7D+other+%7B%23+visits%7D%7D&pluralize_on=value
#: src/helpers/charts/formatters.js:62
msgid "# visit"
msgid_plural "# visits"
msgstr[0] "# visit"
msgstr[1] "# visits"

#: src/pages/newsroom/index.js:62
msgid "<0>Accurate</0> data is part of analytics."
msgstr "<0>Accurate</0> data is part of analytics."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:88
msgid "help.engagementRate"
msgstr "help.engagementRate"

#: src/pages/authors/index.js:69
msgid "<0>Export</0> detailed lists of authors"
msgstr "<0>Export</0> detailed lists of authors"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:21
msgid "<0>Immediate notifications</0> about mentions on <1>TV and radio</1>. Be in the swim of things. Non-stop."
msgstr "<0>Immediate notifications</0> about mentions on <1>TV and radio</1>. Be in the swim of things. Non-stop."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:72
msgid "help.influenceScore"
msgstr "Influence Score is a number (from 1 to 10) calculated for each social media mention. This score is based on two things.<0><1>how likely it is for the mention to be seen,</1><2>how many times a mention has been viewed, shared, or retweeted.</2></0>We believe it helps you discover statements, authors, and sites that are the most popular and influential. This way your influencer marketing campaigns can get more metrics for analysis and be more data-driven."

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:38
msgid "<0>Non-stop</0> monitoring of selected TVs and radios"
msgstr "<0>Non-stop</0> monitoring of selected TVs and radios"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesSearch.tsx:18
msgid "help.search.wordSearch.description"
msgstr "<0>Quick Overview</0><1><2><3><4><5>Entered expression</5><6>What does Monitora do?</6><7>Searches for articles containing</7></4></3><8><9><10>Penguin</10><11>word inflects, diacritic and letter size doesn't matter</11><12>Penguin, Penguins, penguin, penguins, PENGUIN, PENGUINS</12></9><13><14>\"Penguin\"</14><15>word does not inflect, diacritic plays a role, letter size does not matter</15><16>Penguin, penguin, PENGUIN</16></13><17><18>\"!Penguin\"</18><19>word does not inflect, diacritic and letter size plays a role</19><20>Penguin</20></17></8></2></1><21>With inflection</21><22>If we type in the search box: <23>Penguin</23></22><24>Monitora will search for all articles that contain the word <25>Penguin</25> in any shape. So there will be found articles containing the word <26>Penguins</26> (Monitora inflects the word), <27>PENGUIN</27> (letter size does not matter) or <28>penguin´s</28> (diacritics does not matter).</24><29>It is recommended to search for all words that are inflected in normal text this way. These are typically general words (penguin), proper names (Michael) or foreign names (facebook).</29><30>Exact match</30><31>If we type in the search box: <32>\"Penguin\"</32> (put the word in quotation marks)</31><33>Monitora will search for all articles that contain the word <34>Penguin</34>, but only in the specified form (i.e., do not inflect the word). So there will be found articles containing the word <35>Penguin</35> or <36>PENGUIN</36> (letter size does not matter).</33><37>Monitora does not look for those articles that contain inflected word <38>Penguins</38> or the word without diacritics <39>penguin´s</39>.</37><40>It is recommended to search for company and product names (\"McDonald's\"), web domain (\"monitora.com\"), exact match (\"best\") or abbreviations (\"USA\") in this way.</40><41>Exact match, including letter size</41><42>If we type in the search box: <43>\"!Penguin\"</43> (put the word in quotation marks and after the first quotation mark an exclamation mark)</42><44>Monitora will search for all articles that contain the word <45>Penguin</45>, but only in the specified form, including the letter size. This is the strictest option.</44><46>Monitora will not search for those articles that contain, for example, only the word <47>penguin</47> written in lowercase letters.</46><48><49>It is recommended to search for company and product names (\"!Seznam\") or abbreviations (\"!WHO\") this way.</49></48>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesOperators.tsx:18
msgid "help.search.operators.description"
msgstr "<0>Quick Overview</0><1><2><3><4>Entered expression</4><5>Monitora will search</5></3></2><6><7><8>penguin AND walrus</8><9>Articles containing both words <10>penguin</10> and <11>walrus</11>.</9></7><12><13>penguin walrus</13><14>Articles containing both words <15>penguin</15> and <16>walrus</16>. The space between words behaves as if there was an AND operator.</14></12><17><18>penguin OR walrus</18><19>Articles containing at least one of the words <20>penguin</20> or <21>walrus</21>.</19></17><22><23>penguin -walrus</23><24>Articles containing the word <25>penguin</25> but not containing the word <26>walrus</26>.</24></22></6></1><27>AND</27><28>To search for articles that contain several words or phrases at the same time, enter all the words you want, separated by either a space or the word <29>AND</29> (in capital letters).</28><30>If we type in the search box: <31>penguin walrus Prague+zoo \"!ČEZ\"</31></30><32>It’s the same as we would write: <33>penguin AND walrus AND Prague+zoo AND \"!ČEZ\"</33></32><34>OR</34><35>To search for articles that contain at least one of the words or phrases you enter, enter all the words you want and separate them with <36>OR</36> (in capital letters).</35><37>Example: <38>penguin OR walrus OR Prague+zoo OR \"!ČEZ\"</38></37><39>NOT</39><40>To remove articles that contain certain words or phrases from the search results, we type a list of forbidden words and phrases after the search term and precede each with a minus sign.</40><41>Example: <42>penguin -walrus -Prague+zoo -\"!ČEZ\"</42></41><43>Brackets</43><44>Search operators can be combined as needed. For more complex expressions, however, we often need to determine the order in which we want search operators to evaluate. For this purpose, we use brackets that work similarly to mathematics.</44><45>If we type in the search box: <46>\"!Billa\" AND (store OR chain OR shop OR supermarket OR hypermarket)</46></45><47>Monitora will search for all articles that contain the word <48>Billa</48> (only in the specified form including the letter size) in conjunction with at least one of the words <49>store</49>, <50>chain</50>, …</47><51>Finally, a demonstration of how complex terms you can put together in Monitora search operators:<52>Housing estate (house OR prefab OR building) AND (balcony OR (plastic+windows -\"!Vekra\")) AND Praha+Záběhlice~5 -(Spořilov OR Chodov)</52></51>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesPhrase.tsx:18
msgid "help.search.phrase.description"
msgstr "<0>Quick Overview</0><1><2><3><4>Entered expression</4><5>Searches for articles containing</5></3></2><6><7><8>Caffé+Honza</8><9>Caffé Honza, CAFFÉ HONZA, Caffé honza, Caffé Honzy, caffe honza, caffé-honza</9></7><10><11>\"Caffé Honza\"</11><12>Caffé Honza, CAFFÉ HONZA, caffé honza</12></10><13><14>\"Caffé-Honza\"</14><15>Caffé-Honza, CAFFÉ-HONZA, caffé-honza</15></13><16><17>\"!Caffé Honza\"</17><18>Caffé Honza</18></16></6></1><19>With inflection</19><20>If we type in the search box: <21>Caffé+Honza</21>(words are separated by a plus sign so there is no space between them)</20><22>Monitora will search for all articles that contain the phrase <23>Caffé Hoza</23> (ie. these words in sequence) in any shape. So there will be found articles containing the phrase <24>Caffé Honzy</24> (Monitora inflects the word), <25>CAFFÉ HONZA</25> (letter size does not matter), <26>Caffe Honza</26> (diacritics does not matter) or <27>Caffé-Honza</27> (there may be a separator between words, such as a comma or dash).</22><28>It is recommended to search for names of persons (Michael+Smith), names of companies and organizations (Ministry+ for+Local+Development) or phrases (monitoring+media).</28><29>Exact match</29><30>If we type in the search box: <31>\"Caffé Honza\"</31> (put the entire expression in quotation marks)</30><32>Monitora will search for all articles that contain the phrase <33>Caffé Honza</33>, but only in the specified form. So there will be found articles containing <34>Caffé Honza</34> or <35>CAFFÉ HONZA</35> (letter size does not matter).</32><36>Monitora will not search for those articles that contain inflected phrase <37>Caffé Honzy</37>, the phrase without diacritics <38>Caffe Honza</38> or phrase with separator (e.g. comma or dash) <39>Caffé-Honza</39>.</36><40>It is recommended to search for company and product names (\"Monitora Media\"), abbreviations (\"MFF UK\") or exact match (\"to be or not to be\") in this way.</40><41>Exact match with separator</41><42>When searching for an exact match, you must also include quotation marks around the separators that may appear between words - dashes, underscores, at-signs, etc. These include the following characters: & @ _ + - '#</42><43>If we type in the search box: <44>\"Caffé-Honza\"</44></43><45>Monitora will search for all articles that contain the <46>Caffé-Honza</46> phrase with a separator.</45><47>Monitora will not search for articles that contain only the words <48>Caffé Honza</48> without a separator or <49>Caffé&Honza</49> with a separator other than the one you specified.</47><50>Typical phrases where should not forget the separator are \"Ernst&Young\", \"<EMAIL>\", \"Mi+Te\" or \"X-Men\".</50><51>Exact match, including letter size</51><52>If we type in the search box: <53>\"!Caffé Honza\"</53> (put the word in quotation marks and after the first quotation mark an exclamation mark)</52><54>Monitora will search for all articles that contain the phrase <55>Caffé Honza</55>, but only in the specified form, including the letter size. It is the strictest option.</54><56>Monitora will not search for articles that contain, for example, only the phrase <57>caffé honza</57> written in lowercase letters.</56><58>Its recommended to search for company and product names (\"!Golden Spoon\") or abbreviations (\"!AV ČR\") this way.</58>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesDistance.tsx:18
msgid "help.search.distance.description"
msgstr "<0>Quick Overview</0><1><2><3><4>Entered expression</4><5>Searches for articles containing</5></3></2><6><7><8>walrus+penguin~5</8><9>the penguin stroked the walrus</9></7><10><11>toucan+walrus+penguin~10</11><12>toucans wanted to eat a penguin, but the walrus stepped in</12></10><13><14>\"walrus toucan\"~5</14><15>\"Walrus!\" Toucan said, flying away.</15></13><16><17>\"toucan walrus penguin\"~5</17><18>From left: penguin, walrus, toucan.</18></16></6></1><19>With inflection</19><20>If we type in the search box: <21>walrus+penguin~5</21> (we separate words with a plus sign, followed by a tilde and a number)</20><22>Monitora will search for all articles that contain the words <23>walrus</23> and <24>penguin</24> in any order and no more than 5 words apart. The words you type are automatically inflected and letter size or diacritic does not matter.</22><25>Its recommended to search for words related to each other and will be close to each other in the article text (company+Facebook~7).</25><26>Exact match</26><27>If we type in the search box: <28>\"walrus penguin\"~5</28> (put the words in quotation marks and after the second quotation mark we write the tilde and the number)</27><29>Monitora will search for all articles that contain the words <30>walrus</30> and <31>penguin</31> in any order and no more than 5 words apart. Both words are searched only in specified shape, i.e. not inflected and diacritic plays a role.</29>"

#: src/pages/newsroom/index.js:35
msgid "<0>Share press releases</0> and other external and internal communication with <1>Newsroom</1> and have an accurate overview of traffic directly in the application."
msgstr "<0>Share press releases</0> and other external and internal communication with <1>Newsroom</1> and have an accurate overview of traffic directly in the application."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:36
msgid "help.ave"
msgstr "<0>The AVE Coefficient (Advertising Value Equivalent) represents a financial appreciation of media activities. It is equivalent to what would be the amount of space gained by content converted into advertising space according to the price list of the medium.</0><1>The following variables are used for AVE machine calculation:</1><2><3>the unit price of advertising in the medium (e.g.: price per standard page in the press / 1s of broadcast news on TV or radio)</3><4>size of article in press / length of reportage on TV or radio</4><5>scope of information on the topic in the paper</5></2>"

#. js-lingui-explicit-id
#: src/components/layout/Header/MessageDirty/MessageDirty.js:12
msgid "message.dirty.description"
msgstr "<0>The displayed data may not match your current settings because one or more topics have been changed.</0><1>Please reload the page in a few minutes.</1>"

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:105
msgid "help.socialInteractions"
msgstr "<0>..::[TODO]::..</0> The number of social interactions everywhere..."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:59
msgid "help.socialInteractionsOnline"
msgstr "<0>The number of social interactions (likes, shares, comments) for online articles on Facebook.</0><1>Statistics are updated every 24 hours.</1>"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:11
msgid "missingBasics"
msgstr "<div>Thank you for submitting your post. We've conducted an initial review to ensure it meets our basic requirements.</div><br> <strong>Here's what we've found:</strong>"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:15
msgid "responseInfo"
msgstr ""
"<div>This process may take a few moments as we perform the following steps:</div>\n"
"<ol>\n"
"<li><strong>Initial scan:</strong> Quickly reviewing the overall structure and format of your content.</li>\n"
"<li><strong>In-depth analysis:</strong> Carefully examining the details, language, and context of your submission.</li>\n"
"<li><strong>Quality assessment:</strong> Evaluating various aspects such as clarity, coherence, and relevance.</li>\n"
"<li><strong>Error detection:</strong> Identifying any potential issues, inconsistencies, or areas for improvement.</li>\n"
"<li><strong>Optimization suggestions:</strong> Preparing recommendations to enhance your content if needed.</li>\n"
"</ol>\n"
"<div>Our AI is working diligently to provide you with accurate and helpful feedback. We appreciate your patience during this comprehensive analysis. The results will be available shortly.</div>"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:124
#: src/components/newsroom/components/PostsList/PostsList.js:98
msgid "<No title yet>"
msgstr "<No title yet>"

#: src/store/models/admin/customer/CustomerStore.js:220
msgid "<user already exists>"
msgstr "<user already exists>"

#: src/components/tariff/TariffLimits/TariffLimits.js:26
#: src/components/staff/admin/workspace/Workspace.js:359
msgid "30-day article limit"
msgstr "30-day article limit"

#: src/components/tariff/UsageTracker/UsageTracker.js:13
msgid "30-day limit"
msgstr "30-day limit"

#: src/components/tariff/TariffLimits/TariffLimits.js:63
#: src/components/staff/admin/workspace/Workspace.js:378
msgid "30-day limit on exported articles"
msgstr "30-day limit on exported articles"

#: src/components/tariff/TariffLimits/TariffLimits.js:224
#: src/components/staff/admin/workspace/Workspace.js:559
msgid "30-day limit on exported social media mentions"
msgstr "30-day limit on exported social media mentions"

#: src/components/tariff/TariffLimits/TariffLimits.js:241
#: src/components/staff/admin/workspace/Workspace.js:514
msgid "30-day limit on licensed article downloads"
msgstr "30-day limit on licensed article downloads"

#: src/components/staff/admin/workspace/Workspace.js:629
msgid "30-day limit on OCR pages"
msgstr "30-day limit on OCR pages"

#: src/components/tariff/TariffLimits/TariffLimits.js:203
#: src/components/staff/admin/workspace/Workspace.js:540
msgid "30-day limit on social media mentions"
msgstr "30-day limit on social media mentions"

#: src/components/staff/admin/workspace/Workspace.js:650
msgid "30-day limit on transcribed seconds"
msgstr "30-day limit on transcribed seconds"

#: src/components/staff/admin/workspace/Workspace.js:399
msgid "30-day limit on translated articles with Google Translate"
msgstr "30-day limit on translated articles with Google Translate"

#: src/components/medialist/forms/FormEditAuthor.js:755
msgid "About Author"
msgstr "About Author"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:132
msgid "Above avg."
msgstr "Above avg."

#: src/components/tariff/Permissions/Permissions.js:45
msgid "Access"
msgstr "Access"

#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:52
msgid "Access comprehensive articles via {appName}’s media monitoring, covering online, traditional, and social media content."
msgstr "Access comprehensive articles via {appName}’s media monitoring, covering online, traditional, and social media content."

#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:49
msgid "Access Full Articles via Media Monitoring"
msgstr "Access Full Articles via Media Monitoring"

#: src/components/dashboards/PageExpiredSharedDashboard/PageExpiredSharedDashboard.js:37
msgid "Access to this dashboard has expired."
msgstr "Access to this dashboard has expired."

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:22
msgid "Account info"
msgstr "Account info"

#: src/components/misc/Changelog/ChangelogTableRow.js:114
msgid "Account manager"
msgstr "Account manager"

#: src/components/staff/admin/customer/bio/CustomerBio.js:112
msgid "Account managers"
msgstr "Account managers"

#: src/components/settings/SettingsHeader/SettingsHeader.js:8
msgid "Account settings"
msgstr "Account settings"

#: src/components/settings/SettingsTheme/SettingsTheme.js:11
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:87
msgid "Account theme"
msgstr "Account theme"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:57
msgid "Activate"
msgstr "Activate"

#: src/components/staff/admin/user/User.js:235
msgid "Activated"
msgstr "Activated"

#: src/components/emailing/content/EmailingSettingsContent.js:76
#: src/components/emailing/content/EmailingCampaignsContent.tsx:32
msgid "Activated senders without verification:"
msgstr "Activated senders without verification:"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:173
#: src/components/staff/admin/user/User.js:138
#: src/components/staff/admin/customer/users/UsersTable.js:116
#: src/components/forms/dashboard/Search/SearchUsers.js:99
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:151
msgid "Active"
msgstr "Active"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:80
msgid "Active Article Language"
msgstr "Active Article Language"

#: src/components/staff/admin/workspace/ToggleActiveMedia.js:29
msgid "Active only"
msgstr "Active only"

#: src/components/reports/Content/ReportsList/FormToggleActive/FormToggleActive.js:34
msgid "Active report"
msgstr "Active report"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:93
msgid "Active Source Country"
msgstr "Active Source Country"

#: src/components/medialist/content/MedialistAuthorCreate.js:16
msgid "Activity Overview"
msgstr "Activity Overview"

#: src/components/topics/Content/TopicsList/FormAddKeyword/FormAddKeyword.tsx:54
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:591
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:61
#: src/components/medialist/forms/modules/FormArray.js:198
#: src/components/medialist/forms/modules/FormArray.js:220
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:38
#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:190
#: src/components/emailing/forms/FormEmailRecipients.js:120
#: src/components/ReusableFeed/FormAddArticle.tsx:42
msgid "Add"
msgstr "Add"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:178
msgid "Add a sender to activate Emailing."
msgstr "Add a sender to activate Emailing."

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:92
msgid "Add all to selection"
msgstr "Add all to selection"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:96
msgid "Add annotation"
msgstr "Add annotation"

#: src/helpers/modal/withModalAddArticle/withModalAddArticle.tsx:17
#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:95
#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:156
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:90
#: src/components/emailing/content/mediaCoverage/AddArticleToMediaCoverage.tsx:10
msgid "Add article"
msgstr "Add article"

#: src/components/emailing/content/mediaCoverage/AddArticleToMediaCoverage.tsx:13
msgid "Add article media coverage"
msgstr "Add article media coverage"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:237
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:219
msgid "Add article to topic"
msgstr "Add article to topic"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:537
#: src/components/newsroom/content/post/AttachmentsList.js:89
msgid "Add Attachment"
msgstr "Add Attachment"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:129
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:100
msgid "Add authors to list"
msgstr "Add authors to list"

#: src/components/newsroom/forms/FormNewsroomPost/CategoriesSelector.js:71
msgid "Add Category"
msgstr "Add Category"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:118
msgid "Add content"
msgstr "Add content"

#: src/components/dashboards/DashboardSelector/CreateDashboard.js:20
msgid "Add Dashboard"
msgstr "Add Dashboard"

#: src/components/reports/Content/ReportsList/AddDay.js:25
msgid "Add day"
msgstr "Add day"

#: src/components/staff/admin/workspace/Workspace.js:827
msgid "Add domains separated by a comma (domain1.com, domain2.com)"
msgstr "Add domains separated by a comma (domain1.com, domain2.com)"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:79
msgid "Add Gallery"
msgstr "Add Gallery"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:66
msgid "Add Image"
msgstr "Add Image"

#: src/components/topics/Content/TopicsList/FormAddKeyword/FormAddKeyword.tsx:45
msgid "Add Keyword"
msgstr "Add Keyword"

#: src/components/newsroom/forms/FormNewsroomSettings/LanguageSection.tsx:91
msgid "Add language variant"
msgstr "Add language variant"

#: src/components/newsroom/content/posts/ChooseTemplates.tsx:109
msgid "Add main message"
msgstr "Add main message"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:57
msgid "Add manually"
msgstr "Add manually"

#: src/components/newsroom/components/AiTools/AiGenerateContent.tsx:117
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:137
msgid "Add missing data"
msgstr "Add missing data"

#: src/components/emailing/components/EmailRecipientsList/RecipientsButton.tsx:37
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:164
msgid "Add Missing Info"
msgstr "Add Missing Info"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:64
msgid "Add new keypoint"
msgstr "Add new keypoint"

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:111
msgid "Add new mediatypes"
msgstr "Add new mediatypes"

#: src/components/newsroom/content/modules/CustomQuotes.tsx:84
msgid "Add new quote"
msgstr "Add new quote"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:52
msgid "Add new sender"
msgstr "Add new sender"

#: src/components/topics/Content/TopicsList/TopicsList.js:63
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:44
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:70
msgid "Add New Topic"
msgstr "Add New Topic"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:28
#: src/components/medialist/forms/FormEditAuthor.js:710
msgid "Add note"
msgstr "Add note"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:37
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:57
msgid "Add note to article"
msgstr "Add note to article"

#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:55
msgid "Add recipient"
msgstr "Add recipient"

#: src/components/emailing/modules/PreviewEmail/RecipientsIsEmpty.tsx:32
#: src/components/emailing/content/tabs/AddRecipients.tsx:78
msgid "Add recipients"
msgstr "Add recipients"

#: src/components/emailing/content/Signature.tsx:113
#: src/components/emailing/content/Signature.tsx:116
msgid "Add signature"
msgstr "Add signature"

#: src/components/emailing/forms/FormEmailRecipients.js:112
msgid "Add single authors, author’s lists or emails"
msgstr "Add single authors, author’s lists or emails"

#: src/components/newsroom/content/modules/CustomQuotes.tsx:35
msgid "Add specific quotes you want to include in your article, along with the name of the person being quoted. We will use these quotes exactly as provided.\""
msgstr "Add specific quotes you want to include in your article, along with the name of the person being quoted. We will use these quotes exactly as provided.\""

#: src/components/reports/Content/ReportsList/AddTime.js:27
msgid "Add time"
msgstr "Add time"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/ModalAddDashboardFooter.tsx:35
msgid "Add to Dashboard"
msgstr "Add to Dashboard"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:179
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:210
msgid "Add to export"
msgstr "Add to export"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:38
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:94
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:150
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:207
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:130
msgid "Add to filters"
msgstr "Add to filters"

#: src/components/medialist/forms/FormEditAuthor.js:670
#: src/components/medialist/content/withAddToBasketPopup.js:44
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorBaskets/AddAuthorToBasket.js:26
msgid "Add to list"
msgstr "Add to list"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:263
msgid "Add to next report"
msgstr "Add to next report"

#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:282
msgid "Add to report"
msgstr "Add to report"

#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:91
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Keywords.js:52
msgid "Add to search"
msgstr "Add to search"

#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:58
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:60
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:895
msgid "Add Topic"
msgstr "Add Topic"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:41
#: src/components/settings/SettingsUserManagement/AddUsers.tsx:23
msgid "Add users"
msgstr "Add users"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:42
msgid "Add users to workspace"
msgstr "Add users to workspace"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:101
msgid "Add Video"
msgstr "Add Video"

#: src/components/dashboards/Content.js:89
#: src/components/dashboards/Content.js:90
msgid "Add Widget"
msgstr "Add Widget"

#: src/store/models/ExportStore.js:316
#: src/store/models/monitoring/Inspector/Inspector.ts:449
msgid "Added to export."
msgstr "Added to export."

#: src/store/models/monitoring/Inspector/Inspector.ts:422
msgid "Added to next report."
msgstr "Added to next report."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:29
msgid "Additional settings"
msgstr "Additional settings"

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorAddress/AuthorAddress.js:8
msgid "Address"
msgstr "Address"

#: src/constants/analytics.js:143
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:42
#: src/components/misc/ActionsBar/View/ViewMenu.js:237
msgid "Adjusted Reach"
msgstr "Adjusted Reach"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:160
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:162
#: src/components/staff/admin/workspace/Workspace.js:162
#: src/components/staff/admin/user/getUserAttributes.js:9
#: src/components/staff/admin/user/User.js:88
#: src/components/reports/history/HistoryTable.js:452
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:60
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:62
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:406
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:413
#: src/components/medialist/forms/FormEditAuthor.js:396
#: src/components/medialist/forms/FormEditAuthor.js:542
#: src/components/layout/Header/UserMenu/UserMenu.tsx:200
#: src/app/components/monitoring-navigation.tsx:314
msgid "Admin"
msgstr "Admin"

#: src/components/reports/Content/ReportsList/ReportsForm.js:331
#: src/components/forms/dashboard/ExportResend/ExportResend.js:133
msgid "Advanced attachment settings"
msgstr "Advanced attachment settings"

#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:15
msgid "Advanced export settings"
msgstr "Advanced export settings"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:79
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:174
msgid "Advanced settings"
msgstr "Advanced settings"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:58
msgid "Advanced template settings"
msgstr "Advanced template settings"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:255
msgid "Advertising Value Equivalency"
msgstr "Advertising Value Equivalency"

#: src/constants/analytics.js:101
#: src/constants/analytics.js:621
#: src/constants/analytics.js:755
#: src/components/layout/AuthWrapper/constants/features.slides.js:191
msgid "Advertising Value Equivalent (AVE)"
msgstr "Advertising Value Equivalent (AVE)"

#: src/constants/analytics.js:99
msgid "Advertising Value Equivalent (AVE) by sentiment"
msgstr "Advertising Value Equivalent (AVE) by sentiment"

#: src/components/staff/admin/workspace/Workspace.js:920
#: src/components/settings/SettingsTariff/SettingsTariff.js:37
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:48
msgid "Agency media"
msgstr "Agency media"

#: src/components/newsroom/components/AiTools/AiCheckPostResult.tsx:88
msgid "AI check"
msgstr "AI check"

#: src/components/newsroom/components/AiTools/AiCheckLoadingInfo.tsx:28
msgid "AI Checkup information"
msgstr "AI Checkup information"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:218
msgid "Align Center"
msgstr "Align Center"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:230
msgid "Align Justify"
msgstr "Align Justify"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:212
msgid "Align Left"
msgstr "Align Left"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:224
msgid "Align Right"
msgstr "Align Right"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:83
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:37
#: src/components/reports/history/HistoryTable.js:86
#: src/components/reports/history/HistoryTable.js:96
#: src/components/reports/history/HistoryTable.js:331
#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:123
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:144
#: src/components/misc/portable/PortableExport/CounterTitle.js:8
#: src/components/misc/ActionsBar/Selector/Selector.js:51
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:146
#: src/components/analytics/AnalyticsContent.js:156
#: src/components/analytics/AnalyticsContent.js:166
msgid "All"
msgstr "All"

#: src/store/models/ExportStore.js:318
msgid "All articles are already in export."
msgstr "All articles are already in export."

#: src/components/exportList/Content/Content.tsx:97
#: src/app/components/monitoring-navigation.tsx:130
msgid "All articles will be removed from export."
msgstr "All articles will be removed from export."

#: src/components/misc/portable/PortableExport/CounterTitle.js:10
msgid "All except"
msgstr "All except"

#: src/components/layout/AuthWrapper/constants/features.slides.js:400
msgid "All features of the browser app are accessible on a mobile device. The app keeps you informed even when you are drinking a morning cup of coffee."
msgstr "All features of the browser app are accessible on a mobile device. The app keeps you informed even when you are drinking a morning cup of coffee."

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
msgid "all mediatypes for"
msgstr "all mediatypes for"

#: src/store/models/Megalist/MegalistFilter.js:49
#: src/components/topics/Content/TopicsList/MegalistToolbar/MediatypeFilterPopup.js:11
msgid "All Sources"
msgstr "All Sources"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitorsReports.js:40
msgid "All topics"
msgstr "All topics"

#: src/components/medialist/forms/FormEditAuthor.js:576
msgid "All unsaved changes will be lost. Do you really want to cancel the changes?"
msgstr "All unsaved changes will be lost. Do you really want to cancel the changes?"

#: src/components/staff/admin/workspace/Workspace.js:743
msgid "Allow adjusted reach (PL)"
msgstr "Allow adjusted reach (PL)"

#: src/components/staff/admin/workspace/Workspace.js:716
msgid "Allow automatic sentiment"
msgstr "Allow automatic sentiment"

#: src/components/staff/admin/workspace/Workspace.js:725
msgid "Allow automatic summarization"
msgstr "Allow automatic summarization"

#: src/components/staff/admin/workspace/Workspace.js:734
msgid "Allow AVE"
msgstr "Allow AVE"

#: src/components/staff/admin/workspace/Workspace.js:752
msgid "Allow AVE Coefficient (for media analysis)"
msgstr "Allow AVE Coefficient (for media analysis)"

#: src/components/staff/admin/workspace/Workspace.js:707
msgid "Allow custom logo"
msgstr "Allow custom logo"

#: src/components/staff/admin/workspace/Workspace.js:598
msgid "Allow english social media"
msgstr "Allow english social media"

#: src/components/staff/admin/workspace/Workspace.js:772
msgid "Allow forcing articles to email reports"
msgstr "Allow forcing articles to email reports"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:93
msgid "Allow search engines to index this blog (including inclusion of articles in media monitoring and analysis of online mentions)"
msgstr "Allow search engines to index this blog (including inclusion of articles in media monitoring and analysis of online mentions)"

#: src/components/staff/admin/workspace/Workspace.js:609
msgid "Allow users to create own articles"
msgstr "Allow users to create own articles"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:31
msgid "Allowing detailed tracking of distribution campaigns."
msgstr "Allowing detailed tracking of distribution campaigns."

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:63
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:86
#: src/components/staff/admin/customer/expenses/ExpenseTable.js:81
#: src/components/staff/admin/customer/expenses/DetailExpenseModal.js:38
msgid "Amount"
msgstr "Amount"

#: src/components/emailing/content/EmailingSettingsContent.js:32
msgid "An email sender record with this address already exists. Please check your existing records or try again."
msgstr "An email sender record with this address already exists. Please check your existing records or try again."

#: src/pages/_error.js:36
msgid "An error {statusCode} occurred on server"
msgstr "An error {statusCode} occurred on server"

#: src/pages/_error.js:37
msgid "An error occurred on client"
msgstr "An error occurred on client"

#: src/components/emailing/content/EmailingSettingsContent.js:17
msgid "An error occurred while authorizing our application to use the external service."
msgstr "An error occurred while authorizing our application to use the external service."

#: src/components/staff/admin/user/getUserAttributes.js:19
msgid "Analyst"
msgstr "Analyst"

#: src/store/models/dashboards/DashboardPreview.js:87
#: src/pages/analytcs.js:16
#: src/components/widgets/modules/stats/WidgetStats.js:67
#: src/components/widgets/modules/analytics/WidgetAnalytics.js:29
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:102
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:27
#: src/components/layout/AuthWrapper/constants/features.slides.js:165
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:16
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:16
#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:36
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:36
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormSubtype/FormSubtype.js:56
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:20
#: src/components/analytics/AnalyticsContent.js:105
#: src/app/components/monitoring-navigation.tsx:81
msgid "Analytics"
msgstr "Analytics"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:140
msgid "AND"
msgstr "AND"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderVideo/HeaderVideo.js:43
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderSocial/HeaderSocial.js:25
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:37
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:69
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationHeader.js:25
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationHeader.js:29
#: src/components/feed/InspectorToolbar/InspectorToolbar.js:134
msgid "Annotation"
msgstr "Annotation"

#: src/pages/user/yoy-analysis.js:34
msgid "Annual Media Analysis"
msgstr "Annual Media Analysis"

#: src/components/notifications/Content.js:28
msgid "App"
msgstr "App"

#: src/components/staff/admin/workspace/Workspace.js:932
#: src/components/settings/SettingsTariff/SettingsTariff.js:45
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:55
msgid "Application permissions"
msgstr "Application permissions"

#: src/components/settings/SettingsApplication/SettingsApplication.js:19
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:98
msgid "Application settings"
msgstr "Application settings"

#: src/components/newsroom/components/AiTools/AiGenerateContent.tsx:138
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:161
msgid "Apply"
msgstr "Apply"

#: src/components/medialist/content/FeedMedialist/FeedMedialistItem.js:229
msgid "archive"
msgstr "archive"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:22
msgid "Archive"
msgstr "Archive"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:129
msgid "Are you ready to send the email?"
msgstr "Are you ready to send the email?"

#. placeholder {0}: item.filename
#. placeholder {0}: file.name
#: src/components/newsroom/content/post/AttachmentsList.js:66
#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:42
msgid "Are you sure you want to delete {0}?"
msgstr "Are you sure you want to delete {0}?"

#: src/components/emailing/content/SignaturePopup.tsx:36
msgid "Are you sure you want to delete signature?"
msgstr "Are you sure you want to delete signature?"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:397
msgid "Are you sure you want to delete this blog post?"
msgstr "Are you sure you want to delete this blog post?"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:87
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:100
msgid "Are you sure you want to delete this email?"
msgstr "Are you sure you want to delete this email?"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:122
msgid "Are you sure you want to delete this sender?"
msgstr "Are you sure you want to delete this sender?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:662
msgid "Are you sure you want to delete your Newsroom? This action will delete all articles and settings."
msgstr "Are you sure you want to delete your Newsroom? This action will delete all articles and settings."

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:299
msgid "Are you sure you want to publish the changes?"
msgstr "Are you sure you want to publish the changes?"

#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:110
msgid "Are you sure you want to remove all recipients from this report?"
msgstr "Are you sure you want to remove all recipients from this report?"

#: src/components/staff/admin/workspace/UsersTable/RemoveUsers.tsx:27
msgid "Are you sure you want to remove these users from the workspace?"
msgstr "Are you sure you want to remove these users from the workspace?"

#: src/components/newsroom/content/posts/NewsroomPosts.js:206
msgid "Are you sure you want to remove this article?"
msgstr "Are you sure you want to remove this article?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:679
msgid "Are you sure you want to remove this Newsroom?"
msgstr "Are you sure you want to remove this Newsroom?"

#: src/components/staff/admin/workspace/UsersTable/RemoveUser.js:30
#: src/components/staff/admin/user/WorkspacesTable.js:143
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:400
msgid "Are you sure you want to remove this user from the workspace?"
msgstr "Are you sure you want to remove this user from the workspace?"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:300
msgid "Are you sure you want to set this post to draft?"
msgstr "Are you sure you want to set this post to draft?"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:205
msgid "area"
msgstr "area"

#: src/components/OurChart/OurChartAdvanced.js:148
msgid "Area"
msgstr "Area"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:72
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:56
#: src/components/misc/MntrEditor/extensions/ExtensionArticle.tsx:33
msgid "Article"
msgstr "Article"

#. placeholder {0}: item.title
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:364
msgid "Article '<0>{0}</0>' will be removed."
msgstr "Article '<0>{0}</0>' will be removed."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:575
msgid "Article '<0>{title}</0>' will be removed."
msgstr "Article '<0>{title}</0>' will be removed."

#: src/components/misc/ActionsBar/View/ViewMenu.js:140
msgid "Article Area"
msgstr "Article Area"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:53
msgid "Article can still be attached later"
msgstr "Article can still be attached later"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:34
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:48
msgid "Article clipping"
msgstr "Article clipping"

#: src/store/models/OurChart.js:531
#: src/store/models/OurChart.js:563
#: src/store/models/OurChart.js:788
#: src/constants/stats.ts:11
#: src/constants/analytics.js:26
#: src/constants/analytics.js:1097
#: src/components/widgets/modules/stats/WidgetStats.js:203
#: src/components/widgets/modules/stats/WidgetStats.js:216
#: src/components/widgets/modules/stats/WidgetStats.js:229
#: src/components/monitoring/FeedChart/FeedChart.js:28
#: src/components/medialist/content/MedialistInspector/AuthorChart/AuthorChart.js:65
msgid "Article count"
msgstr "Article count"

#: src/components/topics/Content/TopicsList/KeywordStatsTable.js:23
msgid "Article count for the last 30 days"
msgstr "Article count for the last 30 days"

#: src/constants/analytics.js:192
msgid "Article count vs. GRP vs. AVE"
msgstr "Article count vs. GRP vs. AVE"

#: src/store/models/monitoring/Inspector/Inspector.ts:778
msgid "Article has been copied to the clipboard."
msgstr "Article has been copied to the clipboard."

#: src/store/models/monitoring/WorkspaceArticles.js:219
msgid "Article has been removed."
msgstr "Article has been removed."

#: src/store/models/monitoring/WorkspaceArticles.js:193
msgid "Article has been updated."
msgstr "Article has been updated."

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:215
msgid "Article has no annotations assigned. Select the text to add."
msgstr "Article has no annotations assigned. Select the text to add."

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:63
msgid "Article history"
msgstr "Article history"

#: src/store/models/monitoring/Inspector/Inspector.ts:451
msgid "Article is already in export."
msgstr "Article is already in export."

#: src/components/article/Content.js:17
msgid "Article link has expired"
msgstr "Article link has expired"

#: src/components/layout/MntrActiveFilters/modules/ArticleMentions.js:12
msgid "Article mentions"
msgstr "Article mentions"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:146
msgid "Article numbering"
msgstr "Article numbering"

#: src/store/models/admin/customer/CustomerStore.js:303
msgid "Article recreation started successfully."
msgstr "Article recreation started successfully."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:181
msgid "Article screenshot"
msgstr "Article screenshot"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:37
#: src/components/layout/MntrActiveFilters/modules/EmptyTags.js:23
msgid "Article Tags"
msgstr "Article Tags"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:441
msgid "Article Text"
msgstr "Article Text"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:83
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:129
msgid "Article transcript"
msgstr "Article transcript"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:101
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:381
msgid "Article Type"
msgstr "Article Type"

#: src/components/ReusableFeed/FormAddArticle.tsx:30
msgid "Article URL"
msgstr "Article URL"

#: src/store/models/monitoring/Inspector/Inspector.ts:751
#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:58
#: src/components/monitoring/FeedList/FeedListItem/FeedListItem.js:138
msgid "Article URL has been copied to the clipboard. Without a login, it will be accessible for 30 days."
msgstr "Article URL has been copied to the clipboard. Without a login, it will be accessible for 30 days."

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:70
msgid "Article view"
msgstr "Article view"

#: src/store/models/monitoring/Inspector/Inspector.ts:551
msgid "Article was reported"
msgstr "Article was reported"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:278
msgid "Article was successfully published on your Newsroom page"
msgstr "Article was successfully published on your Newsroom page"

#: src/store/models/dashboards/DashboardPreview.js:75
#: src/components/trash/Content.js:55
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:95
#: src/components/newsroom/content/posts/NewsroomPosts.js:80
#: src/components/medialist/constants/medialist.tabNavigation.js:27
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:20
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:18
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:109
#: src/components/layout/MntrActiveFilters/modules/MedialistArticles.js:18
#: src/components/forms/dashboard/Search/SearchNewsroom.js:31
#: src/components/exportList/History/HistoryTable/HistoryTable.js:63
#: src/components/exportList/Content/HeadingExport/HeadingExport.js:36
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:30
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:30
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:58
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:17
#: src/app/components/monitoring-navigation.tsx:71
msgid "Articles"
msgstr "Articles"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:525
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:808
msgid "Articles updated successfully."
msgstr "Articles updated successfully."

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:38
msgid "Assess competitors and trends to refine your strategy."
msgstr "Assess competitors and trends to refine your strategy."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:28
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:64
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:260
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:361
#: src/components/monitoring/FeedActionsBar/withAddTagPopup/AddTagPopupContent.js:16
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:50
msgid "Assign tag"
msgstr "Assign tag"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:34
msgid "Assign tag to article"
msgstr "Assign tag to article"

#: src/components/medialist/forms/FormEditAuthor.js:626
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorTags/AddAuthorTag.js:19
msgid "Assign tag to author"
msgstr "Assign tag to author"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:25
msgid "Assistant creates a draft of the email content based on your specific needs"
msgstr "Assistant creates a draft of the email content based on your specific needs"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:192
msgid "Attached articles"
msgstr "Attached articles"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:238
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:52
msgid "Attachment"
msgstr "Attachment"

#: src/components/reports/history/HistoryTable.js:173
#: src/components/newsroom/content/post/AttachmentsList.js:81
msgid "Attachments"
msgstr "Attachments"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:68
msgid "Audio"
msgstr "Audio"

#: src/constants/analytics.js:1094
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:485
#: src/components/newsroom/content/modules/CustomQuotes.tsx:69
#: src/components/monitoring/Inspector/InspectorMonitora/AuthorsList/AuthorsList.js:21
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:258
msgid "Author"
msgstr "Author"

#: src/components/medialist/constants/medialist.tabNavigation.js:12
msgid "Author Detail"
msgstr "Author Detail"

#: src/components/medialist/content/AuthorBasketsMenu.js:26
#: src/components/medialist/content/AuthorBasketSelectorButton.js:8
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:19
msgid "Author Lists"
msgstr "Author Lists"

#: src/pages/authors/index.js:89
msgid "Author tags"
msgstr "Author tags"

#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:874
msgid "Author Tags"
msgstr "Author Tags"

#: src/components/medialist/forms/FormEditAuthor.js:233
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:122
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:67
msgid "Author type"
msgstr "Author type"

#: src/store/models/authors/AuthorsStore.js:1079
msgid "Author was deleted."
msgstr "Author was deleted."

#: src/store/models/authors/AuthorsStore.js:1168
msgid "Author was reported."
msgstr "Author was reported."

#: src/components/medialist/forms/FormEditAuthor.js:316
#: src/components/medialist/forms/FormEditAuthor.js:481
msgid "Author will be deleted."
msgstr "Author will be deleted."

#: src/components/layout/MntrFiltersBar/forms/FormAuthor/FormAuthor.tsx:18
msgid "Author's name"
msgstr "Author's name"

#: src/components/medialist/forms/FormEditAuthor.js:997
msgid "Author's shortname"
msgstr "Author's shortname"

#: src/components/medialist/forms/FormEditAuthor.js:834
#: src/components/medialist/forms/FormEditAuthor.js:992
msgid "Author's shortnames"
msgstr "Author's shortnames"

#: src/components/monitoring/Inspector/InspectorMonitora/AuthorsList/AuthorsList.js:21
#: src/components/medialist/content/MedialistDashboard.js:82
#: src/components/medialist/content/MedialistDashboard.js:115
#: src/components/layout/Sidebar/modules/AuthorsNavigation/AuthorsNavigation.js:20
#: src/components/forms/dashboard/Search/SearchAuthors.js:39
#: src/components/emailing/forms/FormEmailRecipients.js:131
#: src/components/emailing/components/EmailRecipientsList/RenderAllRecipients.tsx:40
msgid "Authors"
msgstr "Authors"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:560
#: src/store/models/authors/AuthorsStore.js:535
msgid "Authors added."
msgstr "Authors added."

#: src/components/emailing/content/tabs/AddRecipients.tsx:69
msgid "Authors lists"
msgstr "Authors lists"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:598
#: src/store/models/authors/AuthorsStore.js:603
msgid "Authors removed."
msgstr "Authors removed."

#: src/store/models/authors/AuthorsStore.js:664
msgid "Authors updated successfully."
msgstr "Authors updated successfully."

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:94
msgid "Authors with types “agency”, “publisher” or “editorial office” can’t use merge tags  *|LAST_NAME|*,  *|VOKATIV_L|*. If you want to apply these merge tags to the author, change their type to “author” or “blogger” and add the last name."
msgstr "Authors with types “agency”, “publisher” or “editorial office” can’t use merge tags  *|LAST_NAME|*,  *|VOKATIV_L|*. If you want to apply these merge tags to the author, change their type to “author” or “blogger” and add the last name."

#: src/components/staff/admin/user/User.js:62
msgid "Autologin link"
msgstr "Autologin link"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:178
msgid "Automatic summary"
msgstr "Automatic summary"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:270
#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:74
msgid "Automatic transcript"
msgstr "Automatic transcript"

#: src/components/tariff/TariffLimits/TariffLimits.js:99
msgid "Automatic translations 30-day limit"
msgstr "Automatic translations 30-day limit"

#: src/constants/stats.ts:6
#: src/constants/analytics.js:994
#: src/components/widgets/modules/stats/WidgetStats.js:154
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:387
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:252
#: src/components/misc/ActionsBar/View/ViewMenu.js:203
msgid "AVE"
msgstr "AVE"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:237
msgid "AVE and sentiment"
msgstr "AVE and sentiment"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:39
msgid "AVE Coefficient"
msgstr "AVE Coefficient"

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:23
msgid "formatNumber.B"
msgstr "B"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:165
#: src/components/notifications/AppNotifications/AppNotifications.js:18
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:197
#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepTitleAndCommunicationPlan.tsx:106
#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:82
#: src/components/newsroom/content/posts/ChooseTemplates.tsx:98
#: src/components/newsroom/components/NewsroomHeading/NewsroomHeading.js:20
#: src/components/misc/ActionsBar/View/ViewMenu.js:40
#: src/components/misc/ActionsBar/View/ViewMenu.js:260
#: src/components/medialist/content/MedialistActionsBar/FormTransformContacts.tsx:30
#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:73
#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:97
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:49
#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:31
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:145
#: src/components/layout/Header/AppNotifications/AppNotifications.js:110
#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:163
#: src/components/emailing/content/CreateEmailContent.js:296
msgid "Back"
msgstr "Back"

#: src/components/page/auth/ResetPassword/ResetPasswordFooter.js:29
msgid "Back to Log In"
msgstr "Back to Log In"

#: src/components/page/auth/ResetPassword/ResetPasswordFooter.js:19
msgid "Back to settings"
msgstr "Back to settings"

#: src/pages/_error.js:61
#: src/pages/404.js:29
#: src/pages/user/yoy-analysis.js:79
#: src/pages/user/reactivate-24.js:79
#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:28
#: src/components/page/auth/UserInactive/UserInactive.js:25
#: src/components/page/auth/Expired/Expired.js:119
#: src/components/layout/ErrorCustom/ErrorCustom.js:13
#: src/app/not-found-content.tsx:35
msgid "Back to the main page"
msgstr "Back to the main page"

#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:68
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:107
msgid "Background Color"
msgstr "Background Color"

#: src/constants/analytics.js:529
#: src/constants/analytics.js:547
#: src/constants/analytics.js:565
#: src/constants/analytics.js:584
#: src/constants/analytics.js:602
#: src/constants/analytics.js:620
#: src/constants/analytics.js:638
#: src/constants/analytics.js:658
#: src/components/OurChart/OurChartAdvanced.js:141
msgid "Bar"
msgstr "Bar"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:46
msgid "Basic settings"
msgstr "Basic settings"

#: src/components/layout/MntrActiveFilters/modules/Paywalled.js:6
msgid "Behind paywall"
msgstr "Behind paywall"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:141
msgid "Below avg."
msgstr "Below avg."

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:81
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:220
msgid "best"
msgstr "best"

#: src/constants/analytics/primeScoreCharts.ts:95
msgid "Best PRIMe mediatypes"
msgstr "Best PRIMe mediatypes"

#: src/components/staff/admin/customer/bio/CustomerBio.js:106
msgid "Billing email"
msgstr "Billing email"

#: src/components/medialist/forms/FormEditAuthor.js:855
#: src/components/medialist/forms/FormEditAuthor.js:1023
msgid "Bio"
msgstr "Bio"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:273
msgid "Blockquote"
msgstr "Blockquote"

#: src/store/models/newsroom/blogs/posts/NewsroomPostsStoreArrItem.ts:106
msgid "Blog post was successfully deleted."
msgstr "Blog post was successfully deleted."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:123
msgid "Bold"
msgstr "Bold"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:158
msgid "Brackets"
msgstr "Brackets"

#: src/pages/brand-tracking.tsx:29
#: src/components/layout/Sidebar/SidebarNavigation.tsx:169
#: src/app/components/monitoring-navigation.tsx:301
msgid "Brand Tracking"
msgstr "Brand Tracking"

#: src/components/monitoring/Inspector/InspectorMonitora/KeywordsPagination/KeywordsPagination.js:256
msgid "Browse keywords"
msgstr "Browse keywords"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:257
msgid "Bullet list"
msgstr "Bullet list"

#: src/components/newsroom/components/PostsList/PostsList.js:162
msgid "By"
msgstr "By"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:48
msgid "by source"
msgstr "by source"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:152
msgid "By submitting the form, you agree to our <0>terms</0>."
msgstr "By submitting the form, you agree to our <0>terms</0>."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:61
msgid "Call to Action (CTA):"
msgstr "Call to Action (CTA):"

#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:136
msgid "Campaign"
msgstr "Campaign"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:54
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:92
msgid "Campaign will be removed"
msgstr "Campaign will be removed"

#: src/components/forms/dashboard/Search/SearchEmailingCampaigns.js:26
#: src/components/emailing/sidebar/EmailingSidebarDashboard.js:17
#: src/components/emailing/content/EmailingCampaignsContent.tsx:49
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:44
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:57
msgid "Campaigns"
msgstr "Campaigns"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:139
msgid "Can unsubscribe"
msgstr "Can unsubscribe"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:76
msgid "Can't find an article in your feed? Enter a link to the article you are looking for and select a topic."
msgstr "Can't find an article in your feed? Enter a link to the article you are looking for and select a topic."

#: src/components/reports/Content/ReportsList/ReportsForm.js:350
#: src/components/misc/VideoPlayer/getCropAction.js:7
#: src/components/misc/MntrForm/MntrForm.tsx:516
#: src/components/medialist/forms/FormEditAuthor.js:559
#: src/components/medialist/forms/FormEditAuthor.js:571
#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:86
msgid "Cancel"
msgstr "Cancel"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:273
msgid "Cancel choice"
msgstr "Cancel choice"

#: src/components/misc/Changelog/ChangelogTableRow.js:247
msgid "Cancel revert"
msgstr "Cancel revert"

#: src/components/misc/UploadWatcher/UploadWatcher.js:40
msgid "Cancel Upload"
msgstr "Cancel Upload"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:35
msgid "Cannot delete articles, run manual sentiment, create/edit topics or reports, change account settings, delete TV/radio stories, or edit CRM info."
msgstr "Cannot delete articles, run manual sentiment, create/edit topics or reports, change account settings, delete TV/radio stories, or edit CRM info."

#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/MediaDetail.js:120
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:279
msgid "Captured on the screen"
msgstr "Captured on the screen"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:67
msgid "Categories"
msgstr "Categories"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:29
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNewsroomCategory.js:24
#: src/components/layout/MntrActiveFilters/modules/NewsroomCategory.js:21
msgid "Category"
msgstr "Category"

#. placeholder {0}: item.name
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:146
msgid "Category <0>{0}</0> will be removed."
msgstr "Category <0>{0}</0> will be removed."

#: src/components/newsroom/forms/FormCreateCategory/FormCreateCategory.js:25
msgid "Category name"
msgstr "Category name"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:47
msgid "Change email"
msgstr "Change email"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:27
#: src/components/settings/SettingsUserManagement/UpdateRole.tsx:20
msgid "Change role"
msgstr "Change role"

#: src/components/misc/Changelog/ChangelogTable.js:36
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:394
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChangeType.js:24
msgid "Change Type"
msgstr "Change Type"

#: src/pages/topics/[topicId]/changelog.js:13
#: src/pages/staff/admin/workspaces/[workspaceId]/changelog.js:12
#: src/pages/reports/[reportId]/changelog.js:13
#: src/components/staff/admin/workspace/Workspace.js:147
msgid "Changelog"
msgstr "Changelog"

#: src/store/models/admin/customer/CustomerStore.js:163
#: src/store/models/admin/customer/CustomerStore.js:177
#: src/store/models/admin/customer/CustomerStore.js:186
#: src/store/models/admin/customer/CustomerStore.js:231
#: src/store/models/admin/customer/CustomerStore.js:258
#: src/store/models/admin/customer/CustomerStore.js:286
msgid "Changes successfully saved."
msgstr "Changes successfully saved."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:27
msgid "Channel"
msgstr "Channel"

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:241
msgid "Channels"
msgstr "Channels"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:169
msgid "Chart"
msgstr "Chart"

#: src/components/OurChart/OurChartAdvanced.js:128
msgid "Chart Settings"
msgstr "Chart Settings"

#: src/components/OurChart/OurChartAdvanced.js:137
msgid "Chart Type"
msgstr "Chart Type"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:238
msgid "Check"
msgstr "Check"

#: src/components/emailing/content/tabs/AddRecipients.tsx:80
msgid "Choose authors list or tag:"
msgstr "Choose authors list or tag:"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:35
msgid "Choose how to add/edit your signature"
msgstr "Choose how to add/edit your signature"

#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:44
msgid "Clear Color"
msgstr "Clear Color"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:139
msgid "Clear formatting"
msgstr "Clear formatting"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/EntityInfoBox.js:220
msgid "click to open the detail"
msgstr "click to open the detail"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:342
msgid "Click to see options"
msgstr "Click to see options"

#: src/components/forms/inspector/FormMediaEditor.js:124
msgid "Clip duration"
msgstr "Clip duration"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:127
#: src/components/reports/history/Compose.js:36
#: src/components/misc/portable/PortableResend/PortableResend.js:59
#: src/components/misc/portable/PortableResend/PortableResend.js:99
#: src/components/misc/portable/PortableExport/PortableExport.js:55
#: src/components/misc/portable/PortableExport/PortableExport.js:95
#: src/components/misc/MntrHint/MntrHint.js:77
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:111
#: src/components/misc/Mntr/ButtonGroup.tsx:51
msgid "Close"
msgstr "Close"

#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:129
msgid "Collapse"
msgstr "Collapse"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:53
msgid "Color palette"
msgstr "Color palette"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:208
msgid "Colors"
msgstr "Colors"

#: src/constants/analytics/primeScoreCharts.ts:115
#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:60
msgid "Column"
msgstr "Column"

#: src/components/newsroom/content/posts/NewsroomPosts.js:126
msgid "Compact"
msgstr "Compact"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:66
msgid "Company"
msgstr "Company"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:93
msgid "Company (Name or CRN)"
msgstr "Company (Name or CRN)"

#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:79
msgid "Company detail"
msgstr "Company detail"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:72
#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:26
msgid "Company info"
msgstr "Company info"

#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:893
msgid "Compare Topic"
msgstr "Compare Topic"

#: src/pages/sign-up-completion.tsx:30
#: src/pages/staff/sign-up-completion.js:26
msgid "Completion"
msgstr "Completion"

#: src/components/emailing/forms/FormSenderSettings.js:260
msgid "Configuring DKIM (DomainKeys Identified Mail) enhances the integrity and authenticity of your emails, reducing the likelihood of them being marked as spam:"
msgstr "Configuring DKIM (DomainKeys Identified Mail) enhances the integrity and authenticity of your emails, reducing the likelihood of them being marked as spam:"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:51
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:319
msgid "Confirm"
msgstr "Confirm"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:93
msgid "Confirm new password"
msgstr "Confirm new password"

#: src/helpers/store/apiClient.js:240
msgid "Connection with the server was lost. Please try again."
msgstr "Connection with the server was lost. Please try again."

#: src/components/layout/Sidebar/SidebarNavigation.tsx:263
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:160
msgid "Contact"
msgstr "Contact"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:123
msgid "Contact information"
msgstr "Contact information"

#: src/components/medialist/forms/FormEditAuthor.js:761
#: src/components/medialist/content/MedialistDashboard.js:88
#: src/components/medialist/content/MedialistDashboard.js:121
#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:56
msgid "Contacts"
msgstr "Contacts"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:178
msgid "Contacts cannot be imported from this file"
msgstr "Contacts cannot be imported from this file"

#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:19
msgid "Contacts import in progress"
msgstr "Contacts import in progress"

#: src/pages/user/reset-password/new.tsx:48
#: src/pages/user/reset-password/index.tsx:29
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:167
#: src/components/page/auth/SignUp/SignUp.js:58
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:610
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:246
#: src/components/newsroom/content/newsroom/NewsroomCreateBlog.js:19
#: src/components/misc/Wizard/WizardChoice.tsx:150
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:143
#: src/components/medialist/forms/FormEditAuthor.js:300
#: src/components/medialist/forms/FormEditAuthor.js:318
#: src/components/medialist/forms/FormEditAuthor.js:464
#: src/components/medialist/forms/FormEditAuthor.js:483
#: src/components/medialist/forms/FormEditAuthor.js:578
#: src/components/emailing/forms/FormAddCampaign.tsx:20
#: src/components/emailing/content/CreateEmailContent.js:354
msgid "Continue"
msgstr "Continue"

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:58
msgid "Continue to import"
msgstr "Continue to import"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:229
#: src/components/medialist/forms/FormEditAuthor.js:116
#: src/components/medialist/forms/FormEditAuthor.js:120
#: src/components/medialist/forms/FormEditAuthor.js:872
#: src/components/medialist/forms/FormEditAuthor.js:1048
#: src/components/emailing/forms/FormSenderSettings.js:69
msgid "Copied to the clipboard."
msgstr "Copied to the clipboard."

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:64
msgid "Copy article to clipboard"
msgstr "Copy article to clipboard"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:12
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:13
#: src/components/exportList/History/HistoryTable/HistoryTable.js:115
msgid "Copy link to clipboard"
msgstr "Copy link to clipboard"

#: src/components/staff/admin/workspace/UsersTable/CopyPassword.js:27
msgid "Copy password"
msgstr "Copy password"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:117
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:76
msgid "Copy public URL to clipboard"
msgstr "Copy public URL to clipboard"

#: src/components/reports/history/HistoryTable.js:436
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:96
msgid "Copy recipients to clipboard"
msgstr "Copy recipients to clipboard"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:34
#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:66
msgid "Copy share link"
msgstr "Copy share link"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:70
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:82
msgid "Copy to another campaign"
msgstr "Copy to another campaign"

#: src/helpers/modal/withModalEmailPreview.js:102
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:585
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:644
#: src/components/medialist/forms/FormEditAuthor.js:773
#: src/components/medialist/forms/FormEditAuthor.js:801
#: src/components/medialist/forms/FormEditAuthor.js:869
#: src/components/medialist/forms/FormEditAuthor.js:1045
#: src/components/emailing/forms/FormSenderSettings.js:65
msgid "Copy to clipboard"
msgstr "Copy to clipboard"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:112
msgid "Copy to Dashboard"
msgstr "Copy to Dashboard"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:238
#: src/components/misc/ActionsBar/View/ViewMenu.js:187
msgid "Cost per Point (CCP) - how much does one second of advertising cost for each GRP point (AVE = CPP * GRP * duration)"
msgstr "Cost per Point (CCP) - how much does one second of advertising cost for each GRP point (AVE = CPP * GRP * duration)"

#: src/constants/analytics.js:864
msgid "Countries"
msgstr "Countries"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:124
msgid "countries with enabled mediatype"
msgstr "countries with enabled mediatype"

#: src/constants/analytics.js:851
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:74
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:360
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:76
#: src/components/medialist/forms/FormEditAuthor.js:246
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:271
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:291
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:80
msgid "Country"
msgstr "Country"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:63
msgid "Cover page"
msgstr "Cover page"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:233
#: src/components/misc/ActionsBar/View/ViewMenu.js:182
msgid "CPP"
msgstr "CPP"

#: src/components/medialist/forms/FormEditAuthor.js:601
msgid "Create"
msgstr "Create"

#: src/components/page/auth/Login/Login.tsx:75
msgid "Create an account for free"
msgstr "Create an account for free"

#: src/components/emailing/content/NewEmailWizardButton.tsx:15
msgid "Create an email"
msgstr "Create an email"

#: src/components/medialist/content/withAddToBasketPopup.js:52
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorBaskets/AddAuthorToBasket.js:59
msgid "Create and add to new list"
msgstr "Create and add to new list"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:68
#: src/components/monitoring/FeedActionsBar/withAddTagPopup/AddTagPopupContent.js:49
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorTags/AddAuthorTag.js:51
msgid "Create and assign new tag"
msgstr "Create and assign new tag"

#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:48
#: src/app/components/monitoring-navigation.tsx:213
msgid "Create article"
msgstr "Create article"

#: src/pages/workspace-articles.js:64
#: src/components/monitoring/WorkspaceArticles/withWorkspaceArticleModal.js:9
msgid "Create Article"
msgstr "Create Article"

#: src/components/medialist/content/MedialistDashboard.js:144
#: src/components/medialist/content/AuthorBasketsMenu.js:51
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:249
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:250
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:168
msgid "Create author"
msgstr "Create author"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:76
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:77
msgid "Create Folder"
msgstr "Create Folder"

#: src/components/medialist/content/AuthorBasketsMenu.js:151
msgid "Create new list"
msgstr "Create new list"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:69
msgid "Create new Newsroom"
msgstr "Create new Newsroom"

#: src/components/newsroom/content/newsroom/NewsroomCreateBlog.js:18
msgid "Create Newsroom"
msgstr "Create Newsroom"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:240
msgid "Create Own Article"
msgstr "Create Own Article"

#: src/components/newsroom/content/posts/NewPostWizardButton.tsx:16
msgid "Create post"
msgstr "Create post"

#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:69
msgid "Create Report"
msgstr "Create Report"

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:24
#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:26
msgid "Create workspace"
msgstr "Create workspace"

#: src/pages/authors/index.js:61
msgid "Create your own <0>lists</0> and <1>tags</1>"
msgstr "Create your own <0>lists</0> and <1>tags</1>"

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:44
msgid "Created"
msgstr "Created"

#: src/store/models/dashboards/DashboardPreview.js:134
#: src/pages/crisis-communication.js:10
#: src/pages/crisis-communication-story/[articleId].js:10
#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:19
#: src/components/layout/Sidebar/SidebarNavigation.tsx:129
#: src/components/layout/Sidebar/SidebarNavigation.tsx:255
#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:26
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:37
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:23
#: src/app/components/monitoring-navigation.tsx:258
msgid "Crisis communication"
msgstr "Crisis communication"

#: src/store/models/monitoring/Inspector/EntityKnowledgeBaseStore/EntityKnowledgeBaseStore.js:23
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:55
msgid "CRN"
msgstr "CRN"

#. placeholder {0}: option.reg_no
#. placeholder {0}: data.reg_no
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:117
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorAddress/AuthorAddress.js:14
msgid "CRN: {0}"
msgstr "CRN: {0}"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:124
#: src/components/misc/MntrEditor/modals/withModalCTAButton.js:8
#: src/components/misc/MntrEditor/extensions/ExtensionCTAButton.js:23
msgid "CTA Button"
msgstr "CTA Button"

#: src/components/staff/admin/user/User.js:286
#: src/components/settings/SettingsApplication/SettingsApplication.js:42
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:408
msgid "Currency"
msgstr "Currency"

#: src/components/settings/SettingsApplication/SettingsApplication.js:33
msgid "Currency in which to calculate AVE."
msgstr "Currency in which to calculate AVE."

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:81
msgid "Current password"
msgstr "Current password"

#: src/components/newsroom/modals/withModalCustomAiRewrite.tsx:11
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:645
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:65
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:49
#: src/components/emailing/content/CreateEmailContent.js:548
msgid "Custom"
msgstr "Custom"

#: src/components/emailing/content/promo/PromoEmailing.js:22
msgid "Custom branding"
msgstr "Custom branding"

#: src/components/settings/SettingsTheme/SettingsThemePreview/LogoColorPicker/LogoColorPicker.js:70
#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:65
msgid "Custom color"
msgstr "Custom color"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:171
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:135
#: src/components/misc/MntrEditor/modals/withModalHtmlCode.js:17
#: src/components/misc/MntrEditor/extensions/ExtensionHtmlCode.js:23
msgid "Custom HTML Code"
msgstr "Custom HTML Code"

#: src/components/newsroom/modals/withModalCustomAiRewrite.tsx:24
msgid "Custom insruction"
msgstr "Custom insruction"

#: src/components/settings/SettingsLogo/SettingsLogo.js:63
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:78
msgid "Custom logo"
msgstr "Custom logo"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:176
msgid "Custom meta/script/style for <head> section"
msgstr "Custom meta/script/style for <head> section"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:83
msgid "Custom selection"
msgstr "Custom selection"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:353
msgid "Custom Slug"
msgstr "Custom Slug"

#: src/components/reports/Content/ReportsList/ReportsForm.js:306
msgid "Custom subject (optional)"
msgstr "Custom subject (optional)"

#: src/pages/staff/admin/customers/index.js:12
#: src/components/staff/admin/customers/Customers.js:20
#: src/components/layout/Sidebar/SidebarNavigation.tsx:188
#: src/components/layout/Header/UserMenu/UserMenu.tsx:219
#: src/components/forms/dashboard/Search/SearchCustomers.js:54
#: src/app/components/monitoring-navigation.tsx:317
msgid "Customers"
msgstr "Customers"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:313
msgid "Customization"
msgstr "Customization"

#: src/components/misc/VideoPlayer/getCropAction.js:7
msgid "Cut clip"
msgstr "Cut clip"

#: src/components/forms/inspector/FormMediaEditor.js:117
msgid "Cut from"
msgstr "Cut from"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:433
msgid "Cut media"
msgstr "Cut media"

#: src/components/forms/inspector/FormMediaEditor.js:120
msgid "Cut to"
msgstr "Cut to"

#: src/pages/staff/admin/workspaces/[workspaceId]/daily-access.js:12
#: src/pages/staff/admin/users/[userId]/daily-access.js:12
#: src/components/staff/admin/workspace/Workspace.js:153
#: src/components/staff/admin/user/User.js:77
msgid "Daily Access"
msgstr "Daily Access"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:54
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:70
#: src/components/misc/ActionsBar/View/ViewMenu.js:166
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:106
msgid "Daily listenership"
msgstr "Daily listenership"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:71
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:45
msgid "daily users"
msgstr "daily users"

#: src/components/misc/ActionsBar/View/ViewMenu.js:66
msgid "Daily users"
msgstr "Daily users"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:151
msgid "Dark"
msgstr "Dark"

#: src/components/settings/SettingsTheme/SettingsThemePreview/SettingsThemePreview.js:193
msgid "Dark mode preview"
msgstr "Dark mode preview"

#: src/pages/dashboard/index.js:18
#: src/pages/dashboard/shared/[dashboardKey].js:15
#: src/app/components/monitoring-navigation.tsx:91
msgid "Dashboard"
msgstr "Dashboard"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:19
msgid "Dashboard sharing"
msgstr "Dashboard sharing"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:56
msgid "Dashboard will be removed."
msgstr "Dashboard will be removed."

#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:47
msgid "Dashboard will be shared in read-only form (non-interactive) with currently displayed data. Link expiration is 30 days."
msgstr "Dashboard will be shared in read-only form (non-interactive) with currently displayed data. Link expiration is 30 days."

#: src/components/tariff/TariffLimits/TariffLimits.js:150
#: src/components/staff/admin/workspace/Workspace.js:460
msgid "Dashboards limit"
msgstr "Dashboards limit"

#: src/components/staff/admin/DailyAccess/Table.js:21
#: src/components/reports/history/HistoryTable.js:146
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:180
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:81
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:263
#: src/components/exportList/History/HistoryTable/HistoryTable.js:48
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:177
msgid "Date"
msgstr "Date"

#: src/components/misc/Changelog/ChangelogTable.js:30
msgid "Date & User"
msgstr "Date & User"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:238
msgid "Date and time must be in the future"
msgstr "Date and time must be in the future"

#. placeholder {0}: format(effectiveMinDate, DATE_FORMAT)
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:35
msgid "Date cannot be earlier than {0}"
msgstr "Date cannot be earlier than {0}"

#. placeholder {0}: format(effectiveMaxDate, DATE_FORMAT)
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:38
msgid "Date cannot be later than {0}"
msgstr "Date cannot be later than {0}"

#: src/constants/analytics.js:827
msgid "Day of the week"
msgstr "Day of the week"

#: src/helpers/charts/makeGranularityMenu.js:10
#: src/helpers/charts/getGranularityLabel.js:16
msgid "Days"
msgstr "Days"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:289
msgid "DD.MM.YYYY"
msgstr "DD.MM.YYYY"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:32
#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:51
msgid "Deduplicate articles"
msgstr "Deduplicate articles"

#: src/components/staff/admin/workspace/Workspace.js:763
msgid "Deduplicate feed articles"
msgstr "Deduplicate feed articles"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:36
msgid "Deduplication will remove same or similar articles from the report according to your settings. It will not remove any article from the feed."
msgstr "Deduplication will remove same or similar articles from the report according to your settings. It will not remove any article from the feed."

#: src/components/newsroom/content/posts/NewsroomPosts.js:133
msgid "Default"
msgstr "Default"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:62
msgid "Define the action you want recipients to take."
msgstr "Define the action you want recipients to take."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:37
msgid "Define the subject line to set the focus and tone."
msgstr "Define the subject line to set the focus and tone."

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:137
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:78
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:160
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:63
#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:87
#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:60
#: src/components/newsroom/content/posts/NewsroomPosts.js:201
#: src/components/newsroom/content/post/AttachmentsList.js:52
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:101
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:357
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:388
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:37
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:35
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:279
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:75
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:37
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:35
#: src/components/medialist/forms/modules/FormArray.js:119
#: src/components/medialist/forms/modules/FormArray.js:165
#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:33
#: src/components/emailing/content/SignaturePopup.tsx:31
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:85
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:96
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:117
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:53
msgid "Delete"
msgstr "Delete"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:348
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:360
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:385
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:313
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:388
msgid "Delete Article"
msgstr "Delete Article"

#: src/components/newsroom/content/post/AttachmentsList.js:56
msgid "Delete attachment"
msgstr "Delete attachment"

#: src/components/medialist/forms/FormEditAuthor.js:311
#: src/components/medialist/forms/FormEditAuthor.js:317
#: src/components/medialist/forms/FormEditAuthor.js:476
#: src/components/medialist/forms/FormEditAuthor.js:482
msgid "Delete author"
msgstr "Delete author"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:393
msgid "Delete blog post"
msgstr "Delete blog post"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:133
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:142
msgid "Delete category"
msgstr "Delete category"

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:37
msgid "Delete file"
msgstr "Delete file"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:286
msgid "Delete Folder"
msgstr "Delete Folder"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:375
msgid "Delete from media coverage"
msgstr "Delete from media coverage"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:131
#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:140
msgid "Delete Item"
msgstr "Delete Item"

#: src/components/medialist/content/AuthorBasketsMenu.js:126
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:108
msgid "Delete list"
msgstr "Delete list"

#: src/components/medialist/content/AuthorBasketsMenu.js:134
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:123
msgid "Delete list?"
msgstr "Delete list?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:659
msgid "Delete Newsroom"
msgstr "Delete Newsroom"

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:147
msgid "Delete recipient"
msgstr "Delete recipient"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:121
msgid "Delete Sender"
msgstr "Delete Sender"

#: src/components/emailing/content/SignaturePopup.tsx:34
msgid "Delete signature"
msgstr "Delete signature"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:302
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:311
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:192
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:201
msgid "Delete tag"
msgstr "Delete tag"

#: src/pages/trash.js:16
msgid "Deleted Articles"
msgstr "Deleted Articles"

#. placeholder {0}: feed.totalCount
#: src/components/trash/Content.js:41
msgid "Deleted Articles ({0})"
msgstr "Deleted Articles ({0})"

#: src/components/trash/Content.js:49
msgid "Deleted articles will appear here when deleted in the Articles section."
msgstr "Deleted articles will appear here when deleted in the Articles section."

#: src/components/reports/history/RecipientsTableRow.js:40
#: src/components/reports/history/HistoryTable.js:84
#: src/components/reports/history/HistoryTable.js:118
#: src/components/reports/history/HistoryTable.js:328
msgid "Delivered"
msgstr "Delivered"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:204
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:230
msgid "Delivery rate"
msgstr "Delivery rate"

#: src/components/reports/history/HistoryTable.js:162
msgid "Delivery stats"
msgstr "Delivery stats"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:20
msgid "Demo"
msgstr "Demo"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:111
msgid "Demographic Data"
msgstr "Demographic Data"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:47
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:96
#: src/components/feed/InspectorToolbar/InspectorToolbar.js:145
msgid "Demographics"
msgstr "Demographics"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:42
msgid "Describe who will receive the email (demographics, interests)."
msgstr "Describe who will receive the email (demographics, interests)."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:59
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:373
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:223
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:43
msgid "Description"
msgstr "Description"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:44
#: src/components/layout/MntrFiltersBar/modules/MenuFilterToggleAllButtons.js:76
msgid "Deselect all"
msgstr "Deselect all"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:23
msgid "Designed for PR professionals, generates a press release structure."
msgstr "Designed for PR professionals, generates a press release structure."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:80
msgid "Detailed instructions for email text creation"
msgstr "Detailed instructions for email text creation"

#: src/pages/newsroom/index.js:53
msgid "Detailed traffic<0/> <1>analytics</1>"
msgstr "Detailed traffic<0/> <1>analytics</1>"

#: src/constants/analytics/primeScoreCharts.ts:31
msgid "Development of PRIMe by rating"
msgstr "Development of PRIMe by rating"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:149
msgid "Deviation from the average"
msgstr "Deviation from the average"

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:89
msgid "Disable"
msgstr "Disable"

#: src/components/staff/admin/workspace/Workspace.js:225
#: src/components/staff/admin/workspace/Workspace.js:958
#: src/components/staff/admin/user/User.js:167
#: src/components/staff/admin/user/User.js:336
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:279
msgid "Discard"
msgstr "Discard"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:227
msgid "Discard changes"
msgstr "Discard changes"

#: src/components/monitoring/Inspector/InspectorMonitora/DiscussionThreadBar/DiscussionThreadBar.js:20
#: src/components/layout/MntrActiveFilters/modules/DiscussionThread.js:12
msgid "Discussion thread"
msgstr "Discussion thread"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:98
msgid "Display empty categories"
msgstr "Display empty categories"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:205
msgid "Display the article"
msgstr "Display the article"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:154
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:49
#: src/components/misc/ActionsBar/View/ViewMenu.js:116
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:75
msgid "Distribution amount"
msgstr "Distribution amount"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:149
msgid "DNS settings are invalid."
msgstr "DNS settings are invalid."

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:147
msgid "DNS settings are valid."
msgstr "DNS settings are valid."

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:108
msgid "Do not add new media to the medium"
msgstr "Do not add new media to the medium"

#: src/components/medialist/forms/FormEditAuthor.js:299
#: src/components/medialist/forms/FormEditAuthor.js:316
#: src/components/medialist/forms/FormEditAuthor.js:463
#: src/components/medialist/forms/FormEditAuthor.js:481
msgid "Do you really want to continue?"
msgstr "Do you really want to continue?"

#. placeholder {0}: menuItem.name
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:294
msgid "Do you really want to delete '<0>{0}</0>'?"
msgstr "Do you really want to delete '<0>{0}</0>'?"

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:200
msgid "Do you want to add author addresses from this list?"
msgstr "Do you want to add author addresses from this list?"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:84
msgid "Do you want to start with AI assistant?"
msgstr "Do you want to start with AI assistant?"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:124
msgid "Do you wish to reactivate this recipient?"
msgstr "Do you wish to reactivate this recipient?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:55
#: src/components/emailing/forms/FormSenderSettings.js:267
msgid "Domain"
msgstr "Domain"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:489
msgid "Domain change"
msgstr "Domain change"

#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:130
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:24
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:23
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:184
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:30
#: src/components/forms/dashboard/Export/ExportForm.js:100
#: src/components/exportList/History/HistoryTable/HistoryTable.js:96
#: src/components/exportList/Content/Content.tsx:78
#: src/components/OurChart/OurChartAdvanced.js:181
msgid "Download"
msgstr "Download"

#: src/components/forms/inspector/FormMediaEditor.js:141
msgid "Download clip"
msgstr "Download clip"

#: src/components/staff/admin/workspace/Workspace.js:126
msgid "Download settings (.xlsx)"
msgstr "Download settings (.xlsx)"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:82
msgid "Download template"
msgstr "Download template"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewImage/PreviewImage.js:60
msgid "Drag 'n' drop image or click to select files"
msgstr "Drag 'n' drop image or click to select files"

#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryAdapter.js:59
msgid "Drag 'n' drop some images here, or click to select files"
msgstr "Drag 'n' drop some images here, or click to select files"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:66
msgid "Due date"
msgstr "Due date"

#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:36
#: src/components/medialist/content/AuthorBasketsMenu.js:117
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:101
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:59
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:71
msgid "Duplicate"
msgstr "Duplicate"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:74
msgid "Duplicate widget"
msgstr "Duplicate widget"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:214
#: src/components/misc/ActionsBar/View/ViewMenu.js:174
msgid "Duration"
msgstr "Duration"

#: src/components/layout/AuthWrapper/constants/features.slides.js:307
msgid "Dynamic platform for creating, curating, and sharing captivating content."
msgstr "Dynamic platform for creating, curating, and sharing captivating content."

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:89
#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:124
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:51
#: src/components/misc/MntrEditor/extensions/ExtensionImageGallery.js:53
#: src/components/emailing/content/SignaturePopup.tsx:22
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:65
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:46
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:34
msgid "Edit"
msgstr "Edit"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:443
msgid "Edit article"
msgstr "Edit article"

#: src/components/newsroom/content/posts/NewsroomPosts.js:194
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:169
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:170
msgid "Edit Article"
msgstr "Edit Article"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:34
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:37
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:77
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:80
msgid "Edit Campaign"
msgstr "Edit Campaign"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/modalEditCategory.js:10
#: src/components/layout/Sidebar/modules/NewsroomNavigation/modalEditCategory.js:24
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:113
msgid "Edit category"
msgstr "Edit category"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:37
msgid "Edit dashboard"
msgstr "Edit dashboard"

#: src/components/topics/Content/TopicsList/Keyword/Keyword.js:62
msgid "Edit keyword"
msgstr "Edit keyword"

#: src/components/medialist/content/AuthorBasketsMenu.js:102
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:19
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:87
msgid "Edit list"
msgstr "Edit list"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:63
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:135
#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:60
msgid "Edit mediatypes"
msgstr "Edit mediatypes"

#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:56
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:106
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:131
#: src/components/medialist/forms/FormEditAuthor.js:710
msgid "Edit note"
msgstr "Edit note"

#: src/components/medialist/forms/FormEditAuthor.js:282
#: src/components/medialist/forms/FormEditAuthor.js:448
msgid "Edit profile"
msgstr "Edit profile"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:67
msgid "Edit recipient"
msgstr "Edit recipient"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:48
msgid "Edit Sender"
msgstr "Edit Sender"

#: src/components/newsroom/content/posts/NewsroomPosts.js:300
#: src/components/newsroom/content/dashboard/NewsroomBlogSettings.js:23
msgid "Edit settings"
msgstr "Edit settings"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:26
msgid "Edit signature"
msgstr "Edit signature"

#: src/components/layout/Sidebar/modules/SidebarTags/modalEditTags.js:10
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:279
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:168
msgid "Edit tag"
msgstr "Edit tag"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:32
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:58
msgid "Edit topic"
msgstr "Edit topic"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:59
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:63
msgid "Edit widget"
msgstr "Edit widget"

#: src/components/medialist/forms/modules/FormArray.js:94
msgid "Editorial Office"
msgstr "Editorial Office"

#: src/components/medialist/forms/FormEditAuthor.js:848
#: src/components/medialist/forms/FormEditAuthor.js:1012
msgid "Editorial offices and positions"
msgstr "Editorial offices and positions"

#: src/components/medialist/content/MedialistAuthorCreate.js:24
msgid "Eg. sent press releases, profile edits, published articles related to your press releases."
msgstr "Eg. sent press releases, profile edits, published articles related to your press releases."

#: src/pages/user/reset-password/index.tsx:20
#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:116
#: src/components/staff/admin/user/User.js:246
#: src/components/staff/admin/customer/users/UsersTable.js:68
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:61
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:224
#: src/components/reports/history/RecipientsTableHeader.js:30
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:133
#: src/components/page/auth/SignUp/SignUp.js:37
#: src/components/page/auth/Login/Login.tsx:40
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:128
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:74
#: src/components/medialist/forms/FormEditAuthor.js:793
#: src/components/medialist/forms/FormEditAuthor.js:911
#: src/components/medialist/forms/FormEditAuthor.js:916
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:33
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:100
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:111
#: src/components/emailing/content/EmailDetailEmailContent.js:37
#: src/components/emailing/content/CreateEmailContent.js:262
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:50
msgid "Email"
msgstr "Email"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:26
msgid "Email address was verified"
msgstr "Email address was verified"

#. placeholder {0}: campaign.name
#: src/components/emailing/content/CampaignAutocompleteList.tsx:41
msgid "Email copied to {0}"
msgstr "Email copied to {0}"

#: src/store/models/ExportStore.js:126
msgid "Email has been successfully sent."
msgstr "Email has been successfully sent."

#: src/components/emailing/content/CreateEmailContent.js:77
msgid "Email is locked and cannot be edited. If you want to edit the email, return it to the draft state."
msgstr "Email is locked and cannot be edited. If you want to edit the email, return it to the draft state."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:45
msgid "Email is missing"
msgstr "Email is missing"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:12
msgid "Email is required"
msgstr "Email is required"

#: src/components/emailing/content/CreateEmailContent.js:72
msgid "Email is sending"
msgstr "Email is sending"

#: src/helpers/modal/withModalEmailPreview.js:120
msgid "Email preview"
msgstr "Email preview"

#: src/components/reports/Content/ReportsList/ReportsHeading/ReportsHeading.js:10
msgid "Email reports ({counter})"
msgstr "Email reports ({counter})"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:142
msgid "Email subject"
msgstr "Email subject"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:36
msgid "Email Subject:"
msgstr "Email Subject:"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:62
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:74
msgid "Email successfully duplicated"
msgstr "Email successfully duplicated"

#: src/components/emailing/content/CreateEmailContent.js:134
msgid "Email was saved"
msgstr "Email was saved"

#: src/components/emailing/content/CreateEmailContent.js:153
msgid "Email was sent"
msgstr "Email was sent"

#: src/components/emailing/content/CreateEmailContent.js:124
msgid "Email was set to draft"
msgstr "Email was set to draft"

#: src/components/emailing/content/CreateEmailContent.js:74
msgid "Email will be sent at: {scheduledDate}"
msgstr "Email will be sent at: {scheduledDate}"

#: src/components/staff/admin/user/User.js:115
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:384
msgid "Email with further instructions has been sent."
msgstr "Email with further instructions has been sent."

#: src/pages/emailing/settings.tsx:16
#: src/pages/emailing/index.tsx:14
#: src/pages/emailing/campaign/[campaignId]/recipients.tsx:16
#: src/pages/emailing/campaign/[campaignId]/media-coverage.tsx:18
#: src/pages/emailing/campaign/[campaignId]/index.tsx:19
#: src/pages/emailing/campaign/[campaignId]/email/create.tsx:15
#: src/pages/emailing/campaign/[campaignId]/email/edit/[emailId]/index.tsx:12
#: src/pages/emailing/campaign/[campaignId]/email/[emailId]/recipients.tsx:16
#: src/pages/emailing/campaign/[campaignId]/email/[emailId]/index.tsx:16
#: src/components/layout/Sidebar/SidebarNavigation.tsx:158
#: src/components/layout/AuthWrapper/constants/features.slides.js:259
#: src/components/emailing/content/promo/PromoEmailing.js:17
#: src/components/emailing/components/FunnelStats/FunnelStats.tsx:79
#: src/app/components/monitoring-navigation.tsx:290
msgid "Emailing"
msgstr "Emailing"

#: src/components/reports/history/Compose.js:71
#: src/components/forms/dashboard/Search/SearchEmailingEmailMessages.js:27
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:106
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:54
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:158
msgid "Emails"
msgstr "Emails"

#: src/components/medialist/forms/modules/MainEmailHelperText.js:6
msgid "Emails from <0>Emailing</0> will be sent to this address."
msgstr "Emails from <0>Emailing</0> will be sent to this address."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:488
msgid "Embed"
msgstr "Embed"

#: src/components/misc/ActionsBar/View/ViewMenu.js:281
msgid "Emoji reactions"
msgstr "Emoji reactions"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:83
msgid "Empty Body"
msgstr "Empty Body"

#: src/app/components/monitoring-navigation.tsx:123
msgid "Empty export"
msgstr "Empty export"

#: src/app/components/monitoring-navigation.tsx:129
msgid "Empty export?"
msgstr "Empty export?"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:74
msgid "Empty Perex"
msgstr "Empty Perex"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:65
msgid "Empty Title"
msgstr "Empty Title"

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:89
msgid "Enable"
msgstr "Enable"

#: src/components/notifications/Permissions.js:58
msgid "Enable notifications"
msgstr "Enable notifications"

#: src/components/tariff/AgencyMedia/AgencyMedia.js:69
#: src/components/misc/ActionsBar/View/ViewMenu.js:52
#: src/components/misc/ActionsBar/View/ViewMenu.js:272
msgid "Enabled"
msgstr "Enabled"

#: src/components/emailing/forms/FormSenderSettings.js:112
msgid "Encryption method"
msgstr "Encryption method"

#: src/components/settings/SettingsTheme/ThemePicker.tsx:99
msgid "Enforce primary color as header"
msgstr "Enforce primary color as header"

#: src/constants/analytics.js:315
#: src/constants/analytics.js:424
#: src/constants/analytics.js:489
#: src/constants/analytics.js:511
#: src/constants/analytics.js:949
#: src/constants/analytics.js:964
#: src/components/monitoring/FeedList/FeedListItem/MetaData/modules/MetaDataEngagement/MetaDataEngagement.js:19
#: src/components/misc/ActionsBar/View/ViewMenu.js:305
msgid "Engagement rate"
msgstr "Engagement rate"

#: src/constants/analytics.js:336
msgid "Engagement rate by mention type"
msgstr "Engagement rate by mention type"

#: src/constants/analytics.js:334
#: src/constants/analytics.js:443
#: src/constants/analytics.js:509
msgid "Engagement rate by sentiment"
msgstr "Engagement rate by sentiment"

#: src/constants/analytics.js:445
msgid "Engagement rate by social network"
msgstr "Engagement rate by social network"

#: src/components/analytics/SocialMedia.js:25
msgid "Engagement summary"
msgstr "Engagement summary"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:62
#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:18
msgid "Enter a word or phrase"
msgstr "Enter a word or phrase"

#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:53
msgid "Enter text here..."
msgstr "Enter text here..."

#: src/components/emailing/forms/FormSenderSettings.js:83
msgid "Enter the hostname of your SMTP server"
msgstr "Enter the hostname of your SMTP server"

#: src/components/emailing/forms/FormSenderSettings.js:85
msgid "Enter the hostname of your SMTP server. This is usually in the format of \"smtp.yourdomain.com\"."
msgstr "Enter the hostname of your SMTP server. This is usually in the format of \"smtp.yourdomain.com\"."

#: src/components/emailing/forms/FormSenderSettings.js:98
msgid "Enter the password to login to SMTP server"
msgstr "Enter the password to login to SMTP server"

#: src/components/emailing/forms/FormSenderSettings.js:100
msgid "Enter the password to login to SMTP server. This is usually the same password you use for your email."
msgstr "Enter the password to login to SMTP server. This is usually the same password you use for your email."

#: src/components/emailing/forms/FormSenderSettings.js:90
msgid "Enter the username to login to SMTP server"
msgstr "Enter the username to login to SMTP server"

#. placeholder {0}: initialValues.email
#: src/components/emailing/forms/FormSenderSettings.js:92
msgid "Enter the username to login to SMTP server. If left blank, \"{0}\" is used by default."
msgstr "Enter the username to login to SMTP server. If left blank, \"{0}\" is used by default."

#: src/pages/user/reset-password/index.tsx:17
msgid "Enter your email. We'll send you instructions on how to reset your password."
msgstr "Enter your email. We'll send you instructions on how to reset your password."

#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:61
msgid "Error detail"
msgstr "Error detail"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:163
msgid "Estimated number of distributed copies (print and digital)."
msgstr "Estimated number of distributed copies (print and digital)."

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:17
msgid "Eternal"
msgstr "Eternal"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:32
msgid "Everything enabled"
msgstr "Everything enabled"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:24
msgid "Everything went well."
msgstr "Everything went well."

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:44
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:78
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:118
msgid "Exact match"
msgstr "Exact match"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:84
msgid "Exact match with separator"
msgstr "Exact match with separator"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:50
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:90
msgid "Exact match, including letter size"
msgstr "Exact match, including letter size"

#: src/pages/newsroom/index.js:72
msgid "Example Newsroom"
msgstr "Example Newsroom"

#: src/components/misc/VideoPlayer/Controls.js:143
msgid "Exit fullscreen"
msgstr "Exit fullscreen"

#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:83
msgid "Expense type"
msgstr "Expense type"

#: src/pages/staff/admin/customers/[customerId]/expenses.js:12
#: src/components/staff/admin/customer/expenses/Expenses.js:26
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:50
msgid "Expenses"
msgstr "Expenses"

#: src/components/page/auth/Expired/Expired.js:24
msgid "expired"
msgstr "expired"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:12
msgid "Expired"
msgstr "Expired"

#: src/components/staff/admin/workspace/Workspace.js:316
msgid "Expires at"
msgstr "Expires at"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:30
msgid "Expires on: {formattedDate}"
msgstr "Expires on: {formattedDate}"

#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:219
#: src/components/exportList/History/History.js:28
#: src/components/exportList/Content/Content.tsx:58
#: src/components/exportList/Content/Content.tsx:58
#: src/app/components/monitoring-navigation.tsx:102
msgid "Export"
msgstr "Export"

#: src/components/misc/portable/PortableExport/PortableExport.js:64
#: src/components/misc/portable/PortableExport/PortableExport.js:104
msgid "Export all articles"
msgstr "Export all articles"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:142
msgid "Export article"
msgstr "Export article"

#: src/components/misc/portable/PortableExport/PortableExport.js:62
#: src/components/misc/portable/PortableExport/PortableExport.js:102
msgid "Export articles"
msgstr "Export articles"

#: src/components/staff/admin/workspace/Workspace.js:329
msgid "Export basket mode"
msgstr "Export basket mode"

#: src/pages/export/history.js:12
#: src/components/exportList/History/History.js:35
msgid "Export History"
msgstr "Export History"

#: src/store/models/ExportStore.js:311
#: src/store/models/monitoring/Inspector/Inspector.ts:447
msgid "Export is full."
msgstr "Export is full."

#: src/components/exportList/Content/HeadingExport/HeadingExport.js:21
msgid "Export list is empty"
msgstr "Export list is empty"

#: src/components/medialist/forms/FormEditAuthor.js:362
#: src/components/medialist/forms/FormEditAuthor.js:502
msgid "Export XLSX"
msgstr "Export XLSX"

#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:17
#: src/app/components/monitoring-navigation.tsx:117
msgid "Exports to download"
msgstr "Exports to download"

#: src/pages/external-analytics.tsx:29
#: src/components/layout/Sidebar/SidebarNavigation.tsx:179
msgid "External analytics"
msgstr "External analytics"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:41
msgid "External Communication Manager"
msgstr "External Communication Manager"

#: src/components/misc/MntrEditor/forms/FormEmbedSocialUrl/FormEmbedFacebookUrl.tsx:29
msgid "Facebook Post URL"
msgstr "Facebook Post URL"

#: src/components/emailing/content/EmailingSettingsContent.js:30
msgid "Failed to fetch your email address from the service provider. Please try again or contact support if the issue persists."
msgstr "Failed to fetch your email address from the service provider. Please try again or contact support if the issue persists."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:41
msgid "Feature has been requested."
msgstr "Feature has been requested."

#: src/components/forms/dashboard/Export/ExportForm.js:66
msgid "File format"
msgstr "File format"

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:24
msgid "Files"
msgstr "Files"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:207
msgid "Fill from URL"
msgstr "Fill from URL"

#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:105
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:122
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:136
#: src/components/layout/MntrFiltersBar/forms/FormFilterItems/FormFilterItems.js:38
#: src/components/layout/MntrFiltersBar/forms/FormFilterAuthorTitles/FormFilterAuthorTitles.js:13
#: src/components/layout/MntrFiltersBar/forms/FormChannelSearch/FormChannelSearch.js:31
#: src/components/emailing/content/CampaignAutocomplete.tsx:25
msgid "Filter"
msgstr "Filter"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:104
msgid "Filter by absolute score"
msgstr "Filter by absolute score"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorActivity.js:27
msgid "Filter by activity"
msgstr "Filter by activity"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterArticleType.js:26
msgid "Filter by article type"
msgstr "Filter by article type"

#: src/components/layout/MntrFiltersBar/forms/FormAuthor/FormAuthor.tsx:13
msgid "Filter by author"
msgstr "Filter by author"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTypeMultiselect.js:32
msgid "Filter by author type"
msgstr "Filter by author type"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/withModalMedialistArticlesFilter.tsx:21
msgid "Filter by author's articles"
msgstr "Filter by author's articles"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterContactInformationMultiselect.js:32
msgid "Filter by contact"
msgstr "Filter by contact"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:98
#: src/components/layout/MntrFiltersBar/modules/MenuFilterCountryMultiselect.js:29
msgid "Filter by country"
msgstr "Filter by country"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:239
msgid "Filter by date"
msgstr "Filter by date"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorFocusAreasMultiselect.js:32
msgid "Filter by focus area"
msgstr "Filter by focus area"

#: src/components/layout/MntrFiltersBar/forms/FormFilterAuthorTitles/FormFilterAuthorTitles.js:32
msgid "Filter by job position"
msgstr "Filter by job position"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterLanguageTVR.js:24
#: src/components/layout/MntrFiltersBar/modules/MenuFilterLanguageMultiselect.js:26
msgid "Filter by language"
msgstr "Filter by language"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSourceTVR.js:24
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:115
msgid "Filter by media"
msgstr "Filter by media"

#: src/components/emailing/forms/FormFilter.js:35
msgid "Filter by name"
msgstr "Filter by name"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:50
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:80
msgid "Filter by rank"
msgstr "Filter by rank"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:187
msgid "Filter by reach"
msgstr "Filter by reach"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:141
msgid "Filter by relevance"
msgstr "Filter by relevance"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSentimentMultiselect.js:27
msgid "Filter by sentiment"
msgstr "Filter by sentiment"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:59
msgid "Filter sources"
msgstr "Filter sources"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:120
#: src/components/layout/MntrFiltersBar/modals/withModalPageNumbers.js:15
msgid "Filter specific pages"
msgstr "Filter specific pages"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:354
msgid "Filter tags"
msgstr "Filter tags"

#: src/components/layout/Sidebar/modules/SidebarTopics/SidebarTopicsFolders.js:76
msgid "Filter topics"
msgstr "Filter topics"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:56
msgid "Final version"
msgstr "Final version"

#. placeholder {0}: account.enums.analytics.export_charts_file_format.find( ({ id }) => id === fileFormatId, ).text
#: src/components/misc/Capture/Capture.js:304
msgid "finalizing {0} file for download"
msgstr "finalizing {0} file for download"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:67
#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:59
msgid "Find out more"
msgstr "Find out more"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:228
msgid "Find similar articles"
msgstr "Find similar articles"

#. js-lingui-explicit-id
#: src/components/misc/ActionsBar/Selector/Selector.js:45
msgid "selector.first"
msgstr "First"

#: src/components/page/auth/SignUp/SignUp.js:20
msgid "First Name"
msgstr "First Name"

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:21
msgid "First Step"
msgstr "First Step"

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:141
msgid "Focus area"
msgstr "Focus area"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:301
#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:53
msgid "Font Size"
msgstr "Font Size"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:30
msgid "For each functionality choose one of three levels:"
msgstr "For each functionality choose one of three levels:"

#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:68
msgid "For example"
msgstr "For example"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:81
msgid "For example, \"1,4-6\" will filter out 1,4,5,6"
msgstr "For example, \"1,4-6\" will filter out 1,4,5,6"

#: src/components/dashboards/PageExpiredSharedDashboard/PageExpiredSharedDashboard.js:40
msgid "For renewal, contact account admin."
msgstr "For renewal, contact account admin."

#. placeholder {0}: self.filters.topic_monitors[0].text
#: src/store/models/OurChart.js:853
msgid "for topic: {0}"
msgstr "for topic: {0}"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:39
msgid "Forbidden:"
msgstr "Forbidden:"

#: src/components/layout/AuthWrapper/constants/features.slides.js:146
msgid "Foreign Media"
msgstr "Foreign Media"

#: src/components/page/auth/Login/Login.tsx:86
msgid "Forgot password?"
msgstr "Forgot password?"

#: src/components/exportList/History/HistoryTable/HistoryTable.js:51
msgid "Format"
msgstr "Format"

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:18
msgid "Formatting is finished successfully. The prepared file is downloaded automatically. Edit it manually if needed and upload it to the medialist in the next step."
msgstr "Formatting is finished successfully. The prepared file is downloaded automatically. Edit it manually if needed and upload it to the medialist in the next step."

#: src/components/reports/Content/ReportsList/ReportsForm.js:111
msgid "Frequency of report dispatch"
msgstr "Frequency of report dispatch"

#: src/components/reports/Content/ReportsList/ReportsForm.js:279
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:56
msgid "From"
msgstr "From"

#: src/components/forms/dashboard/ExportResend/ExportResend.js:90
msgid "From email"
msgstr "From email"

#: src/components/misc/ActionsBar/View/ViewMenu.js:101
msgid "Frontpage promo"
msgstr "Frontpage promo"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:32
msgid "Full access:"
msgstr "Full access:"

#: src/components/misc/ActionsBar/View/ViewMenu.js:148
msgid "Full page ad price"
msgstr "Full page ad price"

#: src/components/misc/VideoPlayer/Controls.js:143
msgid "Fullscreen"
msgstr "Fullscreen"

#: src/components/tariff/Permissions/Permissions.js:40
msgid "Functionality"
msgstr "Functionality"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:93
msgid "Generate structure"
msgstr "Generate structure"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:149
msgid "Generate text"
msgstr "Generate text"

#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:40
msgid "Generating link"
msgstr "Generating link"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:115
msgid "Get a comprehensive view of the topics that matter to you."
msgstr "Get a comprehensive view of the topics that matter to you."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:102
msgid "Get a more complete view of the topics that interest you"
msgstr "Get a more complete view of the topics that interest you"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:34
msgid "Get access to our media archive."
msgstr "Get access to our media archive."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:71
msgid "Go back to Emailing"
msgstr "Go back to Emailing"

#: src/components/staff/admin/workspace/Workspace.js:403
msgid "Google Translate price: 1 article = 2.5 Kč = 0.09 €"
msgstr "Google Translate price: 1 article = 2.5 Kč = 0.09 €"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:268
msgid "Gross Rating Point"
msgstr "Gross Rating Point"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:107
msgid "Group articles"
msgstr "Group articles"

#: src/constants/stats.ts:16
#: src/constants/analytics.js:1010
#: src/components/widgets/modules/stats/WidgetStats.js:150
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:265
#: src/components/misc/ActionsBar/View/ViewMenu.js:211
msgid "GRP"
msgstr "GRP"

#: src/components/monitoring/Inspector/InspectorMonitora/HashTagsList/HashTagsList.js:24
msgid "Hashtags"
msgstr "Hashtags"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:33
msgid "Hasn't started yet"
msgstr "Hasn't started yet"

#: src/pages/sign-up-completion.tsx:42
#: src/components/page/auth/SignUp/SignUp.js:81
msgid "Have an account? Sign in"
msgstr "Have an account? Sign in"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:11
msgid "Head of External and Internal Communication"
msgstr "Head of External and Internal Communication"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:225
msgid "Header"
msgstr "Header"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:166
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:172
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:178
msgid "Heading"
msgstr "Heading"

#: src/pages/user/reactivate-24.js:37
msgid "Hello!<0/><1/>Thank you for your interest in trying Mediaboard with all the features. Please confirm by clicking on the button below. We will contact you as soon as possible.<2/><3/>Kind regards,<4/><5/>{appName} team"
msgstr "Hello!<0/><1/>Thank you for your interest in trying Mediaboard with all the features. Please confirm by clicking on the button below. We will contact you as soon as possible.<2/><3/>Kind regards,<4/><5/>{appName} team"

#: src/pages/user/yoy-analysis.js:37
msgid "Hello!<0/><1/>Thank you for your interest! Please confirm by clicking on the button below. We will contact you as soon as possible.<2/><3/>Kind regards,<4/><5/>{appName} team"
msgstr "Hello!<0/><1/>Thank you for your interest! Please confirm by clicking on the button below. We will contact you as soon as possible.<2/><3/>Kind regards,<4/><5/>{appName} team"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:30
msgid "Hello!<0/><1/>Thanks for your interest in our Emailing platform. To activate it you need to verify your email first. To do so just check, that the email you have entered is correct and click the activation button.<2/><3/><4/><5/>Best regards,<6/><7/>{appName} team"
msgstr "Hello!<0/><1/>Thanks for your interest in our Emailing platform. To activate it you need to verify your email first. To do so just check, that the email you have entered is correct and click the activation button.<2/><3/><4/><5/>Best regards,<6/><7/>{appName} team"

#: src/helpers/modal/withModalHelp.tsx:17
#: src/components/layout/Sidebar/SidebarNavigation.tsx:215
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:20
#: src/components/OurChart/OurChartAdvanced.js:268
msgid "Help"
msgstr "Help"

#: src/components/medialist/content/MedialistAuthorCreate.js:20
msgid "Here you will see all the activity related to this author."
msgstr "Here you will see all the activity related to this author."

#: src/components/emailing/components/FunnelStats/StatBlock.tsx:121
msgid "Here you will see newsroom analytics affected by the campaign"
msgstr "Here you will see newsroom analytics affected by the campaign"

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:159
msgid "hide"
msgstr "hide"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:246
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:210
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:249
msgid "Hide"
msgstr "Hide"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:557
msgid "Hide header and footer"
msgstr "Hide header and footer"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:63
msgid "hide stats"
msgstr "hide stats"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:230
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:241
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:144
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:153
msgid "Hide tag"
msgstr "Hide tag"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:229
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:190
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:202
msgid "Hide topic"
msgstr "Hide topic"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:210
msgid "Hide topics in folder"
msgstr "Hide topics in folder"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:65
msgid "Highlight Only"
msgstr "Highlight Only"

#: src/components/tariff/Permissions/Permissions.js:51
msgid "Hints"
msgstr "Hints"

#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:28
#: src/components/exportList/Content/Content.tsx:72
msgid "History"
msgstr "History"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:84
msgid "Homepage url"
msgstr "Homepage url"

#: src/components/emailing/forms/FormSenderSettings.js:82
msgid "Host"
msgstr "Host"

#: src/components/emailing/forms/FormSenderSettings.js:123
msgid "Hostname"
msgstr "Hostname"

#: src/components/staff/admin/workspace/Workspace.js:504
msgid "How many years back is the user allowed to search in media archive."
msgstr "How many years back is the user allowed to search in media archive."

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:88
msgid "How permissions work"
msgstr "How permissions work"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:135
msgid "How to help the AI generate a more satisfying and detailed email"
msgstr "How to help the AI generate a more satisfying and detailed email"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:218
msgid "How to use {appName}"
msgstr "How to use {appName}"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:111
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:91
msgid "HTML"
msgstr "HTML"

#: src/pages/user/yoy-analysis.js:59
#: src/pages/user/reactivate-24.js:59
msgid "I am interested"
msgstr "I am interested"

#: src/components/staff/admin/user/WorkspacesTable.js:74
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:74
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:80
msgid "ID"
msgstr "ID"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:43
msgid "Ideal for those with their own content ready."
msgstr "Ideal for those with their own content ready."

#: src/components/staff/admin/workspace/Workspace.js:382
msgid "If set to 0, then: no export basket, no exporting or email sending from feed or export basket."
msgstr "If set to 0, then: no export basket, no exporting or email sending from feed or export basket."

#: src/helpers/modal/withModalReportArticle.tsx:23
msgid "If the article has a bad transcript or screenshot, please report the problem and our staff will look into it and fix the issue."
msgstr "If the article has a bad transcript or screenshot, please report the problem and our staff will look into it and fix the issue."

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:92
msgid "If this was a mistake or if you'd like to re-subscribe at any time, please contact us at"
msgstr "If this was a mistake or if you'd like to re-subscribe at any time, please contact us at"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:64
msgid "If this was a mistake or you'd prefer to stay available for our emails, no further action is needed."
msgstr "If this was a mistake or you'd prefer to stay available for our emails, no further action is needed."

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:39
msgid "If you don't remember your current password, you can <0>reset it</0> or contact us at <1>{salesEmail}</1>."
msgstr "If you don't remember your current password, you can <0>reset it</0> or contact us at <1>{salesEmail}</1>."

#: src/components/page/auth/Expired/Expired.js:63
msgid "If you liked our service and would like to purchase the account, send us an email to <0>{salesEmail}</0>"
msgstr "If you liked our service and would like to purchase the account, send us an email to <0>{salesEmail}</0>"

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:20
msgid "If you would like to purchase a workspace account, send us an email to <0>{salesEmail}</0>"
msgstr "If you would like to purchase a workspace account, send us an email to <0>{salesEmail}</0>"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:126
msgid "If you'd like to re-subscribe, please contact us at"
msgstr "If you'd like to re-subscribe, please contact us at"

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:56
msgid "If you'd like to unsubscribe from emails, sent via mediaboard.com, simply click the button below. Your email <0>{0}</0> will no longer receive new emails form us."
msgstr "If you'd like to unsubscribe from emails, sent via mediaboard.com, simply click the button below. Your email <0>{0}</0> will no longer receive new emails form us."

#: src/components/reports/history/Content.js:38
msgid "If your report wasn't delivered, make sure to check your spam folder and your promotions inbox."
msgstr "If your report wasn't delivered, make sure to check your spam folder and your promotions inbox."

#: src/store/models/dashboards/DashboardPreview.js:147
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:49
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:47
#: src/components/misc/MntrEditor/extensions/ExtensionImageGallery.js:49
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:70
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:70
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewImage/PreviewImage.js:29
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:38
msgid "Image"
msgstr "Image"

#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:61
msgid "Images"
msgstr "Images"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:70
msgid "Import"
msgstr "Import"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:181
#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:84
#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:13
#: src/components/medialist/content/MedialistActionsBar/ContactsImportTitle.tsx:7
msgid "Import contacts"
msgstr "Import contacts"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:54
msgid "Import options"
msgstr "Import options"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:42
msgid "Import to"
msgstr "Import to"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:75
msgid "Import your already formatted contact list or manually completed template."
msgstr "Import your already formatted contact list or manually completed template."

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:106
msgid "Import your contacts to medialist"
msgstr "Import your contacts to medialist"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:92
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:37
msgid "Imprint"
msgstr "Imprint"

#. placeholder {0}: formatDate(lowerDate, 'd. M. yyyy')
#. placeholder {1}: formatDate(upperDate, 'd. M. yyyy')
#: src/helpers/getTitleWithDateFromTo.js:5
msgid "in period from {0} to {1}"
msgstr "in period from {0} to {1}"

#: src/components/newsroom/content/posts/NewsroomPosts.js:297
msgid "In the settings you can edit basic information about the Newsroom, appearance, web address, etc."
msgstr "In the settings you can edit basic information about the Newsroom, appearance, web address, etc."

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:13
msgid "In three years with Mediaboard, our experience has been exceptional. Their professionalism, wide range of services, and top-notch quarterly and annual analyses are highly valuable. We recommend Mediaboard for quality and reliability."
msgstr "In three years with Mediaboard, our experience has been exceptional. Their professionalism, wide range of services, and top-notch quarterly and annual analyses are highly valuable. We recommend Mediaboard for quality and reliability."

#: src/components/settings/SettingsApplication/SettingsApplication.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:106
msgid "In-app currency"
msgstr "In-app currency"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:173
#: src/components/staff/admin/user/User.js:138
#: src/components/staff/admin/customer/users/UsersTable.js:116
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoStaticItem.js:14
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoItem.js:31
#: src/components/forms/dashboard/Search/SearchUsers.js:99
msgid "Inactive"
msgstr "Inactive"

#: src/components/reports/Content/ReportsList/FormToggleActive/FormToggleActive.js:36
msgid "Inactive report"
msgstr "Inactive report"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:22
msgid "Include all the key points you want to specifically mention in your article. These should be the essential details, arguments, or highlights that support and enhance the main content.\""
msgstr "Include all the key points you want to specifically mention in your article. These should be the essential details, arguments, or highlights that support and enhance the main content.\""

#: src/components/tariff/TariffLimits/TariffLimits.js:51
#: src/components/tariff/TariffLimits/TariffLimits.js:88
#: src/components/tariff/TariffLimits/TariffLimits.js:123
#: src/components/tariff/TariffLimits/TariffLimits.js:143
#: src/components/tariff/TariffLimits/TariffLimits.js:159
#: src/components/tariff/TariffLimits/TariffLimits.js:176
#: src/components/tariff/TariffLimits/TariffLimits.js:195
#: src/components/tariff/TariffLimits/TariffLimits.js:212
#: src/components/tariff/TariffLimits/TariffLimits.js:233
#: src/components/tariff/TariffLimits/TariffLimits.js:250
#: src/components/tariff/TariffLimits/TariffLimits.js:283
#: src/components/tariff/TariffLimits/SingleValueLimit/SingleValueLimit.js:20
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:114
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:85
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:102
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:110
msgid "Increase limit"
msgstr "Increase limit"

#: src/components/tariff/TariffLimits/TariffLimits.js:50
#: src/components/tariff/TariffLimits/TariffLimits.js:87
#: src/components/tariff/TariffLimits/TariffLimits.js:122
#: src/components/tariff/TariffLimits/TariffLimits.js:142
#: src/components/tariff/TariffLimits/TariffLimits.js:158
#: src/components/tariff/TariffLimits/TariffLimits.js:175
#: src/components/tariff/TariffLimits/TariffLimits.js:194
#: src/components/tariff/TariffLimits/TariffLimits.js:211
#: src/components/tariff/TariffLimits/TariffLimits.js:232
#: src/components/tariff/TariffLimits/TariffLimits.js:249
#: src/components/tariff/TariffLimits/TariffLimits.js:282
#: src/components/tariff/TariffLimits/SingleValueLimit/SingleValueLimit.js:18
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:84
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:101
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:109
msgid "Increase limit?"
msgstr "Increase limit?"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:52
msgid "Indicate the desired tone (formal, casual) and style (informative, promotional)."
msgstr "Indicate the desired tone (formal, casual) and style (informative, promotional)."

#: src/components/monitoring/FeedList/FeedListItem/MetaData/modules/MetaDataScore/MetaDataScore.js:38
msgid "influence score"
msgstr "influence score"

#: src/constants/stats.ts:21
#: src/constants/analytics.js:262
#: src/constants/analytics.js:371
#: src/constants/analytics.js:451
#: src/constants/analytics.js:483
#: src/constants/analytics.js:644
#: src/constants/analytics.js:659
#: src/constants/analytics.js:929
#: src/constants/analytics.js:944
#: src/components/widgets/modules/stats/WidgetStats.js:162
#: src/components/misc/ActionsBar/View/ViewMenu.js:297
msgid "Influence score"
msgstr "Influence score"

#: src/constants/analytics.js:283
#: src/constants/analytics.js:530
msgid "Influence score by mention type"
msgstr "Influence score by mention type"

#: src/constants/analytics.js:281
#: src/constants/analytics.js:390
#: src/constants/analytics.js:481
msgid "Influence score by sentiment"
msgstr "Influence score by sentiment"

#: src/constants/analytics.js:392
msgid "Influence score by social network"
msgstr "Influence score by social network"

#: src/components/emailing/content/sender/EmailingSenderContent.js:17
msgid "Initial Emailing settings"
msgstr "Initial Emailing settings"

#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:124
msgid "Insert"
msgstr "Insert"

#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:155
msgid "Insert button label to view preview"
msgstr "Insert button label to view preview"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:125
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:169
msgid "Insert HTML code to view preview"
msgstr "Insert HTML code to view preview"

#: src/components/emailing/content/CreateEmailContent.js:408
msgid "Insert internal name of email"
msgstr "Insert internal name of email"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:359
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:363
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:385
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:409
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:413
msgid "Insert link"
msgstr "Insert link"

#: src/components/emailing/content/CreateEmailContent.js:449
msgid "Insert subject"
msgstr "Insert subject"

#: src/components/medialist/forms/FormEditAuthor.js:736
#: src/components/medialist/forms/FormEditAuthor.js:1031
msgid "Insert text..."
msgstr "Insert text..."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:121
msgid "Instructions"
msgstr "Instructions"

#: src/constants/analytics.js:416
msgid "Interactions by sentiment"
msgstr "Interactions by sentiment"

#: src/constants/analytics.js:418
msgid "Interactions by social network"
msgstr "Interactions by social network"

#: src/constants/analytics.js:227
#: src/constants/analytics.js:639
#: src/constants/analytics.js:774
#: src/components/layout/AuthWrapper/constants/features.slides.js:199
msgid "Interactions on social networks"
msgstr "Interactions on social networks"

#: src/constants/analytics.js:225
msgid "Interactions on social networks by sentiment"
msgstr "Interactions on social networks by sentiment"

#: src/components/emailing/content/CreateEmailContent.js:405
msgid "Internal name of email"
msgstr "Internal name of email"

#: src/components/emailing/forms/FormEmailRecipients.js:37
msgid "Invalid"
msgstr "Invalid"

#: src/components/article/Content.js:13
msgid "Invalid article link"
msgstr "Invalid article link"

#. placeholder {0}: format( startOfYear(today), DATE_FORMAT, )
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:83
msgid "Invalid date format. Expected format is {0}"
msgstr "Invalid date format. Expected format is {0}"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:16
#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:10
#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:33
msgid "Invalid email format"
msgstr "Invalid email format"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:118
msgid "Invalid page number"
msgstr "Invalid page number"

#: src/components/forms/inspector/FormMediaEditor.js:76
#: src/components/forms/inspector/FormMediaEditor.js:79
msgid "Invalid time format. Enter hh:mm:ss"
msgstr "Invalid time format. Enter hh:mm:ss"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:57
msgid "Invoice no."
msgstr "Invoice no."

#: src/pages/staff/admin/customers/[customerId]/invoices.js:12
#: src/components/staff/admin/customer/invoices/Invoices.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:42
msgid "Invoices"
msgstr "Invoices"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:56
msgid "Irrelevant"
msgstr "Irrelevant"

#: src/components/misc/Changelog/ChangelogTableRow.js:156
msgid "Irreversible"
msgstr "Irreversible"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:104
msgid "Is overdue"
msgstr "Is overdue"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:296
msgid "Is there a problem with the article?"
msgstr "Is there a problem with the article?"

#. placeholder {0}: data.publication.issue
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:225
msgid "Issue: {0}"
msgstr "Issue: {0}"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:72
msgid "Issued via"
msgstr "Issued via"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:368
msgid "It appears above the description on the search results page."
msgstr "It appears above the description on the search results page."

#: src/pages/_error.js:49
msgid "It looks like you're trying to access a malformed URL. Please review it and try again."
msgstr "It looks like you're trying to access a malformed URL. Please review it and try again."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:131
msgid "Italic"
msgstr "Italic"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:144
msgid "Item '<0>{title}</0>' will be removed."
msgstr "Item '<0>{title}</0>' will be removed."

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:179
msgid "Job position"
msgstr "Job position"

#: src/components/medialist/forms/modules/FormArray.js:107
msgid "Job Position"
msgstr "Job Position"

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:21
msgid "formatNumber.k"
msgstr "k"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:68
msgid "Keep original"
msgstr "Keep original"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:20
msgid "Key points list"
msgstr "Key points list"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:56
msgid "Key Points:"
msgstr "Key Points:"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:40
msgid "Keyword"
msgstr "Keyword"

#: src/helpers/modal/withModalTvrTopics.tsx:53
#: src/constants/analytics.js:1054
#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/KeywordsListMedia/KeywordsListMedia.js:22
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:53
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:119
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:255
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:337
msgid "Keywords"
msgstr "Keywords"

#: src/components/misc/ResendSettings/SaveResendSettings/FormSaveResendSettings.js:21
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:122
#: src/components/misc/ExportSettings/SaveExportSettings/FormSaveExportSettings.js:21
#: src/components/emailing/forms/FormAddCampaign.tsx:15
#: src/components/dashboards/DashboardSelector/FormEditDashboard.js:26
msgid "Label"
msgstr "Label"

#: src/constants/analytics.js:870
#: src/components/staff/admin/user/User.js:278
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:274
#: src/components/misc/ActionsBar/View/ViewMenu.js:333
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:310
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:329
#: src/components/layout/Header/UserMenu/UserMenu.tsx:119
msgid "Language"
msgstr "Language"

#: src/constants/analytics.js:883
msgid "Languages"
msgstr "Languages"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:106
msgid "Languages & connected newsrooms"
msgstr "Languages & connected newsrooms"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:236
msgid "Last access"
msgstr "Last access"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:122
#: src/components/staff/admin/customer/users/UsersTable.js:74
msgid "Last login"
msgstr "Last login"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:147
msgid "Last month"
msgstr "Last month"

#: src/components/page/auth/SignUp/SignUp.js:29
msgid "Last Name"
msgstr "Last Name"

#: src/components/newsroom/components/PostsList/PostsList.js:166
#: src/components/newsroom/components/PostsList/PostsList.js:181
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:152
msgid "Last update"
msgstr "Last update"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:128
msgid "Last week"
msgstr "Last week"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:160
msgid "Last year"
msgstr "Last year"

#: src/components/emailing/forms/FormSenderSettings.js:106
msgid "Leave blank to use the default port"
msgstr "Leave blank to use the default port"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:141
msgid "Light"
msgstr "Light"

#: src/components/settings/SettingsTheme/SettingsThemePreview/SettingsThemePreview.js:193
msgid "Light mode preview"
msgstr "Light mode preview"

#: src/components/medialist/forms/FormEditAuthor.js:504
msgid "limit"
msgstr "limit"

#: src/store/models/monitoring/MedialistMapItem/MedialistMapItem.js:260
#: src/store/models/monitoring/MedialistMapItem/MedialistMapItem.js:285
#: src/store/models/authors/AuthorsStore.js:410
#: src/store/models/authors/AuthorsStore.js:435
msgid "Limit exceeded. Sucessfully exported {generated} of {requested} requested authors."
msgstr "Limit exceeded. Sucessfully exported {generated} of {requested} requested authors."

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:652
msgid "Limit reached. You have selected too many articles."
msgstr "Limit reached. You have selected too many articles."

#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:35
msgid "Limits"
msgstr "Limits"

#: src/components/OurChart/OurChartAdvanced.js:162
msgid "Line"
msgstr "Line"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:76
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:325
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:131
msgid "Link"
msgstr "Link"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:40
#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:69
#: src/components/staff/admin/user/User.js:67
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:224
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:15
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:16
#: src/components/exportList/History/HistoryTable/HistoryTable.js:111
msgid "Link has been copied to the clipboard."
msgstr "Link has been copied to the clipboard."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:116
msgid "Link to other language"
msgstr "Link to other language"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:95
msgid "Link to the article"
msgstr "Link to the article"

#: src/components/emailing/content/EmailingSettingsContent.js:31
msgid "Linking with Google account timed out. Please, try again."
msgstr "Linking with Google account timed out. Please, try again."

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:240
#: src/components/monitoring/Inspector/InspectorMonitora/Links/Links.js:14
msgid "Links"
msgstr "Links"

#: src/components/medialist/content/AuthorBasketsMenu.js:135
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:124
msgid "List {label} will be removed."
msgstr "List {label} will be removed."

#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:25
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:31
#: src/components/forms/baskets/FormNewBasket.js:26
msgid "List name"
msgstr "List name"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:26
msgid "List of tags"
msgstr "List of tags"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:99
msgid "List of topics"
msgstr "List of topics"

#: src/components/medialist/forms/FormEditAuthor.js:660
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:97
msgid "Lists"
msgstr "Lists"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:46
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:20
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:20
msgid "Load"
msgstr "Load"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:54
msgid "Load from"
msgstr "Load from"

#: src/components/monitoring/FeedList/LoadMore/LoadMore.js:27
msgid "Load more"
msgstr "Load more"

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:140
msgid "Load more..."
msgstr "Load more..."

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:46
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:48
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:48
msgid "Load settings"
msgstr "Load settings"

#: src/components/tvr/Content/Content.js:82
#: src/components/trash/Content.js:39
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:70
#: src/components/reports/history/HistoryTable.js:491
#: src/components/notifications/Permissions.js:84
#: src/components/notifications/AppNotifications/AppNotifications.js:21
#: src/components/newsroom/content/dashboard/ChartVisits.js:127
#: src/components/monitoring/WorkspaceArticles/Intro.js:20
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:57
#: src/components/monitoring/FeedList/LoadMore/LoadMore.js:27
#: src/components/monitoring/FeedChart/FeedChart.js:47
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:47
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:55
#: src/components/misc/MntrEditor/forms/FormMediaUpload/UploadProgress.js:31
#: src/components/medialist/content/MedialistHeading.js:14
#: src/components/medialist/content/MedialistInspector/AuthorChart/AuthorChart.js:84
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:31
#: src/components/exportList/Content/HeadingExport/HeadingExport.js:25
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:40
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:48
#: src/components/dashboards/DashboardSelector/DashboardSelector.js:21
#: src/components/analytics/AnalyticsContent.js:38
#: src/components/OurChart/OurChartAdvanced.js:316
msgid "Loading..."
msgstr "Loading..."

#: src/components/layout/Header/UserMenu/UserMenu.tsx:228
msgid "Log back in"
msgstr "Log back in"

#: src/components/page/auth/Login/Login.tsx:55
#: src/components/page/auth/Login/Login.tsx:69
#: src/components/page/auth/Expired/Expired.js:104
msgid "Log In"
msgstr "Log In"

#: src/components/staff/admin/user/User.js:192
#: src/components/staff/admin/customer/users/UsersTable.js:129
msgid "Login as this user"
msgstr "Login as this user"

#: src/components/staff/admin/workspace/Workspace.js:249
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:137
msgid "Login into this workspace"
msgstr "Login into this workspace"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:187
msgid "Login link"
msgstr "Login link"

#: src/components/page/auth/Login/Login.tsx:63
msgid "Login to {appName}"
msgstr "Login to {appName}"

#: src/components/page/auth/Login/Login.tsx:64
msgid "Login to {appName}, the next generation media monitoring tool."
msgstr "Login to {appName}, the next generation media monitoring tool."

#: src/components/page/auth/Expired/Expired.js:75
msgid "Login to different workspace"
msgstr "Login to different workspace"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:187
msgid "Logout"
msgstr "Logout"

#: src/helpers/auth.js:47
msgid "Logout performed in another window."
msgstr "Logout performed in another window."

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:22
msgid "formatNumber.M"
msgstr "M"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:155
msgid "Magazine cover pages"
msgstr "Magazine cover pages"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:59
msgid "Main message"
msgstr "Main message"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:48
msgid "Main message & key points"
msgstr "Main message & key points"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:46
msgid "Main Objective:"
msgstr "Main Objective:"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:61
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:65
msgid "Mainstream sources"
msgstr "Mainstream sources"

#: src/pages/_error.js:44
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:422
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:156
#: src/components/misc/MntrEditor/forms/FormEmbedSocialUrl/FormEmbedFacebookUrl.tsx:44
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:88
msgid "Malformed URL"
msgstr "Malformed URL"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:104
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:107
msgid "Manage hidden tags"
msgstr "Manage hidden tags"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:95
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:98
msgid "Manage hidden topics"
msgstr "Manage hidden topics"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:231
msgid "Management summaries"
msgstr "Management summaries"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:49
msgid "Manual writing"
msgstr "Manual writing"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:36
msgid "Manually add the information for your signature, which will appear in every email or customize it using your own HTML."
msgstr "Manually add the information for your signature, which will appear in every email or customize it using your own HTML."

#: src/components/emailing/content/promo/PromoEmailing.js:27
msgid "Mass mailing"
msgstr "Mass mailing"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:488
msgid "Max. file size:"
msgstr "Max. file size:"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:225
msgid "Media analysis"
msgstr "Media analysis"

#: src/components/tariff/TariffLimits/TariffLimits.js:261
#: src/components/staff/admin/workspace/Workspace.js:500
msgid "Media archive depth limit"
msgstr "Media archive depth limit"

#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:120
msgid "Media Coverage"
msgstr "Media Coverage"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:330
#: src/components/misc/ActionsBar/View/ViewMenu.js:49
#: src/components/misc/ActionsBar/View/ViewMenu.js:344
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:37
msgid "Media data"
msgstr "Media data"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:92
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:168
msgid "Media data (GRP, OTS, AVE, PRIMe)"
msgstr "Media data (GRP, OTS, AVE, PRIMe)"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:16
msgid "Media Monitoring"
msgstr "Media Monitoring"

#: src/constants/analytics.js:79
#: src/constants/analytics.js:585
#: src/constants/analytics.js:717
#: src/components/layout/AuthWrapper/constants/features.slides.js:183
msgid "Media reach (GRP)"
msgstr "Media reach (GRP)"

#: src/constants/analytics.js:77
msgid "Media reach (GRP) by sentiment"
msgstr "Media reach (GRP) by sentiment"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:222
msgid "Media services"
msgstr "Media services"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:43
msgid "Mediaboard transformed our communication at Coca-Cola HBC! A daily essential for top-notch media monitoring, with a user-friendly interface and insightful analytics. Their exceptional customer support makes it a joy to work with Mediaboard."
msgstr "Mediaboard transformed our communication at Coca-Cola HBC! A daily essential for top-notch media monitoring, with a user-friendly interface and insightful analytics. Their exceptional customer support makes it a joy to work with Mediaboard."

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:29
msgid "Mediakit"
msgstr "Mediakit"

#: src/store/models/dashboards/DashboardPreview.js:99
#: src/pages/authors/index.js:32
#: src/pages/authors/index.js:41
#: src/pages/authors/create.js:10
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:110
#: src/components/layout/Sidebar/SidebarNavigation.tsx:137
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:36
#: src/components/layout/AuthWrapper/constants/features.slides.js:214
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:44
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:44
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:29
#: src/app/components/monitoring-navigation.tsx:269
msgid "Medialist"
msgstr "Medialist"

#: src/constants/analytics.js:793
#: src/components/topics/Content/TopicsList/MegalistModal.js:52
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:70
#: src/components/topics/Content/TopicsList/MegalistToolbar/MediatypeFilterPopup.js:8
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:247
msgid "Mediatype"
msgstr "Mediatype"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:91
msgid "mediatype for all countries"
msgstr "mediatype for all countries"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:67
msgid "Mention any personalization details (name, company)."
msgstr "Mention any personalization details (name, company)."

#: src/constants/analytics.js:233
#: src/constants/analytics.js:342
#: src/constants/analytics.js:889
#: src/constants/analytics.js:909
#: src/constants/analytics.js:1312
#: src/components/widgets/modules/stats/WidgetStats.js:241
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:51
msgid "Mentions"
msgstr "Mentions"

#: src/constants/analytics.js:254
#: src/constants/analytics.js:363
msgid "Mentions by sentiment"
msgstr "Mentions by sentiment"

#: src/constants/analytics.js:365
#: src/constants/analytics.js:923
#: src/constants/analytics.js:1325
msgid "Mentions by social network"
msgstr "Mentions by social network"

#: src/constants/analytics.js:256
#: src/constants/analytics.js:903
msgid "Mentions by type"
msgstr "Mentions by type"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:586
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:96
msgid "Merge Tags"
msgstr "Merge Tags"

#: src/pages/staff/admin/customers/[customerId]/merged-customers.js:12
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomers.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:60
msgid "Merged customers"
msgstr "Merged customers"

#: src/components/staff/admin/customer/bio/CustomerBio.js:79
msgid "Merged to"
msgstr "Merged to"

#: src/components/misc/ActionsBar/View/ViewMenu.js:199
msgid "Metrics"
msgstr "Metrics"

#: src/components/misc/portable/PortableResend/PortableResend.js:93
#: src/components/misc/portable/PortableExport/PortableExport.js:88
msgid "Minimize"
msgstr "Minimize"

#: src/components/tariff/MonitoredMedia/MissedArticles/MissedArticles.js:9
msgid "Missed articles"
msgstr "Missed articles"

#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:54
#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:74
msgid "Missing article"
msgstr "Missing article"

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:39
msgid "Missing data"
msgstr "Missing data"

#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:46
msgid "Missing recipient info"
msgstr "Missing recipient info"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:298
#: src/components/layout/AuthWrapper/constants/features.slides.js:399
msgid "Mobile Apps"
msgstr "Mobile Apps"

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:41
msgid "Modified"
msgstr "Modified"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoItem.js:87
msgid "Monitor a wide range of social media platforms including Facebook, LinkedIn, Instagram, TikTok, X.com, and YouTube."
msgstr "Monitor a wide range of social media platforms including Facebook, LinkedIn, Instagram, TikTok, X.com, and YouTube."

#: src/components/layout/AuthWrapper/constants/features.slides.js:23
msgid "Monitor newspapers, magazines, radios, TV stations or the entire online world. Reach out to media, react, track, analyze, and build your brand."
msgstr "Monitor newspapers, magazines, radios, TV stations or the entire online world. Reach out to media, react, track, analyze, and build your brand."

#: src/components/topics/Content/TopicsList/MediaCard.js:21
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:127
#: src/components/staff/admin/workspace/Workspace.js:877
#: src/components/settings/SettingsTariff/SettingsTariff.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:41
msgid "Monitored media"
msgstr "Monitored media"

#: src/components/notifications/Content.js:34
#: src/components/monitoring/Monitoring.js:109
#: src/components/monitoring/Monitoring.js:110
#: src/components/monitoring/Monitoring.js:165
#: src/components/layout/Sidebar/SidebarNavigation.tsx:119
#: src/components/layout/AuthWrapper/constants/features.slides.js:22
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:10
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:10
#: src/app/components/monitoring-navigation.tsx:65
msgid "Monitoring"
msgstr "Monitoring"

#: src/components/staff/admin/customer/expenses/ExpenseTable.js:77
msgid "Month"
msgstr "Month"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:107
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:65
msgid "monthly sessions"
msgstr "monthly sessions"

#: src/components/misc/ActionsBar/View/ViewMenu.js:82
msgid "Monthly sessions"
msgstr "Monthly sessions"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:87
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:55
msgid "monthly users"
msgstr "monthly users"

#: src/components/misc/ActionsBar/View/ViewMenu.js:74
msgid "Monthly users"
msgstr "Monthly users"

#: src/helpers/charts/makeGranularityMenu.js:26
#: src/helpers/charts/getGranularityLabel.js:9
msgid "Months"
msgstr "Months"

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:159
msgid "more"
msgstr "more"

#: src/components/OurChart/OurChartAdvanced.js:247
msgid "More"
msgstr "More"

#: src/constants/analytics.js:1338
msgid "Most common terms"
msgstr "Most common terms"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:96
msgid "Move article"
msgstr "Move article"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:119
msgid "Move to Dashboard"
msgstr "Move to Dashboard"

#: src/pages/workspace-articles.js:51
#: src/components/monitoring/WorkspaceArticles/Intro.js:23
#: src/app/components/monitoring-navigation.tsx:204
msgid "My Articles"
msgstr "My Articles"

#: src/components/medialist/content/AuthorInfoDetail.js:72
msgid "My author"
msgstr "My author"

#: src/components/medialist/content/OwnAuthorsListSelectorButton.js:9
#: src/components/medialist/content/AuthorBasketsMenu.js:41
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:158
msgid "My authors"
msgstr "My authors"

#: src/components/topics/Content/TopicsList/FormSaveMegalist/FormSaveMegalist.js:8
#: src/components/staff/admin/workspace/Workspace.js:288
#: src/components/staff/admin/user/WorkspacesTable.js:71
#: src/components/staff/admin/user/User.js:256
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:71
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:52
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:62
#: src/components/medialist/forms/FormEditAuthor.js:211
#: src/components/medialist/forms/FormEditAuthor.js:212
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:28
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:53
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:45
msgid "Name"
msgstr "Name"

#. js-lingui-explicit-id
#: src/components/dashboards/DashboardSelector/FormCreateDashboard.js:25
msgid "name.nazev"
msgstr "Name"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:57
msgid "Name for expression (optional)"
msgstr "Name for expression (optional)"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:341
msgid "Need help? Ask AI assistant. Select a sentence or paragraph"
msgstr "Need help? Ask AI assistant. Select a sentence or paragraph"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:147
msgid "Need help? Check our <0>tutorial</0> or contact us."
msgstr "Need help? Check our <0>tutorial</0> or contact us."

#: src/components/medialist/content/MedialistDashboard.js:94
#: src/components/medialist/content/MedialistDashboard.js:127
msgid "New"
msgstr "New"

#. js-lingui-explicit-id
#: src/components/layout/Header/AppNotifications/AppNotifications.js:133
msgid "new.notifications"
msgstr "New"

#: src/components/emailing/modules/withModalAddCampaign.tsx:20
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:91
#: src/components/emailing/content/EmailingCampaignsContent.tsx:59
msgid "New Campaign"
msgstr "New Campaign"

#: src/components/newsroom/forms/FormCreateCategory/FormCreateCategory.js:29
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:72
msgid "New Category"
msgstr "New Category"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:29
#: src/components/emailing/content/NewEmailWizardButton.tsx:13
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:72
msgid "New Email"
msgstr "New Email"

#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:38
#: src/components/forms/baskets/FormNewBasket.js:10
msgid "New list"
msgstr "New list"

#: src/pages/user/reset-password/success.tsx:7
#: src/pages/user/reset-password/new.tsx:54
#: src/components/staff/admin/user/User.js:267
#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:87
msgid "New password"
msgstr "New password"

#: src/store/models/admin/customer/CustomerStore.js:227
msgid "New passwords have been copied to the clipboard."
msgstr "New passwords have been copied to the clipboard."

#: src/components/newsroom/content/posts/NewPostWizardButton.tsx:13
msgid "New post"
msgstr "New post"

#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:71
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:70
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:103
msgid "New report"
msgstr "New report"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:78
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:264
#: src/components/forms/tags/FormNewTag/FormNewTag.js:10
msgid "New Tag"
msgstr "New Tag"

#: src/components/topics/Content/TopicsList/TopicsList.js:36
msgid "New topic"
msgstr "New topic"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:55
msgid "New value"
msgstr "New value"

#: src/pages/newsroom/index.js:24
#: src/pages/newsroom/index.js:33
#: src/pages/newsroom/create.js:17
#: src/pages/newsroom/[blogId]/settings.js:15
#: src/pages/newsroom/[blogId]/index.js:16
#: src/pages/newsroom/[blogId]/post/[postId].js:10
#: src/components/newsroom/forms/FormNewsroomSettings/LanguageSection.tsx:61
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:112
#: src/components/layout/Sidebar/SidebarNavigation.tsx:147
#: src/components/layout/AuthWrapper/constants/features.slides.js:306
#: src/components/emailing/components/FunnelStats/FunnelStats.tsx:80
#: src/app/components/monitoring-navigation.tsx:279
msgid "Newsroom"
msgstr "Newsroom"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:85
msgid "Newsroom Articles"
msgstr "Newsroom Articles"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:254
msgid "Newsroom is a blogging platform that allows you to easily share your external and internal communication (e.g. press releases, announcements, etc.). You can read more about the Newsroom <0>on our website</0>."
msgstr "Newsroom is a blogging platform that allows you to easily share your external and internal communication (e.g. press releases, announcements, etc.). You can read more about the Newsroom <0>on our website</0>."

#: src/components/staff/admin/workspace/Workspace.js:481
msgid "Newsroom limit"
msgstr "Newsroom limit"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:305
msgid "Newsroom settings"
msgstr "Newsroom settings"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:69
msgid "Next page"
msgstr "Next page"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:40
msgid "No access to monitoring feeds, archive search, analytics, topic or report settings, crisis communications, medialist, or user settings."
msgstr "No access to monitoring feeds, archive search, analytics, topic or report settings, crisis communications, medialist, or user settings."

#: src/components/emailing/content/mediaCoverage/EmptyFeedMessage.tsx:8
msgid "No articles yet"
msgstr "No articles yet"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:81
msgid "No campaigns yet"
msgstr "No campaigns yet"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:189
msgid "No categories yet."
msgstr "No categories yet."

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:109
msgid "No companies found"
msgstr "No companies found"

#: src/components/staff/admin/user/WorkspacesTable.js:164
#: src/components/staff/admin/customer/workspaces/Workspaces.js:53
#: src/components/staff/admin/customer/users/UsersTable.js:148
#: src/components/staff/admin/customer/users/Users.js:53
#: src/components/staff/admin/customer/invoices/Invoices.js:49
#: src/components/staff/admin/customer/expenses/Expenses.js:46
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomers.js:49
msgid "No data"
msgstr "No data"

#: src/helpers/charts/highcharts.js:20
msgid "No data to display"
msgstr "No data to display"

#: src/components/trash/Content.js:43
msgid "No Deleted Articles"
msgstr "No Deleted Articles"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:47
msgid "No detailed data to track."
msgstr "No detailed data to track."

#: src/components/reports/Content/ReportsList/ReportsHeading/ReportsHeading.js:9
msgid "No email reports created"
msgstr "No email reports created"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:145
msgid "No emails found"
msgstr "No emails found"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:127
msgid "No emails yet"
msgstr "No emails yet"

#: src/components/newsroom/content/posts/NewsroomPosts.js:291
msgid "No posts yet"
msgstr "No posts yet"

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:66
msgid "No recipients found"
msgstr "No recipients found"

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:50
msgid "No recipients yet"
msgstr "No recipients yet"

#: src/components/reports/Content/ReportsList/ReportsList.js:85
msgid "No reports are assigned to the topic."
msgstr "No reports are assigned to the topic."

#: src/store/models/Megalist/MegalistFilter.js:42
#: src/helpers/withTranslatePopup/TranslatePopupContent.js:89
#: src/helpers/withMenuPopup/MntrMenuPopupContent.js:58
#: src/components/widgets/modules/tvr/WidgetTvr.js:78
#: src/components/widgets/modules/medialist/WidgetMedialist.js:127
#: src/components/widgets/modules/feed/WidgetFeedSimple.js:75
#: src/components/staff/admin/customers/Customers.js:37
#: src/components/misc/MntrMultiSelect/MultiSelect.js:22
#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:16
#: src/components/medialist/content/MedialistHeading.js:15
#: src/components/medialist/content/MedialistInspector/Feed/Feed.js:25
#: src/components/medialist/content/FeedMedialist/FeedMedialistEmpty/FeedMedialistEmpty.js:8
#: src/components/layout/Sidebar/modules/SidebarTopics/SidebarTopicsFolders.js:118
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:371
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitorsReports.js:64
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitors.js:86
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:280
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:34
#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTitle.js:99
#: src/components/forms/dashboard/Search/SearchEmailingEmailMessages.js:32
#: src/components/forms/dashboard/Search/SearchEmailingCampaigns.js:31
#: src/components/emailing/content/CampaignAutocompleteList.tsx:23
msgid "No results found"
msgstr "No results found"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:177
msgid "No senders"
msgstr "No senders"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:129
msgid "No subject"
msgstr "No subject"

#: src/components/emailing/helpers/displayEmailingTitle.js:18
#: src/components/emailing/helpers/displayEmailingTitle.js:21
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:149
#: src/components/emailing/components/RecipientsFeed/EmailMessagesList.js:13
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:123
msgid "No title"
msgstr "No title"

#: src/components/topics/Content/TopicsHeading/TopicsHeading.js:9
msgid "No topics created"
msgstr "No topics created"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:209
msgid "No users"
msgstr "No users"

#: src/components/staff/admin/workspace/Workspace.js:862
msgid "No users assigned to this workspace"
msgstr "No users assigned to this workspace"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:187
msgid "No users found"
msgstr "No users found"

#: src/components/newsroom/content/dashboard/ChartVisits.js:59
msgid "No visits yet"
msgstr "No visits yet"

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:14
msgid "No workspace"
msgstr "No workspace"

#: src/components/staff/admin/user/User.js:311
msgid "No workspaces assigned to this user"
msgstr "No workspaces assigned to this user"

#. js-lingui-explicit-id
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:542
msgid "filetypes.none"
msgstr "none"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:90
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:43
#: src/components/misc/ActionsBar/Selector/Selector.js:58
msgid "None"
msgstr "None"

#. js-lingui-explicit-id
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:43
msgid "attachment.none"
msgstr "None"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:152
msgid "NOT"
msgstr "NOT"

#: src/components/reports/history/RecipientsTableRow.js:49
#: src/components/reports/history/HistoryTable.js:82
#: src/components/reports/history/HistoryTable.js:111
#: src/components/reports/history/HistoryTable.js:325
msgid "Not delivered"
msgstr "Not delivered"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:151
msgid "Not verified"
msgstr "Not verified"

#: src/store/models/dashboards/DashboardPreview.js:156
#: src/components/staff/admin/workspace/Workspace.js:339
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:36
#: src/components/medialist/forms/FormEditAuthor.js:703
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:63
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:63
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:35
msgid "Note"
msgstr "Note"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:128
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:368
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:83
msgid "Notes"
msgstr "Notes"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:30
msgid "Notification about mention <0>within 3 minutes</0>"
msgstr "Notification about mention <0>within 3 minutes</0>"

#: src/components/layout/Header/AppNotifications/AppNotifications.js:99
#: src/components/layout/Header/AppNotifications/AppNotifications.js:107
msgid "Notification Settings"
msgstr "Notification Settings"

#: src/components/notifications/ContentTvr.js:39
#: src/components/notifications/ContentTopics.js:24
#: src/components/notifications/AppNotifications/AppNotifications.js:21
#: src/components/layout/Header/AppNotifications/AppNotifications.js:152
msgid "Notifications"
msgstr "Notifications"

#: src/constants/analytics.js:55
#: src/constants/analytics.js:566
#: src/constants/analytics.js:678
msgid "Number of articles"
msgstr "Number of articles"

#: src/constants/analytics/primeScoreCharts.ts:57
msgid "Number of articles by PRIMe relevant vs irrelevant"
msgstr "Number of articles by PRIMe relevant vs irrelevant"

#: src/constants/analytics.js:53
msgid "Number of articles by sentiment"
msgstr "Number of articles by sentiment"

#: src/components/monitoring/Inspector/InspectorMonitora/SocialParentText/SocialParentHeader.js:94
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:41
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:132
#: src/components/misc/ActionsBar/View/ViewMenu.js:289
msgid "Number of followers"
msgstr "Number of followers"

#: src/store/models/OurChart.js:188
msgid "Number of mentions"
msgstr "Number of mentions"

#: src/components/tvr/Content/Content.js:61
msgid "Number of outputs"
msgstr "Number of outputs"

#: src/components/reports/Content/ReportsList/ReportsTopMentionsMode.js:21
msgid "Number of TOP stories"
msgstr "Number of TOP stories"

#: src/components/misc/Changelog/ChangelogTable.js:33
msgid "Object"
msgstr "Object"

#: src/components/monitoring/WorkspaceArticles/Limits.js:57
msgid "OCR"
msgstr "OCR"

#: src/components/monitoring/FeedList/FeedListItem/FeedListOlderDivider/FeedListOlderDivider.js:24
msgid "Older articles"
msgstr "Older articles"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:142
msgid "on frontpage"
msgstr "on frontpage"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:91
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:125
msgid "on this continent"
msgstr "on this continent"

#: src/components/misc/ActionsBar/View/ViewMenu.js:62
#: src/components/layout/AuthWrapper/constants/features.slides.js:47
msgid "Online"
msgstr "Online"

#: src/constants/analytics.js:1287
msgid "Online categories"
msgstr "Online categories"

#: src/store/models/Megalist/MegalistFilter.js:34
msgid "Only Selected"
msgstr "Only Selected"

#: src/store/models/Megalist/MegalistFilter.js:38
msgid "Only Unselected"
msgstr "Only Unselected"

#: src/components/medialist/forms/FormEditAuthor.js:892
msgid "Only you can see all the data you entered and the changes made."
msgstr "Only you can see all the data you entered and the changes made."

#: src/components/newsroom/content/posts/NewsroomPosts.js:185
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:159
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderVideo/HeaderVideo.js:52
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderSocial/HeaderSocial.js:34
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:105
#: src/components/monitoring/Inspector/InspectorEntityKnowledgeBase/InspectorKnowledgeBaseHeader.js:12
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:51
#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthor.js:55
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:20
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:54
msgid "Open"
msgstr "Open"

#: src/components/staff/admin/customers/Customer.js:187
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:102
msgid "Open customer detail"
msgstr "Open customer detail"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:102
msgid "Open In Feed"
msgstr "Open In Feed"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/EmbedFacebook/EmbedFacebook.tsx:49
msgid "Open on Facebook"
msgstr "Open on Facebook"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:209
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:225
msgid "Open rate"
msgstr "Open rate"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:199
#: src/components/staff/admin/customer/users/UsersTable.js:138
msgid "Open user detail"
msgstr "Open user detail"

#: src/components/staff/admin/user/WorkspacesTable.js:154
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:146
msgid "Open workspace detail"
msgstr "Open workspace detail"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:281
msgid "Opportunity to see"
msgstr "Opportunity to see"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:190
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:320
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:360
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:371
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:387
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:421
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:441
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:510
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:230
#: src/components/emailing/forms/FormSenderSettings.js:89
#: src/components/emailing/forms/FormSenderSettings.js:105
msgid "optional"
msgstr "optional"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:146
msgid "OR"
msgstr "OR"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:70
msgid "Or use an external service"
msgstr "Or use an external service"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:91
msgid "Order articles"
msgstr "Order articles"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:265
msgid "Ordered list"
msgstr "Ordered list"

#: src/helpers/withTranslatePopup/TranslatePopupContent.js:70
msgid "Original"
msgstr "Original"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:45
msgid "Original value"
msgstr "Original value"

#: src/constants/analytics.js:1125
#: src/constants/analytics.js:1205
#: src/constants/analytics.js:1227
#: src/constants/analytics.js:1247
#: src/constants/analytics.js:1267
#: src/constants/analytics.js:1286
#: src/constants/analytics.js:1305
#: src/constants/analytics.js:1324
#: src/constants/analytics.js:1337
#: src/constants/analytics/primeScoreCharts.ts:135
#: src/components/notifications/ContentTvr.js:118
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:57
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:57
#: src/app/components/monitoring-navigation.tsx:252
msgid "Other"
msgstr "Other"

#: src/store/models/Megalist/Megalist.js:48
msgid "Other Regions"
msgstr "Other Regions"

#: src/components/staff/admin/workspace/Workspace.js:671
msgid "Other settings"
msgstr "Other settings"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:155
msgid "Others"
msgstr "Others"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:278
#: src/components/misc/ActionsBar/View/ViewMenu.js:219
msgid "OTS"
msgstr "OTS"

#: src/pages/_error.js:55
msgid "Our team has been notified. We're sorry for the inconvenience."
msgstr "Our team has been notified. We're sorry for the inconvenience."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:57
msgid "Outline the main content or details you want included."
msgstr "Outline the main content or details you want included."

#: src/components/medialist/constants/medialist.tabNavigation.js:20
msgid "Overview"
msgstr "Overview"

#: src/components/staff/admin/workspace/Workspace.js:603
msgid "Own content"
msgstr "Own content"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:86
msgid "Own selection"
msgstr "Own selection"

#. js-lingui-explicit-id
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:92
msgid "page.shortened"
msgstr "p."

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:64
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:76
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:72
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:220
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupPagesContent.js:27
#: src/components/misc/Pagination/Pagination.js:26
#: src/components/misc/Pagination/Pagination.js:39
#: src/components/misc/Pagination/Pagination.js:45
#: src/components/misc/Pagination/Pagination.js:76
#: src/components/misc/Pagination/Pagination.js:84
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:118
msgid "Page"
msgstr "Page"

#: src/components/tvr/Inspector/InspectorMedia/PaginationMedia/PaginationMedia.js:30
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPagination.js:48
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:258
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupPagesContent.js:18
#: src/components/layout/MntrActiveFilters/modules/PageNumbers.js:22
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:123
msgid "Pages"
msgstr "Pages"

#. placeholder {0}: data.publication.pages.length
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:238
msgid "Pages ({0} total)"
msgstr "Pages ({0} total)"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:60
msgid "Paid"
msgstr "Paid"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:160
msgid "Paragraph"
msgstr "Paragraph"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:423
msgid "Parse PDF"
msgstr "Parse PDF"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:140
msgid "Partner Code (optional)"
msgstr "Partner Code (optional)"

#: src/pages/user/reset-password/new.tsx:32
#: src/components/page/auth/SignUp/SignUp.js:42
#: src/components/page/auth/Login/Login.tsx:46
#: src/components/emailing/forms/FormSenderSettings.js:96
msgid "Password"
msgstr "Password"

#: src/pages/user/reset-password/new.tsx:39
msgid "Password again"
msgstr "Password again"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:119
msgid "Password change"
msgstr "Password change"

#: src/components/staff/admin/workspace/UsersTable/CopyPassword.js:34
msgid "Password copied to the clipboard."
msgstr "Password copied to the clipboard."

#: src/components/misc/VideoPlayer/CropControls.js:127
#: src/components/misc/VideoPlayer/Controls.js:98
msgid "Pause"
msgstr "Pause"

#: src/components/reports/history/RecipientsTableRow.js:58
msgid "Pending"
msgstr "Pending"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:30
msgid "people"
msgstr "people"

#: src/helpers/formatNumber.js:29
msgid "per month"
msgstr "per month"

#: src/components/OurChart/OurChartAdvanced.js:155
msgid "Percent Share"
msgstr "Percent Share"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:137
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:562
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:564
#: src/components/misc/ActionsBar/View/ViewMenu.js:326
msgid "Perex"
msgstr "Perex"

#: src/components/exportList/History/HistoryTable/HistoryTable.js:60
msgid "Period"
msgstr "Period"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:39
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:44
msgid "Periodicity"
msgstr "Periodicity"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:60
msgid "Permissions"
msgstr "Permissions"

#: src/components/medialist/forms/FormEditAuthor.js:841
#: src/components/medialist/forms/FormEditAuthor.js:1002
#: src/components/medialist/forms/FormEditAuthor.js:1007
msgid "Personal Website"
msgstr "Personal Website"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:66
msgid "Personalization:"
msgstr "Personalization:"

#: src/components/page/auth/SignUp/SignUp.js:49
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:132
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:80
#: src/components/medialist/forms/FormEditAuthor.js:766
#: src/components/medialist/forms/FormEditAuthor.js:900
#: src/components/medialist/forms/FormEditAuthor.js:906
msgid "Phone"
msgstr "Phone"

#: src/constants/analytics.js:677
#: src/constants/analytics.js:697
#: src/constants/analytics.js:716
#: src/constants/analytics.js:735
#: src/constants/analytics.js:754
#: src/constants/analytics.js:773
#: src/constants/analytics.js:792
#: src/constants/analytics.js:811
#: src/constants/analytics.js:826
#: src/constants/analytics.js:844
#: src/constants/analytics.js:863
#: src/constants/analytics.js:882
#: src/constants/analytics.js:902
#: src/constants/analytics.js:922
#: src/constants/analytics.js:943
#: src/constants/analytics.js:963
#: src/constants/analytics.js:978
#: src/constants/analytics.js:992
#: src/constants/analytics.js:1008
#: src/constants/analytics.js:1023
#: src/constants/analytics.js:1038
#: src/constants/analytics/primeScoreCharts.ts:94
msgid "Pie"
msgstr "Pie"

#: src/helpers/formatNumber.js:39
msgid "pieces"
msgstr "pieces"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:71
msgid "Plain"
msgstr "Plain"

#: src/components/staff/admin/workspace/Workspace.js:309
msgid "Plan"
msgstr "Plan"

#: src/components/emailing/content/promo/PromoEmailing.js:18
msgid "Platform for email communication with journalists."
msgstr "Platform for email communication with journalists."

#: src/components/misc/VideoPlayer/CropControls.js:127
#: src/components/misc/VideoPlayer/Controls.js:98
msgid "Play"
msgstr "Play"

#: src/components/emailing/content/sender/EmailingSenderContent.js:34
msgid "Please add a sender address that will be used for sending emails."
msgstr "Please add a sender address that will be used for sending emails."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:545
msgid "Please copy and insert this code into your website. Modify the width and height values of the iframe according to your requirements. Additionally, it’s possible to hide the header and footer if necessary."
msgstr "Please copy and insert this code into your website. Modify the width and height values of the iframe according to your requirements. Additionally, it’s possible to hide the header and footer if necessary."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:95
msgid "Please remove some recipients."
msgstr "Please remove some recipients."

#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:168
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:81
msgid "Please select Image"
msgstr "Please select Image"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepTitleAndCommunicationPlan.tsx:60
msgid "Please select the title and review the communication plan. If it does not meet your expectations, restart the process."
msgstr "Please select the title and review the communication plan. If it does not meet your expectations, restart the process."

#: src/components/emailing/forms/FormSenderSettings.js:105
msgid "Port"
msgstr "Port"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:133
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:100
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:68
msgid "Position"
msgstr "Position"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostPreview.tsx:80
msgid "Post preview"
msgstr "Post preview"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:351
msgid "Post settings"
msgstr "Post settings"

#: src/constants/analytics.js:1132
#: src/constants/analytics.js:1157
#: src/constants/analytics.js:1212
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:37
msgid "Posts"
msgstr "Posts"

#: src/components/topics/Content/TopicsList/KeywordExtraQueryAdvanced/KeywordExtraQueryAdvanced.tsx:26
msgid "Predefined queries"
msgstr "Predefined queries"

#: src/components/misc/Capture/Capture.js:283
msgid "Preparing export..."
msgstr "Preparing export..."

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:86
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:28
#: src/components/reports/history/HistoryTable.js:406
#: src/components/reports/Content/ReportsList/ReportPreview.js:18
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:20
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:115
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:140
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:140
#: src/components/forms/dashboard/ExportResend/ExportResend.js:163
#: src/components/emailing/content/CreateEmailContent.js:278
msgid "Preview"
msgstr "Preview"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:142
msgid "Preview & Publish"
msgstr "Preview & Publish"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:110
msgid "Preview images"
msgstr "Preview images"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:66
msgid "Previous page"
msgstr "Previous page"

#: src/components/staff/SignUp.js:17
#: src/components/staff/admin/workspace/Workspace.js:296
#: src/components/staff/admin/DailyAccess/Table.js:30
msgid "Primary app"
msgstr "Primary app"

#: src/constants/analytics/primeScoreCharts.ts:122
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:420
#: src/components/layout/MntrActiveFilters/modules/PrimeFilter.tsx:25
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:46
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:33
#: src/components/analytics/AnalyticsContent.js:152
#: src/components/analytics/AnalyticsContent.js:179
msgid "PRIMe"
msgstr "PRIMe"

#: src/constants/analytics/primeScoreCharts.ts:116
msgid "PRIMe in mediatype"
msgstr "PRIMe in mediatype"

#: src/components/widgets/modules/stats/WidgetStats.js:190
msgid "PRIMe negative total value"
msgstr "PRIMe negative total value"

#: src/components/widgets/modules/stats/WidgetStats.js:183
msgid "PRIMe positive total value"
msgstr "PRIMe positive total value"

#: src/constants/analytics/primeScoreCharts.ts:76
msgid "PRIMe scale"
msgstr "PRIMe scale"

#: src/constants/analytics/primeScoreCharts.ts:9
#: src/constants/analytics/primeScoreCharts.ts:37
#: src/constants/analytics/primeScoreCharts.ts:69
#: src/constants/analytics/primeScoreCharts.ts:82
#: src/constants/analytics/primeScoreCharts.ts:102
msgid "PRIMe score"
msgstr "PRIMe score"

#: src/components/widgets/modules/stats/WidgetStats.js:176
msgid "PRIMe total average"
msgstr "PRIMe total average"

#: src/components/widgets/modules/stats/WidgetStats.js:169
msgid "PRIMe total value"
msgstr "PRIMe total value"

#: src/components/misc/ActionsBar/View/ViewMenu.js:112
#: src/components/layout/AuthWrapper/constants/features.slides.js:57
#: src/components/OurChart/OurChartAdvanced.js:260
msgid "Print"
msgstr "Print"

#: src/constants/analytics.js:1306
msgid "Print categories"
msgstr "Print categories"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:89
msgid "Professional"
msgstr "Professional"

#: src/constants/analytics.js:1154
msgid "Profile"
msgstr "Profile"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:113
#: src/components/misc/MntrEditor/modals/withModalPromoBox.js:8
#: src/components/misc/MntrEditor/extensions/ExtensionPromoBox.js:33
msgid "Promo Box"
msgstr "Promo Box"

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:66
msgid "Provide additional feedback..."
msgstr "Provide additional feedback..."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:33
msgid "Provide information such as:"
msgstr "Provide information such as:"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:74
msgid "Providing information about your company allows the AI assistant to generate more accurate and tailored content for your newsroom articles. This ensures the text aligns closely with your brand's identity and messaging"
msgstr "Providing information about your company allows the AI assistant to generate more accurate and tailored content for your newsroom articles. This ensures the text aligns closely with your brand's identity and messaging"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:291
msgid "Publication Date"
msgstr "Publication Date"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:95
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:142
msgid "Publish"
msgstr "Publish"

#: src/components/newsroom/forms/FormNewsroomPost/PostStatus.js:49
msgid "Publish date set to"
msgstr "Publish date set to"

#: src/components/newsroom/forms/FormNewsroomPost/PostStatus.js:73
msgid "Publish date set to {scheduledFormatted}"
msgstr "Publish date set to {scheduledFormatted}"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:154
msgid "Publish now"
msgstr "Publish now"

#: src/pages/newsroom/index.js:45
msgid "Publish press releases <0>easily and quickly</0>"
msgstr "Publish press releases <0>easily and quickly</0>"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:155
msgid "Publish this post immediately"
msgstr "Publish this post immediately"

#: src/components/newsroom/components/PostsList/PostsList.js:184
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:30
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:66
msgid "Published"
msgstr "Published"

#: src/components/staff/admin/user/getUserAttributes.js:14
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:35
#: src/components/layout/Sidebar/SidebarNavigation.tsx:195
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:261
#: src/components/layout/MntrActiveFilters/modules/Publisher.js:13
msgid "Publisher"
msgstr "Publisher"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:30
msgid "Publisher copyright fees"
msgstr "Publisher copyright fees"

#: src/constants/analytics.js:979
#: src/constants/analytics.js:993
#: src/constants/analytics.js:1009
#: src/constants/analytics.js:1024
#: src/constants/analytics.js:1039
msgid "Publishers"
msgstr "Publishers"

#: src/components/layout/Header/AppNotifications/AppNotifications.js:201
msgid "Push Notifications"
msgstr "Push Notifications"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:32
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:66
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:106
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:134
msgid "Quick Overview"
msgstr "Quick Overview"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:30
msgid "Quickly protect your brand reputation and stakeholder trust."
msgstr "Quickly protect your brand reputation and stakeholder trust."

#: src/components/newsroom/content/modules/CustomQuotes.tsx:64
msgid "Quote"
msgstr "Quote"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:74
#: src/components/newsroom/content/modules/CustomQuotes.tsx:33
msgid "Quotes"
msgstr "Quotes"

#: src/components/notifications/ContentTvrRequest.js:74
#: src/components/notifications/ContentTvr.js:81
#: src/components/misc/ActionsBar/View/ViewMenu.js:162
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:62
#: src/components/layout/AuthWrapper/constants/features.slides.js:75
msgid "Radio"
msgstr "Radio"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:92
msgid "Rank is primarily based on the reach and the importance of the news source."
msgstr "Rank is primarily based on the reach and the importance of the news source."

#: src/components/newsroom/components/AiTools/AiCheckPostResult.tsx:92
msgid "Re-run check"
msgstr "Re-run check"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:30
msgid "reach"
msgstr "reach"

#: src/constants/stats.ts:26
#: src/constants/analytics.js:107
#: src/constants/analytics.js:121
#: src/constants/analytics.js:123
#: src/constants/analytics.js:129
#: src/constants/analytics.js:590
#: src/constants/analytics.js:603
#: src/constants/analytics.js:736
#: src/constants/analytics.js:1025
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:421
#: src/components/misc/ActionsBar/View/ViewMenu.js:227
#: src/components/analytics/TraditionalMedia.js:34
#: src/components/analytics/TraditionalMedia.js:40
msgid "Reach"
msgstr "Reach"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:98
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:112
msgid "Reactivate"
msgstr "Reactivate"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:101
msgid "Reactivate recipient"
msgstr "Reactivate recipient"

#: src/components/reports/history/RecipientsTableRow.js:31
msgid "Read"
msgstr "Read"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:34
msgid "Read only:"
msgstr "Read only:"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:189
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:63
#: src/components/misc/ActionsBar/View/ViewMenu.js:132
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:95
msgid "Readership"
msgstr "Readership"

#: src/helpers/modal/withModalReportProblem.tsx:32
#: src/helpers/modal/withModalReportArticle.tsx:46
#: src/components/reports/history/RecipientsTableHeader.js:38
msgid "Reason"
msgstr "Reason"

#: src/components/medialist/content/MedialistDashboard.js:179
msgid "Recently edited authors"
msgstr "Recently edited authors"

#: src/components/medialist/content/MedialistDashboard.js:158
msgid "Recently viewed authors"
msgstr "Recently viewed authors"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:70
msgid "Recipient"
msgstr "Recipient"

#: src/store/models/reports/recipients/Recipients.js:27
msgid "Recipient added."
msgstr "Recipient added."

#: src/components/forms/dashboard/ExportResend/ExportResend.js:79
msgid "Recipient emails"
msgstr "Recipient emails"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:83
msgid "Recipient has no email address"
msgstr "Recipient has no email address"

#: src/store/models/reports/recipients/Recipients.js:56
msgid "Recipient removed."
msgstr "Recipient removed."

#: src/store/models/reports/recipients/Recipients.js:44
msgid "Recipient updated."
msgstr "Recipient updated."

#: src/helpers/modal/withModalTvrTopics.tsx:77
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:246
#: src/components/reports/history/HistoryTable.js:169
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:59
#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:55
#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:63
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:101
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:112
#: src/components/emailing/content/CreateEmailContent.js:269
#: src/components/emailing/content/tabs/RecipientsTab.tsx:23
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:163
msgid "Recipients"
msgstr "Recipients"

#: src/store/models/reports/recipients/Recipients.js:27
msgid "Recipients added."
msgstr "Recipients added."

#: src/components/reports/history/HistoryTable.js:52
msgid "Recipients from: {formattedCreated}"
msgstr "Recipients from: {formattedCreated}"

#: src/components/reports/history/HistoryTable.js:432
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:50
msgid "Recipients have been copied to the clipboard."
msgstr "Recipients have been copied to the clipboard."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:45
msgid "Recipients limit"
msgstr "Recipients limit"

#: src/store/models/reports/recipients/Recipients.js:68
msgid "Recipients removed."
msgstr "Recipients removed."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:25
msgid "Recipients with missing information"
msgstr "Recipients with missing information"

#: src/components/forms/dashboard/Export/RecommendedLimit.js:32
msgid "Recomended limit"
msgstr "Recomended limit"

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:59
msgid "Recommended file types: XLSX, CSV"
msgstr "Recommended file types: XLSX, CSV"

#: src/components/forms/adapters/MntrFileAdapter/MntrFileAdapter.js:70
msgid "Recommended resolution"
msgstr "Recommended resolution"

#: src/components/staff/admin/workspace/Workspace.js:136
msgid "Recreate articles"
msgstr "Recreate articles"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:566
msgid "Redo"
msgstr "Redo"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:31
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:110
msgid "Regenerate content"
msgstr "Regenerate content"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:31
msgid "Regenerate until the email text aligns perfectly with your requirements"
msgstr "Regenerate until the email text aligns perfectly with your requirements"

#: src/components/staff/admin/customers/Customers.js:27
msgid "Register new user"
msgstr "Register new user"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:55
msgid "Relevant"
msgstr "Relevant"

#: src/helpers/modal/withModalRemove.tsx:37
#: src/helpers/modal/withModalRemove.tsx:51
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:142
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:47
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:85
#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:7
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:676
#: src/components/newsroom/content/modules/CustomQuotes.tsx:58
#: src/components/newsroom/content/modules/CustomKeypoints.tsx:49
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:570
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:74
#: src/components/emailing/modules/withModalRemoveRecipients.tsx:24
#: src/components/emailing/modules/withModalRemoveRecipients.tsx:39
#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:88
#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:68
msgid "Remove"
msgstr "Remove"

#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:105
#: src/components/exportList/Content/Content.tsx:95
#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:56
msgid "Remove All"
msgstr "Remove All"

#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:34
msgid "Remove all from report"
msgstr "Remove all from report"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:97
msgid "Remove all from selection"
msgstr "Remove all from selection"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:155
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:126
msgid "Remove authors from list"
msgstr "Remove authors from list"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:50
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:88
msgid "Remove Campaign"
msgstr "Remove Campaign"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:57
msgid "Remove from article"
msgstr "Remove from article"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:338
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:285
msgid "Remove from Export"
msgstr "Remove from Export"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:40
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:99
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:173
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:257
msgid "Remove from filters"
msgstr "Remove from filters"

#: src/components/medialist/content/withRemoveFromBasketPopup.js:34
msgid "Remove from list"
msgstr "Remove from list"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:273
msgid "Remove from next report"
msgstr "Remove from next report"

#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:282
msgid "Remove from report"
msgstr "Remove from report"

#: src/components/staff/admin/user/WorkspacesTable.js:138
msgid "Remove from workspace"
msgstr "Remove from workspace"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:200
#: src/components/settings/SettingsLogo/SettingsLogo.js:145
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:335
msgid "Remove Image"
msgstr "Remove Image"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:359
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:457
msgid "Remove Link"
msgstr "Remove Link"

#: src/components/medialist/forms/modules/FormFieldUploadPhoto.js:53
msgid "Remove Photo"
msgstr "Remove Photo"

#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:69
msgid "Remove Recipients"
msgstr "Remove Recipients"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:73
msgid "Remove report"
msgstr "Remove report"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:99
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:274
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:374
#: src/components/monitoring/FeedActionsBar/withRemoveTagPopup/RemoveTagPopupContent.js:11
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:77
msgid "Remove tag"
msgstr "Remove tag"

#: src/components/staff/admin/workspace/UsersTable/RemoveUser.js:25
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:398
msgid "Remove user"
msgstr "Remove user"

#: src/components/staff/admin/workspace/UsersTable/RemoveUsers.tsx:16
msgid "Remove users"
msgstr "Remove users"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:132
msgid "Remove widget"
msgstr "Remove widget"

#: src/store/models/monitoring/Inspector/Inspector.ts:428
msgid "Removed from next report."
msgstr "Removed from next report."

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:257
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:261
msgid "Rename"
msgstr "Rename"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:94
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:36
#: src/components/reports/Content/ReportsList/ReportPreview.js:26
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:28
msgid "Report preview"
msgstr "Report preview"

#: src/helpers/modal/withModalReportProblem.tsx:45
#: src/helpers/modal/withModalReportArticle.tsx:70
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:104
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:26
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:289
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:295
#: src/components/medialist/forms/FormEditAuthor.js:381
#: src/components/medialist/forms/FormEditAuthor.js:387
#: src/components/medialist/forms/FormEditAuthor.js:528
#: src/components/medialist/forms/FormEditAuthor.js:534
msgid "Report problem"
msgstr "Report problem"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:76
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:51
msgid "Report will be removed."
msgstr "Report will be removed."

#: src/pages/reports/index.js:15
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:118
#: src/components/reports/ReportChangelog.js:18
#: src/components/reports/history/Content.js:31
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:45
#: src/app/components/monitoring-navigation.tsx:165
msgid "Reports"
msgstr "Reports"

#: src/components/layout/AuthWrapper/constants/features.slides.js:353
msgid "Reports and exports"
msgstr "Reports and exports"

#: src/pages/reports/history.js:12
#: src/components/reports/history/Content.js:35
#: src/components/reports/Content/ReportsList/ReportsList.js:37
#: src/app/components/monitoring-navigation.tsx:179
msgid "Reports History"
msgstr "Reports History"

#. js-lingui-explicit-id
#: src/helpers/modal/withModalRequestFeature.tsx:24
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:57
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:244
msgid "featureRequest.Request"
msgstr "Request"

#: src/helpers/modal/withModalRequestFeature.tsx:50
#: src/components/misc/MntrButton/modules/ButtonRequestFeature.js:36
msgid "Request Access?"
msgstr "Request Access?"

#: src/helpers/modal/withModalTvrTopics.tsx:41
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:509
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:510
msgid "Request change"
msgstr "Request change"

#: src/components/notifications/ContentTvrRequest.js:32
#: src/components/notifications/ContentTvr.js:120
msgid "Request Channels"
msgstr "Request Channels"

#: src/components/analytics/AnalyticsContent.js:250
msgid "Request social media?"
msgstr "Request social media?"

#: src/components/analytics/AnalyticsContent.js:217
msgid "Request traditional media?"
msgstr "Request traditional media?"

#: src/helpers/store/apiClient.js:153
msgid "Request was cancelled."
msgstr "Request was cancelled."

#. js-lingui-explicit-id
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:104
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:78
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:49
#: src/components/misc/PromoBox/PromoBox.js:142
#: src/components/misc/MntrButton/modules/ButtonRequestFeature.js:25
#: src/components/analytics/AnalyticsContent.js:199
#: src/components/analytics/AnalyticsContent.js:232
msgid "featureRequest.Requested"
msgstr "Requested"

#: src/components/staff/admin/DailyAccess/Table.js:33
msgid "Requests"
msgstr "Requests"

#: src/components/reports/history/HistoryTable.js:65
#: src/components/reports/history/HistoryTable.js:446
msgid "Resend"
msgstr "Resend"

#: src/components/reports/history/Compose.js:42
msgid "Resend email report"
msgstr "Resend email report"

#: src/components/reports/history/Compose.js:44
msgid "Resend email report from: {formattedCreated}"
msgstr "Resend email report from: {formattedCreated}"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:102
msgid "Resend verification email"
msgstr "Resend verification email"

#: src/store/models/reports/history/History.js:92
msgid "Resending email report. Check back later."
msgstr "Resending email report. Check back later."

#: src/components/reports/history/HistoryTable.js:215
msgid "Resent report"
msgstr "Resent report"

#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:33
#: src/components/medialist/forms/FormEditAuthor.js:591
msgid "Reset"
msgstr "Reset"

#: src/components/medialist/forms/FormEditAuthor.js:295
#: src/components/medialist/forms/FormEditAuthor.js:459
msgid "Reset author profile"
msgstr "Reset author profile"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:153
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:72
msgid "Reset filter"
msgstr "Reset filter"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:61
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:241
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:258
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:277
msgid "Reset filters"
msgstr "Reset filters"

#: src/pages/user/reset-password/index.tsx:36
msgid "Reset Password"
msgstr "Reset Password"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:69
msgid "Reset selection"
msgstr "Reset selection"

#: src/helpers/modal/withModalResetAuthor.tsx:25
#: src/helpers/modal/withModalResetAuthor.tsx:39
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:87
msgid "Restore"
msgstr "Restore"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:403
msgid "Restore articles"
msgstr "Restore articles"

#: src/components/settings/SettingsTheme/ThemePicker.tsx:122
msgid "Restore default"
msgstr "Restore default"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:131
#: src/components/misc/Changelog/ChangelogTable.js:40
msgid "Revert"
msgstr "Revert"

#: src/components/misc/Changelog/ChangelogTableRow.js:192
msgid "Revert actions"
msgstr "Revert actions"

#: src/components/misc/Changelog/ChangelogTableRow.js:209
msgid "Revert now"
msgstr "Revert now"

#: src/components/misc/Changelog/ChangelogTableRow.js:163
msgid "Reverted on:"
msgstr "Reverted on:"

#: src/components/newsroom/components/AiTools/AiGenerateCommunicationPlan.tsx:34
msgid "Review communication plan"
msgstr "Review communication plan"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:204
msgid "Roadmap"
msgstr "Roadmap"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:65
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:230
msgid "Role"
msgstr "Role"

#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:59
msgid "Row"
msgstr "Row"

#: src/components/medialist/forms/FormEditAuthor.js:862
#: src/components/medialist/forms/FormEditAuthor.js:1038
msgid "Salutation"
msgstr "Salutation"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:142
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:229
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:103
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:44
#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:116
#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:215
#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:101
#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:70
#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:122
#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:47
#: src/components/staff/admin/workspace/Workspace.js:234
#: src/components/staff/admin/workspace/Workspace.js:951
#: src/components/staff/admin/user/User.js:176
#: src/components/staff/admin/user/User.js:329
#: src/components/settings/SettingsTheme/ThemePicker.tsx:139
#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:103
#: src/components/settings/SettingsLogo/SettingsLogo.js:164
#: src/components/settings/SettingsApplication/SettingsApplication.js:50
#: src/components/reports/Content/ReportsList/ReportsForm.js:342
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:36
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:150
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:224
#: src/components/newsroom/content/dashboard/NewsroomBlogSettings.js:21
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:591
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:73
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:82
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:21
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:467
#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:86
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:242
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:116
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:191
#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:35
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:170
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:21
#: src/components/misc/Capture/Capture.js:238
#: src/components/medialist/forms/FormEditAuthor.js:601
#: src/components/medialist/forms/FormEditAuthor.js:741
#: src/components/layout/Sidebar/modules/SidebarTopics/FormFolder.tsx:25
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:55
#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:91
#: src/components/forms/tags/FormEditTag/FormEditTag.js:48
#: src/components/emailing/forms/FormSenderSettings.js:193
#: src/components/emailing/content/CreateEmailContent.js:321
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:58
#: src/components/dashboards/DashboardSelector/FormEditDashboard.js:35
msgid "Save"
msgstr "Save"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:147
msgid "Save & Publish"
msgstr "Save & Publish"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:151
msgid "Save & Schedule"
msgstr "Save & Schedule"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:188
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:61
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:61
msgid "Save as"
msgstr "Save as"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/ModalAddDashboardFooter.tsx:35
msgid "Save changes"
msgstr "Save changes"

#: src/components/misc/Capture/Capture.js:245
#: src/components/OurChart/OurChartAdvanced.js:187
msgid "Save in format"
msgstr "Save in format"

#: src/helpers/modal/withModalEmailPreview.js:94
msgid "Save report"
msgstr "Save report"

#: src/components/topics/Content/TopicsList/FormSaveMegalist/FormSaveMegalist.js:20
msgid "Save selection"
msgstr "Save selection"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:142
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:51
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:51
msgid "Save settings"
msgstr "Save settings"

#: src/constants/analytics/primeScoreCharts.ts:75
msgid "Scatter"
msgstr "Scatter"

#: src/components/misc/Changelog/ChangelogTableRow.js:212
msgid "Schedule revert"
msgstr "Schedule revert"

#: src/components/newsroom/components/PostsList/PostsList.js:188
msgid "Scheduled"
msgstr "Scheduled"

#: src/components/misc/Changelog/ChangelogTableRow.js:172
msgid "Scheduled on:"
msgstr "Scheduled on:"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:99
msgid "Scheduled to send at {scheduledDateFormatted}, are you sure you want to delete this email?"
msgstr "Scheduled to send at {scheduledDateFormatted}, are you sure you want to delete this email?"

#: src/components/emailing/content/CreateEmailContent.js:150
msgid "Scheduled to send email"
msgstr "Scheduled to send email"

#: src/components/misc/ActionsBar/View/ViewMenu.js:247
msgid "Scope of mention"
msgstr "Scope of mention"

#: src/components/tvr/Inspector/InspectorMedia/PaginationMedia/PaginationMedia.js:46
msgid "Screens"
msgstr "Screens"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:33
msgid "Screenshot"
msgstr "Screenshot"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:106
#: src/components/tariff/MonitoredMedia/MonitoredMedia.js:174
#: src/components/monitoring/Inspector/InspectorMonitora/HashTagsList/HashTagsList.js:55
#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:59
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Keywords.js:38
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:139
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/MedialistArticlesFilterSearchQuery.js:55
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/MedialistArticlesFilterSearchQuery.js:84
#: src/components/layout/MntrFiltersBar/forms/FormNote/FormNote.js:33
#: src/components/layout/Header/SearchSuggest/SearchSuggest.js:51
#: src/components/forms/dashboard/Search/SearchForm.js:71
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery.js:56
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery.js:85
msgid "Search"
msgstr "Search"

#: src/components/forms/dashboard/Search/SearchForm.js:75
msgid "Search authors"
msgstr "Search authors"

#: src/pages/authors/index.js:53
msgid "Search authors by <0>many filters</0>"
msgstr "Search authors by <0>many filters</0>"

#: src/components/forms/dashboard/Search/SearchForm.js:79
msgid "Search changelog"
msgstr "Search changelog"

#: src/components/forms/dashboard/Search/SearchForm.js:43
#: src/components/forms/dashboard/Search/SearchAdmin.js:39
msgid "Search customers"
msgstr "Search customers"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:360
msgid "Search engine metadata"
msgstr "Search engine metadata"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:58
#: src/components/help/search/Content/RulesPhrase.tsx:16
msgid "Search for phrases"
msgstr "Search for phrases"

#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:28
#: src/components/layout/Header/HeaderWithObserver.tsx:201
#: src/components/layout/Header/HeaderWithObserver.tsx:240
#: src/app/(authorized)/help/search/page.tsx:17
msgid "Search help"
msgstr "Search help"

#: src/components/layout/Header/SearchSuggest/SearchSuggest.js:70
msgid "Search History"
msgstr "Search History"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:62
msgid "Search in"
msgstr "Search in"

#: src/components/forms/dashboard/Search/SearchForm.js:52
msgid "Search in {topicName}"
msgstr "Search in {topicName}"

#: src/components/forms/dashboard/Search/SearchForm.js:33
msgid "Search in archive"
msgstr "Search in archive"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:44
msgid "Search in author"
msgstr "Search in author"

#: src/components/forms/dashboard/Search/SearchForm.js:63
msgid "Search in Emailing"
msgstr "Search in Emailing"

#: src/components/forms/dashboard/Search/SearchForm.js:59
msgid "Search in Newsroom"
msgstr "Search in Newsroom"

#: src/components/layout/MntrFiltersBar/forms/FormNote/FormNote.js:31
msgid "Search in Notes"
msgstr "Search in Notes"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:102
#: src/components/forms/dashboard/Search/SearchForm.js:54
msgid "Search in topic"
msgstr "Search in topic"

#: src/components/forms/dashboard/Search/SearchForm.js:34
#: src/components/forms/dashboard/Search/SearchForm.js:67
#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:36
msgid "Search in topics"
msgstr "Search in topics"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTitle.js:90
msgid "Search job position"
msgstr "Search job position"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:112
#: src/components/topics/Content/TopicsList/Keyword/KeywordExtraQuery.js:37
#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:47
msgid "Search keywords in conjunction with the phrase"
msgstr "Search keywords in conjunction with the phrase"

#: src/components/staff/admin/workspace/Workspace.js:172
#: src/components/staff/admin/user/User.js:100
msgid "Search log"
msgstr "Search log"

#: src/components/medialist/forms/FormEditAuthor.js:371
#: src/components/medialist/forms/FormEditAuthor.js:514
#: src/components/medialist/content/FeedMedialist/FeedMedialistItem.js:181
msgid "Search on Google"
msgstr "Search on Google"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:126
#: src/components/help/search/Content/RulesOperators.tsx:16
msgid "Search operators"
msgstr "Search operators"

#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:41
msgid "Search query"
msgstr "Search query"

#: src/store/models/Megalist/MegalistFilter.js:46
msgid "Search Results"
msgstr "Search Results"

#: src/components/layout/MntrFiltersBar/forms/FormSearchSources/FormSearchSources.js:33
msgid "Search source or publisher"
msgstr "Search source or publisher"

#. js-lingui-explicit-id
#: src/components/topics/Content/TopicsList/MegalistSearch.js:42
msgid "megalist.search"
msgstr "Search Source or Publisher"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:122
msgid "Search users"
msgstr "Search users"

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:25
msgid "Second Step"
msgstr "Second Step"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:311
msgid "Section"
msgstr "Section"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:55
#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:79
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:30
#: src/components/misc/ActionsBar/Selector/Selector.js:29
#: src/components/misc/ActionsBar/Selector/Selector.js:70
msgid "Select"
msgstr "Select"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:106
msgid "Select a method"
msgstr "Select a method"

#: src/helpers/modal/withModalAddArticle/ArticleBoxesPreview.tsx:31
msgid "Select a preview card for the newsroom article to include in the email"
msgstr "Select a preview card for the newsroom article to include in the email"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:33
#: src/components/layout/MntrFiltersBar/modules/MenuFilterToggleAllButtons.js:59
msgid "Select all"
msgstr "Select all"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:120
msgid "Select article"
msgstr "Select article"

#: src/components/misc/portable/PortableResend/PortableResend.js:118
#: src/components/misc/portable/PortableExport/PortableExport.js:113
msgid "Select articles to export."
msgstr "Select articles to export."

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:65
msgid "Select at least one mediatype"
msgstr "Select at least one mediatype"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterContent.tsx:67
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:52
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewSocialEngagement/PreviewSocialEngagement.js:32
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:51
#: src/components/analytics/AnalyticsContent.js:106
msgid "Select at least one topic"
msgstr "Select at least one topic"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:73
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:85
msgid "Select campaign"
msgstr "Select campaign"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:41
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:48
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:54
msgid "Select category"
msgstr "Select category"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:350
#: src/components/misc/ColorPicker/ColorPickerSelector.js:100
#: src/components/misc/ColorPicker/ColorPicker.js:61
#: src/components/misc/ColorPicker/ColorPicker.js:67
msgid "Select color"
msgstr "Select color"

#: src/components/newsroom/forms/FormNewsroomPost/CoverImageUpload.js:85
msgid "Select Cover Image"
msgstr "Select Cover Image"

#: src/components/topics/Content/TopicsList/KeywordExtraQueryAdvanced/KeywordExtraQueryAdvanced.tsx:25
msgid "Select from our list of predefined queries"
msgstr "Select from our list of predefined queries"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:130
#: src/components/settings/SettingsLogo/SettingsLogo.js:129
#: src/components/forms/adapters/MntrFileAdapter/MntrFileAdapter.js:58
msgid "Select Image"
msgstr "Select Image"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:320
msgid "Select Logo"
msgstr "Select Logo"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:108
msgid "Select newsroom"
msgstr "Select newsroom"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:75
msgid "Select pages"
msgstr "Select pages"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:96
msgid "Select sources"
msgstr "Select sources"

#: src/components/emailing/forms/FormSenderSettings.js:116
msgid "Select the encryption method used by your SMTP server."
msgstr "Select the encryption method used by your SMTP server."

#: src/components/newsroom/components/AiTools/AiGenerateTitles.tsx:45
msgid "Select the title"
msgstr "Select the title"

#: src/components/widgets/modules/stats/WidgetStats.js:79
#: src/components/widgets/modules/socialEngagement/WidgetSocialEngagement.js:41
#: src/components/widgets/modules/analytics/WidgetAnalytics.js:52
#: src/components/monitoring/Monitoring.js:166
msgid "Select Topic"
msgstr "Select Topic"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:85
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormSubtype/FormSubtype.js:81
msgid "Select type"
msgstr "Select type"

#: src/components/emailing/forms/FormSenderSettings.js:207
msgid "Select verification method"
msgstr "Select verification method"

#: src/helpers/charts/makeGranularityMenu.js:6
msgid "Select view"
msgstr "Select view"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:106
msgid "Select workspace"
msgstr "Select workspace"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:79
msgid "Selected"
msgstr "Selected"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:122
msgid "Selected articles will be removed."
msgstr "Selected articles will be removed."

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:96
msgid "Selected merge tags can not be applied to the author"
msgstr "Selected merge tags can not be applied to the author"

#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:188
msgid "Selected sources"
msgstr "Selected sources"

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:69
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:133
msgid "Selected: {selectedLength}/{MAX_SELECTED_LIMIT}"
msgstr "Selected: {selectedLength}/{MAX_SELECTED_LIMIT}"

#: src/store/models/Megalist/Megalist.js:376
msgid "Selection \"{name}\" was removed."
msgstr "Selection \"{name}\" was removed."

#: src/store/models/Megalist/Megalist.js:335
#: src/store/models/Megalist/Megalist.js:354
msgid "Selection saved as \"{name}\"."
msgstr "Selection saved as \"{name}\"."

#: src/components/reports/history/Compose.js:84
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:198
#: src/components/forms/dashboard/ExportResend/ExportResend.js:179
#: src/components/exportList/Content/Content.tsx:86
#: src/components/emailing/content/CreateEmailContent.js:325
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:122
msgid "Send"
msgstr "Send"

#: src/components/misc/portable/PortableResend/PortableResend.js:70
#: src/components/misc/portable/PortableResend/PortableResend.js:110
msgid "Send all articles to email"
msgstr "Send all articles to email"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:132
msgid "Send article"
msgstr "Send article"

#: src/components/misc/portable/PortableResend/PortableResend.js:66
#: src/components/misc/portable/PortableResend/PortableResend.js:106
msgid "Send article to email"
msgstr "Send article to email"

#: src/components/misc/portable/PortableResend/PortableResend.js:68
#: src/components/misc/portable/PortableResend/PortableResend.js:108
msgid "Send articles to email"
msgstr "Send articles to email"

#: src/components/reports/Content/ReportsList/ReportsForm.js:121
msgid "Send empty reports"
msgstr "Send empty reports"

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:76
msgid "Send Feedback"
msgstr "Send Feedback"

#: src/components/reports/Content/ReportsList/ReportsForm.js:270
msgid "Send in times (optional)"
msgstr "Send in times (optional)"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:150
msgid "Send now"
msgstr "Send now"

#: src/components/reports/Content/ReportsList/ReportsForm.js:131
#: src/components/reports/Content/ReportsList/ReportsForm.js:218
msgid "Send on days"
msgstr "Send on days"

#: src/components/reports/Content/ReportsList/ReportsForm.js:155
msgid "Send on holidays"
msgstr "Send on holidays"

#: src/components/reports/Content/ReportsList/ReportsForm.js:166
msgid "Send on times"
msgstr "Send on times"

#: src/components/emailing/content/promo/PromoEmailing.js:28
msgid "Send press releases to journalists with one click."
msgstr "Send press releases to journalists with one click."

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:151
msgid "Send this email immediately"
msgstr "Send this email immediately"

#: src/components/reports/history/RecipientsTableHeader.js:41
msgid "Send this to your IT specialist"
msgstr "Send this to your IT specialist"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:58
#: src/components/emailing/content/EmailDetailEmailContent.js:17
#: src/components/emailing/content/CreateEmailContent.js:418
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:135
msgid "Sender"
msgstr "Sender"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:186
msgid "Senders"
msgstr "Senders"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:58
msgid "Sending a generic email without an attached article provides no useful data for tracking"
msgstr "Sending a generic email without an attached article provides no useful data for tracking"

#: src/components/reports/history/HistoryTable.js:223
msgid "Sent automatically via scheduled report"
msgstr "Sent automatically via scheduled report"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:69
msgid "Sent to"
msgstr "Sent to"

#: src/constants/analytics.js:812
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:101
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Sentiment.js:30
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:349
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:61
#: src/components/OurChart/OurChartAdvanced.js:108
msgid "Sentiment"
msgstr "Sentiment"

#: src/components/emailing/forms/FormEmailRecipients.js:107
msgid "Separate emails with a space, comma, or semicolon"
msgstr "Separate emails with a space, comma, or semicolon"

#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:50
msgid "Separate regional duplicates"
msgstr "Separate regional duplicates"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:57
#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:60
msgid "Separated by space, newline, comma, or semicolon."
msgstr "Separated by space, newline, comma, or semicolon."

#: src/components/newsroom/content/dashboard/ChartVisits.js:110
msgid "Sessions"
msgstr "Sessions"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:117
msgid "Set annotation"
msgstr "Set annotation"

#: src/components/layout/AuthWrapper/constants/features.slides.js:354
msgid "Set any number of reports that will be sent to any number of contacts. Everyone receives the correct info at the right time and in the format you choose."
msgstr "Set any number of reports that will be sent to any number of contacts. Everyone receives the correct info at the right time and in the format you choose."

#: src/components/emailing/content/CreateEmailContent.js:372
msgid "Set as Draft"
msgstr "Set as Draft"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:93
msgid "Set as primary"
msgstr "Set as primary"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:163
msgid "Set automatic publishing of this post"
msgstr "Set automatic publishing of this post"

#: src/components/staff/admin/workspace/UsersTable/UpdatePermissions.tsx:36
#: src/components/staff/admin/workspace/UsersTable/UpdatePermissions.tsx:40
msgid "Set permissions"
msgstr "Set permissions"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:96
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:162
msgid "Set publish date"
msgstr "Set publish date"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:123
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:158
msgid "Set send date"
msgstr "Set send date"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:229
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:233
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:339
msgid "Set sentiment"
msgstr "Set sentiment"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:159
msgid "Set this email to auto-send"
msgstr "Set this email to auto-send"

#: src/pages/user/settings.js:19
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:387
#: src/components/newsroom/content/posts/NewsroomPosts.js:293
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:57
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:22
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:48
#: src/components/layout/Header/UserMenu/UserMenu.tsx:176
#: src/components/emailing/sidebar/EmailingSidebarDashboard.js:27
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:76
#: src/components/emailing/forms/FormSenderSettings.js:252
#: src/components/emailing/forms/FormSenderSettings.js:283
#: src/components/emailing/content/EmailingSettingsContent.js:54
#: src/components/emailing/content/EmailingCampaignsContent.tsx:40
msgid "Settings"
msgstr "Settings"

#. placeholder {0}: model.name
#: src/store/models/ResendSettings.ts:38
#: src/store/models/ExportSettings.js:26
msgid "Settings \"{0}\" was applied."
msgstr "Settings \"{0}\" was applied."

#. placeholder {0}: exportSettings.name
#: src/store/models/ResendSettings.ts:114
#: src/store/models/ExportSettings.js:80
msgid "Settings \"{0}\" was removed."
msgstr "Settings \"{0}\" was removed."

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:21
msgid "Settings complete"
msgstr "Settings complete"

#. placeholder {0}: model.name
#. placeholder {0}: exportSettings.name
#: src/store/models/ResendSettings.ts:93
#: src/store/models/ResendSettings.ts:132
#: src/store/models/ExportSettings.js:62
#: src/store/models/ExportSettings.js:96
msgid "Settings saved as \"{0}\"."
msgstr "Settings saved as \"{0}\"."

#: src/store/models/topics/TopicsStore.js:229
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:197
msgid "Settings saved."
msgstr "Settings saved."

#: src/components/dashboards/Content.js:75
#: src/components/dashboards/Content.js:76
#: src/components/dashboards/DashboardSelector/DashboardSelector.js:69
msgid "Share"
msgstr "Share"

#: src/constants/analytics.js:698
#: src/constants/analytics.js:979
#: src/constants/analytics.js:993
#: src/constants/analytics.js:1009
#: src/constants/analytics.js:1024
#: src/constants/analytics.js:1039
msgid "Share of voice"
msgstr "Share of voice"

#: src/store/models/dashboards/Dashboards.js:523
msgid "Shared link will expire"
msgstr "Shared link will expire"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:107
#: src/components/medialist/content/MedialistDashboard.js:135
msgid "Show"
msgstr "Show"

#. placeholder {0}: format.formatAttachedArticles(item.mentioned_article_count)
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:191
msgid "Show {0}"
msgstr "Show {0}"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:14
#: src/components/layout/Header/AppNotifications/AppNotifications.js:186
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:95
#: src/components/OurChart/OurChartAdvanced.js:116
msgid "Show All"
msgstr "Show All"

#. placeholder {0}: filteredRecipients.length - displayLimit
#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:130
msgid "Show all recipients (+{0})"
msgstr "Show all recipients (+{0})"

#: src/components/medialist/forms/FormEditAuthor.js:772
#: src/components/medialist/forms/FormEditAuthor.js:800
msgid "Show and copy to clipboard"
msgstr "Show and copy to clipboard"

#: src/components/reports/history/HistoryTable.js:397
#: src/components/exportList/History/HistoryTable/HistoryTable.js:86
msgid "Show articles"
msgstr "Show articles"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:123
msgid "Show articles in feed"
msgstr "Show articles in feed"

#: src/components/medialist/forms/FormEditAuthor.js:343
#: src/components/medialist/forms/FormEditAuthor.js:351
#: src/components/medialist/forms/FormEditAuthor.js:424
#: src/components/medialist/forms/FormEditAuthor.js:433
#: src/components/medialist/content/MedialistDashboard.js:99
msgid "Show authors"
msgstr "Show authors"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:133
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:28
msgid "Show changes"
msgstr "Show changes"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:23
msgid "Show checked only"
msgstr "Show checked only"

#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:21
msgid "Show history for this report"
msgstr "Show history for this report"

#: src/components/monitoring/Inspector/DemographicsData/modules/EntriesList/EntriesListContent.js:93
msgid "Show less"
msgstr "Show less"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:95
msgid "Show Less"
msgstr "Show Less"

#: src/components/monitoring/Inspector/DemographicsData/modules/EntriesList/EntriesListContent.js:93
#: src/components/medialist/content/MedialistDashboard.js:162
#: src/components/medialist/content/MedialistDashboard.js:183
msgid "Show more"
msgstr "Show more"

#: src/components/medialist/forms/FormEditAuthor.js:356
#: src/components/medialist/forms/FormEditAuthor.js:439
msgid "Show newsrooms"
msgstr "Show newsrooms"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:65
msgid "show stats"
msgstr "show stats"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:32
msgid "Show unchecked only"
msgstr "Show unchecked only"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:52
msgid "showing {counterFrom} out of {counterTo}"
msgstr "showing {counterFrom} out of {counterTo}"

#: src/pages/sign-up.tsx:11
#: src/pages/sign-up-completion.tsx:30
#: src/pages/staff/sign-up.js:11
#: src/pages/staff/sign-up-completion.js:26
#: src/components/staff/SignUp.js:30
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:174
#: src/components/page/auth/SignUp/SignUp.js:74
msgid "Sign Up"
msgstr "Sign Up"

#: src/components/emailing/content/Signature.tsx:104
msgid "Signature"
msgstr "Signature"

#: src/components/layout/MntrActiveFilters/modules/SimilarArticle.js:12
msgid "Similar to"
msgstr "Similar to"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:62
msgid "Similarity"
msgstr "Similarity"

#: src/components/emailing/content/promo/PromoEmailing.js:23
msgid "Simple setting of the appearance of the email template."
msgstr "Simple setting of the appearance of the email template."

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:124
msgid "Skip"
msgstr "Skip"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:60
msgid "Skip error lines"
msgstr "Skip error lines"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:249
msgid "SMS alerts"
msgstr "SMS alerts"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:134
msgid "SMTP settings are invalid."
msgstr "SMTP settings are invalid."

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:132
msgid "SMTP settings are valid."
msgstr "SMTP settings are valid."

#: src/components/misc/ActionsBar/View/ViewMenu.js:269
#: src/components/misc/ActionsBar/View/ViewMenu.js:353
msgid "Social data"
msgstr "Social data"

#: src/store/models/dashboards/DashboardPreview.js:110
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:51
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:51
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:32
msgid "Social Engagement"
msgstr "Social Engagement"

#: src/constants/analytics.js:198
#: src/constants/analytics.js:289
#: src/constants/analytics.js:398
#: src/constants/analytics.js:1040
#: src/components/misc/ActionsBar/View/ViewMenu.js:93
msgid "Social interactions"
msgstr "Social interactions"

#: src/constants/stats.ts:31
#: src/components/widgets/modules/stats/WidgetStats.js:143
msgid "Social Interactions"
msgstr "Social Interactions"

#: src/constants/analytics.js:309
#: src/constants/analytics.js:548
msgid "Social interactions by mention type"
msgstr "Social interactions by mention type"

#: src/constants/analytics.js:307
msgid "Social interactions by sentiment"
msgstr "Social interactions by sentiment"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:179
#: src/components/staff/admin/workspace/Workspace.js:534
#: src/components/medialist/forms/FormEditAuthor.js:819
#: src/components/medialist/forms/FormEditAuthor.js:922
#: src/components/layout/AuthWrapper/constants/features.slides.js:87
#: src/components/exportList/ExportLimit/ExportLimit.js:28
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:29
#: src/components/analytics/AnalyticsContent.js:149
#: src/components/analytics/AnalyticsContent.js:228
msgid "Social Media"
msgstr "Social Media"

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:37
msgid "Social Media in {appName}"
msgstr "Social Media in {appName}"

#: src/components/tariff/TariffLimits/TariffLimits.js:186
msgid "Social media topics limit"
msgstr "Social media topics limit"

#: src/components/staff/admin/workspace/Workspace.js:578
msgid "Social media topics limit (Sentione price = 500 Kč per topic)"
msgstr "Social media topics limit (Sentione price = 500 Kč per topic)"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:147
msgid "Social post"
msgstr "Social post"

#: src/components/medialist/content/AuthorContactInformation.js:38
msgid "Social profiles"
msgstr "Social profiles"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:56
#: src/components/misc/ActionsBar/View/ViewMenu.js:124
msgid "Sold amount"
msgstr "Sold amount"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:173
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:85
msgid "Sold amount (print+digital)"
msgstr "Sold amount (print+digital)"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:652
msgid "Some articles may not be deleted."
msgstr "Some articles may not be deleted."

#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:155
msgid "Some data are missing in the generated content. Add them manually before proceeding."
msgstr "Some data are missing in the generated content. Add them manually before proceeding."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:23
msgid "Some recipients are missing information for merge tags or email. Please add the missing information or replace the recipients by clicking on them."
msgstr "Some recipients are missing information for merge tags or email. Please add the missing information or replace the recipients by clicking on them."

#: src/helpers/store/apiClient.js:249
msgid "Something failed while preparing a server request. Our team was notified."
msgstr "Something failed while preparing a server request. Our team was notified."

#: src/pages/_error.js:44
msgid "Something's gone wrong"
msgstr "Something's gone wrong"

#: src/components/misc/ActionsBar/Sort/SortExport.js:19
#: src/components/misc/ActionsBar/Sort/Sort.js:21
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:468
#: src/components/layout/MntrFiltersBar/modules/MenuFilterOrderBy.js:23
msgid "Sort"
msgstr "Sort"

#: src/components/misc/ActionsBar/Sort/SortExport.js:25
#: src/components/misc/ActionsBar/Sort/Sort.js:38
msgid "Sort List"
msgstr "Sort List"

#: src/components/tariff/AgencyMedia/AgencyMedia.js:64
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:371
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:205
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:226
#: src/components/layout/MntrActiveFilters/modules/NewsSource.js:13
#: src/components/exportList/History/HistoryTable/HistoryTable.js:54
msgid "Source"
msgstr "Source"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:326
msgid "Source <0>{newsSourceName}</0> will be removed from the topic <1>{topicMonitorName}</1>."
msgstr "Source <0>{newsSourceName}</0> will be removed from the topic <1>{topicMonitorName}</1>."

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:37
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:456
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:510
msgid "Source File"
msgstr "Source File"

#: src/store/models/monitoring/Inspector/Inspector.ts:532
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:885
msgid "Source removed"
msgstr "Source removed"

#: src/components/staff/admin/workspace/Workspace.js:892
msgid "Sources"
msgstr "Sources"

#: src/components/staff/admin/workspace/Workspace.js:894
#: src/components/staff/admin/workspace/Workspace.js:901
msgid "Sources per client"
msgstr "Sources per client"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:233
msgid "Special tag"
msgstr "Special tag"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:47
msgid "Specify the primary goal (inform, persuade, invite, etc.)."
msgstr "Specify the primary goal (inform, persuade, invite, etc.)."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:137
msgid "Spokesperson"
msgstr "Spokesperson"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:57
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:194
msgid "Start editing"
msgstr "Start editing"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:75
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:213
msgid "Start over"
msgstr "Start over"

#: src/components/forms/inspector/FormMediaEditor.js:82
msgid "Start time must be lower than end time"
msgstr "Start time must be lower than end time"

#: src/components/emailing/content/CreateEmailContent.js:584
msgid "Start typing or click + to add more content"
msgstr "Start typing or click + to add more content"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:681
msgid "Start typing or insert image, video…"
msgstr "Start typing or insert image, video…"

#: src/components/newsroom/content/posts/ChooseTemplates.tsx:76
msgid "Start with template"
msgstr "Start with template"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:119
#: src/components/staff/admin/user/WorkspacesTable.js:77
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:80
#: src/components/staff/admin/customer/users/UsersTable.js:71
msgid "State"
msgstr "State"

#: src/store/models/dashboards/DashboardPreview.js:121
#: src/components/emailing/content/promo/PromoEmailing.js:32
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:23
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:23
#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:44
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:54
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:26
msgid "Statistics"
msgstr "Statistics"

#: src/components/reports/history/RecipientsTableHeader.js:33
#: src/components/newsroom/content/posts/NewsroomPosts.js:166
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:27
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:407
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNewsroomStatus.js:24
#: src/components/layout/MntrActiveFilters/modules/NewsroomStatus.js:21
msgid "Status"
msgstr "Status"

#: src/components/reports/history/HistoryTable.js:80
#: src/components/reports/history/HistoryTable.js:104
#: src/components/reports/history/HistoryTable.js:322
msgid "Status unknown"
msgstr "Status unknown"

#: src/components/layout/AuthWrapper/constants/features.slides.js:260
msgid "Streamline communication efforts and maximize your PR impact."
msgstr "Streamline communication efforts and maximize your PR impact."

#: src/components/reports/history/HistoryTable.js:149
#: src/components/reports/history/Compose.js:62
#: src/components/forms/dashboard/ExportResend/ExportResend.js:102
#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:92
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:86
#: src/components/emailing/content/EmailDetailEmailContent.js:27
#: src/components/emailing/content/CreateEmailContent.js:446
msgid "Subject"
msgstr "Subject"

#: src/components/misc/MntrForm/MntrForm.tsx:525
msgid "Submit"
msgstr "Submit"

#: src/constants/stats.ts:36
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:79
#: src/components/forms/dashboard/ExportResend/ExportResend.js:124
msgid "Summary"
msgstr "Summary"

#: src/components/staff/admin/customer/expenses/DetailExpenseModal.js:35
msgid "Supplier"
msgstr "Supplier"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:210
msgid "Support"
msgstr "Support"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:472
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:538
msgid "Supported file types:"
msgstr "Supported file types:"

#. placeholder {0}: item.label
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:245
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:157
msgid "Tag <0>{0}</0> will be hidden."
msgstr "Tag <0>{0}</0> will be hidden."

#. placeholder {0}: item.label
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:315
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:205
msgid "Tag <0>{0}</0> will be removed."
msgstr "Tag <0>{0}</0> will be removed."

#: src/components/forms/tags/FormNewTag/FormNewTag.js:26
#: src/components/forms/tags/FormEditTag/FormEditTag.js:25
#: src/components/forms/tags/FormEditTag/FormEditTag.js:28
msgid "Tag name"
msgstr "Tag name"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:761
#: src/store/models/authors/AuthorsStore.js:716
msgid "Tag removed successfully."
msgstr "Tag removed successfully."

#: src/constants/analytics.js:845
#: src/constants/analytics.js:1069
#: src/components/medialist/forms/FormEditAuthor.js:616
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:90
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:79
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:72
#: src/components/emailing/content/tabs/AddRecipients.tsx:70
msgid "Tags"
msgstr "Tags"

#: src/components/medialist/forms/FormEditAuthor.js:610
msgid "Tags, lists and note"
msgstr "Tags, lists and note"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:41
msgid "Target Audience:"
msgstr "Target Audience:"

#: src/components/settings/SettingsTariff/SettingsTariff.js:22
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:27
msgid "Tariff information"
msgstr "Tariff information"

#: src/components/reports/Content/ReportsList/ReportsForm.js:317
#: src/components/forms/dashboard/ExportResend/ExportResend.js:107
msgid "Template"
msgstr "Template"

#: src/components/emailing/forms/FormSenderSettings.js:167
msgid "Test DNS Settings"
msgstr "Test DNS Settings"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:377
msgid "Text"
msgstr "Text"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:207
msgid "Text align"
msgstr "Text align"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:281
#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:76
#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:36
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:113
msgid "Text Color"
msgstr "Text Color"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:155
msgid "Text format"
msgstr "Text format"

#: src/components/page/auth/Expired/Expired.js:60
msgid "Thank you for trying out {appName}."
msgstr "Thank you for trying out {appName}."

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:91
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:113
msgid "Thank you for your feedback!"
msgstr "Thank you for your feedback!"

#: src/components/page/auth/UserInactive/UserInactive.js:17
msgid "Thank you for your interest in using {appName}."
msgstr "Thank you for your interest in using {appName}."

#: src/pages/user/yoy-analysis.js:67
#: src/pages/user/reactivate-24.js:67
msgid "Thank you for your interest. We will contact you soon.<0/><1/>Have a great day,<2/><3/>{appName} team"
msgstr "Thank you for your interest. We will contact you soon.<0/><1/>Have a great day,<2/><3/>{appName} team"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:100
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:131
msgid "Thank you!"
msgstr "Thank you!"

#: src/store/models/monitoring/Inspector/Inspector.ts:910
msgid "The article already belongs to the topic."
msgstr "The article already belongs to the topic."

#: src/store/models/monitoring/WorkspaceArticles.js:164
msgid "The article has been uploaded and is currently being processed. After that it will be added to your feed. You can see the processing status in My Articles."
msgstr "The article has been uploaded and is currently being processed. After that it will be added to your feed. You can see the processing status in My Articles."

#: src/store/models/monitoring/Inspector/Inspector.ts:907
msgid "The article was added to the topic. Please reload the feed to see the changes."
msgstr "The article was added to the topic. Please reload the feed to see the changes."

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:392
msgid "The article will be removed from your media coverage view. If the article also exists in your feed, it will remain there and will not be deleted."
msgstr "The article will be removed from your media coverage view. If the article also exists in your feed, it will remain there and will not be deleted."

#: src/components/medialist/forms/FormEditAuthor.js:299
#: src/components/medialist/forms/FormEditAuthor.js:463
msgid "The author's profile will be reset to its original values."
msgstr "The author's profile will be reset to its original values."

#: src/store/models/monitoring/Inspector/MediaEditor/MediaEditorStore.js:55
msgid "The clip is being prepared. It may take a while. When the clip is ready for download, you will receive a notification."
msgstr "The clip is being prepared. It may take a while. When the clip is ready for download, you will receive a notification."

#: src/components/emailing/forms/FormSenderSettings.js:239
msgid "The DNS Verification Is Unavailable"
msgstr "The DNS Verification Is Unavailable"

#: src/components/emailing/forms/FormSenderSettings.js:240
msgid "The DNS verification settings for this email are not accessible. We suggest opting for SMTP (Simple Mail Transfer Protocol) as an alternative way of verification. If you need any additional information or help, our support team is here to assist you."
msgstr "The DNS verification settings for this email are not accessible. We suggest opting for SMTP (Simple Mail Transfer Protocol) as an alternative way of verification. If you need any additional information or help, our support team is here to assist you."

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:37
msgid "The email content can be automatically adjusted to include personalized details for each recipient"
msgstr "The email content can be automatically adjusted to include personalized details for each recipient"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:113
msgid "The email is currently empty. Please add some content to the email."
msgstr "The email is currently empty. Please add some content to the email."

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:28
msgid "The following summary was generated by a machine and may not accurately represent the original content."
msgstr "The following summary was generated by a machine and may not accurately represent the original content."

#: src/store/models/ExportStore.js:251
msgid "The full article text cannot be downloaded as you have reached your limit. To adjust this limit, please contact support."
msgstr "The full article text cannot be downloaded as you have reached your limit. To adjust this limit, please contact support."

#: src/components/tariff/TariffLimits/TariffLimits.js:31
msgid "The limit applies to the number of articles found in the last 30 days generated by set keywords. If you have reached the limit for the number of found articles, <0>edit keywords</0> or contact us to increase the limit."
msgstr "The limit applies to the number of articles found in the last 30 days generated by set keywords. If you have reached the limit for the number of found articles, <0>edit keywords</0> or contact us to increase the limit."

#: src/components/tariff/TariffLimits/TariffLimits.js:68
msgid "The limit applies to the number of exported articles in the last 30 days (topics, archive or report attachments). If you have reached the limit for the number of exported articles, you must wait until the limit is restored or contact us to increase the limit."
msgstr "The limit applies to the number of exported articles in the last 30 days (topics, archive or report attachments). If you have reached the limit for the number of exported articles, you must wait until the limit is restored or contact us to increase the limit."

#: src/components/tariff/TariffLimits/TariffLimits.js:104
msgid "The limit applies to the number of translated articles in the last 30 days (topics, archive, report or report attachments). If you are interested in increasing this limit, please contact us."
msgstr "The limit applies to the number of translated articles in the last 30 days (topics, archive, report or report attachments). If you are interested in increasing this limit, please contact us."

#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:20
msgid "The list of already exported articles can be downloaded without limitation."
msgstr "The list of already exported articles can be downloaded without limitation."

#: src/store/models/authors/Baskets/AuthorBasketDefinitionsStoreArrItem.ts:54
msgid "The list was successfully duplicated."
msgstr "The list was successfully duplicated."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:84
msgid "The main content of your post appears to be empty. The body is where you elaborate on your ideas, present your arguments, or share your story. Please add substantial content to your post to engage your readers and convey your message effectively."
msgstr "The main content of your post appears to be empty. The body is where you elaborate on your ideas, present your arguments, or share your story. Please add substantial content to your post to engage your readers and convey your message effectively."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:20
msgid "help.grp"
msgstr "The media impact aims to present the true media image of the subject as it reaches the widest group of listeners, viewers and media readers, rather than absolute numbers of articles. It is based primarily on readership (press), listenership (radio), viewership (TV) and monthly visits to the website (online). The unit of measurement of media impact is GRP (Gross Rating Points), when one GRP point corresponds to one percent of the population over 15 years of age (i.e. in Czech republic it is a group of 90,000 individuals, in Slovakia 45,000 individuals, etc.). The group consists of readers, listeners or viewers who could be addressed by the published article. A reader who could read more than one article is counted multiple times. OTS (Opportunity to See) then indicates the number of times, when a member of the target group could read or watch the article on average. In the case of the target group of all inhabitants of the Czech Republic over 15 years of age: OTS = GRP / 100."

#: src/pages/authors/index.js:43
msgid "The most <0>extensive</0> and the most <1>actual</1> medialist of journalists, publishers & other authors, in which you will find detailed information including contacts."
msgstr "The most <0>extensive</0> and the most <1>actual</1> medialist of journalists, publishers & other authors, in which you will find detailed information including contacts."

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:31
msgid "The primary sender is used as the default sender for emails. You can change this when you create an email."
msgstr "The primary sender is used as the default sender for emails. You can change this when you create an email."

#. placeholder {0}: senderItem.unverified_recipients_limit
#. placeholder {1}: senderItem.verified_recipients_limit
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:68
msgid "The recipient limit is set to {0}. For a higher limit of {1} recipients, enable DNS or SMTP verification."
msgstr "The recipient limit is set to {0}. For a higher limit of {1} recipients, enable DNS or SMTP verification."

#. placeholder {0}: appSettings.appName
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:136
msgid "The summary was created with the {0} application."
msgstr "The summary was created with the {0} application."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:493
msgid "The user is responsible for the content uploaded to the {appName} application. By uploading files, you confirm that you own the rights to the file or that the file is licensed under <0>CC0</0>."
msgstr "The user is responsible for the content uploaded to the {appName} application. By uploading files, you confirm that you own the rights to the file or that the file is licensed under <0>CC0</0>."

#: src/components/layout/Header/UserMenu/UserMenu.tsx:137
msgid "Theme"
msgstr "Theme"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:81
msgid "There are no keywords assigned to this topic"
msgstr "There are no keywords assigned to this topic"

#: src/pages/404.js:18
#: src/app/not-found-content.tsx:27
msgid "There's nothing here..."
msgstr "There's nothing here..."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:107
msgid "These settings allow not only change language of the newsroom, but to link newsrooms together. Pair them in different languages for quick and seamless transitions."
msgstr "These settings allow not only change language of the newsroom, but to link newsrooms together. Pair them in different languages for quick and seamless transitions."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:184
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:229
msgid "This field is required"
msgstr "This field is required"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:222
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:305
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:418
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:75
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:78
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:83
msgid "This field is required."
msgstr "This field is required."

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:380
msgid "This is a summary of the page's content. It appears below the headline on the search results page."
msgstr "This is a summary of the page's content. It appears below the headline on the search results page."

#: src/components/emailing/forms/FormSenderSettings.js:109
msgid "This is the port number that your SMTP server uses to send email. If you're not sure, leave it blank to use the default port."
msgstr "This is the port number that your SMTP server uses to send email. If you're not sure, leave it blank to use the default port."

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:134
msgid "This list is empty"
msgstr "This list is empty"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:65
msgid "This month"
msgstr "This month"

#: src/components/misc/Capture/Capture.js:300
msgid "this should take only a couple of seconds"
msgstr "this should take only a couple of seconds"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:269
msgid "This template was custom tailored for you. For further customization please contact our <0>support</0>."
msgstr "This template was custom tailored for you. For further customization please contact our <0>support</0>."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:52
msgid "This week"
msgstr "This week"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:78
msgid "This year"
msgstr "This year"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:151
msgid "Threshold"
msgstr "Threshold"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:192
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:316
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:320
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:70
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:99
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:190
msgid "Time"
msgstr "Time"

#: src/components/forms/inspector/FormMediaEditor.js:85
#: src/components/forms/inspector/FormMediaEditor.js:88
msgid "Time must not exceed media length"
msgstr "Time must not exceed media length"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:70
msgid "Timed"
msgstr "Timed"

#: src/constants/analytics.js:54
#: src/constants/analytics.js:78
#: src/constants/analytics.js:100
#: src/constants/analytics.js:122
#: src/constants/analytics.js:142
#: src/constants/analytics.js:191
#: src/constants/analytics.js:226
#: src/constants/analytics.js:255
#: src/constants/analytics.js:282
#: src/constants/analytics.js:308
#: src/constants/analytics.js:335
#: src/constants/analytics.js:364
#: src/constants/analytics.js:391
#: src/constants/analytics.js:417
#: src/constants/analytics.js:444
#: src/constants/analytics.js:482
#: src/constants/analytics.js:510
#: src/constants/analytics/primeScoreCharts.ts:30
#: src/constants/analytics/primeScoreCharts.ts:56
msgid "Timeline"
msgstr "Timeline"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:51
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:506
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:507
#: src/components/newsroom/content/posts/NewsroomPosts.js:156
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:34
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:218
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormWidgetTitle/FormWidgetTitle.js:17
msgid "Title"
msgstr "Title"

#. js-lingui-explicit-id
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:362
msgid "metadata.title"
msgstr "Title"

#: src/components/reports/Content/ReportsList/ReportsForm.js:289
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:85
msgid "To"
msgstr "To"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:33
msgid "To change your password, enter your current password and then the new password."
msgstr "To change your password, enter your current password and then the new password."

#: src/components/emailing/forms/FormSenderSettings.js:286
msgid "To ensure the successful delivery of emails from our system, it's necessary to configure your SMTP server with the following details:"
msgstr "To ensure the successful delivery of emails from our system, it's necessary to configure your SMTP server with the following details:"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:27
msgid "To hide some tags from the list, uncheck these tags. The user can add hidden tags back to their feed again at any time if necessary."
msgstr "To hide some tags from the list, uncheck these tags. The user can add hidden tags back to their feed again at any time if necessary."

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:100
msgid "To hide some topics from the list, uncheck these topics. The user can add hidden topics back to their feed again at any time if necessary."
msgstr "To hide some topics from the list, uncheck these topics. The user can add hidden topics back to their feed again at any time if necessary."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:492
msgid "To set up your own domain (e.g. companyname.com), please contact our team. We will be happy to help you set up your domain. We have also written a detailed guide for you."
msgstr "To set up your own domain (e.g. companyname.com), please contact our team. We will be happy to help you set up your domain. We have also written a detailed guide for you."

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:39
msgid "To view all mentions, it is necessary to activate social media monitoring."
msgstr "To view all mentions, it is necessary to activate social media monitoring."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/metaDataDate.js:19
msgid "today"
msgstr "today"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:110
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:39
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:100
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:327
msgid "Today"
msgstr "Today"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:51
msgid "Tone and Style:"
msgstr "Tone and Style:"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:88
msgid "Tone of voice"
msgstr "Tone of voice"

#: src/constants/analytics.js:1126
msgid "Top authors"
msgstr "Top authors"

#: src/constants/analytics.js:1228
msgid "Top hashtags"
msgstr "Top hashtags"

#: src/constants/analytics.js:1206
msgid "Top profiles"
msgstr "Top profiles"

#: src/constants/analytics.js:1248
msgid "Top publishers"
msgstr "Top publishers"

#: src/constants/analytics.js:1268
msgid "Top sources"
msgstr "Top sources"

#: src/constants/analytics/primeScoreCharts.ts:136
msgid "Top sources by overall PRIMe"
msgstr "Top sources by overall PRIMe"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:200
msgid "Top stories"
msgstr "Top stories"

#: src/helpers/charts/tableTemplates.js:74
#: src/components/exportList/History/HistoryTable/HistoryTable.js:57
msgid "Topic"
msgstr "Topic"

#. placeholder {0}: item.data.name
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:206
msgid "Topic <0>{0}</0> will be hidden."
msgstr "Topic <0>{0}</0> will be hidden."

#. placeholder {0}: item.data.name
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:147
msgid "Topic <0>{0}</0> will be removed."
msgstr "Topic <0>{0}</0> will be removed."

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:147
msgid "Topic Name"
msgstr "Topic Name"

#: src/pages/topics/index.js:24
#: src/components/topics/Content/TopicChangelog.js:18
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:113
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:253
#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:26
#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:80
#: src/components/notifications/ContentTopics.js:29
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:32
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:33
#: src/components/layout/MntrActiveFilters/modules/TvrTopics.js:10
#: src/components/layout/MntrActiveFilters/modules/EmptyTopics.js:21
#: src/app/components/monitoring-navigation.tsx:154
msgid "Topics"
msgstr "Topics"

#: src/components/topics/Content/TopicsHeading/TopicsHeading.js:10
msgid "Topics ({counter})"
msgstr "Topics ({counter})"

#. placeholder {0}: menuItem.topic_monitors .map((item) => { // @ts-expect-error TODO refactor topics to TS return item.label }) .join(', ')
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:233
msgid "Topics <0>{0}</0> will be hidden."
msgstr "Topics <0>{0}</0> will be hidden."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:142
msgid "Topics and keywords"
msgstr "Topics and keywords"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:291
msgid "Topics in this folder will be displayed separately and won't be deleted."
msgstr "Topics in this folder will be displayed separately and won't be deleted."

#: src/components/tariff/TariffLimits/TariffLimits.js:167
#: src/components/staff/admin/workspace/Workspace.js:439
msgid "Topics limit"
msgstr "Topics limit"

#: src/components/monitoring/Inspector/InspectorMonitora/KeywordsPagination/KeywordsPagination.js:304
msgid "total"
msgstr "total"

#: src/helpers/charts/tableTemplates.js:55
#: src/helpers/charts/tableTemplates.js:97
#: src/helpers/charts/tableTemplates.js:136
#: src/components/widgets/modules/stats/StatsBySource.js:120
#: src/components/tvr/Content/Content.js:92
msgid "Total"
msgstr "Total"

#. placeholder {0}: humanizeNumber(data)
#: src/constants/analytics.js:215
msgid "Total {0} interactions"
msgstr "Total {0} interactions"

#. placeholder {0}: humanizeNumber(data)
#: src/constants/analytics.js:471
msgid "Total influence score: {0}"
msgstr "Total influence score: {0}"

#. placeholder {0}: formatter( this.points.reduce((sum, { y }) => sum + y, 0), unit, )
#: src/components/OurChart/HighchartsRenderer.js:645
msgid "Total: {0}"
msgstr "Total: {0}"

#: src/components/emailing/content/promo/PromoEmailing.js:33
msgid "Track delivery and opening statistics."
msgstr "Track delivery and opening statistics."

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:20
msgid "Track online, traditional and social media with {appName} for a complete view of your brand and trends - never miss a beat."
msgstr "Track online, traditional and social media with {appName} for a complete view of your brand and trends - never miss a beat."

#: src/components/layout/AuthWrapper/constants/features.slides.js:166
msgid "Tracking, analysis, and reporting are an integral part of PR. Use comprehensible charts that make data analysis easier. Compare your media output with your competition."
msgstr "Tracking, analysis, and reporting are an integral part of PR. Use comprehensible charts that make data analysis easier. Compare your media output with your competition."

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:159
#: src/components/staff/admin/workspace/Workspace.js:353
#: src/components/layout/AuthWrapper/constants/features.slides.js:41
#: src/components/exportList/ExportLimit/ExportLimit.js:17
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:19
#: src/components/analytics/AnalyticsContent.js:146
#: src/components/analytics/AnalyticsContent.js:195
msgid "Traditional Media"
msgstr "Traditional Media"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:25
msgid "Traditional Media w/o percentage change"
msgstr "Traditional Media w/o percentage change"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:532
msgid "Transcribe the source file"
msgstr "Transcribe the source file"

#: src/components/monitoring/WorkspaceArticles/Limits.js:69
msgid "Transcribed seconds"
msgstr "Transcribed seconds"

#: src/components/monitoring/WorkspaceArticles/Limits.js:73
msgid "Transcript"
msgstr "Transcript"

#: src/components/medialist/content/MedialistActionsBar/FormTransformContacts.tsx:41
msgid "Transform"
msgstr "Transform"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:53
msgid "Transform & import"
msgstr "Transform & import"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:137
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:17
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformTitle.tsx:7
msgid "Transform contact list"
msgstr "Transform contact list"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:134
msgid "Transformation failed"
msgstr "Transformation failed"

#: src/helpers/withTranslatePopup/TranslatePopupContent.js:49
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:75
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:77
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:110
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:189
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:194
msgid "Translate"
msgstr "Translate"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:243
msgid "Translations"
msgstr "Translations"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:569
msgid "Transparent background"
msgstr "Transparent background"

#: src/app/components/monitoring-navigation.tsx:242
msgid "Trash"
msgstr "Trash"

#: src/constants/analytics.js:1053
#: src/constants/analytics.js:1068
msgid "Treemap"
msgstr "Treemap"

#: src/components/staff/admin/user/User.js:120
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:391
msgid "Trigger password reset"
msgstr "Trigger password reset"

#: src/components/notifications/Permissions.js:74
msgid "Try again"
msgstr "Try again"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:82
#: src/components/misc/PromoBox/PromoBox.js:144
msgid "Try for free"
msgstr "Try for free"

#: src/pages/user/reactivate-24.js:34
msgid "Try out Mediaboard"
msgstr "Try out Mediaboard"

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:57
msgid "Try social media monitoring"
msgstr "Try social media monitoring"

#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:18
msgid "Turn off these notifications"
msgstr "Turn off these notifications"

#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:27
msgid "Turn on these notifications"
msgstr "Turn on these notifications"

#: src/components/notifications/ContentTvrRequest.js:41
#: src/components/notifications/ContentTvr.js:46
#: src/components/misc/ActionsBar/View/ViewMenu.js:162
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:41
#: src/components/layout/AuthWrapper/constants/features.slides.js:65
msgid "TV"
msgstr "TV"

#: src/components/emailing/forms/FormSenderSettings.js:123
msgid "TXT record"
msgstr "TXT record"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:58
msgid "Type a coefficient"
msgstr "Type a coefficient"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:38
msgid "Type your keypoint"
msgstr "Type your keypoint"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:56
msgid "Type your main message"
msgstr "Type your main message"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:125
msgid "Type your subject or other instructions. Clearly outline the main message or information you want to convey.Provide instructions on how to structure the information, for example: use bullet points or numbered lists."
msgstr "Type your subject or other instructions. Clearly outline the main message or information you want to convey.Provide instructions on how to structure the information, for example: use bullet points or numbered lists."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:175
msgid "Unable to extract data from the URL."
msgstr "Unable to extract data from the URL."

#: src/components/emailing/content/EmailingSettingsContent.js:29
msgid "Unable to retrieve access token from the OAuth2 provider. This may be due to a network issue or provider outage. Please try again later."
msgstr "Unable to retrieve access token from the OAuth2 provider. This may be due to a network issue or provider outage. Please try again later."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:558
msgid "Undo"
msgstr "Undo"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:79
msgid "Unique visits"
msgstr "Unique visits"

#: src/components/newsroom/content/dashboard/ChartVisits.js:54
#: src/components/newsroom/content/dashboard/ChartVisits.js:96
#: src/components/newsroom/components/PostsList/PostsList.js:209
msgid "Unique Visits"
msgstr "Unique Visits"

#: src/components/reports/history/RecipientsTableRow.js:68
msgid "Unknown"
msgstr "Unknown"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:194
msgid "Unlock licensed articles"
msgstr "Unlock licensed articles"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:148
msgid "Unpublish"
msgstr "Unpublish"

#: src/components/medialist/forms/FormEditAuthor.js:577
msgid "Unsaved changes"
msgstr "Unsaved changes"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:152
msgid "Unschedule"
msgstr "Unschedule"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:149
msgid "Unsubscribe"
msgstr "Unsubscribe"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:47
msgid "Unsubscribe from emails"
msgstr "Unsubscribe from emails"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:311
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:322
msgid "Unsubscribe news source"
msgstr "Unsubscribe news source"

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:138
msgid "Update recipient"
msgstr "Update recipient"

#: src/components/medialist/content/MedialistDashboard.js:75
#: src/components/medialist/content/MedialistDashboard.js:108
msgid "Updated"
msgstr "Updated"

#: src/components/medialist/content/MedialistActionsBar/ContactsImportTitle.tsx:8
msgid "Upload either a manually completed template or a formatted contact list file. Once you import contacts, they will automatically appear in the Import. You can also add the contacts to one of the existing lists."
msgstr "Upload either a manually completed template or a formatted contact list file. Once you import contacts, they will automatically appear in the Import. You can also add the contacts to one of the existing lists."

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:26
msgid "Upload File"
msgstr "Upload File"

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:20
msgid "Upload Image"
msgstr "Upload Image"

#: src/components/medialist/content/MedialistActionsBar/withModalUploadMedialist.tsx:8
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:240
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:241
msgid "Upload medialist"
msgstr "Upload medialist"

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:23
msgid "Upload Video"
msgstr "Upload Video"

#: src/components/settings/SettingsLogo/SettingsLogo.js:95
msgid "Upload your company logo, which will then be displayed in email reports, exports and in the application itself, instead of the {appName} logo."
msgstr "Upload your company logo, which will then be displayed in email reports, exports and in the application itself, instead of the {appName} logo."

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformTitle.tsx:8
msgid "Upload your contact list and we'll transform it to fit our medialist for you."
msgstr "Upload your contact list and we'll transform it to fit our medialist for you."

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:58
msgid "Upload your contact list, and we’ll format it to fit perfectly into our medialist for you."
msgstr "Upload your contact list, and we’ll format it to fit perfectly into our medialist for you."

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:58
msgid "Upload your file"
msgstr "Upload your file"

#: src/components/misc/UploadWatcher/UploadWatcher.js:18
msgid "Uploading has not finished. Please do not refresh or close this page."
msgstr "Uploading has not finished. Please do not refresh or close this page."

#: src/components/misc/UploadWatcher/UploadWatcher.js:46
msgid "Uploading: {lastProgress}%"
msgstr "Uploading: {lastProgress}%"

#: src/components/medialist/forms/modules/FormArray.js:131
msgid "Url"
msgstr "Url"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:190
#: src/components/ReusableFeed/FormAddArticle.tsx:31
msgid "URL"
msgstr "URL"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:38
msgid "Use another email address"
msgstr "Use another email address"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:76
msgid "Use Google account as sender"
msgstr "Use Google account as sender"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:82
msgid "Use Microsoft 365 account as sender"
msgstr "Use Microsoft 365 account as sender"

#: src/components/staff/admin/workspace/Workspace.js:369
#: src/components/staff/admin/workspace/Workspace.js:389
#: src/components/staff/admin/workspace/Workspace.js:410
#: src/components/staff/admin/workspace/Workspace.js:430
#: src/components/staff/admin/workspace/Workspace.js:449
#: src/components/staff/admin/workspace/Workspace.js:470
#: src/components/staff/admin/workspace/Workspace.js:491
#: src/components/staff/admin/workspace/Workspace.js:524
#: src/components/staff/admin/workspace/Workspace.js:550
#: src/components/staff/admin/workspace/Workspace.js:569
#: src/components/staff/admin/workspace/Workspace.js:588
#: src/components/staff/admin/workspace/Workspace.js:639
#: src/components/staff/admin/workspace/Workspace.js:660
#: src/components/staff/admin/workspace/Workspace.js:686
msgid "Used"
msgstr "Used"

#: src/pages/staff/admin/users/[userId]/index.js:12
#: src/components/staff/admin/DailyAccess/Table.js:24
#: src/components/staff/admin/DailyAccess/Content.js:32
msgid "User"
msgstr "User"

#: src/components/tariff/TariffLimits/TariffLimits.js:274
#: src/components/staff/admin/workspace/Workspace.js:677
msgid "User accounts limit"
msgstr "User accounts limit"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:53
#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:59
msgid "User emails"
msgstr "User emails"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:104
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:67
msgid "User management"
msgstr "User management"

#: src/components/staff/admin/user/User.js:224
msgid "User settings"
msgstr "User settings"

#: src/components/emailing/forms/FormSenderSettings.js:89
msgid "Username"
msgstr "Username"

#: src/pages/staff/admin/customers/[customerId]/users.js:12
#: src/components/staff/admin/workspace/Workspace.js:858
#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:69
#: src/components/staff/admin/customers/Customer.js:173
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:77
#: src/components/staff/admin/customer/users/Users.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:34
#: src/components/forms/dashboard/Search/SearchUsers.js:36
msgid "Users"
msgstr "Users"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:27
msgid "Utilizes company profiles for more tailored content."
msgstr "Utilizes company profiles for more tailored content."

#: src/components/emailing/forms/FormSenderSettings.js:168
msgid "Validate"
msgstr "Validate"

#: src/components/emailing/forms/FormSenderSettings.js:134
msgid "Value"
msgstr "Value"

#: src/components/staff/admin/customer/bio/CustomerBio.js:95
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:58
msgid "VAT"
msgstr "VAT"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:227
msgid "Verification"
msgstr "Verification"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:101
msgid "Verification email sent."
msgstr "Verification email sent."

#. placeholder {0}: values.email
#: src/components/emailing/content/EmailingSettingsContent.js:93
msgid "Verification email was sent to {0}. Please check your inbox."
msgstr "Verification email was sent to {0}. Please check your inbox."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:26
msgid "Verify your email"
msgstr "Verify your email"

#: src/components/emailing/forms/FormSenderSettings.js:208
msgid "Verifying your email address with SMTP or DNS can improve email deliverability, protect against spoofing, improve sender reputation, and provide better analytics. It demonstrates legitimacy and helps email providers ensure that emails are not spam."
msgstr "Verifying your email address with SMTP or DNS can improve email deliverability, protect against spoofing, improve sender reputation, and provide better analytics. It demonstrates legitimacy and helps email providers ensure that emails are not spam."

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:87
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:48
msgid "Video"
msgstr "Video"

#: src/components/newsroom/content/posts/NewsroomPosts.js:119
#: src/components/newsroom/content/posts/NewsroomPosts.js:123
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:23
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:32
#: src/components/misc/ActionsBar/View/ViewMenu.js:323
#: src/components/misc/ActionsBar/View/View.js:16
msgid "View"
msgstr "View"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:84
msgid "View changes"
msgstr "View changes"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:44
msgid "View full article"
msgstr "View full article"

#: src/components/OurChart/OurChartAdvanced.js:253
msgid "View in full screen"
msgstr "View in full screen"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:58
msgid "View Newsroom"
msgstr "View Newsroom"

#: src/components/feed/InspectorToolbar/InspectorToolbar.js:120
msgid "View preview"
msgstr "View preview"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:41
msgid "View Screenshot"
msgstr "View Screenshot"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:47
msgid "View Video"
msgstr "View Video"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:41
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:47
msgid "View Web"
msgstr "View Web"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:35
msgid "Viewing this press publication incurs an additional fee as per the Table of Fees approved by the Minister of Culture and National Heritage."
msgstr "Viewing this press publication incurs an additional fee as per the Table of Fees approved by the Minister of Culture and National Heritage."

#: src/components/monitoring/FeedList/FeedListItem/SocialInteractions/SocialInteractions.js:24
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:95
msgid "views"
msgstr "views"

#: src/components/newsroom/content/posts/NewsroomPosts.js:163
msgid "Views"
msgstr "Views"

#: src/components/misc/ActionsBar/Selector/Selector.js:34
msgid "Visible"
msgstr "Visible"

#: src/components/newsroom/content/posts/NewsroomPosts.js:261
#: src/components/newsroom/content/dashboard/ChartVisits.js:86
#: src/components/newsroom/components/PostsList/PostsList.js:209
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:74
msgid "Visits"
msgstr "Visits"

#: src/components/newsroom/content/dashboard/ChartVisits.js:49
msgid "Visits (last 30 days / total):"
msgstr "Visits (last 30 days / total):"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:107
msgid "Warning"
msgstr "Warning"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:46
msgid "Warning via <0>SMS</0>, <1>email</1> or <2>notification</2>"
msgstr "Warning via <0>SMS</0>, <1>email</1> or <2>notification</2>"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:57
msgid "We are the only ones in the Czech Republic to monitor <0>text mentions in the broadcast</0> for selected channels."
msgstr "We are the only ones in the Czech Republic to monitor <0>text mentions in the broadcast</0> for selected channels."

#: src/pages/user/reset-password/success.tsx:8
msgid "We have sent password reset link to your email."
msgstr "We have sent password reset link to your email."

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:29
msgid "We have sent you an activation link. To activate Emailing and to confirm your email address please open the link."
msgstr "We have sent you an activation link. To activate Emailing and to confirm your email address please open the link."

#: src/components/emailing/content/EmailingSettingsContent.js:28
msgid "We haven't been granted access to send emails on your behalf. Please try again and make sure to grant us access."
msgstr "We haven't been granted access to send emails on your behalf. Please try again and make sure to grant us access."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:75
msgid "We noticed that your post lacks an introduction or perex. This section is crucial as it provides a brief overview of your post and entices readers to continue. Consider adding a short paragraph that summarizes your main points or sets the context for your post."
msgstr "We noticed that your post lacks an introduction or perex. This section is crucial as it provides a brief overview of your post and entices readers to continue. Consider adding a short paragraph that summarizes your main points or sets the context for your post."

#: src/components/emailing/forms/FormSenderSettings.js:226
msgid "We recommend that you verify your email address"
msgstr "We recommend that you verify your email address"

#: src/components/page/auth/UserInactive/UserInactive.js:20
msgid "We will contact you shortly, once we setup your account."
msgstr "We will contact you shortly, once we setup your account."

#: src/pages/404.js:24
#: src/app/not-found-content.tsx:33
msgid "We're sorry, but the requested page was not found. It is possible that the page was either removed or moved somewhere else. Please make sure you entered the correct URL address."
msgstr "We're sorry, but the requested page was not found. It is possible that the page was either removed or moved somewhere else. Please make sure you entered the correct URL address."

#. placeholder {0}: topics.getTopicNameById(missingArticle.topicMonitorId)
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:170
msgid "We've added the article to the the topic \"{0}\" and adjusted its settings. The article will appear in your feed shortly."
msgstr "We've added the article to the the topic \"{0}\" and adjusted its settings. The article will appear in your feed shortly."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:110
msgid "We've discovered more content in the media landscape related to your topics and areas of interest."
msgstr "We've discovered more content in the media landscape related to your topics and areas of interest."

#: src/components/medialist/forms/FormEditAuthor.js:842
msgid "Website"
msgstr "Website"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:141
msgid "Website URL"
msgstr "Website URL"

#: src/helpers/charts/makeGranularityMenu.js:18
#: src/helpers/charts/getGranularityLabel.js:6
msgid "Weeks"
msgstr "Weeks"

#: src/components/emailing/content/sender/EmailingSenderContent.js:48
msgid "What is Emailing used for?"
msgstr "What is Emailing used for?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:250
msgid "What is Newsroom?"
msgstr "What is Newsroom?"

#: src/components/emailing/content/sender/EmailingSenderContent.js:49
msgid "While our Emailing tool is designed to send press and PR messages to journalists, its functionality goes beyond that. You can use it for various types of communication, opening up possibilities beyond traditional media outreach."
msgstr "While our Emailing tool is designed to send press and PR messages to journalists, its functionality goes beyond that. You can use it for various types of communication, opening up possibilities beyond traditional media outreach."

#: src/components/staff/admin/workspace/Workspace.js:799
msgid "Whitelisted domains"
msgstr "Whitelisted domains"

#. placeholder {0}: targetDashboard.name
#: src/store/models/dashboards/DashboardItem/DashboardItem.js:323
msgid "Widget copied to \"{0}\"."
msgstr "Widget copied to \"{0}\"."

#. placeholder {0}: targetDashboard.name
#: src/store/models/dashboards/DashboardItem/DashboardItem.js:314
msgid "Widget moved to \"{0}\"."
msgstr "Widget moved to \"{0}\"."

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:138
msgid "Widget will be removed"
msgstr "Widget will be removed"

#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:203
msgid "With contact"
msgstr "With contact"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:38
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:72
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:112
msgid "With inflection"
msgstr "With inflection"

#: src/components/layout/AuthWrapper/constants/features.slides.js:215
msgid "With Medialist, you don't send your media output to randomly selected journalists. You only send it to those who are most likely to publish it."
msgstr "With Medialist, you don't send your media output to randomly selected journalists. You only send it to those who are most likely to publish it."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:32
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:37
#: src/components/layout/MntrActiveFilters/modules/Note.js:11
msgid "With note"
msgstr "With note"

#. placeholder {0}: values.unverified_recipients_limit
#: src/components/emailing/forms/FormSenderSettings.js:227
msgid "Without a verified email address, your emails risk being marked as spam and rejected by providers, potentially damaging your reputation. You will also be limited to sending an email to only {0} recipients at a time."
msgstr "Without a verified email address, your emails risk being marked as spam and rejected by providers, potentially damaging your reputation. You will also be limited to sending an email to only {0} recipients at a time."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:29
msgid "Without limit"
msgstr "Without limit"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:49
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:54
#: src/components/layout/MntrActiveFilters/modules/Note.js:15
msgid "Without note"
msgstr "Without note"

#: src/components/staff/admin/customers/Customers.js:26
msgid "without sending registration email"
msgstr "without sending registration email"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:154
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:166
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:86
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:92
#: src/components/layout/MntrActiveFilters/modules/Tags.js:46
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterTags.js:19
msgid "Without tags"
msgstr "Without tags"

#: src/pages/404.js:14
#: src/app/not-found-content.tsx:23
msgid "Woop woop woop woop, page not found"
msgstr "Woop woop woop woop, page not found"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:24
#: src/components/help/search/Content/RulesSearch.tsx:16
msgid "Word Search"
msgstr "Word Search"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:98
#: src/components/help/search/Content/RulesDistance.tsx:16
msgid "Words to distance"
msgstr "Words to distance"

#: src/pages/staff/admin/workspaces/[workspaceId]/index.js:12
#: src/components/staff/admin/workspace/WorkspaceChangelog.js:21
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:77
#: src/components/staff/admin/DailyAccess/Table.js:27
#: src/components/staff/admin/DailyAccess/Content.js:32
#: src/components/page/auth/Expired/Expired.js:53
#: src/components/layout/Header/UserMenu/UserMenu.tsx:101
msgid "Workspace"
msgstr "Workspace"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:210
msgid "Workspace admin"
msgstr "Workspace admin"

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:55
msgid "Workspace created."
msgstr "Workspace created."

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:37
msgid "Workspace name"
msgstr "Workspace name"

#: src/components/staff/admin/workspace/Workspace.js:280
msgid "Workspace settings"
msgstr "Workspace settings"

#: src/pages/staff/admin/customers/[customerId]/workspaces.js:12
#: src/components/staff/admin/user/WorkspacesTable.js:61
#: src/components/staff/admin/user/User.js:307
#: src/components/staff/admin/customers/Customer.js:150
#: src/components/staff/admin/customer/workspaces/Workspaces.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:26
#: src/components/forms/dashboard/Search/SearchWorkspaces.js:43
msgid "Workspaces"
msgstr "Workspaces"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:65
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:204
msgid "worst"
msgstr "worst"

#: src/components/settings/SettingsLogo/SettingsLogo.js:76
msgid "Would you like to customize the appearance of the app, email reports and exports with your own logo? Contact us at <0>{salesEmail}</0>"
msgstr "Would you like to customize the appearance of the app, email reports and exports with your own logo? Contact us at <0>{salesEmail}</0>"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:61
msgid "Write the main content of your article that you want to create. The main content is considered as the primary theme or topic of your article."
msgstr "Write the main content of your article that you want to create. The main content is considered as the primary theme or topic of your article."

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:19
#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:20
msgid "Write with AI assistant"
msgstr "Write with AI assistant"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:39
msgid "Write without AI assistant"
msgstr "Write without AI assistant"

#: src/components/newsroom/components/AiTools/AiGenerateTitles.tsx:79
msgid "Write your own"
msgstr "Write your own"

#: src/components/tariff/TariffLimits/TariffLimits.js:132
#: src/components/staff/admin/workspace/Workspace.js:420
msgid "Yearly authors export limit"
msgstr "Yearly authors export limit"

#: src/helpers/charts/makeGranularityMenu.js:34
#: src/helpers/charts/getGranularityLabel.js:12
msgid "Years"
msgstr "Years"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/metaDataDate.js:30
msgid "yesterday"
msgstr "yesterday"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:114
#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/MediaDetail.js:72
#: src/components/tvr/Content/TvrStories/TvrStory/TvrStory.js:52
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:100
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:115
msgid "Yesterday"
msgstr "Yesterday"

#: src/components/emailing/modules/withModalRemoveRecipients.tsx:60
msgid "You are about to remove the selected recipients. However, you can keep some of them by clicking on the recipients."
msgstr "You are about to remove the selected recipients. However, you can keep some of them by clicking on the recipients."

#: src/components/emailing/content/mediaCoverage/EmptyFeedMessage.tsx:9
msgid "You can add articles to media coverage"
msgstr "You can add articles to media coverage"

#: src/components/exportList/Content/HeadingExport/HeadingExport.js:30
msgid "You can add items in Articles section."
msgstr "You can add items in Articles section."

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:88
msgid "You can create your first campaign by clicking the button below."
msgstr "You can create your first campaign by clicking the button below."

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:133
msgid "You can create your first email by clicking the button below."
msgstr "You can create your first email by clicking the button below."

#: src/components/monitoring/WorkspaceArticles/Intro.js:30
msgid "You can create your own articles here. They will be added to <0>your feed only</0>."
msgstr "You can create your own articles here. They will be added to <0>your feed only</0>."

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:51
msgid "You can edit recipients in email settings."
msgstr "You can edit recipients in email settings."

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:149
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:70
msgid "You can reset your filter by clicking the button below."
msgstr "You can reset your filter by clicking the button below."

#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:20
msgid "You can safely close this window as the process will continue in the background. Once the import is complete, we will notify you. If any issues occur, you will receive a notification and an email detailing the errors."
msgstr "You can safely close this window as the process will continue in the background. Once the import is complete, we will notify you. If any issues occur, you will receive a notification and an email detailing the errors."

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:91
msgid "You can use an external link to the article or {appName} link."
msgstr "You can use an external link to the article or {appName} link."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:244
msgid "You don't have a Newsroom yet, but you can create a new one right now."
msgstr "You don't have a Newsroom yet, but you can create a new one right now."

#: src/components/monitoring/WorkspaceArticles/Intro.js:26
msgid "You don't have any articles yet, but you can create one right now."
msgstr "You don't have any articles yet, but you can create one right now."

#: src/components/emailing/content/sender/EmailingSenderContent.js:20
msgid "You don't have Emailing set up yet. It only takes a few minutes to set it up."
msgstr "You don't have Emailing set up yet. It only takes a few minutes to set it up."

#: src/components/widgets/modules/stats/StatsBySource.js:35
#: src/components/widgets/components/PermissionErrorHint/PermissionErrorHint.js:13
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:36
msgid "You don't have permission to view"
msgstr "You don't have permission to view"

#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:55
msgid "You have no topics created"
msgstr "You have no topics created"

#: src/components/notifications/AppNotifications/AppNotifications.js:25
#: src/components/layout/Header/AppNotifications/AppNotifications.js:173
msgid "You have not received any notifications yet."
msgstr "You have not received any notifications yet."

#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:52
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:52
msgid "You have not saved any settings"
msgstr "You have not saved any settings"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:106
msgid "You have reached 30-day limit on the number of translated articles."
msgstr "You have reached 30-day limit on the number of translated articles."

#: src/components/tariff/TariffLimits/TariffLimits.js:59
#: src/components/exportList/ExportLimit/ExportLimit.js:19
msgid "You have reached 30-day limit. You cannot export any new articles."
msgstr "You have reached 30-day limit. You cannot export any new articles."

#: src/components/tariff/TariffLimits/TariffLimits.js:220
#: src/components/exportList/ExportLimit/ExportLimit.js:30
msgid "You have reached 30-day limit. You cannot export any new social media mentions."
msgstr "You have reached 30-day limit. You cannot export any new social media mentions."

#: src/store/models/ExportStore.js:238
msgid "You have reached the 30-day limit on the number of exported articles. Exported file doesn't contain all the selected articles."
msgstr "You have reached the 30-day limit on the number of exported articles. Exported file doesn't contain all the selected articles."

#: src/store/models/ExportStore.js:240
msgid "You have reached the 30-day limit on the number of exported social media mentions. Exported file doesn't contain all selected items."
msgstr "You have reached the 30-day limit on the number of exported social media mentions. Exported file doesn't contain all selected items."

#: src/components/monitoring/WorkspaceArticles/Limits.js:59
msgid "You have reached the 30-day limit on the number of OCR pages."
msgstr "You have reached the 30-day limit on the number of OCR pages."

#: src/components/monitoring/WorkspaceArticles/Limits.js:75
msgid "You have reached the 30-day limit on the number of transcribed seconds."
msgstr "You have reached the 30-day limit on the number of transcribed seconds."

#: src/store/models/ExportStore.js:246
msgid "You have reached the 30-day limit on the number of translated articles. Exported file doesn't contain all the selected articles."
msgstr "You have reached the 30-day limit on the number of translated articles. Exported file doesn't contain all the selected articles."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:92
msgid "You have reached the limit of recipients per email"
msgstr "You have reached the limit of recipients per email"

#: src/components/layout/Header/MessageLimit/MessageLimit.js:19
msgid "You have reached the limit on found articles"
msgstr "You have reached the limit on found articles"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:104
msgid "You have reached the limit on the number of dashboards."
msgstr "You have reached the limit on the number of dashboards."

#: src/store/models/ExportStore.js:229
msgid "You have reached the limit on the number of exported articles. Exported file doesn't contain all the selected articles."
msgstr "You have reached the limit on the number of exported articles. Exported file doesn't contain all the selected articles."

#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:79
msgid "You have reached the limit on the number of Newsrooms."
msgstr "You have reached the limit on the number of Newsrooms."

#: src/store/models/emailing/emailEdit/EmailEditStore/recipients/EmailRecipientsStore/EmailRecipientsStore.js:122
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:48
msgid "You have reached the limit on the number of recipients."
msgstr "You have reached the limit on the number of recipients."

#: src/components/emailing/content/EmailingSettingsContent.js:22
msgid "You have successfully authorized our application to use the external service."
msgstr "You have successfully authorized our application to use the external service."

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:123
msgid "You have unsaved changes."
msgstr "You have unsaved changes."

#: src/components/tariff/TariffLimits/TariffLimits.js:23
msgid "You may not see the latest articles."
msgstr "You may not see the latest articles."

#: src/components/layout/Header/MessageLimit/MessageLimit.js:13
msgid "You may not see the latest articles. We recommend that you change your keyword settings or limit your watched media in the Topics section."
msgstr "You may not see the latest articles. We recommend that you change your keyword settings or limit your watched media in the Topics section."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:470
msgid "You will be able to edit the link later to match your own domain."
msgstr "You will be able to edit the link later to match your own domain."

#: src/components/emailing/modules/PreviewEmail/RecipientsIsEmpty.tsx:27
#: src/components/emailing/components/EmailRecipientsList/RenderAllRecipients.tsx:34
msgid "You will see your recipients here"
msgstr "You will see your recipients here"

#: src/helpers/modal/withModalRequestFeature.tsx:40
#: src/components/misc/PromoBox/PromoBox.js:135
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:12
msgid "You'll be contacted by our Sales management."
msgstr "You'll be contacted by our Sales management."

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:17
msgid "Your account does not have access to any workspace."
msgstr "Your account does not have access to any workspace."

#: src/components/page/auth/Expired/Expired.js:49
msgid "Your account has expired"
msgstr "Your account has expired"

#: src/components/page/auth/UserInactive/UserInactive.js:14
msgid "Your account is being prepared"
msgstr "Your account is being prepared"

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:118
msgid "Your email <0>{0}</0> is already unsubscribed from our email list. There is nothing you need to do to stop receiving emails from {host}"
msgstr "Your email <0>{0}</0> is already unsubscribed from our email list. There is nothing you need to do to stop receiving emails from {host}"

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:85
msgid "Your email <0>{0}</0> successfully unsubscribed from our email list. You will no longer receive emails from us."
msgstr "Your email <0>{0}</0> successfully unsubscribed from our email list. You will no longer receive emails from us."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:65
msgid "Your email has been verified. Now you can fully enjoy our platform."
msgstr "Your email has been verified. Now you can fully enjoy our platform."

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:76
msgid "Your email successfully unsubscribed"
msgstr "Your email successfully unsubscribed"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:109
msgid "Your email was already unsubscribed"
msgstr "Your email was already unsubscribed"

#: src/components/emailing/content/EmailingSettingsContent.js:71
#: src/components/emailing/content/EmailingCampaignsContent.tsx:28
msgid "Your Emailing is not fully set up and verified"
msgstr "Your Emailing is not fully set up and verified"

#: src/components/emailing/content/EmailingSettingsContent.js:74
msgid "Your Emailing is not fully set up and verified. This can decrease the trust level and deliverability. You can fully set up and verify your Emailing in the settings. If you need help, please contact our support."
msgstr "Your Emailing is not fully set up and verified. This can decrease the trust level and deliverability. You can fully set up and verify your Emailing in the settings. If you need help, please contact our support."

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:96
msgid "Your HTML code"
msgstr "Your HTML code"

#: src/components/layout/Header/MessageDirty/MessageDirty.js:10
msgid "Your news feed is being updated"
msgstr "Your news feed is being updated"

#: src/store/models/account/user/UserStore.js:247
msgid "Your password has been changed successfully."
msgstr "Your password has been changed successfully."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:66
msgid "Your post is missing a title. A clear, concise title helps readers understand what your post is about at a glance. Please add a title that accurately represents your content."
msgstr "Your post is missing a title. A clear, concise title helps readers understand what your post is about at a glance. Please add a title that accurately represents your content."
