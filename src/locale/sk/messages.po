msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-07-31 13:12+0200\n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: sk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n>=2 && n<=4 ? 1 : 2);\n"
"Mime-Version: 1.0\n"
"X-Generator: Poedit 3.6\n"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:7
msgid "error"
msgstr "<i>Ospravedlňujeme sa, ale máme problémy s naším AI asistentom. Vieme, že je frustru<PERSON>, ke<PERSON> veci nefunguj<PERSON> podľa očakávania.<br> <PERSON><PERSON><PERSON><PERSON> to znova po nejakom čase.</i>"

#. placeholder {0}: data.word_count
#: src/components/monitoring/FeedList/FeedListItem/FeedListItem.js:305
msgid "(full text; {0} words)"
msgstr "(celý text; {0} slov)"

#: src/components/staff/admin/workspace/Workspace.js:781
msgid "(TVR) Allow automatic transcripts in monitoring"
msgstr "(TVR) Povoliť strojové prepisy v monitoringu"

#: src/components/staff/admin/workspace/Workspace.js:790
msgid "(TVR) Allow reruns in monitoring"
msgstr "(TVR) Poviliť reprízy v monitoringu"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+access%7D+other+%7B%23+accesses%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:85
msgid "# access"
msgid_plural "# accesses"
msgstr[0] "# prístup"
msgstr[1] "# prístupy"
msgstr[2] "# prístupov"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(item.article_count)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+article%7D+other+%7B%23+articles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:43
#: src/helpers/charts/formatters.js:55
#: src/components/reports/history/HistoryTable.js:199
#: src/components/analytics/AnalyticsContent.js:122
msgid "# article"
msgid_plural "# articles"
msgstr[0] "# článok"
msgstr[1] "# články"
msgstr[2] "# článkov"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+attached+article%7D+other+%7B%23+attached+articles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:50
msgid "# attached article"
msgid_plural "# attached articles"
msgstr[0] "# priložený článok"
msgstr[1] "# priložené články"
msgstr[2] "# priložených článkov"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+author%7D+other+%7B%23+authors%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:57
msgid "# author"
msgid_plural "# authors"
msgstr[0] "# autor"
msgstr[1] "# autori"
msgstr[2] "# autorov"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: inspector.data.versions_count
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+change%7D+other+%7B%23+changes%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:78
#: src/components/monitoring/Inspector/InspectorMonitora/StatusBar/ArticleHistoryAction.js:22
msgid "# change"
msgid_plural "# changes"
msgstr[0] "# zmena"
msgstr[1] "# zmeny"
msgstr[2] "# zmien"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+comment%7D+other+%7B%23+comments%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:89
msgid "# comment"
msgid_plural "# comments"
msgstr[0] "# komentár"
msgstr[1] "# komentáre"
msgstr[2] "# komentárov"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+dislike%7D+other+%7B%23+dislikes%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:109
msgid "# dislike"
msgid_plural "# dislikes"
msgstr[0] "# dislajk"
msgstr[1] "# dislajky"
msgstr[2] "# dislajkov"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+email%7D+other+%7B%23+emails%7D%7D&pluralize_on=0
#: src/components/emailing/helpers/emailing.plurals.js:3
msgid "# email"
msgid_plural "# emails"
msgstr[0] "# email"
msgstr[1] "# emaily"
msgstr[2] "# emailov"

#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(data.social_shares)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+interaction%7D+other+%7B%23+interactions%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:76
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:78
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:119
msgid "# interaction"
msgid_plural "# interactions"
msgstr[0] "# interakcia"
msgstr[1] "# interakcie"
msgstr[2] "# interakcií"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+like%7D+other+%7B%23+likes%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:69
msgid "# like"
msgid_plural "# likes"
msgstr[0] "# lajk"
msgstr[1] "# lajky"
msgstr[2] "# lajkov"

#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(inspector.data.article_mentions_count)
#. placeholder {0}: parseInt(data.article_mentions_count)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+mention%7D+other+%7B%23+mentions%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:90
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:103
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:81
#: src/components/monitoring/Inspector/InspectorMonitora/StatusBar/ArticleMentionsActions.js:25
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:342
msgid "# mention"
msgid_plural "# mentions"
msgstr[0] "# zmienka"
msgstr[1] "# zmienky"
msgstr[2] "# zmienok"

#. placeholder {0}: parseInt(jobs.length - 1)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+more%7D+other+%7B%23+more%7D%7D&pluralize_on=0
#: src/helpers/getAuthorJobs.js:16
msgid "# more"
msgid_plural "# more"
msgstr[0] "# ďalší"
msgstr[1] "# ďalšie"
msgstr[2] "# ďalších"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: parseInt(value)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+output%7D+other+%7B%23+outputs%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:71
#: src/helpers/charts/formatters.js:69
msgid "# output"
msgid_plural "# outputs"
msgstr[0] "# výstup"
msgstr[1] "# výstupy"
msgstr[2] "# výstupov"

#. placeholder {0}: parseInt(value, 10)
#. placeholder {0}: payload.page_count
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+page%7D+other+%7B%23+pages%7D%7D&pluralize_on=0
#: src/components/monitoring/WorkspaceArticles/Limits.js:51
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:145
msgid "# page"
msgid_plural "# pages"
msgstr[0] "# strana"
msgstr[1] "# strany"
msgstr[2] "# strán"

#. placeholder {0}: parseInt(value)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+person%7D+other+%7B%23+people%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:83
msgid "# person"
msgid_plural "# people"
msgstr[0] "# človek"
msgstr[1] "# ľudia"
msgstr[2] "# ľudí"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+retweet%7D+other+%7B%23+retweets%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:79
msgid "# retweet"
msgid_plural "# retweets"
msgstr[0] "# retweet"
msgstr[1] "# retweety"
msgstr[2] "# retweetov"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+share%7D+other+%7B%23+shares%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:119
msgid "# share"
msgid_plural "# shares"
msgstr[0] "# zdieľanie"
msgstr[1] "# zdieľania"
msgstr[2] "# zdieľaní"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+view%7D+other+%7B%23+views%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:99
msgid "# view"
msgid_plural "# views"
msgstr[0] "# zhliadnutie"
msgstr[1] "# zhliadnutia"
msgstr[2] "# zhliadnutí"

#. placeholder {0}: account.workspace.limits.media_archive_depth_limit
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+year%7D+other+%7B%23+years%7D%7D&pluralize_on=0
#: src/components/tariff/TariffLimits/TariffLimits.js:263
msgid "# year"
msgid_plural "# years"
msgstr[0] "# rok"
msgstr[1] "# roky"
msgstr[2] "# rokov"

#. placeholder {0}: item.recipients.length - shortEmailList.length
#. placeholder {0}: items.length - MAX_ITEMS
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%2B%23+more%7D+other+%7B%2B%23+more%7D%7D&pluralize_on=0
#: src/components/reports/history/HistoryTable.js:373
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:73
msgid "+# more"
msgid_plural "+# more"
msgstr[0] "+# ďalší"
msgstr[1] "+# ďalšie"
msgstr[2] "+# ďalších"

#. placeholder {0}: data.identical_articles.length
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%2B%23+other%7D+other+%7B%2B%23+other%7D%7D&pluralize_on=0
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:128
msgid "+# other"
msgid_plural "+# other"
msgstr[0] "+# ďalší"
msgstr[1] "+# ďalšie"
msgstr[2] "+# ďalších"

#. placeholder {0}: 1
#. placeholder {0}: self.selector.selected.size
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7BArticle+Removed%7D+other+%7BArticles+Removed%7D%7D&pluralize_on=0
#: src/store/models/monitoring/Inspector/Inspector.ts:497
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:659
#: src/store/models/emailing/campaignDetail/CampaignDetailStore/CampaignDetailStore.js:108
msgid "Article Removed"
msgid_plural "Articles Removed"
msgstr[0] "Článok odstránený"
msgstr[1] "Články odstránené"
msgstr[2] "Články odstránené"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7Barticle%7D+other+%7Barticles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:64
msgid "article"
msgid_plural "articles"
msgstr[0] "článok"
msgstr[1] "články"
msgstr[2] "článkov"

#. placeholder {0}: 1
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7BItem+Removed%7D+other+%7BItems+Removed%7D%7D&pluralize_on=0
#: src/store/models/tvr/tvr.js:274
msgid "Item Removed"
msgid_plural "Items Removed"
msgstr[0] "Položka odstránená"
msgstr[1] "Položky odstránené"
msgstr[2] "Položky odstránené"

#: src/components/forms/dashboard/Search/SearchDeclensions.js:51
msgid "{appName} will search"
msgstr "{appName} vyhľadá"

#: src/components/tariff/MonitoredMedia/MonitoredMedia.js:206
msgid "{countriesWithActiveMedia} countries with monitoring enabled"
msgstr "{countriesWithActiveMedia} krajiny so zapnutým monitoringom"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:107
msgid "{i} (current)"
msgstr "{i} (aktuálna)"

#. js-lingui:icu=%7BolderCount%2C+plural%2C+one+%7B%2B+%23+older%7D+other+%7B%2B+%23+older%7D%7D&pluralize_on=olderCount
#: src/components/feed/InspectorToolbar/ToolbarPagination.js:32
msgid "+ # older"
msgid_plural "+ # older"
msgstr[0] "+ # starší"
msgstr[1] "+ # staršie"
msgstr[2] "+ # starších"

#. js-lingui:icu=%7BprocessedCount%2C+plural%2C+one+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+article+has+been+updated.%7D+other+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+articles+have+been+updated.%7D%7D&pluralize_on=processedCount
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:517
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:754
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:800
msgid "You have reached the limit for this action. {processedCount} article has been updated."
msgid_plural "You have reached the limit for this action. {processedCount} articles have been updated."
msgstr[0] "Dosiahli ste limit pre túto akciu. {processedCount} článok bol aktualizovaný."
msgstr[1] "Dosiahli ste limit pre túto akciu. {processedCount} články boli aktualizované."
msgstr[2] "Dosiahli ste limit pre túto akciu. {processedCount} článkov bolo aktualizovaných."

#. js-lingui:icu=%7BprocessedCount%2C+plural%2C+one+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+author+has+been+updated.%7D+other+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+authors+have+been+updated.%7D%7D&pluralize_on=processedCount
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:553
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:590
#: src/store/models/authors/AuthorsStore.js:528
#: src/store/models/authors/AuthorsStore.js:581
#: src/store/models/authors/AuthorsStore.js:657
#: src/store/models/authors/AuthorsStore.js:709
msgid "You have reached the limit for this action. {processedCount} author has been updated."
msgid_plural "You have reached the limit for this action. {processedCount} authors have been updated."
msgstr[0] "Dosiahli ste limit pre túto akciu. {processedCount} autor bol aktualizovaný."
msgstr[1] "Dosiahli ste limit pre túto akciu. {processedCount} autori boli aktualizovaní."
msgstr[2] "Dosiahli ste limit pre túto akciu. {processedCount} autorov bolo aktualizovaných."

#. js-lingui:icu=%7Bvalue%2C+plural%2C+one+%7B%23+post%7D+other+%7B%23+posts%7D%7D&pluralize_on=value
#: src/helpers/charts/formatters.js:97
msgid "# post"
msgid_plural "# posts"
msgstr[0] "# príspevok"
msgstr[1] "# príspevky"
msgstr[2] "# príspevkov"

#. js-lingui:icu=%7Bvalue%2C+plural%2C+one+%7B%23+visit%7D+other+%7B%23+visits%7D%7D&pluralize_on=value
#: src/helpers/charts/formatters.js:62
msgid "# visit"
msgid_plural "# visits"
msgstr[0] "# návšteva"
msgstr[1] "# návštevy"
msgstr[2] "# návštev"

#: src/pages/newsroom/index.js:62
msgid "<0>Accurate</0> data is part of analytics."
msgstr "<0>Presné</0> dáta sú súčasťou analytiky."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:88
msgid "help.engagementRate"
msgstr "<0>Engagement rate je metrika používaná na hodnotenie priemerného počtu interakcií, ktoré príspevok získava na jednoho sledujúceho. Pomáha pri relatívnom porovnaní zmienok z rôznych kanálov a zdrojov. Pomocou engagement rate môžete: </0><1><2>zistiť, aké príspevky majú najlepšie a najhoršie výsledky, </2><3>porovnať mieru zapojenia, ktorú generujete na rôznych sociálnych sieťach, </3><4>porovnať svoje výsledky s konkurenciou, </4><5>vyhodnotiť influencerov. </5></1>"

#: src/pages/authors/index.js:69
msgid "<0>Export</0> detailed lists of authors"
msgstr "<0>Exportujte</0> detailné zoznamy autorov"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:21
msgid "<0>Immediate notifications</0> about mentions on <1>TV and radio</1>. Be in the swim of things. Non-stop."
msgstr "<0>Okamžité upozornenia</0> na zmienky v <1>TV a rozhlase</1>. Buďte nonstop v obraze."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:72
msgid "help.influenceScore"
msgstr "<0>Skóre vplyvu je číslo (od 1 do 10) vypočítané pre každú zmienku zo sociálnych médií. Jeho hodnota je primárne založená na dvoch parametroch:</0><1><2>ako pravdepodobné je, že daná zmienka bude vidieť,</2><3>koľkokrát bola zmienka zobrazená, zdieľaná alebo retweetována.</3></1><4>Veríme, že vám táto hodnota pomôže objaviť zmienky, autorov a weby, ktoré sú najobľúbenejšie a najvplyvnejšie. Tímto spôsobom môžete získať ďalšiu metriku pre analýzy Vašich marketingových kampaní.</4>"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:38
msgid "<0>Non-stop</0> monitoring of selected TVs and radios"
msgstr "<0>Nonstop</0> monitoring vybraných TV a rozhlasových staníc"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesSearch.tsx:18
msgid "help.search.wordSearch.description"
msgstr "<0>Rýchly prehľad</0><1><2><3><4><5>Zadaný výraz</5><6>Čo {appName} urobí</6><7>Vyhľadá články obsahujúce</7></4></3><8><9><10>Tučniak</10><11>slovo skloňuje, diakritika ani veľkosť písmen nehrajú rolu</11><12>Tučniak, TUČNIAK, tučniak, Tučniakov, tučniaci, tučniak</12></9><13><14>\"Tučniak\"</14><15>slovo neskloňuje, diakritika hrá rolu, veľkosť písmen nehrajú rolu</15><16>Tučniak, TUČNIAK, tučniak</16></13><17><18>\"!Tučniak\"</18><19>slovo neskloňuje, diakritika i veľkosť písmen hrá rolu</19><20>Tučniak</20></17></8></2></1><21>So skloňovaním</21><22>Ak napíšeme do vyhľadávacieho poľa: <23>Tučniak</23></22><24>{appName} vyhledá všechny články, které obsahují slovo <25>Tučniak</25> v ľubovoľnom tvare. Vyhľadané budú teda články, ktoré obsahujú slovo <26>Tučniakov</26> ({appName} slovo skloňuje), <27>TUČNIAK</27> (veľkosť písmen nehrá rolu), alebo <28>tucniak</28> (diakritika nehrá rolu).</24><29>Odporúčame týmto spôsobom vyhľadávať všetky slová, ktoré sa v bežnom texte skloňujú. Sú to typicky všeobecné slová (tučniak), vlastné mená (Michal) alebo cudzie slova (facebook).</29><30>Presná zhoda</30><31>Ak napíšeme do vyhľadávacieho pola: <32>\"Tučniak\"</32> (dáme slovo do úvodzoviek)</31><33>{appName} vyhľadá všetky články, ktoré obsahujú slovo <34>Tučniak</34>, ale len v zadanom tvare (tzn. slovo neskloňuje). Vyhľadané budú teda články, ktoré obsahujú slovo <35>Tučniak</35> alebo<36>TUČNIAK</36> (veľkosť písmen nehrá rolu).</33><37>{appName} nevyhľadá tie články, ktoré obsahujú vyskloňované slovo <38>Tučniakov</38> alebo slovo <39>tucniak</39> bez diakritiky.</37><40>Odporúčame týmto spôsobom vyhľadávať názvy firiem a produktov (\"McDonald's\"), webové domény (\"{appName}.sk\"), presnú zhodu slova (\"najlepší\") alebo skratky (\"USA\").</40><41>Presná zhoda vrátane veľkosti písmen</41><42>Ak napíšeme do vyhľadávacieho pola: <43>\"!Tučniak\"</43> (dáme slovo do úvodzoviek a za prvú úvodzovku výkričník)</42><44>{appName} vyhľadá všetky články, které obsahujú slovo <45>Tučniak</45>, ale len v zadanom tvare a to i vrátane veľkosti písmen. Jedná sa o najstriktnejšiu variantu.</44><46>{appName} nevyhledá ty články, které obsahujú např. iba slovo <47>tučniak</47> napísané len malými písmenami.</46><48><49>Odporúčame týmto spôsobom vyhľadávať názvy firiem a produktov (\"!Zoznam\") alebo skratky (\"!WHO\").</49></48>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesOperators.tsx:18
msgid "help.search.operators.description"
msgstr "<0>Rýchly prehľad</0><1><2><3><4>Zadaný výraz</4><5>{appName} vyhľadá</5></3></2><6><7><8>tučniak AND tuleň</8><9>Články obsahujúce obe slová <10>tučniak</10> a <11>tuleň</11>.</9></7><12><13>tučniak tuleň</13><14>Články obsahujúce obe slová <15>tučniak</15> a <16>tuleň</16>. Medzera medzi slovami sa chová rovnako ako keby tam bol operátor AND.</14></12><17><18>tučniak OR tuleň</18><19>Články obsahujúce aspoň jedno zo slov <20>tučniak</20>, alebo <21>tuleň</21>.</19></17><22><23>tučniak -tuleň</23><24>Články obsahujúce slovo <25>tučniak</25>, ale neobsahujúce slovo <26>tuleň</26>.</24></22></6></1><27>AND</27><28>Ak chceme vyhľadať články, ktoré obsahujú niekoľko slov, alebo slovných spojení súčasne, zadáme všetky požadované slová a oddelíme ich buď medzerou, alebo slovom <29>AND</29> (písané veľkými písmenami).</28><30>Ak napíšeme do vyhľadávacieho pola: <31>tučniak tuleň Pražská+zoo \"!SAV\"</31></30><32>Je to rovnaké ako keby sme napísali: <33>tučniak AND tuleň AND Pražská+zoo AND \"!SAV\"</33></32><34>OR</34><35>Ak chceme vyhľadať články, ktoré obsahujú aspoň jedno zo zadaných slov, alebo slovných spojení, zadáme všetky požadované slová a oddelíme ich slovom <36>OR</36> (písané veľkými písmenami).</35><37>Príklad: <38>tučniak OR tuleň OR Pražská+zoo OR \"!ČEZ\"</38></37><39>NOT</39><40>Ak chceme z vyhľadávaných výsledkov odstrániť články, ktoré obsahujú niektoré slová, alebo slovné spojenia, napíšeme za vyhľadávaný výraz zoznam zakázaných slov a slovných spojení a pred každé z nich dáme znamienko mínus.</40><41>Príklad: <42>tučniak -tuleň -Pražská+zoo -\"!SAV\"</42></41><43>Zátvorky</43><44>Vyhľadávacie operátory je možné kombinovať podľa potreby. Pri zložitejších výrazoch však mnohokrát potrebujeme určiť i poradie, v ktorom chceme aby sa vyhľadávacie operátory vyhodnotili. Pre tento účel použijeme zátvorky, ktoré fungujú podobne ako v matematike.</44><45>Ak napíšeme do vyhľadávacieho pola: <46>\"!Billa\" AND (obchod OR reťazec OR predajňa OR supermarket OR hypermarket)</46></45><47>{appName} vyhľadá všetky články obsahujúce slovo <48>Billa</48> (len v zadanom tvare, vrátane veľkosti písmen) v spojení s aspoň jedným zo slov <49>obchod</49>, <50>reťazec</50>, …</47><51>Na záver ukážka toho, ako komplexné výrazy môžete v aplikácii z vyhľadávacích operátorov poskladať: <52>sídlisko (dom OR budova OR stavba) AND (balkón OR (plastové+okná -\"!Velux\")) AND Bratislava+Petržalka~5 -(Rača OR Lamač)</52></51>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesPhrase.tsx:18
msgid "help.search.phrase.description"
msgstr "<0>Rýchly prehľad</0><1><2><3><4>Zadaný výraz</4><5>Vyhľadá články obsahujúce</5></3></2><6><7><8>Múdry+Michal</8><9>Múdry Michal, MÚDRY MICHAL, múdry michal, Múdreho Michala, mudry michal, múdry-michal</9></7><10><11>\"Múdry Michal\"</11><12>Múdry Michal, MÚDRY MICHAL, múdry michal</12></10><13><14>\"Múdry-Michal\"</14><15>Múdry-Michal, MÚDRY-MICHAL, múdry-michal</15></13><16><17>\"!Múdry Michal\"</17><18>Múdry Michal</18></16></6></1><19>So skloňovaním</19><20>Ak napíšeme do vyhľadávacieho pola: <21>Múdry+Michal</21> (slová oddelíme znamienkom plus, tak aby medzi nimi nebola medzera)</20><22>{appName} vyhľadá všetky články, ktoré obsahujú slovné spojenie <23>Múdry Michal</23> (tzn. tieto slová za sebou v tomto poradí) v ľubovoľnom tvare. Vyhľadané budú teda články, ktoré obsahujú slovné spojenie <24>Múdreho Michala</24> ({appName} slová skloňuje), <25>MÚDRY MICHAL</25> (veľkosť písmen nehrá rolu), <26>mudry michal</26> (diakritika nehrá rolu), alebo <27>múdry-michal</27> (medzi slovami môže byť aj oddeľovač, napr. čiarka, alebo pomlčka).</22><28>Odporúčame týmto spôsobom vyhľadávať mená osôb (Michal+Nový), názvy firiem a organizácií (Ministerstvo+životného+prostredia), alebo slovné spojenia (monitoring+médií).</28><29>Presná zhoda</29><30>Ak napíšeme do vyhľadávacieho pola: <31>\"Múdry Michal\"</31> (dáme celý výraz do úvodzoviek)</30><32>{appName} vyhľadá všetky články, ktoré obsahujú slovné spojenie <33>Múdry Michal</33>, ale len v zadanom tvaru. Vyhľadané budú teda články, ktoré obsahujú <34>Múdry Michal</34>, alebo <35>MÚDRY MICHAL</35> (veľkosť písmen nehrá rolu).</32><36>{appName} nevyhľadá tie články, ktoré obsahujú len vyskloňované slovné spojenie <37>Múdreho Michala</37>, slovné spojenie <38>mudry michal</38> bez diakritiky, alebo slovné spojenie <39>múdry-michal</39> s oddeľovačom.</36><40>Odporúčame týmto spôsobom vyhľadávať názvy firiem a produktov  (\"{appName} Media\"), skratky (\"MFF UK\"), alebo presnú zhodu spojenia (\"byť či nebyť\").</40><41>Presná zhoda s oddeľovačom</41><42>Pri hľadaní presnej zhody je potrebné zadať do úvodzoviek i oddeľovače, ktoré sa medzi slovami môžu vyskytovať - pomlčky, podtržníky, zavináče, atď. Týka sa to nasledujúcich znakov: & @ _ + - ' #</42><43>Ak napíšeme do vyhľadávacieho pola: <44>\"Múdry-Michal\"</44></43><45>{appName} vyhľadá všetky články, ktoré obsahujú slovné spojenie <46>Múdry-Michal</46> s oddeľovačom.</45><47>{appName} nevyhľadá tie články, ktoré obsahujú len slovné spojenie <48>Múdry Michal</48> bez oddeľovača, alebo <49>Múdry&Michal</49> s iným oddeľovačom než sme zadali.</47><50>Typické slovné spojenia, pri ktorých nezabudnite na oddeľovač sú \"Ernst & Young\", \"info@{appName}.cz\", \"Mi+Te\" nebo \"X-Men\".</50><51>Presná zhoda vrátane veľkosti písmen</51><52>Ak napíšeme do vyhľadávacieho pola: <53>\"!Múdry Michal\"</53> (dáme celý výraz do úvodzoviek a za prvú úvodzovku výkričník)</52><54>{appName} vyhľadá všetky články, ktoré obsahujú slovné spojenie <55>Múdry Michal</55>, ale len v zadanom tvare a to i vrátane veľkosti písmen. Jedná sa o najstriktnejšiu variantu.</54><56>{appName} nevyhľadá tie články, ktoré obsahujú napr. iba slovné spojenie <57>múdry michal</57> písané len malými písmenami.</56><58>Odporúčame týmto spôsobom vyhľadávať názvy firiem a produktov (\"!Zlatá vareška\"), alebo skratky (\"!OR SR\").</58>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesDistance.tsx:18
msgid "help.search.distance.description"
msgstr "<0>Rýchly prehľad</0><1><2><3><4>Zadaný výraz</4><5>Vyhľadá články obsahujúce</5></3></2><6><7><8>tuleň+tučniak~5</8><9>tučniak si pohladil tuleňa</9></7><10><11>tukan+tuleň+tučniak~10</11><12>tukani chceli zožrať tučniaka, ale tuleň zakročil</12></10><13><14>\"tuleň tukan\"~5</14><15>\"Tuleň!\" povedal tukan a odletel.</15></13><16><17>\"tukan tuleň tučniak\"~5</17><18>Na fotke zľava: tučniak, tuleň, tukan.</18></16></6></1><19>So skloňovaním</19><20>Ak napíšeme do vyhľadávacieho pola: <21>tuleň+tučniak~5</21> (slová oddelíme znamienkom plus a za nimi napíšeme vlnovku a číslo)</20><22>{appName} vyhľadá všetky články, ktoré obsahujú slová <23>tuleň</23> a <24>tučniak</24> v ľubovoľnom poradí a vo vzdialenosti najviac 5 slov od seba. Zadané slová sa automaticky skloňujú a veľkosť písmen, alebo diakritika nehrá rolu.</22><25>Odporúčame týmto spôsobom vyhľadávať slová, ktoré sa k sebe viažu a v texte článku budú blízko seba (firma+Facebook~7).</25><26>Presná zhoda</26><27>Ak napíšeme do vyhľadávacieho pola: <28>\"tuleň tučniak\"~5</28> (slová dáme do úvodzoviek a za druhou úvodzovkou napíšeme vlnovku a číslo)</27><29>{appName} vyhľadá všetky články, ktoré obsahujú slová <30>tuleň</30> a <31>tučniak</31> v ľubovoľnom poradí a vo vzdialenosti najviac 5 slov od seba. Obe zadané slová sa vyhľadávajú len v zadanom tvare, tzn. neskloňujú sa a diakritika hrá rolu.</29>"

#: src/pages/newsroom/index.js:35
msgid "<0>Share press releases</0> and other external and internal communication with <1>Newsroom</1> and have an accurate overview of traffic directly in the application."
msgstr "<0>Zdieľajte tlačové správy</0> a ďalšiu externú a internú komunikáciu pomocou <1>Newsroomu</1> a získajte presný prehľad a štatistiku o návštevnosti priamo v aplikácii."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:36
msgid "help.ave"
msgstr "<0>Koeficient AVE (Advertising Value Equivalent) predstavuje finančné zhodnotenie mediálnych aktivít. Je to ekvivalent toho, koľko by stál priestor získaný obsahom v prepočte na hodnotu reklamnej plochy podľa cenníku daného média.</0><1>Pre strojový výpočet AVE využíváme tieto premenné:</1><2><3>jednotková cena inzercie v danom médiu (napr: cena za normostranu v tlači / 1s odvysielanej správy v TV, alebo rozhlase)</3><4>veľkosť článku v tlači / dĺžka reportáže v TV, alebo rozhlase</4><5>rozsah informácie venovanej danej téme v rámci príspevku</5></2>"

#. js-lingui-explicit-id
#: src/components/layout/Header/MessageDirty/MessageDirty.js:12
msgid "message.dirty.description"
msgstr "<0>Zobrazené údaje nemusia zodpovedať Vašemu súčasnému nastaveniu, pretože jedna alebo viacero tém bolo zmenených.</0><1>Načítajte stránku znovu o niekoľko minút.</1>"

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:105
msgid "help.socialInteractions"
msgstr "<0>Počet sociálnych interakcií (like, share, komentár, vzhliadnutie, retweet) u zmienok.</0>"

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:59
msgid "help.socialInteractionsOnline"
msgstr "<0>Počet sociálnych interakcií (like, share, komentár) u online článkov na Facebooku.</0><1>Štatistika sa aktualizuje raz za 24 hodín.</1>"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:11
msgid "missingBasics"
msgstr "<div>Ďakujeme za odoslanie príspevku. Vykonali sme úvodnú kontrolu, aby sme sa uistili, že spĺňa naše základné požiadavky.</div><br> <strong>Zistili sme toto:</strong>"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:15
msgid "responseInfo"
msgstr ""
"<div>Tento proces môže chvíľu trvať, pretože vykonávame nasledujúce kroky:</div>\n"
" <ol>\n"
" <li><strong>Úvodná kontrola:</strong> Rýchla kontrola celkovej štruktúry a formátu vášho obsahu.</li>\n"
" <li><strong>Hĺbková analýza:</strong> Starostlivo skúmajte podrobnosti, jazyk a kontext vášho príspevku.</li>\n"
" <li><strong>Hodnotenie kvality:</strong> Hodnotenie rôznych aspektov, ako je jasnosť, súdržnosť a relevantnosť.</li>\n"
" <li><strong>Detekcia chýb:</strong> Identifikácia akýchkoľvek potenciálnych problémov, nezrovnalostí alebo oblastí na zlepšenie.</li>\n"
" <li><strong>Návrhy na optimalizáciu:</strong> príprava odporúčaní na vylepšenie obsahu v prípade potreby.</li>\n"
" </ol>\n"
" <div>Naša AI usilovne pracuje na tom, aby vám poskytla presnú a užitočnú spätnú väzbu. Ceníme si vašu trpezlivosť počas tejto komplexnej analýzy. Výsledky budú čoskoro k dispozícii.</div>"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:124
#: src/components/newsroom/components/PostsList/PostsList.js:98
msgid "<No title yet>"
msgstr "<Bez názvu>"

#: src/store/models/admin/customer/CustomerStore.js:220
msgid "<user already exists>"
msgstr "<užívateľ už existuje>"

#: src/components/tariff/TariffLimits/TariffLimits.js:26
#: src/components/staff/admin/workspace/Workspace.js:359
msgid "30-day article limit"
msgstr "30-denný limit na počet článkov"

#: src/components/tariff/UsageTracker/UsageTracker.js:13
msgid "30-day limit"
msgstr "30-denný limit"

#: src/components/tariff/TariffLimits/TariffLimits.js:63
#: src/components/staff/admin/workspace/Workspace.js:378
msgid "30-day limit on exported articles"
msgstr "30-denný limit na počet vyexportovaných článkov"

#: src/components/tariff/TariffLimits/TariffLimits.js:224
#: src/components/staff/admin/workspace/Workspace.js:559
msgid "30-day limit on exported social media mentions"
msgstr "30-denný limit na počet vyexportovaných zmienok zo soc. sietí"

#: src/components/tariff/TariffLimits/TariffLimits.js:241
#: src/components/staff/admin/workspace/Workspace.js:514
msgid "30-day limit on licensed article downloads"
msgstr "30-dňový limit na sťahovanie licencovaných článkov"

#: src/components/staff/admin/workspace/Workspace.js:629
msgid "30-day limit on OCR pages"
msgstr "30-denný limit na počet strán pre OCR"

#: src/components/tariff/TariffLimits/TariffLimits.js:203
#: src/components/staff/admin/workspace/Workspace.js:540
msgid "30-day limit on social media mentions"
msgstr "30-denný limit na počet zmienok zo soc. sietí"

#: src/components/staff/admin/workspace/Workspace.js:650
msgid "30-day limit on transcribed seconds"
msgstr "30-denný limit na počet sekúnd prepisu"

#: src/components/staff/admin/workspace/Workspace.js:399
msgid "30-day limit on translated articles with Google Translate"
msgstr "30-denný limit na počet preložených článkov cez Google Translate"

#: src/components/medialist/forms/FormEditAuthor.js:755
msgid "About Author"
msgstr "O autorovi"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:132
msgid "Above avg."
msgstr "Nadpriemer"

#: src/components/tariff/Permissions/Permissions.js:45
msgid "Access"
msgstr "Prístup"

#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:52
msgid "Access comprehensive articles via {appName}’s media monitoring, covering online, traditional, and social media content."
msgstr "Získajte prístup k celým článkom prostredníctvom monitoringu médií aplikácie {appName}, ktorý pokrýva obsah online, tradičných a sociálnych médií."

#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:49
msgid "Access Full Articles via Media Monitoring"
msgstr "Kompletný obsah vďaka monitoringu médií"

#: src/components/dashboards/PageExpiredSharedDashboard/PageExpiredSharedDashboard.js:37
msgid "Access to this dashboard has expired."
msgstr "Prístup k tomuto dashboardu vypršal."

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:22
msgid "Account info"
msgstr "Kontaktné údaje"

#: src/components/misc/Changelog/ChangelogTableRow.js:114
msgid "Account manager"
msgstr "Account manager"

#: src/components/staff/admin/customer/bio/CustomerBio.js:112
msgid "Account managers"
msgstr "Správcovia účtu"

#: src/components/settings/SettingsHeader/SettingsHeader.js:8
msgid "Account settings"
msgstr "Nastavenie účtu"

#: src/components/settings/SettingsTheme/SettingsTheme.js:11
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:87
msgid "Account theme"
msgstr "Vzhľad účtu"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:57
msgid "Activate"
msgstr "Aktivovať"

#: src/components/staff/admin/user/User.js:235
msgid "Activated"
msgstr "Aktivovaný"

#: src/components/emailing/content/EmailingSettingsContent.js:76
#: src/components/emailing/content/EmailingCampaignsContent.tsx:32
msgid "Activated senders without verification:"
msgstr "Aktivovaní odosielatelia bez pokročilej verifikácie:"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:173
#: src/components/staff/admin/user/User.js:138
#: src/components/staff/admin/customer/users/UsersTable.js:116
#: src/components/forms/dashboard/Search/SearchUsers.js:99
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:151
msgid "Active"
msgstr "Aktívny"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:80
msgid "Active Article Language"
msgstr "Povolené jazyky článkov"

#: src/components/staff/admin/workspace/ToggleActiveMedia.js:29
msgid "Active only"
msgstr "Len aktívne"

#: src/components/reports/Content/ReportsList/FormToggleActive/FormToggleActive.js:34
msgid "Active report"
msgstr "Aktívny report"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:93
msgid "Active Source Country"
msgstr "Povolené krajiny u článkov"

#: src/components/medialist/content/MedialistAuthorCreate.js:16
msgid "Activity Overview"
msgstr "Prehľad udalostí"

#: src/components/topics/Content/TopicsList/FormAddKeyword/FormAddKeyword.tsx:54
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:591
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:61
#: src/components/medialist/forms/modules/FormArray.js:198
#: src/components/medialist/forms/modules/FormArray.js:220
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:38
#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:190
#: src/components/emailing/forms/FormEmailRecipients.js:120
#: src/components/ReusableFeed/FormAddArticle.tsx:42
msgid "Add"
msgstr "Pridať"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:178
msgid "Add a sender to activate Emailing."
msgstr "Vytvorte prvého odosielateľa pre aktivovanie Emailingu."

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:92
msgid "Add all to selection"
msgstr "Pridať všetko do výberu"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:96
msgid "Add annotation"
msgstr "Pridať anotáciu"

#: src/helpers/modal/withModalAddArticle/withModalAddArticle.tsx:17
#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:95
#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:156
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:90
#: src/components/emailing/content/mediaCoverage/AddArticleToMediaCoverage.tsx:10
msgid "Add article"
msgstr "Pridať článok"

#: src/components/emailing/content/mediaCoverage/AddArticleToMediaCoverage.tsx:13
msgid "Add article media coverage"
msgstr "Pridať článok do mediálneho pokrytia"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:237
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:219
msgid "Add article to topic"
msgstr "Priradiť článok pod tému"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:537
#: src/components/newsroom/content/post/AttachmentsList.js:89
msgid "Add Attachment"
msgstr "Pridať prílohu"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:129
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:100
msgid "Add authors to list"
msgstr "Pridať autorov do zoznamu"

#: src/components/newsroom/forms/FormNewsroomPost/CategoriesSelector.js:71
msgid "Add Category"
msgstr "Pridať kategóriu"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:118
msgid "Add content"
msgstr "Pridať obsah"

#: src/components/dashboards/DashboardSelector/CreateDashboard.js:20
msgid "Add Dashboard"
msgstr "Pridať dashboard"

#: src/components/reports/Content/ReportsList/AddDay.js:25
msgid "Add day"
msgstr "Pridať deň"

#: src/components/staff/admin/workspace/Workspace.js:827
msgid "Add domains separated by a comma (domain1.com, domain2.com)"
msgstr "Pridajte domény oddelené čiarkou (domain1.com, domain2.com)"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:79
msgid "Add Gallery"
msgstr "Pridať galériu"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:66
msgid "Add Image"
msgstr "Vložiť obrázok"

#: src/components/topics/Content/TopicsList/FormAddKeyword/FormAddKeyword.tsx:45
msgid "Add Keyword"
msgstr "Pridať kľúčové slovo"

#: src/components/newsroom/forms/FormNewsroomSettings/LanguageSection.tsx:91
msgid "Add language variant"
msgstr "Pridať jazykovú variantu"

#: src/components/newsroom/content/posts/ChooseTemplates.tsx:109
msgid "Add main message"
msgstr "Pridať hlavnú správu"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:57
msgid "Add manually"
msgstr "Pridať ručne"

#: src/components/newsroom/components/AiTools/AiGenerateContent.tsx:117
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:137
msgid "Add missing data"
msgstr "Pridať chýbajúce údaje"

#: src/components/emailing/components/EmailRecipientsList/RecipientsButton.tsx:37
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:164
msgid "Add Missing Info"
msgstr "Pridanť chýbajúce informácie"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:64
msgid "Add new keypoint"
msgstr "Pridať nový kľúčový bod"

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:111
msgid "Add new mediatypes"
msgstr "Pridať ďalšie mediatypy"

#: src/components/newsroom/content/modules/CustomQuotes.tsx:84
msgid "Add new quote"
msgstr "Pridať nový citát"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:52
msgid "Add new sender"
msgstr "Pridať nového odosielateľa"

#: src/components/topics/Content/TopicsList/TopicsList.js:63
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:44
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:70
msgid "Add New Topic"
msgstr "Pridať novú tému"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:28
#: src/components/medialist/forms/FormEditAuthor.js:710
msgid "Add note"
msgstr "Pridať poznámku"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:37
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:57
msgid "Add note to article"
msgstr "Pridať poznámku ku článku"

#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:55
msgid "Add recipient"
msgstr "Pridať príjemcu"

#: src/components/emailing/modules/PreviewEmail/RecipientsIsEmpty.tsx:32
#: src/components/emailing/content/tabs/AddRecipients.tsx:78
msgid "Add recipients"
msgstr "Pridať príjemcov"

#: src/components/emailing/content/Signature.tsx:113
#: src/components/emailing/content/Signature.tsx:116
msgid "Add signature"
msgstr "Pridať podpis"

#: src/components/emailing/forms/FormEmailRecipients.js:112
msgid "Add single authors, author’s lists or emails"
msgstr "Pridávanie jednotlivých autorov, zoznamov autorov alebo e-mailov"

#: src/components/newsroom/content/modules/CustomQuotes.tsx:35
msgid "Add specific quotes you want to include in your article, along with the name of the person being quoted. We will use these quotes exactly as provided.\""
msgstr "Pridajte konkrétne citácie, ktoré chcete zahrnúť do svojho článku, spolu s menom osoby, ktorú citujete. Tieto citácie použijeme presne tak, ako sú uvedené.“"

#: src/components/reports/Content/ReportsList/AddTime.js:27
msgid "Add time"
msgstr "Pridať čas"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/ModalAddDashboardFooter.tsx:35
msgid "Add to Dashboard"
msgstr "Pridať"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:179
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:210
msgid "Add to export"
msgstr "Pridať do exportu"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:38
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:94
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:150
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:207
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:130
msgid "Add to filters"
msgstr "Pridať k filtrácii"

#: src/components/medialist/forms/FormEditAuthor.js:670
#: src/components/medialist/content/withAddToBasketPopup.js:44
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorBaskets/AddAuthorToBasket.js:26
msgid "Add to list"
msgstr "Pridať do zoznamu"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:263
msgid "Add to next report"
msgstr "Poslať v ďalšom reporte"

#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:282
msgid "Add to report"
msgstr "Pridať do reportu"

#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:91
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Keywords.js:52
msgid "Add to search"
msgstr "Pridať do vyhľadávania"

#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:58
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:60
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:895
msgid "Add Topic"
msgstr "Pridať tému"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:41
#: src/components/settings/SettingsUserManagement/AddUsers.tsx:23
msgid "Add users"
msgstr "Pridať užívateľov"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:42
msgid "Add users to workspace"
msgstr "Pridať užívateľov k workspacu"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:101
msgid "Add Video"
msgstr "Vložiť video"

#: src/components/dashboards/Content.js:89
#: src/components/dashboards/Content.js:90
msgid "Add Widget"
msgstr "Pridať widget"

#: src/store/models/ExportStore.js:316
#: src/store/models/monitoring/Inspector/Inspector.ts:449
msgid "Added to export."
msgstr "Pridané do exportu."

#: src/store/models/monitoring/Inspector/Inspector.ts:422
msgid "Added to next report."
msgstr "Pridané do reportu."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:29
msgid "Additional settings"
msgstr "Ďalšie nastavenie"

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorAddress/AuthorAddress.js:8
msgid "Address"
msgstr "Adresa"

#: src/constants/analytics.js:143
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:42
#: src/components/misc/ActionsBar/View/ViewMenu.js:237
msgid "Adjusted Reach"
msgstr "Upravený Dosah"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:160
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:162
#: src/components/staff/admin/workspace/Workspace.js:162
#: src/components/staff/admin/user/getUserAttributes.js:9
#: src/components/staff/admin/user/User.js:88
#: src/components/reports/history/HistoryTable.js:452
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:60
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:62
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:406
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:413
#: src/components/medialist/forms/FormEditAuthor.js:396
#: src/components/medialist/forms/FormEditAuthor.js:542
#: src/components/layout/Header/UserMenu/UserMenu.tsx:200
#: src/app/components/monitoring-navigation.tsx:314
msgid "Admin"
msgstr "Admin"

#: src/components/reports/Content/ReportsList/ReportsForm.js:331
#: src/components/forms/dashboard/ExportResend/ExportResend.js:133
msgid "Advanced attachment settings"
msgstr "Pokročilé nastavenie prílohy"

#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:15
msgid "Advanced export settings"
msgstr "Pokročilé nastavenie exportu"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:79
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:174
msgid "Advanced settings"
msgstr "Pokročilé nastavenie"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:58
msgid "Advanced template settings"
msgstr "Pokročilé nastavenie šablóny"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:255
msgid "Advertising Value Equivalency"
msgstr "Advertising Value Equivalency"

#: src/constants/analytics.js:101
#: src/constants/analytics.js:621
#: src/constants/analytics.js:755
#: src/components/layout/AuthWrapper/constants/features.slides.js:191
msgid "Advertising Value Equivalent (AVE)"
msgstr "Advertising Value Equivalent (AVE)"

#: src/constants/analytics.js:99
msgid "Advertising Value Equivalent (AVE) by sentiment"
msgstr "Advertising Value Equivalent (AVE) podľa sentimentu"

#: src/components/staff/admin/workspace/Workspace.js:920
#: src/components/settings/SettingsTariff/SettingsTariff.js:37
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:48
msgid "Agency media"
msgstr "Agentúrne spravodajstvo"

#: src/components/newsroom/components/AiTools/AiCheckPostResult.tsx:88
msgid "AI check"
msgstr "AI kontrola"

#: src/components/newsroom/components/AiTools/AiCheckLoadingInfo.tsx:28
msgid "AI Checkup information"
msgstr "Informácie o AI kontrole článku"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:218
msgid "Align Center"
msgstr "Zarovnať na stred"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:230
msgid "Align Justify"
msgstr "Zarovnať do bloku"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:212
msgid "Align Left"
msgstr "Zarovnať doľava"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:224
msgid "Align Right"
msgstr "Zarovnať doprava"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:83
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:37
#: src/components/reports/history/HistoryTable.js:86
#: src/components/reports/history/HistoryTable.js:96
#: src/components/reports/history/HistoryTable.js:331
#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:123
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:144
#: src/components/misc/portable/PortableExport/CounterTitle.js:8
#: src/components/misc/ActionsBar/Selector/Selector.js:51
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:146
#: src/components/analytics/AnalyticsContent.js:156
#: src/components/analytics/AnalyticsContent.js:166
msgid "All"
msgstr "Všetky"

#: src/store/models/ExportStore.js:318
msgid "All articles are already in export."
msgstr "Všetky články už sú v exporte."

#: src/components/exportList/Content/Content.tsx:97
#: src/app/components/monitoring-navigation.tsx:130
msgid "All articles will be removed from export."
msgstr "Všetky články budú odstránené z exportu."

#: src/components/misc/portable/PortableExport/CounterTitle.js:10
msgid "All except"
msgstr "Všetky okrem"

#: src/components/layout/AuthWrapper/constants/features.slides.js:400
msgid "All features of the browser app are accessible on a mobile device. The app keeps you informed even when you are drinking a morning cup of coffee."
msgstr "Všetky funkcie webovej aplikácie môžete využívať i na mobile. Vďaka mobilnej aplikácii všetko zistíte, aj keď práve pijete rannú kávu."

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
msgid "all mediatypes for"
msgstr "všetky mediatypy pre"

#: src/store/models/Megalist/MegalistFilter.js:49
#: src/components/topics/Content/TopicsList/MegalistToolbar/MediatypeFilterPopup.js:11
msgid "All Sources"
msgstr "Všetky zdroje"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitorsReports.js:40
msgid "All topics"
msgstr "Všetky témy"

#: src/components/medialist/forms/FormEditAuthor.js:576
msgid "All unsaved changes will be lost. Do you really want to cancel the changes?"
msgstr "Všetky neuložené zmeny budú stratené. Naozaj chcete zrušiť zmeny?"

#: src/components/staff/admin/workspace/Workspace.js:743
msgid "Allow adjusted reach (PL)"
msgstr "Povoliť upravený dosah (PL)"

#: src/components/staff/admin/workspace/Workspace.js:716
msgid "Allow automatic sentiment"
msgstr "Povoliť automatickú detekciu sentimentu"

#: src/components/staff/admin/workspace/Workspace.js:725
msgid "Allow automatic summarization"
msgstr "Povoliť strojové zhrnutie"

#: src/components/staff/admin/workspace/Workspace.js:734
msgid "Allow AVE"
msgstr "Povoliť AVE"

#: src/components/staff/admin/workspace/Workspace.js:752
msgid "Allow AVE Coefficient (for media analysis)"
msgstr "Povoliť koeficient AVE (pre mediálne anlýzy)"

#: src/components/staff/admin/workspace/Workspace.js:707
msgid "Allow custom logo"
msgstr "Povoliť nahranie vlastného loga"

#: src/components/staff/admin/workspace/Workspace.js:598
msgid "Allow english social media"
msgstr "Povoliť príspevky zo soc. sietí v angličtine"

#: src/components/staff/admin/workspace/Workspace.js:772
msgid "Allow forcing articles to email reports"
msgstr "Povoliť funkciu \"Poslať článok v ďalšom reporte\""

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:93
msgid "Allow search engines to index this blog (including inclusion of articles in media monitoring and analysis of online mentions)"
msgstr "Povoliť vyhľadávačom indexovať tento blog (vrátane zaradenia článkov do mediálneho monitoringu a analýzy online zmienok)"

#: src/components/staff/admin/workspace/Workspace.js:609
msgid "Allow users to create own articles"
msgstr "Povoliť užívateľovi vytváranie vlastných článkov"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:31
msgid "Allowing detailed tracking of distribution campaigns."
msgstr "Umožňuje podrobné sledovanie distribučných kampaní."

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:63
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:86
#: src/components/staff/admin/customer/expenses/ExpenseTable.js:81
#: src/components/staff/admin/customer/expenses/DetailExpenseModal.js:38
msgid "Amount"
msgstr "Čiastka"

#: src/components/emailing/content/EmailingSettingsContent.js:32
msgid "An email sender record with this address already exists. Please check your existing records or try again."
msgstr "Záznam odosielateľa e-mailu s touto adresou už existuje. Skontrolujte svoje existujúce záznamy alebo to skúste znova."

#: src/pages/_error.js:36
msgid "An error {statusCode} occurred on server"
msgstr "Na serveri nastala chyba {statusCode}"

#: src/pages/_error.js:37
msgid "An error occurred on client"
msgstr "Nastala chyba na klientovi"

#: src/components/emailing/content/EmailingSettingsContent.js:17
msgid "An error occurred while authorizing our application to use the external service."
msgstr "Pri autorizácii našej aplikácie na používanie externej služby sa vyskytla chyba."

#: src/components/staff/admin/user/getUserAttributes.js:19
msgid "Analyst"
msgstr "Analytik"

#: src/store/models/dashboards/DashboardPreview.js:87
#: src/pages/analytcs.js:16
#: src/components/widgets/modules/stats/WidgetStats.js:67
#: src/components/widgets/modules/analytics/WidgetAnalytics.js:29
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:102
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:27
#: src/components/layout/AuthWrapper/constants/features.slides.js:165
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:16
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:16
#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:36
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:36
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormSubtype/FormSubtype.js:56
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:20
#: src/components/analytics/AnalyticsContent.js:105
#: src/app/components/monitoring-navigation.tsx:81
msgid "Analytics"
msgstr "Analytika"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:140
msgid "AND"
msgstr "AND"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderVideo/HeaderVideo.js:43
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderSocial/HeaderSocial.js:25
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:37
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:69
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationHeader.js:25
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationHeader.js:29
#: src/components/feed/InspectorToolbar/InspectorToolbar.js:134
msgid "Annotation"
msgstr "Anotácia"

#: src/pages/user/yoy-analysis.js:34
msgid "Annual Media Analysis"
msgstr "Výročná mediálna analýza"

#: src/components/notifications/Content.js:28
msgid "App"
msgstr "Aplikácia"

#: src/components/staff/admin/workspace/Workspace.js:932
#: src/components/settings/SettingsTariff/SettingsTariff.js:45
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:55
msgid "Application permissions"
msgstr "Aplikácia"

#: src/components/settings/SettingsApplication/SettingsApplication.js:19
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:98
msgid "Application settings"
msgstr "Nastavenie aplikácie"

#: src/components/newsroom/components/AiTools/AiGenerateContent.tsx:138
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:161
msgid "Apply"
msgstr "Aplikovať"

#: src/components/medialist/content/FeedMedialist/FeedMedialistItem.js:229
msgid "archive"
msgstr "archív"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:22
msgid "Archive"
msgstr "Archív"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:129
msgid "Are you ready to send the email?"
msgstr "Ste pripravení odoslať e-mail?"

#. placeholder {0}: item.filename
#. placeholder {0}: file.name
#: src/components/newsroom/content/post/AttachmentsList.js:66
#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:42
msgid "Are you sure you want to delete {0}?"
msgstr "Naozaj chcete odstrániť {0}?"

#: src/components/emailing/content/SignaturePopup.tsx:36
msgid "Are you sure you want to delete signature?"
msgstr "Určite chcete odstrániť podpis?"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:397
msgid "Are you sure you want to delete this blog post?"
msgstr "Naozaj chcete zmazať tento príspevok?"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:87
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:100
msgid "Are you sure you want to delete this email?"
msgstr "Naozaj chcete zmazať tento email?"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:122
msgid "Are you sure you want to delete this sender?"
msgstr "Naozaj chcete zmazať tohoto odosielateľa?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:662
msgid "Are you sure you want to delete your Newsroom? This action will delete all articles and settings."
msgstr "Naozaj chcete zmazať váš Newsroom? Táto akcia zmaže všetky články a nastavenia."

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:299
msgid "Are you sure you want to publish the changes?"
msgstr "Chcete zmeny zverejniť?"

#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:110
msgid "Are you sure you want to remove all recipients from this report?"
msgstr "Naozaj chcete odstrániť všetkých príjemcov z tohto reportu?"

#: src/components/staff/admin/workspace/UsersTable/RemoveUsers.tsx:27
msgid "Are you sure you want to remove these users from the workspace?"
msgstr "Naozaj chcete odobrať týchto užívateľov z workspacu?"

#: src/components/newsroom/content/posts/NewsroomPosts.js:206
msgid "Are you sure you want to remove this article?"
msgstr "Naozaj chcete zmazať tento článok?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:679
msgid "Are you sure you want to remove this Newsroom?"
msgstr "Naozaj chcete zmazať tento Newsroom?"

#: src/components/staff/admin/workspace/UsersTable/RemoveUser.js:30
#: src/components/staff/admin/user/WorkspacesTable.js:143
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:400
msgid "Are you sure you want to remove this user from the workspace?"
msgstr "Naozaj chcete odobrať tohto užívateľa z workspacu?"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:300
msgid "Are you sure you want to set this post to draft?"
msgstr "Naozaj chcete tento príspevok nastaviť ako koncept?"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:205
msgid "area"
msgstr "plocha"

#: src/components/OurChart/OurChartAdvanced.js:148
msgid "Area"
msgstr "Plošný"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:72
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:56
#: src/components/misc/MntrEditor/extensions/ExtensionArticle.tsx:33
msgid "Article"
msgstr "Článok"

#. placeholder {0}: item.title
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:364
msgid "Article '<0>{0}</0>' will be removed."
msgstr "Článok '<0>{0}</0>' bude odstránený."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:575
msgid "Article '<0>{title}</0>' will be removed."
msgstr "Článok '<0>{title}</0>' bude odstránený."

#: src/components/misc/ActionsBar/View/ViewMenu.js:140
msgid "Article Area"
msgstr "Plocha článku"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:53
msgid "Article can still be attached later"
msgstr "Článok je možné pripojiť aj neskôr"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:34
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:48
msgid "Article clipping"
msgstr "Výstrižok"

#: src/store/models/OurChart.js:531
#: src/store/models/OurChart.js:563
#: src/store/models/OurChart.js:788
#: src/constants/stats.ts:11
#: src/constants/analytics.js:26
#: src/constants/analytics.js:1097
#: src/components/widgets/modules/stats/WidgetStats.js:203
#: src/components/widgets/modules/stats/WidgetStats.js:216
#: src/components/widgets/modules/stats/WidgetStats.js:229
#: src/components/monitoring/FeedChart/FeedChart.js:28
#: src/components/medialist/content/MedialistInspector/AuthorChart/AuthorChart.js:65
msgid "Article count"
msgstr "Počet článkov"

#: src/components/topics/Content/TopicsList/KeywordStatsTable.js:23
msgid "Article count for the last 30 days"
msgstr "Počet článkov za posledných 30 dní"

#: src/constants/analytics.js:192
msgid "Article count vs. GRP vs. AVE"
msgstr "Počet článkov vs. GRP vs. AVE"

#: src/store/models/monitoring/Inspector/Inspector.ts:778
msgid "Article has been copied to the clipboard."
msgstr "Článok bol skopírovaný do schránky."

#: src/store/models/monitoring/WorkspaceArticles.js:219
msgid "Article has been removed."
msgstr "Článok bol odstránený."

#: src/store/models/monitoring/WorkspaceArticles.js:193
msgid "Article has been updated."
msgstr "Článok bol aktualizovaný."

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:215
msgid "Article has no annotations assigned. Select the text to add."
msgstr "K článku nie sú priradené žiadne anotácie. Vyberte text, ktorý chcete pridať."

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:63
msgid "Article history"
msgstr "História článku"

#: src/store/models/monitoring/Inspector/Inspector.ts:451
msgid "Article is already in export."
msgstr "Článok už je v exporte."

#: src/components/article/Content.js:17
msgid "Article link has expired"
msgstr "Odkaz na článok expiroval"

#: src/components/layout/MntrActiveFilters/modules/ArticleMentions.js:12
msgid "Article mentions"
msgstr "Zmienky o článku"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:146
msgid "Article numbering"
msgstr "Číslovanie článkov"

#: src/store/models/admin/customer/CustomerStore.js:303
msgid "Article recreation started successfully."
msgstr "Články sa pregenerovávajú."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:181
msgid "Article screenshot"
msgstr "Screenshot článku"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:37
#: src/components/layout/MntrActiveFilters/modules/EmptyTags.js:23
msgid "Article Tags"
msgstr "Štítky článkov"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:441
msgid "Article Text"
msgstr "Text článku"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:83
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:129
msgid "Article transcript"
msgstr "Textový prepis"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:101
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:381
msgid "Article Type"
msgstr "Typ článku"

#: src/components/ReusableFeed/FormAddArticle.tsx:30
msgid "Article URL"
msgstr "URL adresa článku"

#: src/store/models/monitoring/Inspector/Inspector.ts:751
#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:58
#: src/components/monitoring/FeedList/FeedListItem/FeedListItem.js:138
msgid "Article URL has been copied to the clipboard. Without a login, it will be accessible for 30 days."
msgstr "URL adresa článku bola skopírovaná do schránky. Bez prihlásenia bude prístupná po dobu 30 dní."

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:70
msgid "Article view"
msgstr "Zobrazenie článku"

#: src/store/models/monitoring/Inspector/Inspector.ts:551
msgid "Article was reported"
msgstr "Článok bol nahlásený"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:278
msgid "Article was successfully published on your Newsroom page"
msgstr "Článok bol úspešne uverejnený"

#: src/store/models/dashboards/DashboardPreview.js:75
#: src/components/trash/Content.js:55
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:95
#: src/components/newsroom/content/posts/NewsroomPosts.js:80
#: src/components/medialist/constants/medialist.tabNavigation.js:27
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:20
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:18
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:109
#: src/components/layout/MntrActiveFilters/modules/MedialistArticles.js:18
#: src/components/forms/dashboard/Search/SearchNewsroom.js:31
#: src/components/exportList/History/HistoryTable/HistoryTable.js:63
#: src/components/exportList/Content/HeadingExport/HeadingExport.js:36
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:30
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:30
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:58
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:17
#: src/app/components/monitoring-navigation.tsx:71
msgid "Articles"
msgstr "Príspevky"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:525
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:808
msgid "Articles updated successfully."
msgstr "Články boli úspešne aktualizované."

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:38
msgid "Assess competitors and trends to refine your strategy."
msgstr "Analyzujte silné a slabé stránky konkurencie, sledujte trendy a posúvajte svoju stratégiu."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:28
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:64
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:260
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:361
#: src/components/monitoring/FeedActionsBar/withAddTagPopup/AddTagPopupContent.js:16
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:50
msgid "Assign tag"
msgstr "Priradiť štítok"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:34
msgid "Assign tag to article"
msgstr "Priradiť štítok k článku"

#: src/components/medialist/forms/FormEditAuthor.js:626
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorTags/AddAuthorTag.js:19
msgid "Assign tag to author"
msgstr "Priradiť štítok autorovi"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:25
msgid "Assistant creates a draft of the email content based on your specific needs"
msgstr "Asistent vytvorí návrh obsahu e-mailu na základe vašich špecifických potrieb"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:192
msgid "Attached articles"
msgstr "Priložené články"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:238
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:52
msgid "Attachment"
msgstr "Príloha emailu"

#: src/components/reports/history/HistoryTable.js:173
#: src/components/newsroom/content/post/AttachmentsList.js:81
msgid "Attachments"
msgstr "Prílohy"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:68
msgid "Audio"
msgstr "Audio"

#: src/constants/analytics.js:1094
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:485
#: src/components/newsroom/content/modules/CustomQuotes.tsx:69
#: src/components/monitoring/Inspector/InspectorMonitora/AuthorsList/AuthorsList.js:21
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:258
msgid "Author"
msgstr "Autor"

#: src/components/medialist/constants/medialist.tabNavigation.js:12
msgid "Author Detail"
msgstr "Detail autora"

#: src/components/medialist/content/AuthorBasketsMenu.js:26
#: src/components/medialist/content/AuthorBasketSelectorButton.js:8
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:19
msgid "Author Lists"
msgstr "Zoznamy autorov"

#: src/pages/authors/index.js:89
msgid "Author tags"
msgstr "Štítky autorov"

#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:874
msgid "Author Tags"
msgstr "Štítky autorov"

#: src/components/medialist/forms/FormEditAuthor.js:233
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:122
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:67
msgid "Author type"
msgstr "Typ autora"

#: src/store/models/authors/AuthorsStore.js:1079
msgid "Author was deleted."
msgstr "Autor bol zmazaný."

#: src/store/models/authors/AuthorsStore.js:1168
msgid "Author was reported."
msgstr "Autor bol nahlásený."

#: src/components/medialist/forms/FormEditAuthor.js:316
#: src/components/medialist/forms/FormEditAuthor.js:481
msgid "Author will be deleted."
msgstr "Autor bude zmazaný."

#: src/components/layout/MntrFiltersBar/forms/FormAuthor/FormAuthor.tsx:18
msgid "Author's name"
msgstr "Meno autora"

#: src/components/medialist/forms/FormEditAuthor.js:997
msgid "Author's shortname"
msgstr "Skratka autora"

#: src/components/medialist/forms/FormEditAuthor.js:834
#: src/components/medialist/forms/FormEditAuthor.js:992
msgid "Author's shortnames"
msgstr "Autorské skratky"

#: src/components/monitoring/Inspector/InspectorMonitora/AuthorsList/AuthorsList.js:21
#: src/components/medialist/content/MedialistDashboard.js:82
#: src/components/medialist/content/MedialistDashboard.js:115
#: src/components/layout/Sidebar/modules/AuthorsNavigation/AuthorsNavigation.js:20
#: src/components/forms/dashboard/Search/SearchAuthors.js:39
#: src/components/emailing/forms/FormEmailRecipients.js:131
#: src/components/emailing/components/EmailRecipientsList/RenderAllRecipients.tsx:40
msgid "Authors"
msgstr "Autori"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:560
#: src/store/models/authors/AuthorsStore.js:535
msgid "Authors added."
msgstr "Autori boli pridaní."

#: src/components/emailing/content/tabs/AddRecipients.tsx:69
msgid "Authors lists"
msgstr "Zoznamy autorov"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:598
#: src/store/models/authors/AuthorsStore.js:603
msgid "Authors removed."
msgstr "Autori boli odstránení."

#: src/store/models/authors/AuthorsStore.js:664
msgid "Authors updated successfully."
msgstr "Autori boli úspešne aktualizovaní."

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:94
msgid "Authors with types “agency”, “publisher” or “editorial office” can’t use merge tags  *|LAST_NAME|*,  *|VOKATIV_L|*. If you want to apply these merge tags to the author, change their type to “author” or “blogger” and add the last name."
msgstr "Autori s typom „agentúra“, „vydavateľstvo“ alebo „redakcia“ nemôžu používať merge tagy *|LAST_NAME|*, *|VOKATIV_L|*. Ak chcete tieto merge tagy použiť na autora, zmeňte jeho typ na „autor“ alebo „bloger“ a pridajte priezvisko."

#: src/components/staff/admin/user/User.js:62
msgid "Autologin link"
msgstr "Autologin odkaz"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:178
msgid "Automatic summary"
msgstr "Automatické zhrnutie"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:270
#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:74
msgid "Automatic transcript"
msgstr "Strojový prepis"

#: src/components/tariff/TariffLimits/TariffLimits.js:99
msgid "Automatic translations 30-day limit"
msgstr "30-denný limit na počet preložených článkov"

#: src/constants/stats.ts:6
#: src/constants/analytics.js:994
#: src/components/widgets/modules/stats/WidgetStats.js:154
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:387
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:252
#: src/components/misc/ActionsBar/View/ViewMenu.js:203
msgid "AVE"
msgstr "AVE"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:237
msgid "AVE and sentiment"
msgstr "AVE a sentiment"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:39
msgid "AVE Coefficient"
msgstr "Koeficient AVE"

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:23
msgid "formatNumber.B"
msgstr "mld."

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:165
#: src/components/notifications/AppNotifications/AppNotifications.js:18
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:197
#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepTitleAndCommunicationPlan.tsx:106
#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:82
#: src/components/newsroom/content/posts/ChooseTemplates.tsx:98
#: src/components/newsroom/components/NewsroomHeading/NewsroomHeading.js:20
#: src/components/misc/ActionsBar/View/ViewMenu.js:40
#: src/components/misc/ActionsBar/View/ViewMenu.js:260
#: src/components/medialist/content/MedialistActionsBar/FormTransformContacts.tsx:30
#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:73
#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:97
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:49
#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:31
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:145
#: src/components/layout/Header/AppNotifications/AppNotifications.js:110
#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:163
#: src/components/emailing/content/CreateEmailContent.js:296
msgid "Back"
msgstr "Späť"

#: src/components/page/auth/ResetPassword/ResetPasswordFooter.js:29
msgid "Back to Log In"
msgstr "Späť na prihlásenie"

#: src/components/page/auth/ResetPassword/ResetPasswordFooter.js:19
msgid "Back to settings"
msgstr "Späť do nastavenia"

#: src/pages/_error.js:61
#: src/pages/404.js:29
#: src/pages/user/yoy-analysis.js:79
#: src/pages/user/reactivate-24.js:79
#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:28
#: src/components/page/auth/UserInactive/UserInactive.js:25
#: src/components/page/auth/Expired/Expired.js:119
#: src/components/layout/ErrorCustom/ErrorCustom.js:13
#: src/app/not-found-content.tsx:35
msgid "Back to the main page"
msgstr "Prejsť na hlavnú stránku"

#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:68
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:107
msgid "Background Color"
msgstr "Farba pozadia"

#: src/constants/analytics.js:529
#: src/constants/analytics.js:547
#: src/constants/analytics.js:565
#: src/constants/analytics.js:584
#: src/constants/analytics.js:602
#: src/constants/analytics.js:620
#: src/constants/analytics.js:638
#: src/constants/analytics.js:658
#: src/components/OurChart/OurChartAdvanced.js:141
msgid "Bar"
msgstr "Stĺpcový"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:46
msgid "Basic settings"
msgstr "Základné nastavenie"

#: src/components/layout/MntrActiveFilters/modules/Paywalled.js:6
msgid "Behind paywall"
msgstr "Za paywallom"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:141
msgid "Below avg."
msgstr "Podpriemer"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:81
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:220
msgid "best"
msgstr "najlepšie"

#: src/constants/analytics/primeScoreCharts.ts:95
msgid "Best PRIMe mediatypes"
msgstr "Najlepšie mediatypy PRIMe"

#: src/components/staff/admin/customer/bio/CustomerBio.js:106
msgid "Billing email"
msgstr "Fakturačný email"

#: src/components/medialist/forms/FormEditAuthor.js:855
#: src/components/medialist/forms/FormEditAuthor.js:1023
msgid "Bio"
msgstr "Bio"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:273
msgid "Blockquote"
msgstr "Citácia"

#: src/store/models/newsroom/blogs/posts/NewsroomPostsStoreArrItem.ts:106
msgid "Blog post was successfully deleted."
msgstr "Príspevok bol úspešne zmazaný."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:123
msgid "Bold"
msgstr "Tučne"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:158
msgid "Brackets"
msgstr "Zátvorky"

#: src/pages/brand-tracking.tsx:29
#: src/components/layout/Sidebar/SidebarNavigation.tsx:169
#: src/app/components/monitoring-navigation.tsx:301
msgid "Brand Tracking"
msgstr "Sledovanie brandu"

#: src/components/monitoring/Inspector/InspectorMonitora/KeywordsPagination/KeywordsPagination.js:256
msgid "Browse keywords"
msgstr "Prechádzať kľ. slová"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:257
msgid "Bullet list"
msgstr "Odrážky"

#: src/components/newsroom/components/PostsList/PostsList.js:162
msgid "By"
msgstr "Od"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:48
msgid "by source"
msgstr "podľa zdroja"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:152
msgid "By submitting the form, you agree to our <0>terms</0>."
msgstr "Odoslaním formuláru súhlasite s <0>podmienkami služby</0>."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:61
msgid "Call to Action (CTA):"
msgstr "Výzva k akcii (CTA):"

#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:136
msgid "Campaign"
msgstr "Kampaň"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:54
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:92
msgid "Campaign will be removed"
msgstr "Kampaň bude odstránená"

#: src/components/forms/dashboard/Search/SearchEmailingCampaigns.js:26
#: src/components/emailing/sidebar/EmailingSidebarDashboard.js:17
#: src/components/emailing/content/EmailingCampaignsContent.tsx:49
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:44
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:57
msgid "Campaigns"
msgstr "Kampane"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:139
msgid "Can unsubscribe"
msgstr "Môže zrušiť odber"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:76
msgid "Can't find an article in your feed? Enter a link to the article you are looking for and select a topic."
msgstr "Nedohľadali ste v niektorej z tém článok? Pre jeho vyhľadanie, alebo pridanie použite odkaz a vyberte tému, pod ktorou by mal byť uvedený."

#: src/components/reports/Content/ReportsList/ReportsForm.js:350
#: src/components/misc/VideoPlayer/getCropAction.js:7
#: src/components/misc/MntrForm/MntrForm.tsx:516
#: src/components/medialist/forms/FormEditAuthor.js:559
#: src/components/medialist/forms/FormEditAuthor.js:571
#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:86
msgid "Cancel"
msgstr "Zrušiť"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:273
msgid "Cancel choice"
msgstr "Zrušiť"

#: src/components/misc/Changelog/ChangelogTableRow.js:247
msgid "Cancel revert"
msgstr "Zrušiť revert"

#: src/components/misc/UploadWatcher/UploadWatcher.js:40
msgid "Cancel Upload"
msgstr "Zrušiť nahrávanie"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:35
msgid "Cannot delete articles, run manual sentiment, create/edit topics or reports, change account settings, delete TV/radio stories, or edit CRM info."
msgstr "Nemôže mazať články, spúšťať manuálnu analýzu sentimentu, vytvárať/upravovať témy alebo správy, meniť nastavenia účtu, mazať televízne/rádiové príbehy ani upravovať informácie v CRM."

#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/MediaDetail.js:120
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:279
msgid "Captured on the screen"
msgstr "Zachytené na obrazovke"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:67
msgid "Categories"
msgstr "Kategórie"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:29
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNewsroomCategory.js:24
#: src/components/layout/MntrActiveFilters/modules/NewsroomCategory.js:21
msgid "Category"
msgstr "Kategória"

#. placeholder {0}: item.name
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:146
msgid "Category <0>{0}</0> will be removed."
msgstr "Kategória <0>{0}</0> bude odstránená"

#: src/components/newsroom/forms/FormCreateCategory/FormCreateCategory.js:25
msgid "Category name"
msgstr "Názov kategórie"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:47
msgid "Change email"
msgstr "Zmeniť email"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:27
#: src/components/settings/SettingsUserManagement/UpdateRole.tsx:20
msgid "Change role"
msgstr "Zmeniť rolu"

#: src/components/misc/Changelog/ChangelogTable.js:36
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:394
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChangeType.js:24
msgid "Change Type"
msgstr "Typ zmeny"

#: src/pages/topics/[topicId]/changelog.js:13
#: src/pages/staff/admin/workspaces/[workspaceId]/changelog.js:12
#: src/pages/reports/[reportId]/changelog.js:13
#: src/components/staff/admin/workspace/Workspace.js:147
msgid "Changelog"
msgstr "História zmien"

#: src/store/models/admin/customer/CustomerStore.js:163
#: src/store/models/admin/customer/CustomerStore.js:177
#: src/store/models/admin/customer/CustomerStore.js:186
#: src/store/models/admin/customer/CustomerStore.js:231
#: src/store/models/admin/customer/CustomerStore.js:258
#: src/store/models/admin/customer/CustomerStore.js:286
msgid "Changes successfully saved."
msgstr "Zmeny úspešne uložené."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:27
msgid "Channel"
msgstr "Stanica"

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:241
msgid "Channels"
msgstr "Kanály"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:169
msgid "Chart"
msgstr "Graf"

#: src/components/OurChart/OurChartAdvanced.js:128
msgid "Chart Settings"
msgstr "Nastavenie grafu"

#: src/components/OurChart/OurChartAdvanced.js:137
msgid "Chart Type"
msgstr "Typ grafu"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:238
msgid "Check"
msgstr "Kontrola"

#: src/components/emailing/content/tabs/AddRecipients.tsx:80
msgid "Choose authors list or tag:"
msgstr "Vyberte zoznam autorov alebo štítok:"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:35
msgid "Choose how to add/edit your signature"
msgstr "Vyberte si, ako pridať/upraviť svoj podpis"

#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:44
msgid "Clear Color"
msgstr "Zrušiť farbu"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:139
msgid "Clear formatting"
msgstr "Odstrániť formátovanie"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/EntityInfoBox.js:220
msgid "click to open the detail"
msgstr "kliknutím otvoríte detail"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:342
msgid "Click to see options"
msgstr "Kliknite pre zobrazenie možností"

#: src/components/forms/inspector/FormMediaEditor.js:124
msgid "Clip duration"
msgstr "Dĺžka klipu"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:127
#: src/components/reports/history/Compose.js:36
#: src/components/misc/portable/PortableResend/PortableResend.js:59
#: src/components/misc/portable/PortableResend/PortableResend.js:99
#: src/components/misc/portable/PortableExport/PortableExport.js:55
#: src/components/misc/portable/PortableExport/PortableExport.js:95
#: src/components/misc/MntrHint/MntrHint.js:77
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:111
#: src/components/misc/Mntr/ButtonGroup.tsx:51
msgid "Close"
msgstr "Zatvoriť"

#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:129
msgid "Collapse"
msgstr "Zbaliť"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:53
msgid "Color palette"
msgstr "Farebná paleta"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:208
msgid "Colors"
msgstr "Farby"

#: src/constants/analytics/primeScoreCharts.ts:115
#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:60
msgid "Column"
msgstr "Stlpec"

#: src/components/newsroom/content/posts/NewsroomPosts.js:126
msgid "Compact"
msgstr "Kompaktné"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:66
msgid "Company"
msgstr "Spoločnosť"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:93
msgid "Company (Name or CRN)"
msgstr "Firma (názov alebo IČO)"

#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:79
msgid "Company detail"
msgstr "Detail firmy"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:72
#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:26
msgid "Company info"
msgstr "Firemné údaje"

#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:893
msgid "Compare Topic"
msgstr "Porovnať téma"

#: src/pages/sign-up-completion.tsx:30
#: src/pages/staff/sign-up-completion.js:26
msgid "Completion"
msgstr "Dokončenie registrácie"

#: src/components/emailing/forms/FormSenderSettings.js:260
msgid "Configuring DKIM (DomainKeys Identified Mail) enhances the integrity and authenticity of your emails, reducing the likelihood of them being marked as spam:"
msgstr "Konfigurácia DKIM (DomainKeys Identified Mail) zvyšuje integritu a autentickosť vašich emailov a znižuje pravdepodobnosť, že budú označené ako spam:"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:51
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:319
msgid "Confirm"
msgstr "Potvrdiť"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:93
msgid "Confirm new password"
msgstr "Potvrdiť nové heslo"

#: src/helpers/store/apiClient.js:240
msgid "Connection with the server was lost. Please try again."
msgstr "Spojenie so serverom bolo prerušené. Prosím skúste znova."

#: src/components/layout/Sidebar/SidebarNavigation.tsx:263
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:160
msgid "Contact"
msgstr "Kontakt"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:123
msgid "Contact information"
msgstr "Kontaktné údaje"

#: src/components/medialist/forms/FormEditAuthor.js:761
#: src/components/medialist/content/MedialistDashboard.js:88
#: src/components/medialist/content/MedialistDashboard.js:121
#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:56
msgid "Contacts"
msgstr "Kontakty"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:178
msgid "Contacts cannot be imported from this file"
msgstr "Kontakty nie je možné importovať z tohto súboru"

#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:19
msgid "Contacts import in progress"
msgstr "Prebieha import kontaktov"

#: src/pages/user/reset-password/new.tsx:48
#: src/pages/user/reset-password/index.tsx:29
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:167
#: src/components/page/auth/SignUp/SignUp.js:58
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:610
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:246
#: src/components/newsroom/content/newsroom/NewsroomCreateBlog.js:19
#: src/components/misc/Wizard/WizardChoice.tsx:150
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:143
#: src/components/medialist/forms/FormEditAuthor.js:300
#: src/components/medialist/forms/FormEditAuthor.js:318
#: src/components/medialist/forms/FormEditAuthor.js:464
#: src/components/medialist/forms/FormEditAuthor.js:483
#: src/components/medialist/forms/FormEditAuthor.js:578
#: src/components/emailing/forms/FormAddCampaign.tsx:20
#: src/components/emailing/content/CreateEmailContent.js:354
msgid "Continue"
msgstr "Pokračovať"

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:58
msgid "Continue to import"
msgstr "Pokračovať v importe"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:229
#: src/components/medialist/forms/FormEditAuthor.js:116
#: src/components/medialist/forms/FormEditAuthor.js:120
#: src/components/medialist/forms/FormEditAuthor.js:872
#: src/components/medialist/forms/FormEditAuthor.js:1048
#: src/components/emailing/forms/FormSenderSettings.js:69
msgid "Copied to the clipboard."
msgstr "Skopírované do schránky."

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:64
msgid "Copy article to clipboard"
msgstr "Skopírovať článok do schránky"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:12
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:13
#: src/components/exportList/History/HistoryTable/HistoryTable.js:115
msgid "Copy link to clipboard"
msgstr "Skopírovať odkaz do schránky"

#: src/components/staff/admin/workspace/UsersTable/CopyPassword.js:27
msgid "Copy password"
msgstr "Skopírovať heslo"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:117
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:76
msgid "Copy public URL to clipboard"
msgstr "Skopírovať verejnú URL do schránky"

#: src/components/reports/history/HistoryTable.js:436
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:96
msgid "Copy recipients to clipboard"
msgstr "Skopírovať príjemcov do schránky"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:34
#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:66
msgid "Copy share link"
msgstr "Skopírovať odkaz na zdieľanie"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:70
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:82
msgid "Copy to another campaign"
msgstr "Skopírovať do inej kampane"

#: src/helpers/modal/withModalEmailPreview.js:102
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:585
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:644
#: src/components/medialist/forms/FormEditAuthor.js:773
#: src/components/medialist/forms/FormEditAuthor.js:801
#: src/components/medialist/forms/FormEditAuthor.js:869
#: src/components/medialist/forms/FormEditAuthor.js:1045
#: src/components/emailing/forms/FormSenderSettings.js:65
msgid "Copy to clipboard"
msgstr "Skopírovať do schránky"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:112
msgid "Copy to Dashboard"
msgstr "Skopírovať do dashboardu"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:238
#: src/components/misc/ActionsBar/View/ViewMenu.js:187
msgid "Cost per Point (CCP) - how much does one second of advertising cost for each GRP point (AVE = CPP * GRP * duration)"
msgstr "Cost per Point (CCP) - koľko stojí jedna sekunda reklamy na každý GRP bod (AVE = CPP * GRP * dĺžka)"

#: src/constants/analytics.js:864
msgid "Countries"
msgstr "Krajiny"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:124
msgid "countries with enabled mediatype"
msgstr "krajiny so zapnutým mediatypom"

#: src/constants/analytics.js:851
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:74
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:360
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:76
#: src/components/medialist/forms/FormEditAuthor.js:246
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:271
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:291
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:80
msgid "Country"
msgstr "Krajina"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:63
msgid "Cover page"
msgstr "Titulná strana"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:233
#: src/components/misc/ActionsBar/View/ViewMenu.js:182
msgid "CPP"
msgstr "CPP"

#: src/components/medialist/forms/FormEditAuthor.js:601
msgid "Create"
msgstr "Vytvoriť"

#: src/components/page/auth/Login/Login.tsx:75
msgid "Create an account for free"
msgstr "Vytvoriť účet zdarma"

#: src/components/emailing/content/NewEmailWizardButton.tsx:15
msgid "Create an email"
msgstr "Vytvoriť e-mail"

#: src/components/medialist/content/withAddToBasketPopup.js:52
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorBaskets/AddAuthorToBasket.js:59
msgid "Create and add to new list"
msgstr "Vytvoriť zoznam a pridať autora"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:68
#: src/components/monitoring/FeedActionsBar/withAddTagPopup/AddTagPopupContent.js:49
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorTags/AddAuthorTag.js:51
msgid "Create and assign new tag"
msgstr "Vytvoriť a priradiť nový štítok"

#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:48
#: src/app/components/monitoring-navigation.tsx:213
msgid "Create article"
msgstr "Vytvoriť článok"

#: src/pages/workspace-articles.js:64
#: src/components/monitoring/WorkspaceArticles/withWorkspaceArticleModal.js:9
msgid "Create Article"
msgstr "Vytvoriť článok"

#: src/components/medialist/content/MedialistDashboard.js:144
#: src/components/medialist/content/AuthorBasketsMenu.js:51
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:249
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:250
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:168
msgid "Create author"
msgstr "Vytvoriť autora"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:76
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:77
msgid "Create Folder"
msgstr "Vytvoriť priečinok"

#: src/components/medialist/content/AuthorBasketsMenu.js:151
msgid "Create new list"
msgstr "Vytvoriť nový zoznam"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:69
msgid "Create new Newsroom"
msgstr "Vytvoriť nový Newsroom"

#: src/components/newsroom/content/newsroom/NewsroomCreateBlog.js:18
msgid "Create Newsroom"
msgstr "Vytvoriť Newsroom"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:240
msgid "Create Own Article"
msgstr "Vytvoriť vlastný článok"

#: src/components/newsroom/content/posts/NewPostWizardButton.tsx:16
msgid "Create post"
msgstr "Vytvoriť článok"

#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:69
msgid "Create Report"
msgstr "Vytvoriť report"

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:24
#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:26
msgid "Create workspace"
msgstr "Vytvoriť workspace"

#: src/pages/authors/index.js:61
msgid "Create your own <0>lists</0> and <1>tags</1>"
msgstr "Vytvárajte vlastné <0>zoznamy</0> a <1>štítky</1>"

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:44
msgid "Created"
msgstr "Vytvorené"

#: src/store/models/dashboards/DashboardPreview.js:134
#: src/pages/crisis-communication.js:10
#: src/pages/crisis-communication-story/[articleId].js:10
#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:19
#: src/components/layout/Sidebar/SidebarNavigation.tsx:129
#: src/components/layout/Sidebar/SidebarNavigation.tsx:255
#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:26
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:37
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:23
#: src/app/components/monitoring-navigation.tsx:258
msgid "Crisis communication"
msgstr "Krízová komunikácia"

#: src/store/models/monitoring/Inspector/EntityKnowledgeBaseStore/EntityKnowledgeBaseStore.js:23
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:55
msgid "CRN"
msgstr "IČ"

#. placeholder {0}: option.reg_no
#. placeholder {0}: data.reg_no
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:117
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorAddress/AuthorAddress.js:14
msgid "CRN: {0}"
msgstr "IČO: {0}"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:124
#: src/components/misc/MntrEditor/modals/withModalCTAButton.js:8
#: src/components/misc/MntrEditor/extensions/ExtensionCTAButton.js:23
msgid "CTA Button"
msgstr "CTA tlačidlo"

#: src/components/staff/admin/user/User.js:286
#: src/components/settings/SettingsApplication/SettingsApplication.js:42
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:408
msgid "Currency"
msgstr "Mena"

#: src/components/settings/SettingsApplication/SettingsApplication.js:33
msgid "Currency in which to calculate AVE."
msgstr "Mena, v ktorej sa počíta AVE."

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:81
msgid "Current password"
msgstr "Súčasné heslo"

#: src/components/newsroom/modals/withModalCustomAiRewrite.tsx:11
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:645
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:65
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:49
#: src/components/emailing/content/CreateEmailContent.js:548
msgid "Custom"
msgstr "Vlastný"

#: src/components/emailing/content/promo/PromoEmailing.js:22
msgid "Custom branding"
msgstr "Vlastný branding"

#: src/components/settings/SettingsTheme/SettingsThemePreview/LogoColorPicker/LogoColorPicker.js:70
#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:65
msgid "Custom color"
msgstr "Vlastná farba"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:171
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:135
#: src/components/misc/MntrEditor/modals/withModalHtmlCode.js:17
#: src/components/misc/MntrEditor/extensions/ExtensionHtmlCode.js:23
msgid "Custom HTML Code"
msgstr "Vlastný HTML kód"

#: src/components/newsroom/modals/withModalCustomAiRewrite.tsx:24
msgid "Custom insruction"
msgstr "Vlastná inštrukcia"

#: src/components/settings/SettingsLogo/SettingsLogo.js:63
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:78
msgid "Custom logo"
msgstr "Vlastné logo"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:176
msgid "Custom meta/script/style for <head> section"
msgstr "Vlastný meta/script/style kód pre sekciu <head>"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:83
msgid "Custom selection"
msgstr "Vlastný výber"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:353
msgid "Custom Slug"
msgstr "Vlastný URL slug"

#: src/components/reports/Content/ReportsList/ReportsForm.js:306
msgid "Custom subject (optional)"
msgstr "Vlastný predmet (voliteľný)"

#: src/pages/staff/admin/customers/index.js:12
#: src/components/staff/admin/customers/Customers.js:20
#: src/components/layout/Sidebar/SidebarNavigation.tsx:188
#: src/components/layout/Header/UserMenu/UserMenu.tsx:219
#: src/components/forms/dashboard/Search/SearchCustomers.js:54
#: src/app/components/monitoring-navigation.tsx:317
msgid "Customers"
msgstr "Zákazníci"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:313
msgid "Customization"
msgstr "Prispôsobenie"

#: src/components/misc/VideoPlayer/getCropAction.js:7
msgid "Cut clip"
msgstr "Ustrihnúť klip"

#: src/components/forms/inspector/FormMediaEditor.js:117
msgid "Cut from"
msgstr "Strih od"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:433
msgid "Cut media"
msgstr "Ustrihnúť nahrávku"

#: src/components/forms/inspector/FormMediaEditor.js:120
msgid "Cut to"
msgstr "Strih do"

#: src/pages/staff/admin/workspaces/[workspaceId]/daily-access.js:12
#: src/pages/staff/admin/users/[userId]/daily-access.js:12
#: src/components/staff/admin/workspace/Workspace.js:153
#: src/components/staff/admin/user/User.js:77
msgid "Daily Access"
msgstr "Prístupy do app"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:54
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:70
#: src/components/misc/ActionsBar/View/ViewMenu.js:166
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:106
msgid "Daily listenership"
msgstr "Denná počúvanosť"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:71
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:45
msgid "daily users"
msgstr "užívateľov za deň"

#: src/components/misc/ActionsBar/View/ViewMenu.js:66
msgid "Daily users"
msgstr "Denne užívateľov"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:151
msgid "Dark"
msgstr "Tmavý"

#: src/components/settings/SettingsTheme/SettingsThemePreview/SettingsThemePreview.js:193
msgid "Dark mode preview"
msgstr "Náhľad tmavého režimu"

#: src/pages/dashboard/index.js:18
#: src/pages/dashboard/shared/[dashboardKey].js:15
#: src/app/components/monitoring-navigation.tsx:91
msgid "Dashboard"
msgstr "Dashboard"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:19
msgid "Dashboard sharing"
msgstr "Zdieľanie dashboardu"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:56
msgid "Dashboard will be removed."
msgstr "Dashboard bude odstránený."

#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:47
msgid "Dashboard will be shared in read-only form (non-interactive) with currently displayed data. Link expiration is 30 days."
msgstr "Dashboard bude nazdieľaný so statickými dátami, ktoré zobrazujú aktuálne hodnoty. Odkaz vyprší za 30 dní."

#: src/components/tariff/TariffLimits/TariffLimits.js:150
#: src/components/staff/admin/workspace/Workspace.js:460
msgid "Dashboards limit"
msgstr "Počet dashboardov"

#: src/components/staff/admin/DailyAccess/Table.js:21
#: src/components/reports/history/HistoryTable.js:146
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:180
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:81
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:263
#: src/components/exportList/History/HistoryTable/HistoryTable.js:48
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:177
msgid "Date"
msgstr "Dátum"

#: src/components/misc/Changelog/ChangelogTable.js:30
msgid "Date & User"
msgstr "Dátum a užívateľ"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:238
msgid "Date and time must be in the future"
msgstr "Dátum a čas musia byť v budúcnosti"

#. placeholder {0}: format(effectiveMinDate, DATE_FORMAT)
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:35
msgid "Date cannot be earlier than {0}"
msgstr "Dátum nemôže byť skorší ako {0}"

#. placeholder {0}: format(effectiveMaxDate, DATE_FORMAT)
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:38
msgid "Date cannot be later than {0}"
msgstr "Dátum nemôže byť neskorší ako {0}"

#: src/constants/analytics.js:827
msgid "Day of the week"
msgstr "Deň v týždni"

#: src/helpers/charts/makeGranularityMenu.js:10
#: src/helpers/charts/getGranularityLabel.js:16
msgid "Days"
msgstr "Dni"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:289
msgid "DD.MM.YYYY"
msgstr "DD.MM.YYYY"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:32
#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:51
msgid "Deduplicate articles"
msgstr "Deduplikácia článkov"

#: src/components/staff/admin/workspace/Workspace.js:763
msgid "Deduplicate feed articles"
msgstr "Deduplikovať články vo feede"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:36
msgid "Deduplication will remove same or similar articles from the report according to your settings. It will not remove any article from the feed."
msgstr "Deduplikácia odstráni z reportu rovnaké alebo podobné články podľa Vašeho nastavenia rozsahu a podobnosti. Články budú naďalej dostupné vo feede v aplikácii."

#: src/components/newsroom/content/posts/NewsroomPosts.js:133
msgid "Default"
msgstr "Východzie"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:62
msgid "Define the action you want recipients to take."
msgstr "Definujte akciu, ktorú majú príjemcovia vykonať."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:37
msgid "Define the subject line to set the focus and tone."
msgstr "Definujte predmet, aby ste určili zameranie a tón."

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:137
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:78
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:160
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:63
#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:87
#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:60
#: src/components/newsroom/content/posts/NewsroomPosts.js:201
#: src/components/newsroom/content/post/AttachmentsList.js:52
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:101
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:357
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:388
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:37
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:35
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:279
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:75
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:37
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:35
#: src/components/medialist/forms/modules/FormArray.js:119
#: src/components/medialist/forms/modules/FormArray.js:165
#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:33
#: src/components/emailing/content/SignaturePopup.tsx:31
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:85
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:96
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:117
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:53
msgid "Delete"
msgstr "Odstrániť"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:348
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:360
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:385
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:313
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:388
msgid "Delete Article"
msgstr "Odstrániť článok"

#: src/components/newsroom/content/post/AttachmentsList.js:56
msgid "Delete attachment"
msgstr "Zmazať prílohu"

#: src/components/medialist/forms/FormEditAuthor.js:311
#: src/components/medialist/forms/FormEditAuthor.js:317
#: src/components/medialist/forms/FormEditAuthor.js:476
#: src/components/medialist/forms/FormEditAuthor.js:482
msgid "Delete author"
msgstr "Zmazať autora"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:393
msgid "Delete blog post"
msgstr "Zmazať príspevok"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:133
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:142
msgid "Delete category"
msgstr "Zmazať kategóriu"

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:37
msgid "Delete file"
msgstr "Vymazať súbor"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:286
msgid "Delete Folder"
msgstr "Odstrániť priečinok"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:375
msgid "Delete from media coverage"
msgstr "Odstrániť z mediálneho pokrytia"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:131
#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:140
msgid "Delete Item"
msgstr "Odstrániť položku"

#: src/components/medialist/content/AuthorBasketsMenu.js:126
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:108
msgid "Delete list"
msgstr "Zmazať zoznam"

#: src/components/medialist/content/AuthorBasketsMenu.js:134
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:123
msgid "Delete list?"
msgstr "Zmazať zoznam?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:659
msgid "Delete Newsroom"
msgstr "Zmazať Newsroom"

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:147
msgid "Delete recipient"
msgstr "Odstrániť príjemcu"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:121
msgid "Delete Sender"
msgstr "Odstrániť odosielateľa"

#: src/components/emailing/content/SignaturePopup.tsx:34
msgid "Delete signature"
msgstr "Odstrániť podpis"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:302
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:311
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:192
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:201
msgid "Delete tag"
msgstr "Vymazať štítok"

#: src/pages/trash.js:16
msgid "Deleted Articles"
msgstr "Zmazané články"

#. placeholder {0}: feed.totalCount
#: src/components/trash/Content.js:41
msgid "Deleted Articles ({0})"
msgstr "Zmazané články ({0})"

#: src/components/trash/Content.js:49
msgid "Deleted articles will appear here when deleted in the Articles section."
msgstr "Tu nájdete zmazané články zo sekcie Príspevky."

#: src/components/reports/history/RecipientsTableRow.js:40
#: src/components/reports/history/HistoryTable.js:84
#: src/components/reports/history/HistoryTable.js:118
#: src/components/reports/history/HistoryTable.js:328
msgid "Delivered"
msgstr "Doručený"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:204
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:230
msgid "Delivery rate"
msgstr "Miera doručenia"

#: src/components/reports/history/HistoryTable.js:162
msgid "Delivery stats"
msgstr "Doručiteľnosť"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:20
msgid "Demo"
msgstr "Demo"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:111
msgid "Demographic Data"
msgstr "Demografické údaje"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:47
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:96
#: src/components/feed/InspectorToolbar/InspectorToolbar.js:145
msgid "Demographics"
msgstr "Demografia"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:42
msgid "Describe who will receive the email (demographics, interests)."
msgstr "Popíšte, kto bude dostávať e-mail (demografické údaje, záujmy)."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:59
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:373
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:223
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:43
msgid "Description"
msgstr "Popis"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:44
#: src/components/layout/MntrFiltersBar/modules/MenuFilterToggleAllButtons.js:76
msgid "Deselect all"
msgstr "Zrušiť výber"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:23
msgid "Designed for PR professionals, generates a press release structure."
msgstr "Navrhnuté pre PR profesionálov, generuje štruktúru tlačovej správy."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:80
msgid "Detailed instructions for email text creation"
msgstr "Podrobné pokyny na vytvorenie textu e-mailu"

#: src/pages/newsroom/index.js:53
msgid "Detailed traffic<0/> <1>analytics</1>"
msgstr "Detailný<0/> <1>prehľad návštevnosti</1>"

#: src/constants/analytics/primeScoreCharts.ts:31
msgid "Development of PRIMe by rating"
msgstr "Vývoj PRIMe podľa hodnotenia"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:149
msgid "Deviation from the average"
msgstr "Odchýlka od priemeru. Čím vyššia hodnota, tým zaujímavejšie výsledky."

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:89
msgid "Disable"
msgstr "Vypnúť"

#: src/components/staff/admin/workspace/Workspace.js:225
#: src/components/staff/admin/workspace/Workspace.js:958
#: src/components/staff/admin/user/User.js:167
#: src/components/staff/admin/user/User.js:336
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:279
msgid "Discard"
msgstr "Zahodiť zmeny"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:227
msgid "Discard changes"
msgstr "Zahodiť zmeny"

#: src/components/monitoring/Inspector/InspectorMonitora/DiscussionThreadBar/DiscussionThreadBar.js:20
#: src/components/layout/MntrActiveFilters/modules/DiscussionThread.js:12
msgid "Discussion thread"
msgstr "Diskusné vlákno"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:98
msgid "Display empty categories"
msgstr "Zobraziť prázdne kategórie"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:205
msgid "Display the article"
msgstr "Zobraziť článok"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:154
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:49
#: src/components/misc/ActionsBar/View/ViewMenu.js:116
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:75
msgid "Distribution amount"
msgstr "Náklad"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:149
msgid "DNS settings are invalid."
msgstr "DNS nie je nastavené správne."

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:147
msgid "DNS settings are valid."
msgstr "DNS je nastavené správne."

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:108
msgid "Do not add new media to the medium"
msgstr "Nepridávať nové zdroje k mediatypu"

#: src/components/medialist/forms/FormEditAuthor.js:299
#: src/components/medialist/forms/FormEditAuthor.js:316
#: src/components/medialist/forms/FormEditAuthor.js:463
#: src/components/medialist/forms/FormEditAuthor.js:481
msgid "Do you really want to continue?"
msgstr "Naozaj chcete pokračovať?"

#. placeholder {0}: menuItem.name
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:294
msgid "Do you really want to delete '<0>{0}</0>'?"
msgstr "Naozaj chcete odstrániť '<0>{0}</0>'?"

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:200
msgid "Do you want to add author addresses from this list?"
msgstr "Chcete pridať adresy autorov z tohto zoznamu?"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:84
msgid "Do you want to start with AI assistant?"
msgstr "Chcete začať s AI asistentom?"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:124
msgid "Do you wish to reactivate this recipient?"
msgstr "Prajete si znova aktivovať tohto príjemcu?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:55
#: src/components/emailing/forms/FormSenderSettings.js:267
msgid "Domain"
msgstr "Doména"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:489
msgid "Domain change"
msgstr "Zmena domény"

#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:130
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:24
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:23
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:184
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:30
#: src/components/forms/dashboard/Export/ExportForm.js:100
#: src/components/exportList/History/HistoryTable/HistoryTable.js:96
#: src/components/exportList/Content/Content.tsx:78
#: src/components/OurChart/OurChartAdvanced.js:181
msgid "Download"
msgstr "Stiahnuť"

#: src/components/forms/inspector/FormMediaEditor.js:141
msgid "Download clip"
msgstr "Stiahnuť klip"

#: src/components/staff/admin/workspace/Workspace.js:126
msgid "Download settings (.xlsx)"
msgstr "Stiahnuť nastavenie (.xlsx)"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:82
msgid "Download template"
msgstr "Stiahnuť šablónu"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewImage/PreviewImage.js:60
msgid "Drag 'n' drop image or click to select files"
msgstr "Kliknutím alebo pretiahnutím vyberte obrázok"

#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryAdapter.js:59
msgid "Drag 'n' drop some images here, or click to select files"
msgstr "Pretiahnite sem obrázky alebo kliknite pre výber súborov"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:66
msgid "Due date"
msgstr "Dátum splatnosti"

#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:36
#: src/components/medialist/content/AuthorBasketsMenu.js:117
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:101
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:59
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:71
msgid "Duplicate"
msgstr "Duplikovať"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:74
msgid "Duplicate widget"
msgstr "Duplikovať widget"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:214
#: src/components/misc/ActionsBar/View/ViewMenu.js:174
msgid "Duration"
msgstr "Dĺžka"

#: src/components/layout/AuthWrapper/constants/features.slides.js:307
msgid "Dynamic platform for creating, curating, and sharing captivating content."
msgstr "Vytvárajte a zdieľajte vizuálne pútavé tlačové správy vďaka našej modernej platforme."

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:89
#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:124
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:51
#: src/components/misc/MntrEditor/extensions/ExtensionImageGallery.js:53
#: src/components/emailing/content/SignaturePopup.tsx:22
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:65
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:46
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:34
msgid "Edit"
msgstr "Upraviť"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:443
msgid "Edit article"
msgstr "Editovať článok"

#: src/components/newsroom/content/posts/NewsroomPosts.js:194
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:169
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:170
msgid "Edit Article"
msgstr "Editovať článok"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:34
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:37
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:77
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:80
msgid "Edit Campaign"
msgstr "Upraviť kampaň"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/modalEditCategory.js:10
#: src/components/layout/Sidebar/modules/NewsroomNavigation/modalEditCategory.js:24
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:113
msgid "Edit category"
msgstr "Upraviť kategóriu"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:37
msgid "Edit dashboard"
msgstr "Upraviť dashboard"

#: src/components/topics/Content/TopicsList/Keyword/Keyword.js:62
msgid "Edit keyword"
msgstr "Upraviť kľúčové slovo"

#: src/components/medialist/content/AuthorBasketsMenu.js:102
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:19
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:87
msgid "Edit list"
msgstr "Upraviť zoznam"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:63
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:135
#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:60
msgid "Edit mediatypes"
msgstr "Upraviť mediatypy"

#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:56
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:106
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:131
#: src/components/medialist/forms/FormEditAuthor.js:710
msgid "Edit note"
msgstr "Upraviť poznámku"

#: src/components/medialist/forms/FormEditAuthor.js:282
#: src/components/medialist/forms/FormEditAuthor.js:448
msgid "Edit profile"
msgstr "Upraviť profil"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:67
msgid "Edit recipient"
msgstr "Upraviť príjemcu"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:48
msgid "Edit Sender"
msgstr "Upraviť odosielateľa"

#: src/components/newsroom/content/posts/NewsroomPosts.js:300
#: src/components/newsroom/content/dashboard/NewsroomBlogSettings.js:23
msgid "Edit settings"
msgstr "Upraviť nastavenie"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:26
msgid "Edit signature"
msgstr "Upraviť podpis"

#: src/components/layout/Sidebar/modules/SidebarTags/modalEditTags.js:10
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:279
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:168
msgid "Edit tag"
msgstr "Upraviť štítok"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:32
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:58
msgid "Edit topic"
msgstr "Upraviť tému"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:59
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:63
msgid "Edit widget"
msgstr "Upraviť widget"

#: src/components/medialist/forms/modules/FormArray.js:94
msgid "Editorial Office"
msgstr "Redakcia"

#: src/components/medialist/forms/FormEditAuthor.js:848
#: src/components/medialist/forms/FormEditAuthor.js:1012
msgid "Editorial offices and positions"
msgstr "Redakcie a pozície"

#: src/components/medialist/content/MedialistAuthorCreate.js:24
msgid "Eg. sent press releases, profile edits, published articles related to your press releases."
msgstr "Napr. odoslané tlačové správy, úpravy profilov, publikované články súvisiace s vašimi tlačovými správami."

#: src/pages/user/reset-password/index.tsx:20
#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:116
#: src/components/staff/admin/user/User.js:246
#: src/components/staff/admin/customer/users/UsersTable.js:68
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:61
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:224
#: src/components/reports/history/RecipientsTableHeader.js:30
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:133
#: src/components/page/auth/SignUp/SignUp.js:37
#: src/components/page/auth/Login/Login.tsx:40
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:128
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:74
#: src/components/medialist/forms/FormEditAuthor.js:793
#: src/components/medialist/forms/FormEditAuthor.js:911
#: src/components/medialist/forms/FormEditAuthor.js:916
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:33
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:100
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:111
#: src/components/emailing/content/EmailDetailEmailContent.js:37
#: src/components/emailing/content/CreateEmailContent.js:262
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:50
msgid "Email"
msgstr "Email"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:26
msgid "Email address was verified"
msgstr "Emailová adresa bola overená"

#. placeholder {0}: campaign.name
#: src/components/emailing/content/CampaignAutocompleteList.tsx:41
msgid "Email copied to {0}"
msgstr "Email skopírovaný do {0}"

#: src/store/models/ExportStore.js:126
msgid "Email has been successfully sent."
msgstr "Email bol úspešne odoslaný."

#: src/components/emailing/content/CreateEmailContent.js:77
msgid "Email is locked and cannot be edited. If you want to edit the email, return it to the draft state."
msgstr "Email je uzamknutý a nie je možné ho upravovať. Ak chcete email upraviť, vráťte ho do stavu návrhu."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:45
msgid "Email is missing"
msgstr "Chýba e-mail "

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:12
msgid "Email is required"
msgstr "Email je povinný"

#: src/components/emailing/content/CreateEmailContent.js:72
msgid "Email is sending"
msgstr "E-mail sa odosiela"

#: src/helpers/modal/withModalEmailPreview.js:120
msgid "Email preview"
msgstr "Náhľad emailu"

#: src/components/reports/Content/ReportsList/ReportsHeading/ReportsHeading.js:10
msgid "Email reports ({counter})"
msgstr "Emailové reporty ({counter})"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:142
msgid "Email subject"
msgstr "Predmet e-mailu"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:36
msgid "Email Subject:"
msgstr "Predmet e-mailu:"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:62
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:74
msgid "Email successfully duplicated"
msgstr "Email bol úspešne zduplikovaný"

#: src/components/emailing/content/CreateEmailContent.js:134
msgid "Email was saved"
msgstr "Email bol uložený"

#: src/components/emailing/content/CreateEmailContent.js:153
msgid "Email was sent"
msgstr "Email bol odoslaný"

#: src/components/emailing/content/CreateEmailContent.js:124
msgid "Email was set to draft"
msgstr "Email bol nastavený do stavu návrhu"

#: src/components/emailing/content/CreateEmailContent.js:74
msgid "Email will be sent at: {scheduledDate}"
msgstr "Email bude poslaný v čase: {scheduledDate}"

#: src/components/staff/admin/user/User.js:115
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:384
msgid "Email with further instructions has been sent."
msgstr "Email s ďalšími pokynmi bol odoslaný."

#: src/pages/emailing/settings.tsx:16
#: src/pages/emailing/index.tsx:14
#: src/pages/emailing/campaign/[campaignId]/recipients.tsx:16
#: src/pages/emailing/campaign/[campaignId]/media-coverage.tsx:18
#: src/pages/emailing/campaign/[campaignId]/index.tsx:19
#: src/pages/emailing/campaign/[campaignId]/email/create.tsx:15
#: src/pages/emailing/campaign/[campaignId]/email/edit/[emailId]/index.tsx:12
#: src/pages/emailing/campaign/[campaignId]/email/[emailId]/recipients.tsx:16
#: src/pages/emailing/campaign/[campaignId]/email/[emailId]/index.tsx:16
#: src/components/layout/Sidebar/SidebarNavigation.tsx:158
#: src/components/layout/AuthWrapper/constants/features.slides.js:259
#: src/components/emailing/content/promo/PromoEmailing.js:17
#: src/components/emailing/components/FunnelStats/FunnelStats.tsx:79
#: src/app/components/monitoring-navigation.tsx:290
msgid "Emailing"
msgstr "Emailing"

#: src/components/reports/history/Compose.js:71
#: src/components/forms/dashboard/Search/SearchEmailingEmailMessages.js:27
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:106
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:54
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:158
msgid "Emails"
msgstr "Emaily"

#: src/components/medialist/forms/modules/MainEmailHelperText.js:6
msgid "Emails from <0>Emailing</0> will be sent to this address."
msgstr "Na túto adresu budú odosielané emaily z <0>Emailingu</0>."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:488
msgid "Embed"
msgstr "Vložiť"

#: src/components/misc/ActionsBar/View/ViewMenu.js:281
msgid "Emoji reactions"
msgstr "Emoji reakcie"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:83
msgid "Empty Body"
msgstr "Prázdne telo článku"

#: src/app/components/monitoring-navigation.tsx:123
msgid "Empty export"
msgstr "Vyprázdniť export"

#: src/app/components/monitoring-navigation.tsx:129
msgid "Empty export?"
msgstr "Vyprázdniť export?"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:74
msgid "Empty Perex"
msgstr "Prázdny perex"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:65
msgid "Empty Title"
msgstr "Prázdny názov článku"

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:89
msgid "Enable"
msgstr "Zapnúť"

#: src/components/notifications/Permissions.js:58
msgid "Enable notifications"
msgstr "Povoliť oznámenie"

#: src/components/tariff/AgencyMedia/AgencyMedia.js:69
#: src/components/misc/ActionsBar/View/ViewMenu.js:52
#: src/components/misc/ActionsBar/View/ViewMenu.js:272
msgid "Enabled"
msgstr "Zapnuté"

#: src/components/emailing/forms/FormSenderSettings.js:112
msgid "Encryption method"
msgstr "Metóda šifrovania"

#: src/components/settings/SettingsTheme/ThemePicker.tsx:99
msgid "Enforce primary color as header"
msgstr "Vynútiť primárnu farbu ako záhlavie"

#: src/constants/analytics.js:315
#: src/constants/analytics.js:424
#: src/constants/analytics.js:489
#: src/constants/analytics.js:511
#: src/constants/analytics.js:949
#: src/constants/analytics.js:964
#: src/components/monitoring/FeedList/FeedListItem/MetaData/modules/MetaDataEngagement/MetaDataEngagement.js:19
#: src/components/misc/ActionsBar/View/ViewMenu.js:305
msgid "Engagement rate"
msgstr "Engagement rate"

#: src/constants/analytics.js:336
msgid "Engagement rate by mention type"
msgstr "Engagement rate podľa typu zmienky"

#: src/constants/analytics.js:334
#: src/constants/analytics.js:443
#: src/constants/analytics.js:509
msgid "Engagement rate by sentiment"
msgstr "Engagement rate podľa sentimentu"

#: src/constants/analytics.js:445
msgid "Engagement rate by social network"
msgstr "Engagement rate podľa soc. siete"

#: src/components/analytics/SocialMedia.js:25
msgid "Engagement summary"
msgstr "Zhrnutie interakcií"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:62
#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:18
msgid "Enter a word or phrase"
msgstr "Zadajte vyhľadávací výraz"

#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:53
msgid "Enter text here..."
msgstr "Zadajte text sem..."

#: src/components/emailing/forms/FormSenderSettings.js:83
msgid "Enter the hostname of your SMTP server"
msgstr "Zadajte hostname serveru SMTP"

#: src/components/emailing/forms/FormSenderSettings.js:85
msgid "Enter the hostname of your SMTP server. This is usually in the format of \"smtp.yourdomain.com\"."
msgstr "Zadajte hostname SMTP serveru. Obvykle je vo formáte „smtp.vasadomena.sk“."

#: src/components/emailing/forms/FormSenderSettings.js:98
msgid "Enter the password to login to SMTP server"
msgstr "Zadajte heslo na prihlásenie k SMTP serveru"

#: src/components/emailing/forms/FormSenderSettings.js:100
msgid "Enter the password to login to SMTP server. This is usually the same password you use for your email."
msgstr "Zadajte heslo na prihlásenie k SMTP serveru. Zvyčajne je to to isté heslo, ktoré používate pre prihlásenie do emailu."

#: src/components/emailing/forms/FormSenderSettings.js:90
msgid "Enter the username to login to SMTP server"
msgstr "Zadajte username na prihlásenie k SMTP serveru"

#. placeholder {0}: initialValues.email
#: src/components/emailing/forms/FormSenderSettings.js:92
msgid "Enter the username to login to SMTP server. If left blank, \"{0}\" is used by default."
msgstr "Zadajte username na prihlásenie k SMTP serveru. Ak zostane prázdne, použije sa \"{0}\"."

#: src/pages/user/reset-password/index.tsx:17
msgid "Enter your email. We'll send you instructions on how to reset your password."
msgstr "Zadajte svoj email. Pošleme vám pokyny pre obnovenie hesla."

#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:61
msgid "Error detail"
msgstr "Podrobnosti chyby"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:163
msgid "Estimated number of distributed copies (print and digital)."
msgstr "Odhadovaný počet distribuovaných kópií (tlačených aj digitálnych)."

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:17
msgid "Eternal"
msgstr "Eternal"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:32
msgid "Everything enabled"
msgstr "Všetko povolené"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:24
msgid "Everything went well."
msgstr "Všetko prebehlo v poriadku."

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:44
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:78
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:118
msgid "Exact match"
msgstr "Presná zhoda"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:84
msgid "Exact match with separator"
msgstr "Presná zhoda s oddeľovačom"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:50
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:90
msgid "Exact match, including letter size"
msgstr "Presná zhoda vrátane veľkosti písmen"

#: src/pages/newsroom/index.js:72
msgid "Example Newsroom"
msgstr "Ukážkový Newsroom"

#: src/components/misc/VideoPlayer/Controls.js:143
msgid "Exit fullscreen"
msgstr "Ukončiť režim celej obrazovky"

#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:83
msgid "Expense type"
msgstr "Typ nákladu"

#: src/pages/staff/admin/customers/[customerId]/expenses.js:12
#: src/components/staff/admin/customer/expenses/Expenses.js:26
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:50
msgid "Expenses"
msgstr "Náklady"

#: src/components/page/auth/Expired/Expired.js:24
msgid "expired"
msgstr "expiroval"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:12
msgid "Expired"
msgstr "Expiroval"

#: src/components/staff/admin/workspace/Workspace.js:316
msgid "Expires at"
msgstr "Expiruje dne"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:30
msgid "Expires on: {formattedDate}"
msgstr "Expiruje: {formattedDate}"

#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:219
#: src/components/exportList/History/History.js:28
#: src/components/exportList/Content/Content.tsx:58
#: src/components/exportList/Content/Content.tsx:58
#: src/app/components/monitoring-navigation.tsx:102
msgid "Export"
msgstr "Export"

#: src/components/misc/portable/PortableExport/PortableExport.js:64
#: src/components/misc/portable/PortableExport/PortableExport.js:104
msgid "Export all articles"
msgstr "Export všetkých článkov"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:142
msgid "Export article"
msgstr "Exportovať článok"

#: src/components/misc/portable/PortableExport/PortableExport.js:62
#: src/components/misc/portable/PortableExport/PortableExport.js:102
msgid "Export articles"
msgstr "Export článkov"

#: src/components/staff/admin/workspace/Workspace.js:329
msgid "Export basket mode"
msgstr "Exportný košík"

#: src/pages/export/history.js:12
#: src/components/exportList/History/History.js:35
msgid "Export History"
msgstr "História exportov"

#: src/store/models/ExportStore.js:311
#: src/store/models/monitoring/Inspector/Inspector.ts:447
msgid "Export is full."
msgstr "Exportný košík je plný. Ďalšie články sa už do neho nedajú pridať."

#: src/components/exportList/Content/HeadingExport/HeadingExport.js:21
msgid "Export list is empty"
msgstr "Export je prázdny"

#: src/components/medialist/forms/FormEditAuthor.js:362
#: src/components/medialist/forms/FormEditAuthor.js:502
msgid "Export XLSX"
msgstr "Export XLSX"

#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:17
#: src/app/components/monitoring-navigation.tsx:117
msgid "Exports to download"
msgstr "Exporty ku stiahnutiu"

#: src/pages/external-analytics.tsx:29
#: src/components/layout/Sidebar/SidebarNavigation.tsx:179
msgid "External analytics"
msgstr "Externá analytika"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:41
msgid "External Communication Manager"
msgstr "Manažér externej komunikácie"

#: src/components/misc/MntrEditor/forms/FormEmbedSocialUrl/FormEmbedFacebookUrl.tsx:29
msgid "Facebook Post URL"
msgstr "URL príspevku na Facebooku"

#: src/components/emailing/content/EmailingSettingsContent.js:30
msgid "Failed to fetch your email address from the service provider. Please try again or contact support if the issue persists."
msgstr "Nepodarilo sa načítať vašu e-mailovú adresu od poskytovateľa služby. Skúste to znova alebo kontaktujte podporu, ak problém pretrváva."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:41
msgid "Feature has been requested."
msgstr "Funkcionalita bola vyžiadaná."

#: src/components/forms/dashboard/Export/ExportForm.js:66
msgid "File format"
msgstr "Formát súboru"

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:24
msgid "Files"
msgstr "Súbory"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:207
msgid "Fill from URL"
msgstr "Vyplniť z URL"

#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:105
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:122
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:136
#: src/components/layout/MntrFiltersBar/forms/FormFilterItems/FormFilterItems.js:38
#: src/components/layout/MntrFiltersBar/forms/FormFilterAuthorTitles/FormFilterAuthorTitles.js:13
#: src/components/layout/MntrFiltersBar/forms/FormChannelSearch/FormChannelSearch.js:31
#: src/components/emailing/content/CampaignAutocomplete.tsx:25
msgid "Filter"
msgstr "Filtrovať"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:104
msgid "Filter by absolute score"
msgstr "Filtrovať podľa absolutného skóre"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorActivity.js:27
msgid "Filter by activity"
msgstr "Filtrovať podľa aktivity"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterArticleType.js:26
msgid "Filter by article type"
msgstr "Filtrovať podľa typu článku"

#: src/components/layout/MntrFiltersBar/forms/FormAuthor/FormAuthor.tsx:13
msgid "Filter by author"
msgstr "Filtrovať podľa autora"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTypeMultiselect.js:32
msgid "Filter by author type"
msgstr "Filtrovať podľa typu autora"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/withModalMedialistArticlesFilter.tsx:21
msgid "Filter by author's articles"
msgstr "Filtrovať podľa príspevkov autora"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterContactInformationMultiselect.js:32
msgid "Filter by contact"
msgstr "Filtrovať podľa kontaktu"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:98
#: src/components/layout/MntrFiltersBar/modules/MenuFilterCountryMultiselect.js:29
msgid "Filter by country"
msgstr "Filtrovať podľa krajiny"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:239
msgid "Filter by date"
msgstr "Filtrovať podľa dátumu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorFocusAreasMultiselect.js:32
msgid "Filter by focus area"
msgstr "Filtrovať podľa zamerania"

#: src/components/layout/MntrFiltersBar/forms/FormFilterAuthorTitles/FormFilterAuthorTitles.js:32
msgid "Filter by job position"
msgstr "Filtrovať podľa pozície"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterLanguageTVR.js:24
#: src/components/layout/MntrFiltersBar/modules/MenuFilterLanguageMultiselect.js:26
msgid "Filter by language"
msgstr "Filtrovať podľa jazyka"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSourceTVR.js:24
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:115
msgid "Filter by media"
msgstr "Filtrovať podľa média"

#: src/components/emailing/forms/FormFilter.js:35
msgid "Filter by name"
msgstr "Filtrovať podľa mena"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:50
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:80
msgid "Filter by rank"
msgstr "Filtrovať podľa skóre"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:187
msgid "Filter by reach"
msgstr "Filtrovať podľa dosahu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:141
msgid "Filter by relevance"
msgstr "Filtrovať podľa relevancie"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSentimentMultiselect.js:27
msgid "Filter by sentiment"
msgstr "Filtrovať podľa sentimentu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:59
msgid "Filter sources"
msgstr "Filtrovať zdroje"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:120
#: src/components/layout/MntrFiltersBar/modals/withModalPageNumbers.js:15
msgid "Filter specific pages"
msgstr "Filtrovať konkrétne strany"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:354
msgid "Filter tags"
msgstr "Filtrovať štítky"

#: src/components/layout/Sidebar/modules/SidebarTopics/SidebarTopicsFolders.js:76
msgid "Filter topics"
msgstr "Filtrovať témy"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:56
msgid "Final version"
msgstr "Finálna verzia"

#. placeholder {0}: account.enums.analytics.export_charts_file_format.find( ({ id }) => id === fileFormatId, ).text
#: src/components/misc/Capture/Capture.js:304
msgid "finalizing {0} file for download"
msgstr "finalizácia {0} súboru na stiahnutie"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:67
#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:59
msgid "Find out more"
msgstr "Zistiť viac"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:228
msgid "Find similar articles"
msgstr "Vyhľadať podobné články"

#. js-lingui-explicit-id
#: src/components/misc/ActionsBar/Selector/Selector.js:45
msgid "selector.first"
msgstr "Prvých"

#: src/components/page/auth/SignUp/SignUp.js:20
msgid "First Name"
msgstr "Meno"

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:21
msgid "First Step"
msgstr "Prvý krok"

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:141
msgid "Focus area"
msgstr "Zameranie"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:301
#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:53
msgid "Font Size"
msgstr "Veľkosť písma"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:30
msgid "For each functionality choose one of three levels:"
msgstr "Pre každú funkcionalitu vyberte jednu z troch úrovní:"

#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:68
msgid "For example"
msgstr "Napríklad"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:81
msgid "For example, \"1,4-6\" will filter out 1,4,5,6"
msgstr "Napr.: „1,4-6“ vyfiltruje strany 1,4,5,6"

#: src/components/dashboards/PageExpiredSharedDashboard/PageExpiredSharedDashboard.js:40
msgid "For renewal, contact account admin."
msgstr "Pre obnovenie kontaktujte správcu účtu."

#. placeholder {0}: self.filters.topic_monitors[0].text
#: src/store/models/OurChart.js:853
msgid "for topic: {0}"
msgstr "pre tému: {0}"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:39
msgid "Forbidden:"
msgstr "Zakázané:"

#: src/components/layout/AuthWrapper/constants/features.slides.js:146
msgid "Foreign Media"
msgstr "Zahraničné médiá"

#: src/components/page/auth/Login/Login.tsx:86
msgid "Forgot password?"
msgstr "Zabudli ste heslo?"

#: src/components/exportList/History/HistoryTable/HistoryTable.js:51
msgid "Format"
msgstr "Formát"

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:18
msgid "Formatting is finished successfully. The prepared file is downloaded automatically. Edit it manually if needed and upload it to the medialist in the next step."
msgstr "Formátovanie bolo úspešne dokončené. Pripravený súbor sa automaticky stiahne. Ak je to potrebné, upravte ho manuálne a nahrajte do medialistu v ďalšom kroku."

#: src/components/reports/Content/ReportsList/ReportsForm.js:111
msgid "Frequency of report dispatch"
msgstr "Frekvencia odosielania reportu"

#: src/components/reports/Content/ReportsList/ReportsForm.js:279
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:56
msgid "From"
msgstr "Od"

#: src/components/forms/dashboard/ExportResend/ExportResend.js:90
msgid "From email"
msgstr "Email odosielateľa"

#: src/components/misc/ActionsBar/View/ViewMenu.js:101
msgid "Frontpage promo"
msgstr "Promovanie na frontpage"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:32
msgid "Full access:"
msgstr "Plný prístup:"

#: src/components/misc/ActionsBar/View/ViewMenu.js:148
msgid "Full page ad price"
msgstr "Celostránková inzercia"

#: src/components/misc/VideoPlayer/Controls.js:143
msgid "Fullscreen"
msgstr "Celá obrazovka"

#: src/components/tariff/Permissions/Permissions.js:40
msgid "Functionality"
msgstr "Funkcionalita"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:93
msgid "Generate structure"
msgstr "Generovať štruktúru"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:149
msgid "Generate text"
msgstr "Generovať text"

#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:40
msgid "Generating link"
msgstr "Odkaz sa pripravuje"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:115
msgid "Get a comprehensive view of the topics that matter to you."
msgstr "Získajte ucelený mediálny prehľad o všetkom, čo je pre vás dôležité."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:102
msgid "Get a more complete view of the topics that interest you"
msgstr "Získajte ešte komplexnejší prehľad o témach, ktoré vás zaujímajú"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:34
msgid "Get access to our media archive."
msgstr "Získajte prístup do našeho mediálneho archívu."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:71
msgid "Go back to Emailing"
msgstr "Späť na Emailing"

#: src/components/staff/admin/workspace/Workspace.js:403
msgid "Google Translate price: 1 article = 2.5 Kč = 0.09 €"
msgstr "Google Translate cena: 1 článok = 2.5 Kč = 0.09 €"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:268
msgid "Gross Rating Point"
msgstr "Gross Rating Point"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:107
msgid "Group articles"
msgstr "Zoskupiť články"

#: src/constants/stats.ts:16
#: src/constants/analytics.js:1010
#: src/components/widgets/modules/stats/WidgetStats.js:150
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:265
#: src/components/misc/ActionsBar/View/ViewMenu.js:211
msgid "GRP"
msgstr "GRP"

#: src/components/monitoring/Inspector/InspectorMonitora/HashTagsList/HashTagsList.js:24
msgid "Hashtags"
msgstr "Hashtagy"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:33
msgid "Hasn't started yet"
msgstr "Ešte netestoval"

#: src/pages/sign-up-completion.tsx:42
#: src/components/page/auth/SignUp/SignUp.js:81
msgid "Have an account? Sign in"
msgstr "Máte účet? Prihláste sa"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:11
msgid "Head of External and Internal Communication"
msgstr "Vedúca externej a internej komunikácie"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:225
msgid "Header"
msgstr "Header"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:166
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:172
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:178
msgid "Heading"
msgstr "Nadpis"

#: src/pages/user/reactivate-24.js:37
msgid "Hello!<0/><1/>Thank you for your interest in trying Mediaboard with all the features. Please confirm by clicking on the button below. We will contact you as soon as possible.<2/><3/>Kind regards,<4/><5/>{appName} team"
msgstr "Dobrý deň!<0/><1/>Ďakujeme za váš záujem vyskúšať Mediaboard so všetkými novinkami. Potvrďte ho prosím kliknutím na tlačidlo nižšie. Budeme vás čoskoro kontaktovať.<2/><3/>S pozdravom,<4/><5/>{appName} tím"

#: src/pages/user/yoy-analysis.js:37
msgid "Hello!<0/><1/>Thank you for your interest! Please confirm by clicking on the button below. We will contact you as soon as possible.<2/><3/>Kind regards,<4/><5/>{appName} team"
msgstr "Dobrý deň!<0/><1/>Ďakujeme za váš záujem! Potvrďte ho prosím kliknutím na tlačidlo nižšie. Budeme vás čoskoro kontaktovať.<2/><3/>S pozdravom,<4/><5/>{appName} tím"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:30
msgid "Hello!<0/><1/>Thanks for your interest in our Emailing platform. To activate it you need to verify your email first. To do so just check, that the email you have entered is correct and click the activation button.<2/><3/><4/><5/>Best regards,<6/><7/>{appName} team"
msgstr "Dobrý deň!<0/><1/>Ďakujeme za váš záujem o našu Emailingovú platformu. Na jej aktiváciu musíte najprv overiť váš email. Na to stačí skontrolovať, či je zadaný email správny, a následne kliknúť na aktivačné tlačidlo.<2/><3/><4/><5/>S pozdravom,<6/><7/>{appName} tím"

#: src/helpers/modal/withModalHelp.tsx:17
#: src/components/layout/Sidebar/SidebarNavigation.tsx:215
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:20
#: src/components/OurChart/OurChartAdvanced.js:268
msgid "Help"
msgstr "Nápoveda"

#: src/components/medialist/content/MedialistAuthorCreate.js:20
msgid "Here you will see all the activity related to this author."
msgstr "Tu uvidíte všetky aktivity súvisiace s týmto autorom."

#: src/components/emailing/components/FunnelStats/StatBlock.tsx:121
msgid "Here you will see newsroom analytics affected by the campaign"
msgstr "Tu uvidíte analytické údaje newsroomu ovplyvnené kampaňou"

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:159
msgid "hide"
msgstr "skryť"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:246
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:210
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:249
msgid "Hide"
msgstr "Skryť"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:557
msgid "Hide header and footer"
msgstr "Skryť hlavičku a pätičku"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:63
msgid "hide stats"
msgstr "skryť štatistiku"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:230
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:241
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:144
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:153
msgid "Hide tag"
msgstr "Skryť štítok"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:229
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:190
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:202
msgid "Hide topic"
msgstr "Skryť tému"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:210
msgid "Hide topics in folder"
msgstr "Skryť témy v priečinku"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:65
msgid "Highlight Only"
msgstr "Len zvýrazniť"

#: src/components/tariff/Permissions/Permissions.js:51
msgid "Hints"
msgstr "Nápoveda"

#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:28
#: src/components/exportList/Content/Content.tsx:72
msgid "History"
msgstr "História"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:84
msgid "Homepage url"
msgstr "URL adresa domovskej stránky"

#: src/components/emailing/forms/FormSenderSettings.js:82
msgid "Host"
msgstr "Host"

#: src/components/emailing/forms/FormSenderSettings.js:123
msgid "Hostname"
msgstr "Hostname"

#: src/components/staff/admin/workspace/Workspace.js:504
msgid "How many years back is the user allowed to search in media archive."
msgstr "Koľko rokov do histórie môže užívateľ hľadať v archíve."

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:88
msgid "How permissions work"
msgstr "Ako fungujú oprávnenia"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:135
msgid "How to help the AI generate a more satisfying and detailed email"
msgstr "Ako pomôcť umelej inteligencii generovať uspokojivejší a podrobnejší e-mail"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:218
msgid "How to use {appName}"
msgstr "Ako používať aplikáciu {appName}"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:111
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:91
msgid "HTML"
msgstr "HTML"

#: src/pages/user/yoy-analysis.js:59
#: src/pages/user/reactivate-24.js:59
msgid "I am interested"
msgstr "Mám záujem"

#: src/components/staff/admin/user/WorkspacesTable.js:74
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:74
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:80
msgid "ID"
msgstr "ID"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:43
msgid "Ideal for those with their own content ready."
msgstr "Ideálne pre tých, ktorí majú vlastný obsah pripravený."

#: src/components/staff/admin/workspace/Workspace.js:382
msgid "If set to 0, then: no export basket, no exporting or email sending from feed or export basket."
msgstr "Ak je 0, tak: nie je exportný košík, nejde exportovať ani posielať email z feedu."

#: src/helpers/modal/withModalReportArticle.tsx:23
msgid "If the article has a bad transcript or screenshot, please report the problem and our staff will look into it and fix the issue."
msgstr "Ak má článok zlý prepis alebo screenshot, nahláste prosím problém a naši pracovníci sa ho pokúsia vyriešiť v čo najkratšom čase."

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:92
msgid "If this was a mistake or if you'd like to re-subscribe at any time, please contact us at"
msgstr "Ak došlo k omylu alebo ak sa chcete kedykoľvek znovu prihlásiť k odberu, kontaktujte nás na adrese"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:64
msgid "If this was a mistake or you'd prefer to stay available for our emails, no further action is needed."
msgstr "Ak išlo o omyl alebo chcete zostať k dispozícii pre naše e-maily, nie sú potrebné žiadne ďalšie kroky."

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:39
msgid "If you don't remember your current password, you can <0>reset it</0> or contact us at <1>{salesEmail}</1>."
msgstr "Ak si Vaše súčasné heslo nepamätáte, prejdite na stránku <0>obnovenia hesla</0> alebo nás kontaktujte na adrese <1>{salesEmail}</1>."

#: src/components/page/auth/Expired/Expired.js:63
msgid "If you liked our service and would like to purchase the account, send us an email to <0>{salesEmail}</0>"
msgstr "Ak vás naša služba zaujala a máte záujem o zakúpenie plnej verzie, neváhajte nás kontaktovať na adrese <0>{salesEmail}</0>"

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:20
msgid "If you would like to purchase a workspace account, send us an email to <0>{salesEmail}</0>"
msgstr "Ak máte záujem o zakúpenie workspacu, neváhajte nás kontaktovať na adrese <0>{salesEmail}</0>"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:126
msgid "If you'd like to re-subscribe, please contact us at"
msgstr "Ak sa chcete opätovne prihlásiť k odberu, kontaktujte nás na adrese"

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:56
msgid "If you'd like to unsubscribe from emails, sent via mediaboard.com, simply click the button below. Your email <0>{0}</0> will no longer receive new emails form us."
msgstr "Ak sa chcete odhlásiť z odberu e-mailov zasielaných prostredníctvom stránky mediaboard.com, jednoducho kliknite na tlačidlo nižšie. Na váš e-mail <0>{0}</0> už nebudú prichádzať nové e-maily."

#: src/components/reports/history/Content.js:38
msgid "If your report wasn't delivered, make sure to check your spam folder and your promotions inbox."
msgstr "Nedorazil vám emailový report? Skúste sa pozrieť do zložky SPAM alebo Promotions (ak používate Gmail)."

#: src/store/models/dashboards/DashboardPreview.js:147
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:49
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:47
#: src/components/misc/MntrEditor/extensions/ExtensionImageGallery.js:49
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:70
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:70
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewImage/PreviewImage.js:29
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:38
msgid "Image"
msgstr "Obrázok"

#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:61
msgid "Images"
msgstr "Obrázky"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:70
msgid "Import"
msgstr "Importovať"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:181
#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:84
#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:13
#: src/components/medialist/content/MedialistActionsBar/ContactsImportTitle.tsx:7
msgid "Import contacts"
msgstr "Importovať kontakty"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:54
msgid "Import options"
msgstr "Možnosti importu"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:42
msgid "Import to"
msgstr "Možnosti importu"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:75
msgid "Import your already formatted contact list or manually completed template."
msgstr "Importujte svoj už naformátovaný zoznam kontaktov alebo ručne vyplnenú šablónu."

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:106
msgid "Import your contacts to medialist"
msgstr "Importujte svoje kontakty do medialistu"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:92
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:37
msgid "Imprint"
msgstr "Tiráž"

#. placeholder {0}: formatDate(lowerDate, 'd. M. yyyy')
#. placeholder {1}: formatDate(upperDate, 'd. M. yyyy')
#: src/helpers/getTitleWithDateFromTo.js:5
msgid "in period from {0} to {1}"
msgstr "za obdobie od {0} do {1}"

#: src/components/newsroom/content/posts/NewsroomPosts.js:297
msgid "In the settings you can edit basic information about the Newsroom, appearance, web address, etc."
msgstr "V nastavení môžete upraviť základné informácie o Newsroome, vzhľad, web adresu atď."

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:13
msgid "In three years with Mediaboard, our experience has been exceptional. Their professionalism, wide range of services, and top-notch quarterly and annual analyses are highly valuable. We recommend Mediaboard for quality and reliability."
msgstr "Za tri roky spolupráce s Mediaboardom máme výnimočné skúsenosti. Ich profesionalita, široká ponuka služieb a špičkové štvrťročné a ročné analýzy sú pre nás nepostrádateľné. Mediaboard by som odporúčila hlavne kvôli vysokej kvalite výstupov a spoľahlivosti."

#: src/components/settings/SettingsApplication/SettingsApplication.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:106
msgid "In-app currency"
msgstr "Mena použitá v aplikácii"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:173
#: src/components/staff/admin/user/User.js:138
#: src/components/staff/admin/customer/users/UsersTable.js:116
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoStaticItem.js:14
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoItem.js:31
#: src/components/forms/dashboard/Search/SearchUsers.js:99
msgid "Inactive"
msgstr "Neaktívny"

#: src/components/reports/Content/ReportsList/FormToggleActive/FormToggleActive.js:36
msgid "Inactive report"
msgstr "Neaktívny report"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:22
msgid "Include all the key points you want to specifically mention in your article. These should be the essential details, arguments, or highlights that support and enhance the main content.\""
msgstr "Zahrňte všetky kľúčové body, ktoré chcete konkrétne spomenúť vo svojom článku. Toto by mali byť podstatné detaily, argumenty alebo zvýraznenia, ktoré podporujú a vylepšujú hlavný obsah.“"

#: src/components/tariff/TariffLimits/TariffLimits.js:51
#: src/components/tariff/TariffLimits/TariffLimits.js:88
#: src/components/tariff/TariffLimits/TariffLimits.js:123
#: src/components/tariff/TariffLimits/TariffLimits.js:143
#: src/components/tariff/TariffLimits/TariffLimits.js:159
#: src/components/tariff/TariffLimits/TariffLimits.js:176
#: src/components/tariff/TariffLimits/TariffLimits.js:195
#: src/components/tariff/TariffLimits/TariffLimits.js:212
#: src/components/tariff/TariffLimits/TariffLimits.js:233
#: src/components/tariff/TariffLimits/TariffLimits.js:250
#: src/components/tariff/TariffLimits/TariffLimits.js:283
#: src/components/tariff/TariffLimits/SingleValueLimit/SingleValueLimit.js:20
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:114
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:85
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:102
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:110
msgid "Increase limit"
msgstr "Navýšiť limit"

#: src/components/tariff/TariffLimits/TariffLimits.js:50
#: src/components/tariff/TariffLimits/TariffLimits.js:87
#: src/components/tariff/TariffLimits/TariffLimits.js:122
#: src/components/tariff/TariffLimits/TariffLimits.js:142
#: src/components/tariff/TariffLimits/TariffLimits.js:158
#: src/components/tariff/TariffLimits/TariffLimits.js:175
#: src/components/tariff/TariffLimits/TariffLimits.js:194
#: src/components/tariff/TariffLimits/TariffLimits.js:211
#: src/components/tariff/TariffLimits/TariffLimits.js:232
#: src/components/tariff/TariffLimits/TariffLimits.js:249
#: src/components/tariff/TariffLimits/TariffLimits.js:282
#: src/components/tariff/TariffLimits/SingleValueLimit/SingleValueLimit.js:18
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:84
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:101
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:109
msgid "Increase limit?"
msgstr "Navýšiť limit?"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:52
msgid "Indicate the desired tone (formal, casual) and style (informative, promotional)."
msgstr "Uveďte požadovaný tón (formálny, neformálny) a štýl (informačný, propagačný)."

#: src/components/monitoring/FeedList/FeedListItem/MetaData/modules/MetaDataScore/MetaDataScore.js:38
msgid "influence score"
msgstr "skóre vplyvu"

#: src/constants/stats.ts:21
#: src/constants/analytics.js:262
#: src/constants/analytics.js:371
#: src/constants/analytics.js:451
#: src/constants/analytics.js:483
#: src/constants/analytics.js:644
#: src/constants/analytics.js:659
#: src/constants/analytics.js:929
#: src/constants/analytics.js:944
#: src/components/widgets/modules/stats/WidgetStats.js:162
#: src/components/misc/ActionsBar/View/ViewMenu.js:297
msgid "Influence score"
msgstr "Skóre vplyvu"

#: src/constants/analytics.js:283
#: src/constants/analytics.js:530
msgid "Influence score by mention type"
msgstr "Skóre vplyvu (Influence Score) podľa typu zmienky"

#: src/constants/analytics.js:281
#: src/constants/analytics.js:390
#: src/constants/analytics.js:481
msgid "Influence score by sentiment"
msgstr "Skóre vplyvu (Influence Score) podľa sentimentu"

#: src/constants/analytics.js:392
msgid "Influence score by social network"
msgstr "Skóre vplyvu (Influence Score) podľa soc. siete"

#: src/components/emailing/content/sender/EmailingSenderContent.js:17
msgid "Initial Emailing settings"
msgstr "Úvodné nastavenie Emailingu"

#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:124
msgid "Insert"
msgstr "Vložiť"

#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:155
msgid "Insert button label to view preview"
msgstr "Zadajte text tlačidla pre zobrazenie náhľadu"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:125
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:169
msgid "Insert HTML code to view preview"
msgstr "Vložte HTML kód pre zobrazenie náhľadu"

#: src/components/emailing/content/CreateEmailContent.js:408
msgid "Insert internal name of email"
msgstr "Zadajte interný názov emailu"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:359
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:363
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:385
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:409
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:413
msgid "Insert link"
msgstr "Vložiť odkaz"

#: src/components/emailing/content/CreateEmailContent.js:449
msgid "Insert subject"
msgstr "Zadajte predmet"

#: src/components/medialist/forms/FormEditAuthor.js:736
#: src/components/medialist/forms/FormEditAuthor.js:1031
msgid "Insert text..."
msgstr "Vložte text..."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:121
msgid "Instructions"
msgstr "Inštrukcie"

#: src/constants/analytics.js:416
msgid "Interactions by sentiment"
msgstr "Interakcie podľa sentimentu"

#: src/constants/analytics.js:418
msgid "Interactions by social network"
msgstr "Interakcie podľa soc. siete"

#: src/constants/analytics.js:227
#: src/constants/analytics.js:639
#: src/constants/analytics.js:774
#: src/components/layout/AuthWrapper/constants/features.slides.js:199
msgid "Interactions on social networks"
msgstr "Interakcie na soc. sieťach"

#: src/constants/analytics.js:225
msgid "Interactions on social networks by sentiment"
msgstr "Interakcie na soc. sieťach podľa sentimentu"

#: src/components/emailing/content/CreateEmailContent.js:405
msgid "Internal name of email"
msgstr "Interný názov emailu"

#: src/components/emailing/forms/FormEmailRecipients.js:37
msgid "Invalid"
msgstr "Neplatné"

#: src/components/article/Content.js:13
msgid "Invalid article link"
msgstr "Neplatný odkaz na článok"

#. placeholder {0}: format( startOfYear(today), DATE_FORMAT, )
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:83
msgid "Invalid date format. Expected format is {0}"
msgstr "Nesprávny formát dátumu. Očakávaný formát je {0}"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:16
#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:10
#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:33
msgid "Invalid email format"
msgstr "Neplatný formát emailu"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:118
msgid "Invalid page number"
msgstr "Neplatné číslo strany"

#: src/components/forms/inspector/FormMediaEditor.js:76
#: src/components/forms/inspector/FormMediaEditor.js:79
msgid "Invalid time format. Enter hh:mm:ss"
msgstr "Nesprávny formát času. Zadajte hh:mm:ss"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:57
msgid "Invoice no."
msgstr "Číslo faktúry"

#: src/pages/staff/admin/customers/[customerId]/invoices.js:12
#: src/components/staff/admin/customer/invoices/Invoices.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:42
msgid "Invoices"
msgstr "Faktúry"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:56
msgid "Irrelevant"
msgstr "Irelevantné"

#: src/components/misc/Changelog/ChangelogTableRow.js:156
msgid "Irreversible"
msgstr "Nevratné"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:104
msgid "Is overdue"
msgstr "Po splatnosti"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:296
msgid "Is there a problem with the article?"
msgstr "Je s článkom nejaký problém?"

#. placeholder {0}: data.publication.issue
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:225
msgid "Issue: {0}"
msgstr "Číslo: {0}"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:72
msgid "Issued via"
msgstr "Vystavené cez"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:368
msgid "It appears above the description on the search results page."
msgstr "Zobrazuje sa nad popisom na stránke s výsledkami vyhľadávania."

#: src/pages/_error.js:49
msgid "It looks like you're trying to access a malformed URL. Please review it and try again."
msgstr "Pokúšate sa pristúpiť na neplatnú URL. Prosím skontrolujte ju a vyskúšajte znovu."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:131
msgid "Italic"
msgstr "Kurzíva"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:144
msgid "Item '<0>{title}</0>' will be removed."
msgstr "Položka '<0>{title}</0>' bude odstránená."

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:179
msgid "Job position"
msgstr "Pracovná pozíci"

#: src/components/medialist/forms/modules/FormArray.js:107
msgid "Job Position"
msgstr "Pracovná pozícia"

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:21
msgid "formatNumber.k"
msgstr "tis."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:68
msgid "Keep original"
msgstr "Ponechať originál"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:20
msgid "Key points list"
msgstr "Zoznam kľúčových bodov"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:56
msgid "Key Points:"
msgstr "Kľúčové body:"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:40
msgid "Keyword"
msgstr "Kľúčové slovo"

#: src/helpers/modal/withModalTvrTopics.tsx:53
#: src/constants/analytics.js:1054
#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/KeywordsListMedia/KeywordsListMedia.js:22
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:53
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:119
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:255
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:337
msgid "Keywords"
msgstr "Kľúčové slová"

#: src/components/misc/ResendSettings/SaveResendSettings/FormSaveResendSettings.js:21
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:122
#: src/components/misc/ExportSettings/SaveExportSettings/FormSaveExportSettings.js:21
#: src/components/emailing/forms/FormAddCampaign.tsx:15
#: src/components/dashboards/DashboardSelector/FormEditDashboard.js:26
msgid "Label"
msgstr "Názov"

#: src/constants/analytics.js:870
#: src/components/staff/admin/user/User.js:278
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:274
#: src/components/misc/ActionsBar/View/ViewMenu.js:333
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:310
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:329
#: src/components/layout/Header/UserMenu/UserMenu.tsx:119
msgid "Language"
msgstr "Jazyk"

#: src/constants/analytics.js:883
msgid "Languages"
msgstr "Jazyky"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:106
msgid "Languages & connected newsrooms"
msgstr "Jazyky a prepojené newsroomy"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:236
msgid "Last access"
msgstr "Posledný prístup"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:122
#: src/components/staff/admin/customer/users/UsersTable.js:74
msgid "Last login"
msgstr "Posledné prihlásenie"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:147
msgid "Last month"
msgstr "Minulý mesiac"

#: src/components/page/auth/SignUp/SignUp.js:29
msgid "Last Name"
msgstr "Priezvisko"

#: src/components/newsroom/components/PostsList/PostsList.js:166
#: src/components/newsroom/components/PostsList/PostsList.js:181
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:152
msgid "Last update"
msgstr "Posledná zmena"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:128
msgid "Last week"
msgstr "Minulý týždeň"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:160
msgid "Last year"
msgstr "Minulý rok"

#: src/components/emailing/forms/FormSenderSettings.js:106
msgid "Leave blank to use the default port"
msgstr "Ponechajte prázdne, ak chcete použiť predvolený port"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:141
msgid "Light"
msgstr "Svetlý"

#: src/components/settings/SettingsTheme/SettingsThemePreview/SettingsThemePreview.js:193
msgid "Light mode preview"
msgstr "Náhľad svetlého režimu"

#: src/components/medialist/forms/FormEditAuthor.js:504
msgid "limit"
msgstr "limit"

#: src/store/models/monitoring/MedialistMapItem/MedialistMapItem.js:260
#: src/store/models/monitoring/MedialistMapItem/MedialistMapItem.js:285
#: src/store/models/authors/AuthorsStore.js:410
#: src/store/models/authors/AuthorsStore.js:435
msgid "Limit exceeded. Sucessfully exported {generated} of {requested} requested authors."
msgstr "Bol prekročený limit pre export. Úspešne vyexportovaných {generated} z {requested} autorov."

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:652
msgid "Limit reached. You have selected too many articles."
msgstr "Limit dosiahnutý. Vybrali ste príliš veľa článkov."

#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:35
msgid "Limits"
msgstr "Limity"

#: src/components/OurChart/OurChartAdvanced.js:162
msgid "Line"
msgstr "Čiarový"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:76
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:325
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:131
msgid "Link"
msgstr "Odkaz"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:40
#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:69
#: src/components/staff/admin/user/User.js:67
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:224
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:15
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:16
#: src/components/exportList/History/HistoryTable/HistoryTable.js:111
msgid "Link has been copied to the clipboard."
msgstr "Odkaz bol skopírovaný do schránky."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:116
msgid "Link to other language"
msgstr "Odkaz na iný jazyk"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:95
msgid "Link to the article"
msgstr "Odkaz na článok"

#: src/components/emailing/content/EmailingSettingsContent.js:31
msgid "Linking with Google account timed out. Please, try again."
msgstr "Časový limit prepojenia s účtom Google vypršal. Skúste to znova."

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:240
#: src/components/monitoring/Inspector/InspectorMonitora/Links/Links.js:14
msgid "Links"
msgstr "Odkazy"

#: src/components/medialist/content/AuthorBasketsMenu.js:135
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:124
msgid "List {label} will be removed."
msgstr "Zoznam {label} bude odstránený."

#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:25
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:31
#: src/components/forms/baskets/FormNewBasket.js:26
msgid "List name"
msgstr "Názov zoznamu"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:26
msgid "List of tags"
msgstr "Zoznam štítkov"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:99
msgid "List of topics"
msgstr "Zoznam tém"

#: src/components/medialist/forms/FormEditAuthor.js:660
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:97
msgid "Lists"
msgstr "Zoznamy"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:46
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:20
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:20
msgid "Load"
msgstr "Nahrať"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:54
msgid "Load from"
msgstr "Nahrať z"

#: src/components/monitoring/FeedList/LoadMore/LoadMore.js:27
msgid "Load more"
msgstr "Nahrať ďalšie"

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:140
msgid "Load more..."
msgstr "Načítať viac…"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:46
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:48
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:48
msgid "Load settings"
msgstr "Nahrať nastavenie"

#: src/components/tvr/Content/Content.js:82
#: src/components/trash/Content.js:39
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:70
#: src/components/reports/history/HistoryTable.js:491
#: src/components/notifications/Permissions.js:84
#: src/components/notifications/AppNotifications/AppNotifications.js:21
#: src/components/newsroom/content/dashboard/ChartVisits.js:127
#: src/components/monitoring/WorkspaceArticles/Intro.js:20
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:57
#: src/components/monitoring/FeedList/LoadMore/LoadMore.js:27
#: src/components/monitoring/FeedChart/FeedChart.js:47
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:47
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:55
#: src/components/misc/MntrEditor/forms/FormMediaUpload/UploadProgress.js:31
#: src/components/medialist/content/MedialistHeading.js:14
#: src/components/medialist/content/MedialistInspector/AuthorChart/AuthorChart.js:84
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:31
#: src/components/exportList/Content/HeadingExport/HeadingExport.js:25
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:40
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:48
#: src/components/dashboards/DashboardSelector/DashboardSelector.js:21
#: src/components/analytics/AnalyticsContent.js:38
#: src/components/OurChart/OurChartAdvanced.js:316
msgid "Loading..."
msgstr "Nahrávam..."

#: src/components/layout/Header/UserMenu/UserMenu.tsx:228
msgid "Log back in"
msgstr "Prihlásiť sa späť"

#: src/components/page/auth/Login/Login.tsx:55
#: src/components/page/auth/Login/Login.tsx:69
#: src/components/page/auth/Expired/Expired.js:104
msgid "Log In"
msgstr "Prihlásiť"

#: src/components/staff/admin/user/User.js:192
#: src/components/staff/admin/customer/users/UsersTable.js:129
msgid "Login as this user"
msgstr "Prihlásiť sa ako tento užívateľ"

#: src/components/staff/admin/workspace/Workspace.js:249
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:137
msgid "Login into this workspace"
msgstr "Prihlásiť sa do workspacu"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:187
msgid "Login link"
msgstr "Odkaz na prihlásenie"

#: src/components/page/auth/Login/Login.tsx:63
msgid "Login to {appName}"
msgstr "Prihlásenie do aplikácie {appName}"

#: src/components/page/auth/Login/Login.tsx:64
msgid "Login to {appName}, the next generation media monitoring tool."
msgstr "Prihlásenie do aplikácie {appName}, monitoringu médií a sociálnych sietí. Sledujte, merajte a analyzujte vašu komunikáciu."

#: src/components/page/auth/Expired/Expired.js:75
msgid "Login to different workspace"
msgstr "Prihlásiť sa do iného workspacu"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:187
msgid "Logout"
msgstr "Odhlásiť sa"

#: src/helpers/auth.js:47
msgid "Logout performed in another window."
msgstr "Ohlásenie prebehlo v inom okne."

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:22
msgid "formatNumber.M"
msgstr "mil."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:155
msgid "Magazine cover pages"
msgstr "Titulné strany z tlače"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:59
msgid "Main message"
msgstr "Hlavná správa"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:48
msgid "Main message & key points"
msgstr "Hlavná správa a kľúčové body"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:46
msgid "Main Objective:"
msgstr "Hlavný cieľ:"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:61
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:65
msgid "Mainstream sources"
msgstr "Mainstreamové zdroje"

#: src/pages/_error.js:44
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:422
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:156
#: src/components/misc/MntrEditor/forms/FormEmbedSocialUrl/FormEmbedFacebookUrl.tsx:44
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:88
msgid "Malformed URL"
msgstr "Neplatná URL"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:104
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:107
msgid "Manage hidden tags"
msgstr "Spravovať skryté štítky"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:95
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:98
msgid "Manage hidden topics"
msgstr "Spravovať skryté témy"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:231
msgid "Management summaries"
msgstr "Manažerské zhrnutia"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:49
msgid "Manual writing"
msgstr "Ručné písanie"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:36
msgid "Manually add the information for your signature, which will appear in every email or customize it using your own HTML."
msgstr "Ručne pridajte informácie pre svoj podpis, ktorý sa bude zobrazovať v každom e-maile, alebo si ho prispôsobte pomocou vlastného HTML."

#: src/components/emailing/content/promo/PromoEmailing.js:27
msgid "Mass mailing"
msgstr "Hromadné rozosielanie"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:488
msgid "Max. file size:"
msgstr "Max. veľkosť súboru:"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:225
msgid "Media analysis"
msgstr "Mediálne analýzy"

#: src/components/tariff/TariffLimits/TariffLimits.js:261
#: src/components/staff/admin/workspace/Workspace.js:500
msgid "Media archive depth limit"
msgstr "Hĺbka mediálneho archívu"

#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:120
msgid "Media Coverage"
msgstr "Mediálne pokrytie"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:330
#: src/components/misc/ActionsBar/View/ViewMenu.js:49
#: src/components/misc/ActionsBar/View/ViewMenu.js:344
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:37
msgid "Media data"
msgstr "Mediálne dáta"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:92
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:168
msgid "Media data (GRP, OTS, AVE, PRIMe)"
msgstr "Mediálne dáta (GRP, OTS, AVE, PRIMe)"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:16
msgid "Media Monitoring"
msgstr "Monitoring médií"

#: src/constants/analytics.js:79
#: src/constants/analytics.js:585
#: src/constants/analytics.js:717
#: src/components/layout/AuthWrapper/constants/features.slides.js:183
msgid "Media reach (GRP)"
msgstr "Mediálny dopad (GRP)"

#: src/constants/analytics.js:77
msgid "Media reach (GRP) by sentiment"
msgstr "Mediálny dopad (GRP) podľa sentimentu"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:222
msgid "Media services"
msgstr "Mediálne služby"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:43
msgid "Mediaboard transformed our communication at Coca-Cola HBC! A daily essential for top-notch media monitoring, with a user-friendly interface and insightful analytics. Their exceptional customer support makes it a joy to work with Mediaboard."
msgstr "Mediaboard premenil spôsob našej komunikácie v Coca-Cola HBC! Každodenný základ pre špičkové monitorovanie médií s používateľsky prívetivým rozhraním a prehľadnou analytikou. Vďaka ich výnimočnej zákazníckej podpore je radosť s Mediaboardom pracovať."

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:29
msgid "Mediakit"
msgstr "Mediakit"

#: src/store/models/dashboards/DashboardPreview.js:99
#: src/pages/authors/index.js:32
#: src/pages/authors/index.js:41
#: src/pages/authors/create.js:10
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:110
#: src/components/layout/Sidebar/SidebarNavigation.tsx:137
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:36
#: src/components/layout/AuthWrapper/constants/features.slides.js:214
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:44
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:44
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:29
#: src/app/components/monitoring-navigation.tsx:269
msgid "Medialist"
msgstr "Medialist"

#: src/constants/analytics.js:793
#: src/components/topics/Content/TopicsList/MegalistModal.js:52
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:70
#: src/components/topics/Content/TopicsList/MegalistToolbar/MediatypeFilterPopup.js:8
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:247
msgid "Mediatype"
msgstr "Mediatyp"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:91
msgid "mediatype for all countries"
msgstr "mediatyp pre všetky krajiny"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:67
msgid "Mention any personalization details (name, company)."
msgstr "Uveďte všetky personalizačné údaje (meno, spoločnosť)."

#: src/constants/analytics.js:233
#: src/constants/analytics.js:342
#: src/constants/analytics.js:889
#: src/constants/analytics.js:909
#: src/constants/analytics.js:1312
#: src/components/widgets/modules/stats/WidgetStats.js:241
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:51
msgid "Mentions"
msgstr "Zmienky"

#: src/constants/analytics.js:254
#: src/constants/analytics.js:363
msgid "Mentions by sentiment"
msgstr "Zmienky podľa sentimentu"

#: src/constants/analytics.js:365
#: src/constants/analytics.js:923
#: src/constants/analytics.js:1325
msgid "Mentions by social network"
msgstr "Zmienky podľa soc. siete"

#: src/constants/analytics.js:256
#: src/constants/analytics.js:903
msgid "Mentions by type"
msgstr "Zmienky podľa typu"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:586
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:96
msgid "Merge Tags"
msgstr "Merga tagy"

#: src/pages/staff/admin/customers/[customerId]/merged-customers.js:12
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomers.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:60
msgid "Merged customers"
msgstr "Zlúčení zákazníci"

#: src/components/staff/admin/customer/bio/CustomerBio.js:79
msgid "Merged to"
msgstr "Zlúčené do"

#: src/components/misc/ActionsBar/View/ViewMenu.js:199
msgid "Metrics"
msgstr "Metriky"

#: src/components/misc/portable/PortableResend/PortableResend.js:93
#: src/components/misc/portable/PortableExport/PortableExport.js:88
msgid "Minimize"
msgstr "Minimalizovať"

#: src/components/tariff/MonitoredMedia/MissedArticles/MissedArticles.js:9
msgid "Missed articles"
msgstr "Zmeškané články"

#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:54
#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:74
msgid "Missing article"
msgstr "Nedohľadaný článok"

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:39
msgid "Missing data"
msgstr "Chýbajúce údaje"

#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:46
msgid "Missing recipient info"
msgstr "Chýbajúce informácie o príjemcovi"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:298
#: src/components/layout/AuthWrapper/constants/features.slides.js:399
msgid "Mobile Apps"
msgstr "Mobilné aplikácie"

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:41
msgid "Modified"
msgstr "Zmenené"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoItem.js:87
msgid "Monitor a wide range of social media platforms including Facebook, LinkedIn, Instagram, TikTok, X.com, and YouTube."
msgstr "Monitorujte širokú škálu sociálnych médií vrátane Facebooku, LinkedIn, Instagramu, TikToku, X.com a YouTube."

#: src/components/layout/AuthWrapper/constants/features.slides.js:23
msgid "Monitor newspapers, magazines, radios, TV stations or the entire online world. Reach out to media, react, track, analyze, and build your brand."
msgstr "Majte prehľad o dianí v novinách, časopisoch, rádiách, televíznych staniciach i v celom online svete. Oslovte médiá, reagujte, sledujte, analyzujte a budujte svoju značku."

#: src/components/topics/Content/TopicsList/MediaCard.js:21
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:127
#: src/components/staff/admin/workspace/Workspace.js:877
#: src/components/settings/SettingsTariff/SettingsTariff.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:41
msgid "Monitored media"
msgstr "Monitorované médiá"

#: src/components/notifications/Content.js:34
#: src/components/monitoring/Monitoring.js:109
#: src/components/monitoring/Monitoring.js:110
#: src/components/monitoring/Monitoring.js:165
#: src/components/layout/Sidebar/SidebarNavigation.tsx:119
#: src/components/layout/AuthWrapper/constants/features.slides.js:22
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:10
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:10
#: src/app/components/monitoring-navigation.tsx:65
msgid "Monitoring"
msgstr "Monitoring"

#: src/components/staff/admin/customer/expenses/ExpenseTable.js:77
msgid "Month"
msgstr "Mesiac"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:107
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:65
msgid "monthly sessions"
msgstr "návštev za mesiac"

#: src/components/misc/ActionsBar/View/ViewMenu.js:82
msgid "Monthly sessions"
msgstr "Mesačná návštevnosť"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:87
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:55
msgid "monthly users"
msgstr "užívateľov za mesiac"

#: src/components/misc/ActionsBar/View/ViewMenu.js:74
msgid "Monthly users"
msgstr "Mesačne užívateľov"

#: src/helpers/charts/makeGranularityMenu.js:26
#: src/helpers/charts/getGranularityLabel.js:9
msgid "Months"
msgstr "Mesiace"

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:159
msgid "more"
msgstr "viac"

#: src/components/OurChart/OurChartAdvanced.js:247
msgid "More"
msgstr "Viac"

#: src/constants/analytics.js:1338
msgid "Most common terms"
msgstr "Najčastejšie pojmy"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:96
msgid "Move article"
msgstr "Presunúť článok"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:119
msgid "Move to Dashboard"
msgstr "Presunúť do dashboardu"

#: src/pages/workspace-articles.js:51
#: src/components/monitoring/WorkspaceArticles/Intro.js:23
#: src/app/components/monitoring-navigation.tsx:204
msgid "My Articles"
msgstr "Moje články"

#: src/components/medialist/content/AuthorInfoDetail.js:72
msgid "My author"
msgstr "Vlastný autor"

#: src/components/medialist/content/OwnAuthorsListSelectorButton.js:9
#: src/components/medialist/content/AuthorBasketsMenu.js:41
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:158
msgid "My authors"
msgstr "Vlastní autori"

#: src/components/topics/Content/TopicsList/FormSaveMegalist/FormSaveMegalist.js:8
#: src/components/staff/admin/workspace/Workspace.js:288
#: src/components/staff/admin/user/WorkspacesTable.js:71
#: src/components/staff/admin/user/User.js:256
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:71
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:52
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:62
#: src/components/medialist/forms/FormEditAuthor.js:211
#: src/components/medialist/forms/FormEditAuthor.js:212
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:28
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:53
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:45
msgid "Name"
msgstr "Meno"

#. js-lingui-explicit-id
#: src/components/dashboards/DashboardSelector/FormCreateDashboard.js:25
msgid "name.nazev"
msgstr "Názov"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:57
msgid "Name for expression (optional)"
msgstr "Názov pre výraz (nepovinné)"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:341
msgid "Need help? Ask AI assistant. Select a sentence or paragraph"
msgstr "Potrebujete pomoc? Opýtajte sa AI asistenta. Vyberte vetu alebo odsek."

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:147
msgid "Need help? Check our <0>tutorial</0> or contact us."
msgstr "Potrebujete pomôcť? Prečítajte si tento <0>návod</0> alebo nás kontaktujte."

#: src/components/medialist/content/MedialistDashboard.js:94
#: src/components/medialist/content/MedialistDashboard.js:127
msgid "New"
msgstr "Nový"

#. js-lingui-explicit-id
#: src/components/layout/Header/AppNotifications/AppNotifications.js:133
msgid "new.notifications"
msgstr "Nové"

#: src/components/emailing/modules/withModalAddCampaign.tsx:20
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:91
#: src/components/emailing/content/EmailingCampaignsContent.tsx:59
msgid "New Campaign"
msgstr "Nová kampaň"

#: src/components/newsroom/forms/FormCreateCategory/FormCreateCategory.js:29
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:72
msgid "New Category"
msgstr "Nová kategória"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:29
#: src/components/emailing/content/NewEmailWizardButton.tsx:13
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:72
msgid "New Email"
msgstr "Nový email"

#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:38
#: src/components/forms/baskets/FormNewBasket.js:10
msgid "New list"
msgstr "Nový zoznam"

#: src/pages/user/reset-password/success.tsx:7
#: src/pages/user/reset-password/new.tsx:54
#: src/components/staff/admin/user/User.js:267
#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:87
msgid "New password"
msgstr "Nové heslo"

#: src/store/models/admin/customer/CustomerStore.js:227
msgid "New passwords have been copied to the clipboard."
msgstr "Nové heslá boli skopírované do schránky."

#: src/components/newsroom/content/posts/NewPostWizardButton.tsx:13
msgid "New post"
msgstr "Nový článok"

#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:71
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:70
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:103
msgid "New report"
msgstr "Nový report"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:78
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:264
#: src/components/forms/tags/FormNewTag/FormNewTag.js:10
msgid "New Tag"
msgstr "Nový štítok"

#: src/components/topics/Content/TopicsList/TopicsList.js:36
msgid "New topic"
msgstr "Nová téma"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:55
msgid "New value"
msgstr "Nová hodnota"

#: src/pages/newsroom/index.js:24
#: src/pages/newsroom/index.js:33
#: src/pages/newsroom/create.js:17
#: src/pages/newsroom/[blogId]/settings.js:15
#: src/pages/newsroom/[blogId]/index.js:16
#: src/pages/newsroom/[blogId]/post/[postId].js:10
#: src/components/newsroom/forms/FormNewsroomSettings/LanguageSection.tsx:61
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:112
#: src/components/layout/Sidebar/SidebarNavigation.tsx:147
#: src/components/layout/AuthWrapper/constants/features.slides.js:306
#: src/components/emailing/components/FunnelStats/FunnelStats.tsx:80
#: src/app/components/monitoring-navigation.tsx:279
msgid "Newsroom"
msgstr "Newsroom"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:85
msgid "Newsroom Articles"
msgstr "Články v newsroome"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:254
msgid "Newsroom is a blogging platform that allows you to easily share your external and internal communication (e.g. press releases, announcements, etc.). You can read more about the Newsroom <0>on our website</0>."
msgstr "Newsroom je blogovacia platforma, ktorá vám umožňuje jednoducho zdieľať externú a internú komunikáciu (napr. tlačové správy, oznámenia, atď.). Viac si o Newsroome môžete prečítať <0>na našom webe</0>."

#: src/components/staff/admin/workspace/Workspace.js:481
msgid "Newsroom limit"
msgstr "Limit na počet Newsroomov"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:305
msgid "Newsroom settings"
msgstr "Nastavenie Newsroomu"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:69
msgid "Next page"
msgstr "Ďalšia strana"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:40
msgid "No access to monitoring feeds, archive search, analytics, topic or report settings, crisis communications, medialist, or user settings."
msgstr "Žiadny prístup k monitorovacím kanálom, archívnemu vyhľadávaniu, analytike, nastaveniam tém alebo správ, krízovej komunikácii, mediálnemu zoznamu alebo nastaveniam používateľa."

#: src/components/emailing/content/mediaCoverage/EmptyFeedMessage.tsx:8
msgid "No articles yet"
msgstr "Zatiaľ nemáte žiadne články"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:81
msgid "No campaigns yet"
msgstr "Zatiaľ nemáte žiadne kampane"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:189
msgid "No categories yet."
msgstr "Zatiaľ nemáte žiadne kategórie."

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:109
msgid "No companies found"
msgstr "Nenašli sme žiadne firmy"

#: src/components/staff/admin/user/WorkspacesTable.js:164
#: src/components/staff/admin/customer/workspaces/Workspaces.js:53
#: src/components/staff/admin/customer/users/UsersTable.js:148
#: src/components/staff/admin/customer/users/Users.js:53
#: src/components/staff/admin/customer/invoices/Invoices.js:49
#: src/components/staff/admin/customer/expenses/Expenses.js:46
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomers.js:49
msgid "No data"
msgstr "Žiadne dáta"

#: src/helpers/charts/highcharts.js:20
msgid "No data to display"
msgstr "Žiadne dáta na zobrazenie"

#: src/components/trash/Content.js:43
msgid "No Deleted Articles"
msgstr "Žiadne zmazané články"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:47
msgid "No detailed data to track."
msgstr "Nie sú dostupné žiadne podrobné údaje na sledovanie."

#: src/components/reports/Content/ReportsList/ReportsHeading/ReportsHeading.js:9
msgid "No email reports created"
msgstr "Nemáte vytvorené žiadne reporty"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:145
msgid "No emails found"
msgstr "Neboli nájdené žiadne emaily"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:127
msgid "No emails yet"
msgstr "Zatiaľ nemáte žiadne emaily"

#: src/components/newsroom/content/posts/NewsroomPosts.js:291
msgid "No posts yet"
msgstr "Zatiaľ nemáte žiadne články."

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:66
msgid "No recipients found"
msgstr "Neboli nájdení žiadni príjemcovia"

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:50
msgid "No recipients yet"
msgstr "Zatiaľ neboli zadaní žiadni príjemcovia"

#: src/components/reports/Content/ReportsList/ReportsList.js:85
msgid "No reports are assigned to the topic."
msgstr "K téme nie sú priradené žiadne reporty."

#: src/store/models/Megalist/MegalistFilter.js:42
#: src/helpers/withTranslatePopup/TranslatePopupContent.js:89
#: src/helpers/withMenuPopup/MntrMenuPopupContent.js:58
#: src/components/widgets/modules/tvr/WidgetTvr.js:78
#: src/components/widgets/modules/medialist/WidgetMedialist.js:127
#: src/components/widgets/modules/feed/WidgetFeedSimple.js:75
#: src/components/staff/admin/customers/Customers.js:37
#: src/components/misc/MntrMultiSelect/MultiSelect.js:22
#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:16
#: src/components/medialist/content/MedialistHeading.js:15
#: src/components/medialist/content/MedialistInspector/Feed/Feed.js:25
#: src/components/medialist/content/FeedMedialist/FeedMedialistEmpty/FeedMedialistEmpty.js:8
#: src/components/layout/Sidebar/modules/SidebarTopics/SidebarTopicsFolders.js:118
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:371
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitorsReports.js:64
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitors.js:86
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:280
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:34
#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTitle.js:99
#: src/components/forms/dashboard/Search/SearchEmailingEmailMessages.js:32
#: src/components/forms/dashboard/Search/SearchEmailingCampaigns.js:31
#: src/components/emailing/content/CampaignAutocompleteList.tsx:23
msgid "No results found"
msgstr "Neboli nájdené žiadne výsledky"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:177
msgid "No senders"
msgstr "Žiadni odosielatelia"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:129
msgid "No subject"
msgstr "Bez predmetu"

#: src/components/emailing/helpers/displayEmailingTitle.js:18
#: src/components/emailing/helpers/displayEmailingTitle.js:21
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:149
#: src/components/emailing/components/RecipientsFeed/EmailMessagesList.js:13
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:123
msgid "No title"
msgstr "Bez názvu"

#: src/components/topics/Content/TopicsHeading/TopicsHeading.js:9
msgid "No topics created"
msgstr "Nemáte vytvorenú žiadnu tému"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:209
msgid "No users"
msgstr "Žiadni užívatelia"

#: src/components/staff/admin/workspace/Workspace.js:862
msgid "No users assigned to this workspace"
msgstr "K tomuto workspacu nie sú priradení žiadni užívatelia"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:187
msgid "No users found"
msgstr "Nenašli sa žiadni používatelia"

#: src/components/newsroom/content/dashboard/ChartVisits.js:59
msgid "No visits yet"
msgstr "Zatiaľ nemáte žiadne zobrazenia."

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:14
msgid "No workspace"
msgstr "Žiadny workspace"

#: src/components/staff/admin/user/User.js:311
msgid "No workspaces assigned to this user"
msgstr "Tomuto užívateľovi nie sú priradené žiadne workspacy"

#. js-lingui-explicit-id
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:542
msgid "filetypes.none"
msgstr "žiadne"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:90
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:43
#: src/components/misc/ActionsBar/Selector/Selector.js:58
msgid "None"
msgstr "Nič"

#. js-lingui-explicit-id
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:43
msgid "attachment.none"
msgstr "Žiadna"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:152
msgid "NOT"
msgstr "NOT"

#: src/components/reports/history/RecipientsTableRow.js:49
#: src/components/reports/history/HistoryTable.js:82
#: src/components/reports/history/HistoryTable.js:111
#: src/components/reports/history/HistoryTable.js:325
msgid "Not delivered"
msgstr "Nedoručený"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:151
msgid "Not verified"
msgstr "Neoverené"

#: src/store/models/dashboards/DashboardPreview.js:156
#: src/components/staff/admin/workspace/Workspace.js:339
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:36
#: src/components/medialist/forms/FormEditAuthor.js:703
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:63
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:63
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:35
msgid "Note"
msgstr "Poznámka"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:128
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:368
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:83
msgid "Notes"
msgstr "Poznámky"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:30
msgid "Notification about mention <0>within 3 minutes</0>"
msgstr "Upozornenie na zmienku <0>do 3 minút</0>"

#: src/components/layout/Header/AppNotifications/AppNotifications.js:99
#: src/components/layout/Header/AppNotifications/AppNotifications.js:107
msgid "Notification Settings"
msgstr "Nastavenie notifikácií"

#: src/components/notifications/ContentTvr.js:39
#: src/components/notifications/ContentTopics.js:24
#: src/components/notifications/AppNotifications/AppNotifications.js:21
#: src/components/layout/Header/AppNotifications/AppNotifications.js:152
msgid "Notifications"
msgstr "Notifikácie"

#: src/constants/analytics.js:55
#: src/constants/analytics.js:566
#: src/constants/analytics.js:678
msgid "Number of articles"
msgstr "Počet článkov"

#: src/constants/analytics/primeScoreCharts.ts:57
msgid "Number of articles by PRIMe relevant vs irrelevant"
msgstr "Počet článkov podľa PRIMe relevantné vs. irelevantné"

#: src/constants/analytics.js:53
msgid "Number of articles by sentiment"
msgstr "Počet článkov podľa sentimentu"

#: src/components/monitoring/Inspector/InspectorMonitora/SocialParentText/SocialParentHeader.js:94
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:41
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:132
#: src/components/misc/ActionsBar/View/ViewMenu.js:289
msgid "Number of followers"
msgstr "Počet fanúšikov"

#: src/store/models/OurChart.js:188
msgid "Number of mentions"
msgstr "Počet zmienok"

#: src/components/tvr/Content/Content.js:61
msgid "Number of outputs"
msgstr "Počet výstupov"

#: src/components/reports/Content/ReportsList/ReportsTopMentionsMode.js:21
msgid "Number of TOP stories"
msgstr "Počet TOP správ"

#: src/components/misc/Changelog/ChangelogTable.js:33
msgid "Object"
msgstr "Objekt"

#: src/components/monitoring/WorkspaceArticles/Limits.js:57
msgid "OCR"
msgstr "OCR"

#: src/components/monitoring/FeedList/FeedListItem/FeedListOlderDivider/FeedListOlderDivider.js:24
msgid "Older articles"
msgstr "Starší články"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:142
msgid "on frontpage"
msgstr "na frontpage"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:91
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:125
msgid "on this continent"
msgstr "na tomto kontinente"

#: src/components/misc/ActionsBar/View/ViewMenu.js:62
#: src/components/layout/AuthWrapper/constants/features.slides.js:47
msgid "Online"
msgstr "Online"

#: src/constants/analytics.js:1287
msgid "Online categories"
msgstr "Kategórie - Online"

#: src/store/models/Megalist/MegalistFilter.js:34
msgid "Only Selected"
msgstr "Iba vybrané"

#: src/store/models/Megalist/MegalistFilter.js:38
msgid "Only Unselected"
msgstr "Iba nevybrané"

#: src/components/medialist/forms/FormEditAuthor.js:892
msgid "Only you can see all the data you entered and the changes made."
msgstr "Všetky zadané údaje a vykonané zmeny môžete vidieť len vy a vaši kolegovia."

#: src/components/newsroom/content/posts/NewsroomPosts.js:185
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:159
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderVideo/HeaderVideo.js:52
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderSocial/HeaderSocial.js:34
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:105
#: src/components/monitoring/Inspector/InspectorEntityKnowledgeBase/InspectorKnowledgeBaseHeader.js:12
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:51
#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthor.js:55
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:20
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:54
msgid "Open"
msgstr "Otvoriť"

#: src/components/staff/admin/customers/Customer.js:187
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:102
msgid "Open customer detail"
msgstr "Otvoriť detail zákazníka"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:102
msgid "Open In Feed"
msgstr "Zobraziť vo feede"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/EmbedFacebook/EmbedFacebook.tsx:49
msgid "Open on Facebook"
msgstr "Otvoriť vo facebooku"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:209
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:225
msgid "Open rate"
msgstr "Miera otvorenia"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:199
#: src/components/staff/admin/customer/users/UsersTable.js:138
msgid "Open user detail"
msgstr "Otvoriť detail užívateľa"

#: src/components/staff/admin/user/WorkspacesTable.js:154
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:146
msgid "Open workspace detail"
msgstr "Otvoriť detail workspacu"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:281
msgid "Opportunity to see"
msgstr "Opportunity to see"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:190
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:320
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:360
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:371
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:387
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:421
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:441
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:510
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:230
#: src/components/emailing/forms/FormSenderSettings.js:89
#: src/components/emailing/forms/FormSenderSettings.js:105
msgid "optional"
msgstr "nepovinné"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:146
msgid "OR"
msgstr "OR"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:70
msgid "Or use an external service"
msgstr "Alebo použite externú službu"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:91
msgid "Order articles"
msgstr "Zoradiť články"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:265
msgid "Ordered list"
msgstr "Číslovanie"

#: src/helpers/withTranslatePopup/TranslatePopupContent.js:70
msgid "Original"
msgstr "Originál"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:45
msgid "Original value"
msgstr "Pôvodná hodnota"

#: src/constants/analytics.js:1125
#: src/constants/analytics.js:1205
#: src/constants/analytics.js:1227
#: src/constants/analytics.js:1247
#: src/constants/analytics.js:1267
#: src/constants/analytics.js:1286
#: src/constants/analytics.js:1305
#: src/constants/analytics.js:1324
#: src/constants/analytics.js:1337
#: src/constants/analytics/primeScoreCharts.ts:135
#: src/components/notifications/ContentTvr.js:118
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:57
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:57
#: src/app/components/monitoring-navigation.tsx:252
msgid "Other"
msgstr "Ostatné"

#: src/store/models/Megalist/Megalist.js:48
msgid "Other Regions"
msgstr "Ostatné regióny"

#: src/components/staff/admin/workspace/Workspace.js:671
msgid "Other settings"
msgstr "Ďalšie nastavenia"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:155
msgid "Others"
msgstr "Ostatné"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:278
#: src/components/misc/ActionsBar/View/ViewMenu.js:219
msgid "OTS"
msgstr "OTS"

#: src/pages/_error.js:55
msgid "Our team has been notified. We're sorry for the inconvenience."
msgstr "Náš tím bol upozornený. Za nepríjemnosť sa vám ospravedlňujeme."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:57
msgid "Outline the main content or details you want included."
msgstr "Načrtnite hlavný obsah alebo podrobnosti, ktoré chcete zahrnúť."

#: src/components/medialist/constants/medialist.tabNavigation.js:20
msgid "Overview"
msgstr "Prehľad"

#: src/components/staff/admin/workspace/Workspace.js:603
msgid "Own content"
msgstr "Vlastný obsah"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:86
msgid "Own selection"
msgstr "Vlastný výber"

#. js-lingui-explicit-id
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:92
msgid "page.shortened"
msgstr "str."

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:64
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:76
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:72
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:220
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupPagesContent.js:27
#: src/components/misc/Pagination/Pagination.js:26
#: src/components/misc/Pagination/Pagination.js:39
#: src/components/misc/Pagination/Pagination.js:45
#: src/components/misc/Pagination/Pagination.js:76
#: src/components/misc/Pagination/Pagination.js:84
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:118
msgid "Page"
msgstr "Strana"

#: src/components/tvr/Inspector/InspectorMedia/PaginationMedia/PaginationMedia.js:30
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPagination.js:48
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:258
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupPagesContent.js:18
#: src/components/layout/MntrActiveFilters/modules/PageNumbers.js:22
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:123
msgid "Pages"
msgstr "Strany"

#. placeholder {0}: data.publication.pages.length
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:238
msgid "Pages ({0} total)"
msgstr "Strany ({0} celkovo)"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:60
msgid "Paid"
msgstr "Zaplatené"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:160
msgid "Paragraph"
msgstr "Odstavec"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:423
msgid "Parse PDF"
msgstr "Parsovať PDF"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:140
msgid "Partner Code (optional)"
msgstr "Partnerský kód (nepovinné)"

#: src/pages/user/reset-password/new.tsx:32
#: src/components/page/auth/SignUp/SignUp.js:42
#: src/components/page/auth/Login/Login.tsx:46
#: src/components/emailing/forms/FormSenderSettings.js:96
msgid "Password"
msgstr "Heslo"

#: src/pages/user/reset-password/new.tsx:39
msgid "Password again"
msgstr "Heslo znova"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:119
msgid "Password change"
msgstr "Zmena hesla"

#: src/components/staff/admin/workspace/UsersTable/CopyPassword.js:34
msgid "Password copied to the clipboard."
msgstr "Heslo bolo skopírované do schránky."

#: src/components/misc/VideoPlayer/CropControls.js:127
#: src/components/misc/VideoPlayer/Controls.js:98
msgid "Pause"
msgstr "Pozastaviť"

#: src/components/reports/history/RecipientsTableRow.js:58
msgid "Pending"
msgstr "Čaká sa"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:30
msgid "people"
msgstr "ľudí"

#: src/helpers/formatNumber.js:29
msgid "per month"
msgstr "za mesiac"

#: src/components/OurChart/OurChartAdvanced.js:155
msgid "Percent Share"
msgstr "Percentuálny podiel"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:137
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:562
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:564
#: src/components/misc/ActionsBar/View/ViewMenu.js:326
msgid "Perex"
msgstr "Perex"

#: src/components/exportList/History/HistoryTable/HistoryTable.js:60
msgid "Period"
msgstr "Za obdobie"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:39
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:44
msgid "Periodicity"
msgstr "Periodicita"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:60
msgid "Permissions"
msgstr "Oprávnenia"

#: src/components/medialist/forms/FormEditAuthor.js:841
#: src/components/medialist/forms/FormEditAuthor.js:1002
#: src/components/medialist/forms/FormEditAuthor.js:1007
msgid "Personal Website"
msgstr "Osobný web"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:66
msgid "Personalization:"
msgstr "Personalizácia:"

#: src/components/page/auth/SignUp/SignUp.js:49
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:132
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:80
#: src/components/medialist/forms/FormEditAuthor.js:766
#: src/components/medialist/forms/FormEditAuthor.js:900
#: src/components/medialist/forms/FormEditAuthor.js:906
msgid "Phone"
msgstr "Telefón"

#: src/constants/analytics.js:677
#: src/constants/analytics.js:697
#: src/constants/analytics.js:716
#: src/constants/analytics.js:735
#: src/constants/analytics.js:754
#: src/constants/analytics.js:773
#: src/constants/analytics.js:792
#: src/constants/analytics.js:811
#: src/constants/analytics.js:826
#: src/constants/analytics.js:844
#: src/constants/analytics.js:863
#: src/constants/analytics.js:882
#: src/constants/analytics.js:902
#: src/constants/analytics.js:922
#: src/constants/analytics.js:943
#: src/constants/analytics.js:963
#: src/constants/analytics.js:978
#: src/constants/analytics.js:992
#: src/constants/analytics.js:1008
#: src/constants/analytics.js:1023
#: src/constants/analytics.js:1038
#: src/constants/analytics/primeScoreCharts.ts:94
msgid "Pie"
msgstr "Koláč"

#: src/helpers/formatNumber.js:39
msgid "pieces"
msgstr "kusov"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:71
msgid "Plain"
msgstr "Jednoduchý"

#: src/components/staff/admin/workspace/Workspace.js:309
msgid "Plan"
msgstr "Plán"

#: src/components/emailing/content/promo/PromoEmailing.js:18
msgid "Platform for email communication with journalists."
msgstr "Platforma na emailovú komunikáciu s novinármi."

#: src/components/misc/VideoPlayer/CropControls.js:127
#: src/components/misc/VideoPlayer/Controls.js:98
msgid "Play"
msgstr "Prehrať"

#: src/components/emailing/content/sender/EmailingSenderContent.js:34
msgid "Please add a sender address that will be used for sending emails."
msgstr "Uveďte prosím adresu odosielateľa, ktorá sa bude používať na odosielanie emailov."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:545
msgid "Please copy and insert this code into your website. Modify the width and height values of the iframe according to your requirements. Additionally, it’s possible to hide the header and footer if necessary."
msgstr "Skopírujte a vložte tento kód do zdrojového kódu vašeho webu. Šírku a výšku iframu môžete upraviť podľa potreby. Môžete taktiež skyť hlavičku a pätičku."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:95
msgid "Please remove some recipients."
msgstr "Odstráňte niektorých príjemcov."

#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:168
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:81
msgid "Please select Image"
msgstr "Prosím, vyberte obrázok"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepTitleAndCommunicationPlan.tsx:60
msgid "Please select the title and review the communication plan. If it does not meet your expectations, restart the process."
msgstr "Vyberte, prosím, názov a skontrolujte komunikačný plán. Ak nesplňuje vaše očakávania, reštartujte proces."

#: src/components/emailing/forms/FormSenderSettings.js:105
msgid "Port"
msgstr "Port"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:133
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:100
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:68
msgid "Position"
msgstr "Pozícia"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostPreview.tsx:80
msgid "Post preview"
msgstr "Ukážka článku"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:351
msgid "Post settings"
msgstr "Nastavenia príspevku"

#: src/constants/analytics.js:1132
#: src/constants/analytics.js:1157
#: src/constants/analytics.js:1212
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:37
msgid "Posts"
msgstr "Príspevky"

#: src/components/topics/Content/TopicsList/KeywordExtraQueryAdvanced/KeywordExtraQueryAdvanced.tsx:26
msgid "Predefined queries"
msgstr "Predpripravevné výrazy"

#: src/components/misc/Capture/Capture.js:283
msgid "Preparing export..."
msgstr "Pripravujeme export..."

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:86
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:28
#: src/components/reports/history/HistoryTable.js:406
#: src/components/reports/Content/ReportsList/ReportPreview.js:18
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:20
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:115
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:140
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:140
#: src/components/forms/dashboard/ExportResend/ExportResend.js:163
#: src/components/emailing/content/CreateEmailContent.js:278
msgid "Preview"
msgstr "Náhľad"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:142
msgid "Preview & Publish"
msgstr "Skontrolovať a zverejniť"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:110
msgid "Preview images"
msgstr "Obrázky ku článku"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:66
msgid "Previous page"
msgstr "Predchádzajúca strana"

#: src/components/staff/SignUp.js:17
#: src/components/staff/admin/workspace/Workspace.js:296
#: src/components/staff/admin/DailyAccess/Table.js:30
msgid "Primary app"
msgstr "Aplikácia"

#: src/constants/analytics/primeScoreCharts.ts:122
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:420
#: src/components/layout/MntrActiveFilters/modules/PrimeFilter.tsx:25
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:46
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:33
#: src/components/analytics/AnalyticsContent.js:152
#: src/components/analytics/AnalyticsContent.js:179
msgid "PRIMe"
msgstr "PRIMe"

#: src/constants/analytics/primeScoreCharts.ts:116
msgid "PRIMe in mediatype"
msgstr "PRIMe v mediatype"

#: src/components/widgets/modules/stats/WidgetStats.js:190
msgid "PRIMe negative total value"
msgstr "PRIMe negatívna celková hodnota"

#: src/components/widgets/modules/stats/WidgetStats.js:183
msgid "PRIMe positive total value"
msgstr "PRIMe pozitívna celková hodnota"

#: src/constants/analytics/primeScoreCharts.ts:76
msgid "PRIMe scale"
msgstr "Stupnica PRIMe"

#: src/constants/analytics/primeScoreCharts.ts:9
#: src/constants/analytics/primeScoreCharts.ts:37
#: src/constants/analytics/primeScoreCharts.ts:69
#: src/constants/analytics/primeScoreCharts.ts:82
#: src/constants/analytics/primeScoreCharts.ts:102
msgid "PRIMe score"
msgstr "Skóre PRIMe"

#: src/components/widgets/modules/stats/WidgetStats.js:176
msgid "PRIMe total average"
msgstr "PRIMe celkový priemer"

#: src/components/widgets/modules/stats/WidgetStats.js:169
msgid "PRIMe total value"
msgstr "PRIMe celková hodnota"

#: src/components/misc/ActionsBar/View/ViewMenu.js:112
#: src/components/layout/AuthWrapper/constants/features.slides.js:57
#: src/components/OurChart/OurChartAdvanced.js:260
msgid "Print"
msgstr "Tlač"

#: src/constants/analytics.js:1306
msgid "Print categories"
msgstr "Kategórie - Tlač"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:89
msgid "Professional"
msgstr "Profesionálny"

#: src/constants/analytics.js:1154
msgid "Profile"
msgstr "Profil"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:113
#: src/components/misc/MntrEditor/modals/withModalPromoBox.js:8
#: src/components/misc/MntrEditor/extensions/ExtensionPromoBox.js:33
msgid "Promo Box"
msgstr "Promo box"

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:66
msgid "Provide additional feedback..."
msgstr "Poskytnúť dodatočnú spätnú väzbu..."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:33
msgid "Provide information such as:"
msgstr "Poskytnite informácie, ako napríklad:"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:74
msgid "Providing information about your company allows the AI assistant to generate more accurate and tailored content for your newsroom articles. This ensures the text aligns closely with your brand's identity and messaging"
msgstr "Poskytnutím informácií o vašej spoločnosti umožníte AI asistentovi generovať presnejší a prispôsobenejší obsah pre vaše články. To zaisťuje, že text je v tesnom súlade s identitou a posolstvom vašej značky"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:291
msgid "Publication Date"
msgstr "Dátum publikácie"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:95
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:142
msgid "Publish"
msgstr "Zverejniť"

#: src/components/newsroom/forms/FormNewsroomPost/PostStatus.js:49
msgid "Publish date set to"
msgstr "Dátum zverejnenia nastavený na"

#: src/components/newsroom/forms/FormNewsroomPost/PostStatus.js:73
msgid "Publish date set to {scheduledFormatted}"
msgstr "Dátum zverejnenia nastavený na {scheduledFormatted}"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:154
msgid "Publish now"
msgstr "Zverejniť hneď"

#: src/pages/newsroom/index.js:45
msgid "Publish press releases <0>easily and quickly</0>"
msgstr "Zverejnite tlačovú správu <0>jednoducho a bez čakania</0>"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:155
msgid "Publish this post immediately"
msgstr "Okamžite zverejniť tento príspevok"

#: src/components/newsroom/components/PostsList/PostsList.js:184
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:30
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:66
msgid "Published"
msgstr "Publikované"

#: src/components/staff/admin/user/getUserAttributes.js:14
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:35
#: src/components/layout/Sidebar/SidebarNavigation.tsx:195
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:261
#: src/components/layout/MntrActiveFilters/modules/Publisher.js:13
msgid "Publisher"
msgstr "Vydavateľ"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:30
msgid "Publisher copyright fees"
msgstr "Autorské poplatky pre vydavateľa"

#: src/constants/analytics.js:979
#: src/constants/analytics.js:993
#: src/constants/analytics.js:1009
#: src/constants/analytics.js:1024
#: src/constants/analytics.js:1039
msgid "Publishers"
msgstr "Vydavateľstvá"

#: src/components/layout/Header/AppNotifications/AppNotifications.js:201
msgid "Push Notifications"
msgstr "Push notifikácie"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:32
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:66
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:106
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:134
msgid "Quick Overview"
msgstr "Rýchly prehľad"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:30
msgid "Quickly protect your brand reputation and stakeholder trust."
msgstr "Majte pod kontrolou reputáciu a dôveryhodnosť vašej značky."

#: src/components/newsroom/content/modules/CustomQuotes.tsx:64
msgid "Quote"
msgstr "Citát"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:74
#: src/components/newsroom/content/modules/CustomQuotes.tsx:33
msgid "Quotes"
msgstr "Citáty"

#: src/components/notifications/ContentTvrRequest.js:74
#: src/components/notifications/ContentTvr.js:81
#: src/components/misc/ActionsBar/View/ViewMenu.js:162
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:62
#: src/components/layout/AuthWrapper/constants/features.slides.js:75
msgid "Radio"
msgstr "Rozhlas"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:92
msgid "Rank is primarily based on the reach and the importance of the news source."
msgstr "Skóre vychádza primárne z dosahu a dôležitosti zdroje."

#: src/components/newsroom/components/AiTools/AiCheckPostResult.tsx:92
msgid "Re-run check"
msgstr "Znova spustiť AI kontrolu"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:30
msgid "reach"
msgstr "dosah"

#: src/constants/stats.ts:26
#: src/constants/analytics.js:107
#: src/constants/analytics.js:121
#: src/constants/analytics.js:123
#: src/constants/analytics.js:129
#: src/constants/analytics.js:590
#: src/constants/analytics.js:603
#: src/constants/analytics.js:736
#: src/constants/analytics.js:1025
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:421
#: src/components/misc/ActionsBar/View/ViewMenu.js:227
#: src/components/analytics/TraditionalMedia.js:34
#: src/components/analytics/TraditionalMedia.js:40
msgid "Reach"
msgstr "Dosah"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:98
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:112
msgid "Reactivate"
msgstr "Znova aktivovať"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:101
msgid "Reactivate recipient"
msgstr "Znova aktivovať príjemcu"

#: src/components/reports/history/RecipientsTableRow.js:31
msgid "Read"
msgstr "Prečítaný"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:34
msgid "Read only:"
msgstr "Iba na čítanie:"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:189
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:63
#: src/components/misc/ActionsBar/View/ViewMenu.js:132
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:95
msgid "Readership"
msgstr "Čítanosť"

#: src/helpers/modal/withModalReportProblem.tsx:32
#: src/helpers/modal/withModalReportArticle.tsx:46
#: src/components/reports/history/RecipientsTableHeader.js:38
msgid "Reason"
msgstr "Dôvod"

#: src/components/medialist/content/MedialistDashboard.js:179
msgid "Recently edited authors"
msgstr "Naposledy upravení autori"

#: src/components/medialist/content/MedialistDashboard.js:158
msgid "Recently viewed authors"
msgstr "Naposledy zobrazení autori"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:70
msgid "Recipient"
msgstr "Príjemca"

#: src/store/models/reports/recipients/Recipients.js:27
msgid "Recipient added."
msgstr "Príjemca bol pridaný."

#: src/components/forms/dashboard/ExportResend/ExportResend.js:79
msgid "Recipient emails"
msgstr "Emaily príjemcov"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:83
msgid "Recipient has no email address"
msgstr "Príjemca nemá e-mailovú adresu"

#: src/store/models/reports/recipients/Recipients.js:56
msgid "Recipient removed."
msgstr "Príjemca bol odstránený."

#: src/store/models/reports/recipients/Recipients.js:44
msgid "Recipient updated."
msgstr "Príjemca bol upravený."

#: src/helpers/modal/withModalTvrTopics.tsx:77
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:246
#: src/components/reports/history/HistoryTable.js:169
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:59
#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:55
#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:63
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:101
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:112
#: src/components/emailing/content/CreateEmailContent.js:269
#: src/components/emailing/content/tabs/RecipientsTab.tsx:23
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:163
msgid "Recipients"
msgstr "Príjemcovia"

#: src/store/models/reports/recipients/Recipients.js:27
msgid "Recipients added."
msgstr "Príjemcovia boli pridaní."

#: src/components/reports/history/HistoryTable.js:52
msgid "Recipients from: {formattedCreated}"
msgstr "Príjemci z: {formattedCreated}"

#: src/components/reports/history/HistoryTable.js:432
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:50
msgid "Recipients have been copied to the clipboard."
msgstr "Príjemci boli skopírovaní do schránky."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:45
msgid "Recipients limit"
msgstr "Limit príjemcov"

#: src/store/models/reports/recipients/Recipients.js:68
msgid "Recipients removed."
msgstr "Príjemcovia boli odstránení."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:25
msgid "Recipients with missing information"
msgstr "Príjemci s chýbajúcimi informáciami"

#: src/components/forms/dashboard/Export/RecommendedLimit.js:32
msgid "Recomended limit"
msgstr "Doporučený limit"

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:59
msgid "Recommended file types: XLSX, CSV"
msgstr "Odporúčané typy súborov: XLSX, CSV"

#: src/components/forms/adapters/MntrFileAdapter/MntrFileAdapter.js:70
msgid "Recommended resolution"
msgstr "Odporúčané rozlíšenie"

#: src/components/staff/admin/workspace/Workspace.js:136
msgid "Recreate articles"
msgstr "Pregenerovať články"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:566
msgid "Redo"
msgstr "Znova"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:31
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:110
msgid "Regenerate content"
msgstr "Znovu vygenerovať obsah"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:31
msgid "Regenerate until the email text aligns perfectly with your requirements"
msgstr "Regenerujte, kým sa text e-mailu dokonale nezhoduje s vašimi požiadavkami"

#: src/components/staff/admin/customers/Customers.js:27
msgid "Register new user"
msgstr "Registrovať nového užívateľa"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:55
msgid "Relevant"
msgstr "Relevantné"

#: src/helpers/modal/withModalRemove.tsx:37
#: src/helpers/modal/withModalRemove.tsx:51
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:142
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:47
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:85
#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:7
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:676
#: src/components/newsroom/content/modules/CustomQuotes.tsx:58
#: src/components/newsroom/content/modules/CustomKeypoints.tsx:49
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:570
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:74
#: src/components/emailing/modules/withModalRemoveRecipients.tsx:24
#: src/components/emailing/modules/withModalRemoveRecipients.tsx:39
#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:88
#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:68
msgid "Remove"
msgstr "Odstrániť"

#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:105
#: src/components/exportList/Content/Content.tsx:95
#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:56
msgid "Remove All"
msgstr "Odstrániť všetky"

#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:34
msgid "Remove all from report"
msgstr "Odobrať všetko z reportu"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:97
msgid "Remove all from selection"
msgstr "Odobrať všetko z výberu"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:155
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:126
msgid "Remove authors from list"
msgstr "Odobrať autorov zo zoznamu"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:50
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:88
msgid "Remove Campaign"
msgstr "Odstrániť kampaň"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:57
msgid "Remove from article"
msgstr "Odobrať z článku"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:338
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:285
msgid "Remove from Export"
msgstr "Odstrániť z exportu"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:40
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:99
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:173
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:257
msgid "Remove from filters"
msgstr "Odobrať z filtrácie"

#: src/components/medialist/content/withRemoveFromBasketPopup.js:34
msgid "Remove from list"
msgstr "Odobrať zo zoznamu"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:273
msgid "Remove from next report"
msgstr "Odobrať z ďalšieho reportu"

#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:282
msgid "Remove from report"
msgstr "Odobrať z reportu"

#: src/components/staff/admin/user/WorkspacesTable.js:138
msgid "Remove from workspace"
msgstr "Odobrať z workspacu"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:200
#: src/components/settings/SettingsLogo/SettingsLogo.js:145
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:335
msgid "Remove Image"
msgstr "Zmazať obrázok"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:359
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:457
msgid "Remove Link"
msgstr "Odstrániť odkaz"

#: src/components/medialist/forms/modules/FormFieldUploadPhoto.js:53
msgid "Remove Photo"
msgstr "Odstrániť fotku"

#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:69
msgid "Remove Recipients"
msgstr "Odstránenie príjemcov"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:73
msgid "Remove report"
msgstr "Odstrániť report"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:99
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:274
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:374
#: src/components/monitoring/FeedActionsBar/withRemoveTagPopup/RemoveTagPopupContent.js:11
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:77
msgid "Remove tag"
msgstr "Odstrániť štítok"

#: src/components/staff/admin/workspace/UsersTable/RemoveUser.js:25
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:398
msgid "Remove user"
msgstr "Odobrať užívateľa"

#: src/components/staff/admin/workspace/UsersTable/RemoveUsers.tsx:16
msgid "Remove users"
msgstr "Odobrať užívateľov"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:132
msgid "Remove widget"
msgstr "Odstrániť widget"

#: src/store/models/monitoring/Inspector/Inspector.ts:428
msgid "Removed from next report."
msgstr "Odobrané z reportu."

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:257
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:261
msgid "Rename"
msgstr "Premenovať"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:94
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:36
#: src/components/reports/Content/ReportsList/ReportPreview.js:26
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:28
msgid "Report preview"
msgstr "Náhľad reportu"

#: src/helpers/modal/withModalReportProblem.tsx:45
#: src/helpers/modal/withModalReportArticle.tsx:70
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:104
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:26
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:289
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:295
#: src/components/medialist/forms/FormEditAuthor.js:381
#: src/components/medialist/forms/FormEditAuthor.js:387
#: src/components/medialist/forms/FormEditAuthor.js:528
#: src/components/medialist/forms/FormEditAuthor.js:534
msgid "Report problem"
msgstr "Nahlásiť problém"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:76
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:51
msgid "Report will be removed."
msgstr "Report bude odstránený."

#: src/pages/reports/index.js:15
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:118
#: src/components/reports/ReportChangelog.js:18
#: src/components/reports/history/Content.js:31
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:45
#: src/app/components/monitoring-navigation.tsx:165
msgid "Reports"
msgstr "Reporty"

#: src/components/layout/AuthWrapper/constants/features.slides.js:353
msgid "Reports and exports"
msgstr "Reporty a exporty"

#: src/pages/reports/history.js:12
#: src/components/reports/history/Content.js:35
#: src/components/reports/Content/ReportsList/ReportsList.js:37
#: src/app/components/monitoring-navigation.tsx:179
msgid "Reports History"
msgstr "Odoslané reporty"

#. js-lingui-explicit-id
#: src/helpers/modal/withModalRequestFeature.tsx:24
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:57
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:244
msgid "featureRequest.Request"
msgstr "Vyskúšať"

#: src/helpers/modal/withModalRequestFeature.tsx:50
#: src/components/misc/MntrButton/modules/ButtonRequestFeature.js:36
msgid "Request Access?"
msgstr "Požiadať o spustenie?"

#: src/helpers/modal/withModalTvrTopics.tsx:41
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:509
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:510
msgid "Request change"
msgstr "Požiadať o úpravu"

#: src/components/notifications/ContentTvrRequest.js:32
#: src/components/notifications/ContentTvr.js:120
msgid "Request Channels"
msgstr "Požiadať o kanály"

#: src/components/analytics/AnalyticsContent.js:250
msgid "Request social media?"
msgstr "Požiadať o spustenie sociálnych médií?"

#: src/components/analytics/AnalyticsContent.js:217
msgid "Request traditional media?"
msgstr "Požiadať o spustenie tradičných médií?"

#: src/helpers/store/apiClient.js:153
msgid "Request was cancelled."
msgstr "Požiadavka bola zrušená."

#. js-lingui-explicit-id
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:104
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:78
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:49
#: src/components/misc/PromoBox/PromoBox.js:142
#: src/components/misc/MntrButton/modules/ButtonRequestFeature.js:25
#: src/components/analytics/AnalyticsContent.js:199
#: src/components/analytics/AnalyticsContent.js:232
msgid "featureRequest.Requested"
msgstr "Požiadané"

#: src/components/staff/admin/DailyAccess/Table.js:33
msgid "Requests"
msgstr "Dotazy"

#: src/components/reports/history/HistoryTable.js:65
#: src/components/reports/history/HistoryTable.js:446
msgid "Resend"
msgstr "Preposlať"

#: src/components/reports/history/Compose.js:42
msgid "Resend email report"
msgstr "Preposlať report"

#: src/components/reports/history/Compose.js:44
msgid "Resend email report from: {formattedCreated}"
msgstr "Preposlať report z: {formattedCreated}"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:102
msgid "Resend verification email"
msgstr "Znovu poslať verifikačný email"

#: src/store/models/reports/history/History.js:92
msgid "Resending email report. Check back later."
msgstr "Preposielam report."

#: src/components/reports/history/HistoryTable.js:215
msgid "Resent report"
msgstr "Preposlaný report"

#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:33
#: src/components/medialist/forms/FormEditAuthor.js:591
msgid "Reset"
msgstr "Vrátiť späť"

#: src/components/medialist/forms/FormEditAuthor.js:295
#: src/components/medialist/forms/FormEditAuthor.js:459
msgid "Reset author profile"
msgstr "Obnoviť profil autora"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:153
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:72
msgid "Reset filter"
msgstr "Zrušiť filtráciu"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:61
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:241
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:258
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:277
msgid "Reset filters"
msgstr "Zrušiť filtráciu"

#: src/pages/user/reset-password/index.tsx:36
msgid "Reset Password"
msgstr "Obnoviť heslo"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:69
msgid "Reset selection"
msgstr "Zrušiť výber"

#: src/helpers/modal/withModalResetAuthor.tsx:25
#: src/helpers/modal/withModalResetAuthor.tsx:39
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:87
msgid "Restore"
msgstr "Obnoviť"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:403
msgid "Restore articles"
msgstr "Obnoviť články"

#: src/components/settings/SettingsTheme/ThemePicker.tsx:122
msgid "Restore default"
msgstr "Obnoviť pôvodné"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:131
#: src/components/misc/Changelog/ChangelogTable.js:40
msgid "Revert"
msgstr "Revert"

#: src/components/misc/Changelog/ChangelogTableRow.js:192
msgid "Revert actions"
msgstr "Revert akcie"

#: src/components/misc/Changelog/ChangelogTableRow.js:209
msgid "Revert now"
msgstr "Revertnúť hneď"

#: src/components/misc/Changelog/ChangelogTableRow.js:163
msgid "Reverted on:"
msgstr "Reverntúť dňa:"

#: src/components/newsroom/components/AiTools/AiGenerateCommunicationPlan.tsx:34
msgid "Review communication plan"
msgstr "Skontrolujte komunikačný plán"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:204
msgid "Roadmap"
msgstr "Roadmapa"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:65
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:230
msgid "Role"
msgstr "Rola"

#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:59
msgid "Row"
msgstr "Riadok"

#: src/components/medialist/forms/FormEditAuthor.js:862
#: src/components/medialist/forms/FormEditAuthor.js:1038
msgid "Salutation"
msgstr "Oslovenie"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:142
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:229
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:103
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:44
#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:116
#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:215
#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:101
#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:70
#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:122
#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:47
#: src/components/staff/admin/workspace/Workspace.js:234
#: src/components/staff/admin/workspace/Workspace.js:951
#: src/components/staff/admin/user/User.js:176
#: src/components/staff/admin/user/User.js:329
#: src/components/settings/SettingsTheme/ThemePicker.tsx:139
#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:103
#: src/components/settings/SettingsLogo/SettingsLogo.js:164
#: src/components/settings/SettingsApplication/SettingsApplication.js:50
#: src/components/reports/Content/ReportsList/ReportsForm.js:342
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:36
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:150
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:224
#: src/components/newsroom/content/dashboard/NewsroomBlogSettings.js:21
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:591
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:73
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:82
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:21
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:467
#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:86
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:242
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:116
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:191
#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:35
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:170
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:21
#: src/components/misc/Capture/Capture.js:238
#: src/components/medialist/forms/FormEditAuthor.js:601
#: src/components/medialist/forms/FormEditAuthor.js:741
#: src/components/layout/Sidebar/modules/SidebarTopics/FormFolder.tsx:25
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:55
#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:91
#: src/components/forms/tags/FormEditTag/FormEditTag.js:48
#: src/components/emailing/forms/FormSenderSettings.js:193
#: src/components/emailing/content/CreateEmailContent.js:321
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:58
#: src/components/dashboards/DashboardSelector/FormEditDashboard.js:35
msgid "Save"
msgstr "Uložiť"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:147
msgid "Save & Publish"
msgstr "Uložiť a publikovať"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:151
msgid "Save & Schedule"
msgstr "Uložiť a načasovať"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:188
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:61
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:61
msgid "Save as"
msgstr "Uložiť ako"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/ModalAddDashboardFooter.tsx:35
msgid "Save changes"
msgstr "Uložiť zmeny"

#: src/components/misc/Capture/Capture.js:245
#: src/components/OurChart/OurChartAdvanced.js:187
msgid "Save in format"
msgstr "Uložiť vo formáte"

#: src/helpers/modal/withModalEmailPreview.js:94
msgid "Save report"
msgstr "Uložiť report"

#: src/components/topics/Content/TopicsList/FormSaveMegalist/FormSaveMegalist.js:20
msgid "Save selection"
msgstr "Uložiť výber"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:142
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:51
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:51
msgid "Save settings"
msgstr "Uložiť nastavenie"

#: src/constants/analytics/primeScoreCharts.ts:75
msgid "Scatter"
msgstr "Scatter"

#: src/components/misc/Changelog/ChangelogTableRow.js:212
msgid "Schedule revert"
msgstr "Naplánovať revert"

#: src/components/newsroom/components/PostsList/PostsList.js:188
msgid "Scheduled"
msgstr "Naplánované"

#: src/components/misc/Changelog/ChangelogTableRow.js:172
msgid "Scheduled on:"
msgstr "Naplánované na:"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:99
msgid "Scheduled to send at {scheduledDateFormatted}, are you sure you want to delete this email?"
msgstr "Naplánované odoslanie na {scheduledDateFormatted}, určite chcete tento email vymazať?"

#: src/components/emailing/content/CreateEmailContent.js:150
msgid "Scheduled to send email"
msgstr "Naplánované odoslanie emailu"

#: src/components/misc/ActionsBar/View/ViewMenu.js:247
msgid "Scope of mention"
msgstr "Rozsah zmienky"

#: src/components/tvr/Inspector/InspectorMedia/PaginationMedia/PaginationMedia.js:46
msgid "Screens"
msgstr "Obrazovky"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:33
msgid "Screenshot"
msgstr "Screenshot"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:106
#: src/components/tariff/MonitoredMedia/MonitoredMedia.js:174
#: src/components/monitoring/Inspector/InspectorMonitora/HashTagsList/HashTagsList.js:55
#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:59
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Keywords.js:38
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:139
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/MedialistArticlesFilterSearchQuery.js:55
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/MedialistArticlesFilterSearchQuery.js:84
#: src/components/layout/MntrFiltersBar/forms/FormNote/FormNote.js:33
#: src/components/layout/Header/SearchSuggest/SearchSuggest.js:51
#: src/components/forms/dashboard/Search/SearchForm.js:71
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery.js:56
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery.js:85
msgid "Search"
msgstr "Vyhľadať"

#: src/components/forms/dashboard/Search/SearchForm.js:75
msgid "Search authors"
msgstr "Hľadať autorov"

#: src/pages/authors/index.js:53
msgid "Search authors by <0>many filters</0>"
msgstr "Vyhľadávajte autorov pomocou <0>mnohých filtrov</0>"

#: src/components/forms/dashboard/Search/SearchForm.js:79
msgid "Search changelog"
msgstr "Hľadať v histórii zmien"

#: src/components/forms/dashboard/Search/SearchForm.js:43
#: src/components/forms/dashboard/Search/SearchAdmin.js:39
msgid "Search customers"
msgstr "Vyhľadať v zákazníkoch"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:360
msgid "Search engine metadata"
msgstr "Metadáta pre vyhľadávače"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:58
#: src/components/help/search/Content/RulesPhrase.tsx:16
msgid "Search for phrases"
msgstr "Vyhľadávanie slovných spojení"

#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:28
#: src/components/layout/Header/HeaderWithObserver.tsx:201
#: src/components/layout/Header/HeaderWithObserver.tsx:240
#: src/app/(authorized)/help/search/page.tsx:17
msgid "Search help"
msgstr "Nápoveda pre vyhľadávanie"

#: src/components/layout/Header/SearchSuggest/SearchSuggest.js:70
msgid "Search History"
msgstr "História vyhľadávania"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:62
msgid "Search in"
msgstr "Vyhľadať v"

#: src/components/forms/dashboard/Search/SearchForm.js:52
msgid "Search in {topicName}"
msgstr "Vyhľadať v {topicName}"

#: src/components/forms/dashboard/Search/SearchForm.js:33
msgid "Search in archive"
msgstr "Vyhľadať v archíve"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:44
msgid "Search in author"
msgstr "Vyhľadať pod autorom"

#: src/components/forms/dashboard/Search/SearchForm.js:63
msgid "Search in Emailing"
msgstr "Vyhľadať v Emailingu"

#: src/components/forms/dashboard/Search/SearchForm.js:59
msgid "Search in Newsroom"
msgstr "Vyhľadať v Newsroome"

#: src/components/layout/MntrFiltersBar/forms/FormNote/FormNote.js:31
msgid "Search in Notes"
msgstr "Text v poznámke"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:102
#: src/components/forms/dashboard/Search/SearchForm.js:54
msgid "Search in topic"
msgstr "Vyhľadať v téme"

#: src/components/forms/dashboard/Search/SearchForm.js:34
#: src/components/forms/dashboard/Search/SearchForm.js:67
#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:36
msgid "Search in topics"
msgstr "Vyhľadať v témach"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTitle.js:90
msgid "Search job position"
msgstr "Hľadať pracovnú pozíciu"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:112
#: src/components/topics/Content/TopicsList/Keyword/KeywordExtraQuery.js:37
#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:47
msgid "Search keywords in conjunction with the phrase"
msgstr "Hľadať v spojení s výrazom"

#: src/components/staff/admin/workspace/Workspace.js:172
#: src/components/staff/admin/user/User.js:100
msgid "Search log"
msgstr "Záznamy o vyhľadávaní"

#: src/components/medialist/forms/FormEditAuthor.js:371
#: src/components/medialist/forms/FormEditAuthor.js:514
#: src/components/medialist/content/FeedMedialist/FeedMedialistItem.js:181
msgid "Search on Google"
msgstr "Vyhľadať na Google"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:126
#: src/components/help/search/Content/RulesOperators.tsx:16
msgid "Search operators"
msgstr "Vyhľadávacie operátory"

#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:41
msgid "Search query"
msgstr "Vyhľadávací výraz"

#: src/store/models/Megalist/MegalistFilter.js:46
msgid "Search Results"
msgstr "Výsledky hľadania"

#: src/components/layout/MntrFiltersBar/forms/FormSearchSources/FormSearchSources.js:33
msgid "Search source or publisher"
msgstr "Vyhľadať zdroj alebo vydavateľstvo"

#. js-lingui-explicit-id
#: src/components/topics/Content/TopicsList/MegalistSearch.js:42
msgid "megalist.search"
msgstr "Vyhľadať zdroj alebo vydavateľstvo"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:122
msgid "Search users"
msgstr "Hľadať užívateľov"

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:25
msgid "Second Step"
msgstr "Druhý krok"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:311
msgid "Section"
msgstr "Rubrika"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:55
#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:79
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:30
#: src/components/misc/ActionsBar/Selector/Selector.js:29
#: src/components/misc/ActionsBar/Selector/Selector.js:70
msgid "Select"
msgstr "Vybrať"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:106
msgid "Select a method"
msgstr "Výber metódy"

#: src/helpers/modal/withModalAddArticle/ArticleBoxesPreview.tsx:31
msgid "Select a preview card for the newsroom article to include in the email"
msgstr "Vyberte náhľad článku z newsroomu, ktorý bude vložený do e-mailu"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:33
#: src/components/layout/MntrFiltersBar/modules/MenuFilterToggleAllButtons.js:59
msgid "Select all"
msgstr "Vybrať všetko"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:120
msgid "Select article"
msgstr "Vybrať článok"

#: src/components/misc/portable/PortableResend/PortableResend.js:118
#: src/components/misc/portable/PortableExport/PortableExport.js:113
msgid "Select articles to export."
msgstr "Vyberte články k exportu."

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:65
msgid "Select at least one mediatype"
msgstr "Vyberte aspoň jeden mediatyp"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterContent.tsx:67
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:52
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewSocialEngagement/PreviewSocialEngagement.js:32
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:51
#: src/components/analytics/AnalyticsContent.js:106
msgid "Select at least one topic"
msgstr "Vyberte aspoň jednu tému"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:73
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:85
msgid "Select campaign"
msgstr "Vyberte kampaň"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:41
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:48
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:54
msgid "Select category"
msgstr "Vyberte kategóriu"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:350
#: src/components/misc/ColorPicker/ColorPickerSelector.js:100
#: src/components/misc/ColorPicker/ColorPicker.js:61
#: src/components/misc/ColorPicker/ColorPicker.js:67
msgid "Select color"
msgstr "Vyberte farbu"

#: src/components/newsroom/forms/FormNewsroomPost/CoverImageUpload.js:85
msgid "Select Cover Image"
msgstr "Vybrať náhľadový obrázok"

#: src/components/topics/Content/TopicsList/KeywordExtraQueryAdvanced/KeywordExtraQueryAdvanced.tsx:25
msgid "Select from our list of predefined queries"
msgstr "Vybrať zo zoznamu predpripravených výrazov"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:130
#: src/components/settings/SettingsLogo/SettingsLogo.js:129
#: src/components/forms/adapters/MntrFileAdapter/MntrFileAdapter.js:58
msgid "Select Image"
msgstr "Vybrať obrázok"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:320
msgid "Select Logo"
msgstr "Vybrať logo"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:108
msgid "Select newsroom"
msgstr "Vyberte newsroom"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:75
msgid "Select pages"
msgstr "Zvoliť strany"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:96
msgid "Select sources"
msgstr "Vybrať zdroje"

#: src/components/emailing/forms/FormSenderSettings.js:116
msgid "Select the encryption method used by your SMTP server."
msgstr "Vyberte metódu šifrovania, ktorú používa váš SMTP server."

#: src/components/newsroom/components/AiTools/AiGenerateTitles.tsx:45
msgid "Select the title"
msgstr "Vyberte názov"

#: src/components/widgets/modules/stats/WidgetStats.js:79
#: src/components/widgets/modules/socialEngagement/WidgetSocialEngagement.js:41
#: src/components/widgets/modules/analytics/WidgetAnalytics.js:52
#: src/components/monitoring/Monitoring.js:166
msgid "Select Topic"
msgstr "Vyberte tému"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:85
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormSubtype/FormSubtype.js:81
msgid "Select type"
msgstr "Vyberte typ"

#: src/components/emailing/forms/FormSenderSettings.js:207
msgid "Select verification method"
msgstr "Výber metódy overenia"

#: src/helpers/charts/makeGranularityMenu.js:6
msgid "Select view"
msgstr "Vyberte zobrazenie"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:106
msgid "Select workspace"
msgstr "Vyberte workspace"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:79
msgid "Selected"
msgstr "Vybrané"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:122
msgid "Selected articles will be removed."
msgstr "Vybrané články budú odstránené."

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:96
msgid "Selected merge tags can not be applied to the author"
msgstr "Vybrané merge tagy nie je možné použiť na autora"

#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:188
msgid "Selected sources"
msgstr "Vybrané zdroje"

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:69
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:133
msgid "Selected: {selectedLength}/{MAX_SELECTED_LIMIT}"
msgstr "Vybrané: {selectedLength} z {MAX_SELECTED_LIMIT}"

#: src/store/models/Megalist/Megalist.js:376
msgid "Selection \"{name}\" was removed."
msgstr "Výber \"{name}\" bol odstránený."

#: src/store/models/Megalist/Megalist.js:335
#: src/store/models/Megalist/Megalist.js:354
msgid "Selection saved as \"{name}\"."
msgstr "Výber bol uložený ako \"{name}\"."

#: src/components/reports/history/Compose.js:84
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:198
#: src/components/forms/dashboard/ExportResend/ExportResend.js:179
#: src/components/exportList/Content/Content.tsx:86
#: src/components/emailing/content/CreateEmailContent.js:325
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:122
msgid "Send"
msgstr "Odoslať"

#: src/components/misc/portable/PortableResend/PortableResend.js:70
#: src/components/misc/portable/PortableResend/PortableResend.js:110
msgid "Send all articles to email"
msgstr "Odoslať všetky články na email"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:132
msgid "Send article"
msgstr "Odoslať článok"

#: src/components/misc/portable/PortableResend/PortableResend.js:66
#: src/components/misc/portable/PortableResend/PortableResend.js:106
msgid "Send article to email"
msgstr "Odoslať článok na email"

#: src/components/misc/portable/PortableResend/PortableResend.js:68
#: src/components/misc/portable/PortableResend/PortableResend.js:108
msgid "Send articles to email"
msgstr "Odoslať články na email"

#: src/components/reports/Content/ReportsList/ReportsForm.js:121
msgid "Send empty reports"
msgstr "Posielať aj prázdny report"

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:76
msgid "Send Feedback"
msgstr "Poslať spätnú väzbu"

#: src/components/reports/Content/ReportsList/ReportsForm.js:270
msgid "Send in times (optional)"
msgstr "Posielať iba v časoch (nepovinné)"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:150
msgid "Send now"
msgstr "Odoslať hneď"

#: src/components/reports/Content/ReportsList/ReportsForm.js:131
#: src/components/reports/Content/ReportsList/ReportsForm.js:218
msgid "Send on days"
msgstr "Dni posielania"

#: src/components/reports/Content/ReportsList/ReportsForm.js:155
msgid "Send on holidays"
msgstr "Posielať aj cez sviatky"

#: src/components/reports/Content/ReportsList/ReportsForm.js:166
msgid "Send on times"
msgstr "Časy posielania"

#: src/components/emailing/content/promo/PromoEmailing.js:28
msgid "Send press releases to journalists with one click."
msgstr "Odosielanie tlačových správ novinárom jedným kliknutím."

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:151
msgid "Send this email immediately"
msgstr "Odoslať email okamžite"

#: src/components/reports/history/RecipientsTableHeader.js:41
msgid "Send this to your IT specialist"
msgstr "Prepošlite chybovú správu Vašemu IT oddeleniu"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:58
#: src/components/emailing/content/EmailDetailEmailContent.js:17
#: src/components/emailing/content/CreateEmailContent.js:418
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:135
msgid "Sender"
msgstr "Odosielateľ"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:186
msgid "Senders"
msgstr "Odosielatielia"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:58
msgid "Sending a generic email without an attached article provides no useful data for tracking"
msgstr "Odoslanie všeobecného e-mailu bez priloženého článku neposkytuje žiadne užitočné údaje na sledovanie"

#: src/components/reports/history/HistoryTable.js:223
msgid "Sent automatically via scheduled report"
msgstr "Poslaný automaticky"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:69
msgid "Sent to"
msgstr "Odoslaná na"

#: src/constants/analytics.js:812
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:101
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Sentiment.js:30
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:349
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:61
#: src/components/OurChart/OurChartAdvanced.js:108
msgid "Sentiment"
msgstr "Sentiment"

#: src/components/emailing/forms/FormEmailRecipients.js:107
msgid "Separate emails with a space, comma, or semicolon"
msgstr "Oddeľte emaily medzerou, čiarkou alebo bodkočiarkou"

#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:50
msgid "Separate regional duplicates"
msgstr "Nezlučovať regionálne duplicity"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:57
#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:60
msgid "Separated by space, newline, comma, or semicolon."
msgstr "Oddelené medzerou, novým riadkom, čiarkou alebo bodkočiarkou."

#: src/components/newsroom/content/dashboard/ChartVisits.js:110
msgid "Sessions"
msgstr "Návštev"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:117
msgid "Set annotation"
msgstr "Zvoliť anotáciu"

#: src/components/layout/AuthWrapper/constants/features.slides.js:354
msgid "Set any number of reports that will be sent to any number of contacts. Everyone receives the correct info at the right time and in the format you choose."
msgstr "Nastavte si ľubovoľný počet reportov, ktoré sa budú posielať na ľubovoľné množstvo kontaktov. Každý tak dostane správne informácie v správnom čase a vo vami zvolenom formáte."

#: src/components/emailing/content/CreateEmailContent.js:372
msgid "Set as Draft"
msgstr "Nastaviť ako Návrh"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:93
msgid "Set as primary"
msgstr "Nastaviť ako primárny"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:163
msgid "Set automatic publishing of this post"
msgstr "Nastavenie automatického zverejnenia tohto príspevku"

#: src/components/staff/admin/workspace/UsersTable/UpdatePermissions.tsx:36
#: src/components/staff/admin/workspace/UsersTable/UpdatePermissions.tsx:40
msgid "Set permissions"
msgstr "Nastaviť oprávnenia"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:96
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:162
msgid "Set publish date"
msgstr "Nastaviť dátum zverejnenia"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:123
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:158
msgid "Set send date"
msgstr "Nastaviť dátum odoslania"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:229
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:233
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:339
msgid "Set sentiment"
msgstr "Nastaviť sentiment"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:159
msgid "Set this email to auto-send"
msgstr "Nastaviť automatické odoslanie e-mailu"

#: src/pages/user/settings.js:19
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:387
#: src/components/newsroom/content/posts/NewsroomPosts.js:293
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:57
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:22
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:48
#: src/components/layout/Header/UserMenu/UserMenu.tsx:176
#: src/components/emailing/sidebar/EmailingSidebarDashboard.js:27
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:76
#: src/components/emailing/forms/FormSenderSettings.js:252
#: src/components/emailing/forms/FormSenderSettings.js:283
#: src/components/emailing/content/EmailingSettingsContent.js:54
#: src/components/emailing/content/EmailingCampaignsContent.tsx:40
msgid "Settings"
msgstr "Nastavenie"

#. placeholder {0}: model.name
#: src/store/models/ResendSettings.ts:38
#: src/store/models/ExportSettings.js:26
msgid "Settings \"{0}\" was applied."
msgstr "Použité nastavenie \"{0}\"."

#. placeholder {0}: exportSettings.name
#: src/store/models/ResendSettings.ts:114
#: src/store/models/ExportSettings.js:80
msgid "Settings \"{0}\" was removed."
msgstr "Nastavenie \"{0}\" bolo zmazané."

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:21
msgid "Settings complete"
msgstr "Nastavenie dokončené"

#. placeholder {0}: model.name
#. placeholder {0}: exportSettings.name
#: src/store/models/ResendSettings.ts:93
#: src/store/models/ResendSettings.ts:132
#: src/store/models/ExportSettings.js:62
#: src/store/models/ExportSettings.js:96
msgid "Settings saved as \"{0}\"."
msgstr "Nastavenie bolo uložené ako \"{0}\"."

#: src/store/models/topics/TopicsStore.js:229
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:197
msgid "Settings saved."
msgstr "Nastavenie uložené."

#: src/components/dashboards/Content.js:75
#: src/components/dashboards/Content.js:76
#: src/components/dashboards/DashboardSelector/DashboardSelector.js:69
msgid "Share"
msgstr "Zdieľať"

#: src/constants/analytics.js:698
#: src/constants/analytics.js:979
#: src/constants/analytics.js:993
#: src/constants/analytics.js:1009
#: src/constants/analytics.js:1024
#: src/constants/analytics.js:1039
msgid "Share of voice"
msgstr "Share of voice"

#: src/store/models/dashboards/Dashboards.js:523
msgid "Shared link will expire"
msgstr "Platnosť zdieľaného odkazu vyprší"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:107
#: src/components/medialist/content/MedialistDashboard.js:135
msgid "Show"
msgstr "Zobraziť"

#. placeholder {0}: format.formatAttachedArticles(item.mentioned_article_count)
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:191
msgid "Show {0}"
msgstr "Ukázať {0}"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:14
#: src/components/layout/Header/AppNotifications/AppNotifications.js:186
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:95
#: src/components/OurChart/OurChartAdvanced.js:116
msgid "Show All"
msgstr "Zobraziť všetko"

#. placeholder {0}: filteredRecipients.length - displayLimit
#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:130
msgid "Show all recipients (+{0})"
msgstr "Zobraziť všetkých príjemcov (+{0})"

#: src/components/medialist/forms/FormEditAuthor.js:772
#: src/components/medialist/forms/FormEditAuthor.js:800
msgid "Show and copy to clipboard"
msgstr "Zobraziť a skopírovať do schránky"

#: src/components/reports/history/HistoryTable.js:397
#: src/components/exportList/History/HistoryTable/HistoryTable.js:86
msgid "Show articles"
msgstr "Zobraziť články"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:123
msgid "Show articles in feed"
msgstr "Zobraziť v sekcii Príspevky"

#: src/components/medialist/forms/FormEditAuthor.js:343
#: src/components/medialist/forms/FormEditAuthor.js:351
#: src/components/medialist/forms/FormEditAuthor.js:424
#: src/components/medialist/forms/FormEditAuthor.js:433
#: src/components/medialist/content/MedialistDashboard.js:99
msgid "Show authors"
msgstr "Zobraziť autorov"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:133
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:28
msgid "Show changes"
msgstr "Zobraziť zmeny"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:23
msgid "Show checked only"
msgstr "Zobraziť iba zaškrtnuté"

#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:21
msgid "Show history for this report"
msgstr "Zobraziť odoslané reporty"

#: src/components/monitoring/Inspector/DemographicsData/modules/EntriesList/EntriesListContent.js:93
msgid "Show less"
msgstr "Skryť"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:95
msgid "Show Less"
msgstr "Zobraziť menej"

#: src/components/monitoring/Inspector/DemographicsData/modules/EntriesList/EntriesListContent.js:93
#: src/components/medialist/content/MedialistDashboard.js:162
#: src/components/medialist/content/MedialistDashboard.js:183
msgid "Show more"
msgstr "Zobraziť ďalšie"

#: src/components/medialist/forms/FormEditAuthor.js:356
#: src/components/medialist/forms/FormEditAuthor.js:439
msgid "Show newsrooms"
msgstr "Zobraziť redakcie"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:65
msgid "show stats"
msgstr "zobraziť štatistiku"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:32
msgid "Show unchecked only"
msgstr "Zobraziť iba nezaškrtnuté"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:52
msgid "showing {counterFrom} out of {counterTo}"
msgstr "zobrazených {counterFrom} z {counterTo}"

#: src/pages/sign-up.tsx:11
#: src/pages/sign-up-completion.tsx:30
#: src/pages/staff/sign-up.js:11
#: src/pages/staff/sign-up-completion.js:26
#: src/components/staff/SignUp.js:30
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:174
#: src/components/page/auth/SignUp/SignUp.js:74
msgid "Sign Up"
msgstr "Registrovať"

#: src/components/emailing/content/Signature.tsx:104
msgid "Signature"
msgstr "Podpis"

#: src/components/layout/MntrActiveFilters/modules/SimilarArticle.js:12
msgid "Similar to"
msgstr "Podobné článku"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:62
msgid "Similarity"
msgstr "Podobnosť"

#: src/components/emailing/content/promo/PromoEmailing.js:23
msgid "Simple setting of the appearance of the email template."
msgstr "Jednoduché nastavenie vzhľadu emailu."

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:124
msgid "Skip"
msgstr "Preskočiť"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:60
msgid "Skip error lines"
msgstr "Preskočiť chybné riadky"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:249
msgid "SMS alerts"
msgstr "SMS upozornenia"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:134
msgid "SMTP settings are invalid."
msgstr "SMTP nie je nastavené správne."

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:132
msgid "SMTP settings are valid."
msgstr "SMTP je nastavené správne."

#: src/components/misc/ActionsBar/View/ViewMenu.js:269
#: src/components/misc/ActionsBar/View/ViewMenu.js:353
msgid "Social data"
msgstr "Sociálne dáta"

#: src/store/models/dashboards/DashboardPreview.js:110
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:51
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:51
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:32
msgid "Social Engagement"
msgstr "Sociálne interakcie"

#: src/constants/analytics.js:198
#: src/constants/analytics.js:289
#: src/constants/analytics.js:398
#: src/constants/analytics.js:1040
#: src/components/misc/ActionsBar/View/ViewMenu.js:93
msgid "Social interactions"
msgstr "Sociálne interakcie"

#: src/constants/stats.ts:31
#: src/components/widgets/modules/stats/WidgetStats.js:143
msgid "Social Interactions"
msgstr "Sociálne interakcie"

#: src/constants/analytics.js:309
#: src/constants/analytics.js:548
msgid "Social interactions by mention type"
msgstr "Sociálne interakcie podľa typu zmienky"

#: src/constants/analytics.js:307
msgid "Social interactions by sentiment"
msgstr "Sociálne interakcie podľa sentimentu"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:179
#: src/components/staff/admin/workspace/Workspace.js:534
#: src/components/medialist/forms/FormEditAuthor.js:819
#: src/components/medialist/forms/FormEditAuthor.js:922
#: src/components/layout/AuthWrapper/constants/features.slides.js:87
#: src/components/exportList/ExportLimit/ExportLimit.js:28
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:29
#: src/components/analytics/AnalyticsContent.js:149
#: src/components/analytics/AnalyticsContent.js:228
msgid "Social Media"
msgstr "Sociálne médiá"

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:37
msgid "Social Media in {appName}"
msgstr "Sociálne médiá v aplikácii {appName}"

#: src/components/tariff/TariffLimits/TariffLimits.js:186
msgid "Social media topics limit"
msgstr "Počet tém (soc. médiá)"

#: src/components/staff/admin/workspace/Workspace.js:578
msgid "Social media topics limit (Sentione price = 500 Kč per topic)"
msgstr "Limit na počet tém (soc. médiá) (Sentione náklad = 20 Eur/téma)"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:147
msgid "Social post"
msgstr "Príspevok na sociálnej sieti"

#: src/components/medialist/content/AuthorContactInformation.js:38
msgid "Social profiles"
msgstr "Sociálne profily"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:56
#: src/components/misc/ActionsBar/View/ViewMenu.js:124
msgid "Sold amount"
msgstr "Predaný náklad"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:173
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:85
msgid "Sold amount (print+digital)"
msgstr "Predané (tlač+el.)"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:652
msgid "Some articles may not be deleted."
msgstr "Niektoré články tak možno neboli odstránené."

#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:155
msgid "Some data are missing in the generated content. Add them manually before proceeding."
msgstr "Vo vygenerovanom obsahu chýbajú niektoré údaje. Pred pokračovaním ich doplňte ručne."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:23
msgid "Some recipients are missing information for merge tags or email. Please add the missing information or replace the recipients by clicking on them."
msgstr "U niektorých príjemcov chýba informácia pre merge tagy nebo e-mail. Chýbajúce informácie doplňte nebo príjemcu nahraďte kliknutím."

#: src/helpers/store/apiClient.js:249
msgid "Something failed while preparing a server request. Our team was notified."
msgstr "Pri spracovaní požiadavky nastala chyba. Náš tím bol informovaný."

#: src/pages/_error.js:44
msgid "Something's gone wrong"
msgstr "Došlo k chybe"

#: src/components/misc/ActionsBar/Sort/SortExport.js:19
#: src/components/misc/ActionsBar/Sort/Sort.js:21
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:468
#: src/components/layout/MntrFiltersBar/modules/MenuFilterOrderBy.js:23
msgid "Sort"
msgstr "Zoradiť"

#: src/components/misc/ActionsBar/Sort/SortExport.js:25
#: src/components/misc/ActionsBar/Sort/Sort.js:38
msgid "Sort List"
msgstr "Zoradiť zoznam"

#: src/components/tariff/AgencyMedia/AgencyMedia.js:64
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:371
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:205
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:226
#: src/components/layout/MntrActiveFilters/modules/NewsSource.js:13
#: src/components/exportList/History/HistoryTable/HistoryTable.js:54
msgid "Source"
msgstr "Zdroj"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:326
msgid "Source <0>{newsSourceName}</0> will be removed from the topic <1>{topicMonitorName}</1>."
msgstr "Zdroj <0>{newsSourceName}</0> bude odobraný z témy <1>{topicMonitorName}</1>."

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:37
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:456
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:510
msgid "Source File"
msgstr "Zdrojový súbor"

#: src/store/models/monitoring/Inspector/Inspector.ts:532
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:885
msgid "Source removed"
msgstr "Zdroj bol odstránený"

#: src/components/staff/admin/workspace/Workspace.js:892
msgid "Sources"
msgstr "Zdroje"

#: src/components/staff/admin/workspace/Workspace.js:894
#: src/components/staff/admin/workspace/Workspace.js:901
msgid "Sources per client"
msgstr "Zdroje per klient"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:233
msgid "Special tag"
msgstr "Špeciálny tag"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:47
msgid "Specify the primary goal (inform, persuade, invite, etc.)."
msgstr "Uveďte hlavný cieľ (informovať, presvedčiť, pozvať atď.)."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:137
msgid "Spokesperson"
msgstr "Tlačový hovorca"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:57
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:194
msgid "Start editing"
msgstr "Začať upravovať"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:75
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:213
msgid "Start over"
msgstr "Začať odznova"

#: src/components/forms/inspector/FormMediaEditor.js:82
msgid "Start time must be lower than end time"
msgstr "Čas začiatku musí byť nižší ako čas konca"

#: src/components/emailing/content/CreateEmailContent.js:584
msgid "Start typing or click + to add more content"
msgstr "Začnite písať alebo kliknite na + pre pridanie ďalšieho obsahu"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:681
msgid "Start typing or insert image, video…"
msgstr "Začnite písať alebo vložte obrázok, video…"

#: src/components/newsroom/content/posts/ChooseTemplates.tsx:76
msgid "Start with template"
msgstr "Začať so šablónou"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:119
#: src/components/staff/admin/user/WorkspacesTable.js:77
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:80
#: src/components/staff/admin/customer/users/UsersTable.js:71
msgid "State"
msgstr "Stav"

#: src/store/models/dashboards/DashboardPreview.js:121
#: src/components/emailing/content/promo/PromoEmailing.js:32
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:23
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:23
#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:44
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:54
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:26
msgid "Statistics"
msgstr "Štatistiky"

#: src/components/reports/history/RecipientsTableHeader.js:33
#: src/components/newsroom/content/posts/NewsroomPosts.js:166
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:27
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:407
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNewsroomStatus.js:24
#: src/components/layout/MntrActiveFilters/modules/NewsroomStatus.js:21
msgid "Status"
msgstr "Stav"

#: src/components/reports/history/HistoryTable.js:80
#: src/components/reports/history/HistoryTable.js:104
#: src/components/reports/history/HistoryTable.js:322
msgid "Status unknown"
msgstr "Stav neznámy"

#: src/components/layout/AuthWrapper/constants/features.slides.js:260
msgid "Streamline communication efforts and maximize your PR impact."
msgstr "Komunikujte s médiami ľahko a efektívne a zvýšte účinnosť vašeho PR."

#: src/components/reports/history/HistoryTable.js:149
#: src/components/reports/history/Compose.js:62
#: src/components/forms/dashboard/ExportResend/ExportResend.js:102
#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:92
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:86
#: src/components/emailing/content/EmailDetailEmailContent.js:27
#: src/components/emailing/content/CreateEmailContent.js:446
msgid "Subject"
msgstr "Predmet"

#: src/components/misc/MntrForm/MntrForm.tsx:525
msgid "Submit"
msgstr "Potvrdiť"

#: src/constants/stats.ts:36
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:79
#: src/components/forms/dashboard/ExportResend/ExportResend.js:124
msgid "Summary"
msgstr "Zhrnutie"

#: src/components/staff/admin/customer/expenses/DetailExpenseModal.js:35
msgid "Supplier"
msgstr "Dodávateľ"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:210
msgid "Support"
msgstr "Podpora"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:472
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:538
msgid "Supported file types:"
msgstr "Podporované typy súborov:"

#. placeholder {0}: item.label
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:245
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:157
msgid "Tag <0>{0}</0> will be hidden."
msgstr "Štítok <0>{0}</0> bude skrytý."

#. placeholder {0}: item.label
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:315
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:205
msgid "Tag <0>{0}</0> will be removed."
msgstr "Štítok <0>{0}</0> bude odstránený."

#: src/components/forms/tags/FormNewTag/FormNewTag.js:26
#: src/components/forms/tags/FormEditTag/FormEditTag.js:25
#: src/components/forms/tags/FormEditTag/FormEditTag.js:28
msgid "Tag name"
msgstr "Názov štítku"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:761
#: src/store/models/authors/AuthorsStore.js:716
msgid "Tag removed successfully."
msgstr "Štítok bol odstránený."

#: src/constants/analytics.js:845
#: src/constants/analytics.js:1069
#: src/components/medialist/forms/FormEditAuthor.js:616
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:90
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:79
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:72
#: src/components/emailing/content/tabs/AddRecipients.tsx:70
msgid "Tags"
msgstr "Štítky"

#: src/components/medialist/forms/FormEditAuthor.js:610
msgid "Tags, lists and note"
msgstr "Štítky, zoznamy a poznámka"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:41
msgid "Target Audience:"
msgstr "Cieľová skupina:"

#: src/components/settings/SettingsTariff/SettingsTariff.js:22
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:27
msgid "Tariff information"
msgstr "Informácie o tarife"

#: src/components/reports/Content/ReportsList/ReportsForm.js:317
#: src/components/forms/dashboard/ExportResend/ExportResend.js:107
msgid "Template"
msgstr "Šablóna"

#: src/components/emailing/forms/FormSenderSettings.js:167
msgid "Test DNS Settings"
msgstr "Otestovať nastavenie DNS"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:377
msgid "Text"
msgstr "Text"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:207
msgid "Text align"
msgstr "Zarovnanie textu"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:281
#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:76
#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:36
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:113
msgid "Text Color"
msgstr "Farba textu"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:155
msgid "Text format"
msgstr "Formát textu"

#: src/components/page/auth/Expired/Expired.js:60
msgid "Thank you for trying out {appName}."
msgstr "Ďakujeme za využitie aplikácie {appName}."

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:91
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:113
msgid "Thank you for your feedback!"
msgstr "Ďakujeme vám za spätnú väzbu!"

#: src/components/page/auth/UserInactive/UserInactive.js:17
msgid "Thank you for your interest in using {appName}."
msgstr "Ďakujeme za váš záujem o aplikáciu {appName}."

#: src/pages/user/yoy-analysis.js:67
#: src/pages/user/reactivate-24.js:67
msgid "Thank you for your interest. We will contact you soon.<0/><1/>Have a great day,<2/><3/>{appName} team"
msgstr "Ďakujeme vám za váš záujem. Budeme vás čoskoro kontaktovať.<0/><1/>Krásny deň,<2/><3/>tím {appName}"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:100
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:131
msgid "Thank you!"
msgstr "Ďakujeme!"

#: src/store/models/monitoring/Inspector/Inspector.ts:910
msgid "The article already belongs to the topic."
msgstr "Článok už patrí pod tému."

#: src/store/models/monitoring/WorkspaceArticles.js:164
msgid "The article has been uploaded and is currently being processed. After that it will be added to your feed. You can see the processing status in My Articles."
msgstr "Článok bol úspešne nahraný a prebieha jeho spracovanie. Následne bude pridaný do sekcie Príspevky. Stav spracovania môžete vidieť v sekcii Moje články."

#: src/store/models/monitoring/Inspector/Inspector.ts:907
msgid "The article was added to the topic. Please reload the feed to see the changes."
msgstr "Článok bol pridaný pod tému. Pre jeho zobrazenie načítajte znovu stránku. Ak ho nevidíte, skontrolujte nastavenie filtrácie."

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:392
msgid "The article will be removed from your media coverage view. If the article also exists in your feed, it will remain there and will not be deleted."
msgstr "Článok bude odstránený z vášho prehľadu mediálneho pokrytia. Ak sa článok nachádza aj vo vašom feede, zostane tam a nebude vymazaný."

#: src/components/medialist/forms/FormEditAuthor.js:299
#: src/components/medialist/forms/FormEditAuthor.js:463
msgid "The author's profile will be reset to its original values."
msgstr "Profil autora sa obnoví na pôvodné hodnoty."

#: src/store/models/monitoring/Inspector/MediaEditor/MediaEditorStore.js:55
msgid "The clip is being prepared. It may take a while. When the clip is ready for download, you will receive a notification."
msgstr "Klip sa pripravuje. Môže to chvíľu trvať. Keď bude klip pripravený na stiahnutie, obdržíte upozornenie."

#: src/components/emailing/forms/FormSenderSettings.js:239
msgid "The DNS Verification Is Unavailable"
msgstr "Overenie pomocou DNS nie je k dispozícii"

#: src/components/emailing/forms/FormSenderSettings.js:240
msgid "The DNS verification settings for this email are not accessible. We suggest opting for SMTP (Simple Mail Transfer Protocol) as an alternative way of verification. If you need any additional information or help, our support team is here to assist you."
msgstr "Overenie DNS nie je pre tento email k dispozícii. Ako alternatívnu metódu overovania odporúčame zvoliť SMTP (Simple Mail Transfer Protocol). Ak potrebujete ďalšie informácie alebo pomoc, obráťte sa na našu podporu."

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:37
msgid "The email content can be automatically adjusted to include personalized details for each recipient"
msgstr "Obsah e-mailu sa môže automaticky upraviť tak, aby obsahoval personalizované údaje pre každého príjemcu."

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:113
msgid "The email is currently empty. Please add some content to the email."
msgstr "Email je v súčasnosti prázdny. Prosím, pridajte do emailu nejaký obsah."

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:28
msgid "The following summary was generated by a machine and may not accurately represent the original content."
msgstr "Nasledujúce zhrnutie bolo vytvorené strojovo a nemusí presne odrážať pôvodný obsah."

#: src/store/models/ExportStore.js:251
msgid "The full article text cannot be downloaded as you have reached your limit. To adjust this limit, please contact support."
msgstr "Celý text článku sa nedá stiahnuť, pretože ste dosiahli svoj limit. Ak chcete upraviť tento limit, kontaktujte podporu."

#: src/components/tariff/TariffLimits/TariffLimits.js:31
msgid "The limit applies to the number of articles found in the last 30 days generated by set keywords. If you have reached the limit for the number of found articles, <0>edit keywords</0> or contact us to increase the limit."
msgstr "Limit sa vzťahuje na počet nájdených článkov za posledných 30 dní, ktoré vygenerovali nastavené kľúčové slová v Témach. Ak ste dosiahli limit na počet nájdených článkov, <0>upravte kľúčové slová</0> alebo nás kontaktujte pre navýšenie limitu."

#: src/components/tariff/TariffLimits/TariffLimits.js:68
msgid "The limit applies to the number of exported articles in the last 30 days (topics, archive or report attachments). If you have reached the limit for the number of exported articles, you must wait until the limit is restored or contact us to increase the limit."
msgstr "Limit sa vzťahuje na počet exportovaných článkov za posledných 30 dní (v rámci tém, archívu alebo príloh reportu). Ak ste dosiahli limit na počet exportovaných člákov, je nutné počkať, než sa limit obnoví, vzhľadom na Vaše predchádzajúce exportovanie. Alebo nás kontaktujte pre navýšenie limitu."

#: src/components/tariff/TariffLimits/TariffLimits.js:104
msgid "The limit applies to the number of translated articles in the last 30 days (topics, archive, report or report attachments). If you are interested in increasing this limit, please contact us."
msgstr "Limit sa vzťahuje na počet preložených článkov za posledných 30 dní (v rámci tém, archívu, reportov alebo príloh reportov). V prípade záujmu o navýšenie tohto limitu nás kontaktujte."

#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:20
msgid "The list of already exported articles can be downloaded without limitation."
msgstr "Zoznam už exportovaných článkov si môžete bez obmedzení opäť stiahnuť."

#: src/store/models/authors/Baskets/AuthorBasketDefinitionsStoreArrItem.ts:54
msgid "The list was successfully duplicated."
msgstr "Zoznam bol úspešne duplikovaný."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:84
msgid "The main content of your post appears to be empty. The body is where you elaborate on your ideas, present your arguments, or share your story. Please add substantial content to your post to engage your readers and convey your message effectively."
msgstr "Zdá sa, že hlavný obsah vášho príspevku je prázdny. Telo článku je miesto, kde rozvíjate svoje nápady, prezentujete svoje argumenty alebo zdieľate svoj príbeh. Pridajte do svojho príspevku podstatný obsah, aby ste zaujali svojich čitateľov a efektívne odovzdali svoje posolstvo."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:20
msgid "help.grp"
msgstr "Mediálny dopad má za cieľ lepšie než absolútne počty článkov prezentovať skutočný mediálny obraz sledovaného subjektu tak, ako sa dostáva k najširšej skupine posluchačov, divákov a čítateľov médií. Vychádza predovšetkým z čítanosti (tlač), počúvanosti (rozhlas), sledovanosti (TV) a mesačnej návštevnosti webu (online). Jednotkou merania mediálneho dopadu sú GRP body (Gross Rating Points), pričom jeden GRP bod zodpovedá jednému percentu populácie starej aspoň pätnásť rokov (napr. pre SR skupine 45 000 jedincov, pre ČR 90 000 jedincov). Jedná sa o čítateľov, posluchačov, alebo divákov, ktorí mohli byť publikovaným príspevkom oslovení. Čítateľ, ktorý mohol prečítať viac ako jeden príspevok, je započítaný viackrát. OTS (Opportunity to See) znamená, koľkokrát mal príslušník cieľovej skupiny priemerne možnosť príspevok prečítať, alebo vidieť. V prípade cieľovej skupiny všetkých obyvateľov SR starých aspoň pätnásť rokov sa: OTS = GRP / 100."

#: src/pages/authors/index.js:43
msgid "The most <0>extensive</0> and the most <1>actual</1> medialist of journalists, publishers & other authors, in which you will find detailed information including contacts."
msgstr "<0>Najrozsiahlejší</0> a <1>najaktuálnejší</1> medialist novinárov, vydavateľov a ďalších autorov, v ktorom nájdete podrobné informácie vrátane kontaktov."

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:31
msgid "The primary sender is used as the default sender for emails. You can change this when you create an email."
msgstr "Primárny odosielateľ bude použitý ako predvolený odosielateľ pri vytváraní emailu. Odosielateľa môžete zmeniť aj pri odosielaní emailu."

#. placeholder {0}: senderItem.unverified_recipients_limit
#. placeholder {1}: senderItem.verified_recipients_limit
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:68
msgid "The recipient limit is set to {0}. For a higher limit of {1} recipients, enable DNS or SMTP verification."
msgstr "Je nastavený limit na počet príjemcov na {0}. Pre vyšší limit {1} príjemcov nastavte overovanie pomocou DNS alebo SMTP."

#. placeholder {0}: appSettings.appName
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:136
msgid "The summary was created with the {0} application."
msgstr "Zhrnutie bolo vytvorené strojovo aplikáciou {0}."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:493
msgid "The user is responsible for the content uploaded to the {appName} application. By uploading files, you confirm that you own the rights to the file or that the file is licensed under <0>CC0</0>."
msgstr "Za obsah nahraný do aplikácie {appName} je zodpovedný užívateľ. Nahraním súboru potvrdzujete, že k nemu vlastníte práva na používanie alebo že súbor podlieha licencii <0>CC0</0>."

#: src/components/layout/Header/UserMenu/UserMenu.tsx:137
msgid "Theme"
msgstr "Vzhľad"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:81
msgid "There are no keywords assigned to this topic"
msgstr "K téme nie sú priradené žiadne kľúčové slová"

#: src/pages/404.js:18
#: src/app/not-found-content.tsx:27
msgid "There's nothing here..."
msgstr "Bok bok, bakaw..."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:107
msgid "These settings allow not only change language of the newsroom, but to link newsrooms together. Pair them in different languages for quick and seamless transitions."
msgstr "Tieto nastavenia umožňujú zmeniť jazyk newsroomu a prepojiť newsroomy navzájom. Prepojte ich v rôznych jazykoch pre rýchle a bezproblémové prechody."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:184
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:229
msgid "This field is required"
msgstr "Tento údaj je povinný"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:222
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:305
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:418
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:75
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:78
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:83
msgid "This field is required."
msgstr "Toto pole je povinné."

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:380
msgid "This is a summary of the page's content. It appears below the headline on the search results page."
msgstr "Toto je zhrnutie obsahu stránky. Zobrazuje sa pod titulkou na stránke s výsledkami vyhľadávania."

#: src/components/emailing/forms/FormSenderSettings.js:109
msgid "This is the port number that your SMTP server uses to send email. If you're not sure, leave it blank to use the default port."
msgstr "Toto je číslo portu, ktoré váš server SMTP používa na odosielanie emailov. Ak si nie ste istí, nechajte ho prázdne a použije sa predvolený port."

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:134
msgid "This list is empty"
msgstr "Zoznam je prázdny"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:65
msgid "This month"
msgstr "Tento mesiac"

#: src/components/misc/Capture/Capture.js:300
msgid "this should take only a couple of seconds"
msgstr "operácia potrvá niekoľko sekúnd"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:269
msgid "This template was custom tailored for you. For further customization please contact our <0>support</0>."
msgstr "Táto šablóna vám bola vytvorená na mieru. Pre jej úpravu potrebujete kontaktovať našu <0>podporu</0>."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:52
msgid "This week"
msgstr "Tento týždeň"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:78
msgid "This year"
msgstr "Tento rok"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:151
msgid "Threshold"
msgstr "Relevancia (%)"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:192
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:316
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:320
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:70
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:99
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:190
msgid "Time"
msgstr "Čas"

#: src/components/forms/inspector/FormMediaEditor.js:85
#: src/components/forms/inspector/FormMediaEditor.js:88
msgid "Time must not exceed media length"
msgstr "Čas nesmie presiahnuť dĺžku média"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:70
msgid "Timed"
msgstr "Časovaný"

#: src/constants/analytics.js:54
#: src/constants/analytics.js:78
#: src/constants/analytics.js:100
#: src/constants/analytics.js:122
#: src/constants/analytics.js:142
#: src/constants/analytics.js:191
#: src/constants/analytics.js:226
#: src/constants/analytics.js:255
#: src/constants/analytics.js:282
#: src/constants/analytics.js:308
#: src/constants/analytics.js:335
#: src/constants/analytics.js:364
#: src/constants/analytics.js:391
#: src/constants/analytics.js:417
#: src/constants/analytics.js:444
#: src/constants/analytics.js:482
#: src/constants/analytics.js:510
#: src/constants/analytics/primeScoreCharts.ts:30
#: src/constants/analytics/primeScoreCharts.ts:56
msgid "Timeline"
msgstr "Timeline"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:51
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:506
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:507
#: src/components/newsroom/content/posts/NewsroomPosts.js:156
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:34
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:218
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormWidgetTitle/FormWidgetTitle.js:17
msgid "Title"
msgstr "Nadpis"

#. js-lingui-explicit-id
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:362
msgid "metadata.title"
msgstr "Titulka"

#: src/components/reports/Content/ReportsList/ReportsForm.js:289
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:85
msgid "To"
msgstr "Do"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:33
msgid "To change your password, enter your current password and then the new password."
msgstr "Pre zmenu hesla zadajte Vaše súčasné heslo a následne nové heslo."

#: src/components/emailing/forms/FormSenderSettings.js:286
msgid "To ensure the successful delivery of emails from our system, it's necessary to configure your SMTP server with the following details:"
msgstr "Pre zaistenie úspešného doručovania emailov z nášho systému je potrebné nakonfigurovať váš server SMTP s nasledujúcimi údajmi:"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:27
msgid "To hide some tags from the list, uncheck these tags. The user can add hidden tags back to their feed again at any time if necessary."
msgstr "Ak chcete niektoré štítky skryť zo zoznamu, zrušte ich začiarknutie. Používateľ môže kedykoľvek opäť pridať skryté štítky späť do svojho prehľadu."

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:100
msgid "To hide some topics from the list, uncheck these topics. The user can add hidden topics back to their feed again at any time if necessary."
msgstr "Ak chcete niektoré témy skryť zo zoznamu, zrušte ich začiarknutie. Používateľ môže kedykoľvek opäť pridať skryté témy späť do svojho prehľadu."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:492
msgid "To set up your own domain (e.g. companyname.com), please contact our team. We will be happy to help you set up your domain. We have also written a detailed guide for you."
msgstr "Pre nastavenie vlastnej domény (napr. blog.firma.sk) kontaktujte, prosím, náš tím. Radi vám pomôžeme s nastavením domény."

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:39
msgid "To view all mentions, it is necessary to activate social media monitoring."
msgstr "Pre zobrazenie všetkých zmienok je potrebné aktivovať monitoring sociálnych médií."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/metaDataDate.js:19
msgid "today"
msgstr "dnes"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:110
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:39
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:100
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:327
msgid "Today"
msgstr "Dnes"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:51
msgid "Tone and Style:"
msgstr "Tón a štýl:"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:88
msgid "Tone of voice"
msgstr "Tón článku"

#: src/constants/analytics.js:1126
msgid "Top authors"
msgstr "Top autori"

#: src/constants/analytics.js:1228
msgid "Top hashtags"
msgstr "Top hashtagy"

#: src/constants/analytics.js:1206
msgid "Top profiles"
msgstr "Top profily"

#: src/constants/analytics.js:1248
msgid "Top publishers"
msgstr "Najčastejší vydavatelia"

#: src/constants/analytics.js:1268
msgid "Top sources"
msgstr "Najčastejšie zdroje"

#: src/constants/analytics/primeScoreCharts.ts:136
msgid "Top sources by overall PRIMe"
msgstr "Zdroje podľa celkového PRIMe"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:200
msgid "Top stories"
msgstr "Top správy"

#: src/helpers/charts/tableTemplates.js:74
#: src/components/exportList/History/HistoryTable/HistoryTable.js:57
msgid "Topic"
msgstr "Téma"

#. placeholder {0}: item.data.name
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:206
msgid "Topic <0>{0}</0> will be hidden."
msgstr "Téma <0>{0}</0> bude skrytá."

#. placeholder {0}: item.data.name
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:147
msgid "Topic <0>{0}</0> will be removed."
msgstr "Téma <0>{0}</0> bude odstránená."

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:147
msgid "Topic Name"
msgstr "Názov témy"

#: src/pages/topics/index.js:24
#: src/components/topics/Content/TopicChangelog.js:18
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:113
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:253
#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:26
#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:80
#: src/components/notifications/ContentTopics.js:29
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:32
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:33
#: src/components/layout/MntrActiveFilters/modules/TvrTopics.js:10
#: src/components/layout/MntrActiveFilters/modules/EmptyTopics.js:21
#: src/app/components/monitoring-navigation.tsx:154
msgid "Topics"
msgstr "Témy"

#: src/components/topics/Content/TopicsHeading/TopicsHeading.js:10
msgid "Topics ({counter})"
msgstr "Témy ({counter})"

#. placeholder {0}: menuItem.topic_monitors .map((item) => { // @ts-expect-error TODO refactor topics to TS return item.label }) .join(', ')
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:233
msgid "Topics <0>{0}</0> will be hidden."
msgstr "Témy <0>{0}</0> budú skryté."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:142
msgid "Topics and keywords"
msgstr "Témy a kľúčové slová"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:291
msgid "Topics in this folder will be displayed separately and won't be deleted."
msgstr "Témy v tomto priečinku nebudú odstránené, ale zobrazia sa samostatne."

#: src/components/tariff/TariffLimits/TariffLimits.js:167
#: src/components/staff/admin/workspace/Workspace.js:439
msgid "Topics limit"
msgstr "Počet tém"

#: src/components/monitoring/Inspector/InspectorMonitora/KeywordsPagination/KeywordsPagination.js:304
msgid "total"
msgstr "celkovo"

#: src/helpers/charts/tableTemplates.js:55
#: src/helpers/charts/tableTemplates.js:97
#: src/helpers/charts/tableTemplates.js:136
#: src/components/widgets/modules/stats/StatsBySource.js:120
#: src/components/tvr/Content/Content.js:92
msgid "Total"
msgstr "Celkovo"

#. placeholder {0}: humanizeNumber(data)
#: src/constants/analytics.js:215
msgid "Total {0} interactions"
msgstr "Celkovo {0} interakcií"

#. placeholder {0}: humanizeNumber(data)
#: src/constants/analytics.js:471
msgid "Total influence score: {0}"
msgstr "Skóre vplyvu celkovo: {0}"

#. placeholder {0}: formatter( this.points.reduce((sum, { y }) => sum + y, 0), unit, )
#: src/components/OurChart/HighchartsRenderer.js:645
msgid "Total: {0}"
msgstr "Celkovo: {0}"

#: src/components/emailing/content/promo/PromoEmailing.js:33
msgid "Track delivery and opening statistics."
msgstr "Sledovanie štatistík doručenia a otvorenia."

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:20
msgid "Track online, traditional and social media with {appName} for a complete view of your brand and trends - never miss a beat."
msgstr "Sledujte online, tradičné a sociálne médiá pomocou aplikácie {appName} a získajte kompletný prehľad nielen o vašej značke, trendoch a dianí v médiách."

#: src/components/layout/AuthWrapper/constants/features.slides.js:166
msgid "Tracking, analysis, and reporting are an integral part of PR. Use comprehensible charts that make data analysis easier. Compare your media output with your competition."
msgstr "Meranie, analýza a reporting sú neoddeliteľnou súčasťou PR. Využívajte prehľadné grafy, ktoré vám uľahčia analýzu dát, a porovnávajte si svoje mediálne výstupy s konkurenciou."

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:159
#: src/components/staff/admin/workspace/Workspace.js:353
#: src/components/layout/AuthWrapper/constants/features.slides.js:41
#: src/components/exportList/ExportLimit/ExportLimit.js:17
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:19
#: src/components/analytics/AnalyticsContent.js:146
#: src/components/analytics/AnalyticsContent.js:195
msgid "Traditional Media"
msgstr "Tradičné médiá"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:25
msgid "Traditional Media w/o percentage change"
msgstr "Tradičné médiá (bez percentuálnej zmeny)"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:532
msgid "Transcribe the source file"
msgstr "Zhotoviť prepis zdrojového súboru"

#: src/components/monitoring/WorkspaceArticles/Limits.js:69
msgid "Transcribed seconds"
msgstr "Prepísaných sekúnd"

#: src/components/monitoring/WorkspaceArticles/Limits.js:73
msgid "Transcript"
msgstr "Prepis"

#: src/components/medialist/content/MedialistActionsBar/FormTransformContacts.tsx:41
msgid "Transform"
msgstr "Transformovať"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:53
msgid "Transform & import"
msgstr "Transformovať a importovať"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:137
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:17
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformTitle.tsx:7
msgid "Transform contact list"
msgstr "Transformovať zoznam kontaktov"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:134
msgid "Transformation failed"
msgstr "Transformácia zlyhala"

#: src/helpers/withTranslatePopup/TranslatePopupContent.js:49
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:75
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:77
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:110
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:189
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:194
msgid "Translate"
msgstr "Preložiť"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:243
msgid "Translations"
msgstr "Preklady"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:569
msgid "Transparent background"
msgstr "Transparentné pozadie"

#: src/app/components/monitoring-navigation.tsx:242
msgid "Trash"
msgstr "Kôš"

#: src/constants/analytics.js:1053
#: src/constants/analytics.js:1068
msgid "Treemap"
msgstr "Treemap"

#: src/components/staff/admin/user/User.js:120
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:391
msgid "Trigger password reset"
msgstr "Spustiť obnovenie hesla"

#: src/components/notifications/Permissions.js:74
msgid "Try again"
msgstr "Skúsiť znova"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:82
#: src/components/misc/PromoBox/PromoBox.js:144
msgid "Try for free"
msgstr "Vyskúšať zadarmo"

#: src/pages/user/reactivate-24.js:34
msgid "Try out Mediaboard"
msgstr "Vyskúšanie Mediaboardu"

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:57
msgid "Try social media monitoring"
msgstr "Vyskúšať monitoring sociálnych médií"

#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:18
msgid "Turn off these notifications"
msgstr "Vypnúť tieto notifikácie"

#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:27
msgid "Turn on these notifications"
msgstr "Zapnúť tieto notifikácie"

#: src/components/notifications/ContentTvrRequest.js:41
#: src/components/notifications/ContentTvr.js:46
#: src/components/misc/ActionsBar/View/ViewMenu.js:162
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:41
#: src/components/layout/AuthWrapper/constants/features.slides.js:65
msgid "TV"
msgstr "TV"

#: src/components/emailing/forms/FormSenderSettings.js:123
msgid "TXT record"
msgstr "TXT záznam"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:58
msgid "Type a coefficient"
msgstr "Zadajte koeficient"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:38
msgid "Type your keypoint"
msgstr "Napíšte svoj kľúčový bod"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:56
msgid "Type your main message"
msgstr "Napíšte svoju hlavnú správu"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:125
msgid "Type your subject or other instructions. Clearly outline the main message or information you want to convey.Provide instructions on how to structure the information, for example: use bullet points or numbered lists."
msgstr "Zadajte predmet alebo iné pokyny. Jasne načrtnite hlavné posolstvo alebo informácie, ktoré chcete odovzdať. uveďte pokyny, ako informácie štruktúrovať, napríklad: použite odrážky alebo číslované zoznamy."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:175
msgid "Unable to extract data from the URL."
msgstr "Nepodarilo sa získať dáta zo zadanej URL."

#: src/components/emailing/content/EmailingSettingsContent.js:29
msgid "Unable to retrieve access token from the OAuth2 provider. This may be due to a network issue or provider outage. Please try again later."
msgstr "Nie je možné získať prístupový token od poskytovateľa OAuth2. Môže to byť spôsobené problémom so sieťou alebo výpadkom poskytovateľa. Skúste to znova neskôr."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:558
msgid "Undo"
msgstr "Späť"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:79
msgid "Unique visits"
msgstr "Jedinečné návštevy"

#: src/components/newsroom/content/dashboard/ChartVisits.js:54
#: src/components/newsroom/content/dashboard/ChartVisits.js:96
#: src/components/newsroom/components/PostsList/PostsList.js:209
msgid "Unique Visits"
msgstr "Unikátne návštevy"

#: src/components/reports/history/RecipientsTableRow.js:68
msgid "Unknown"
msgstr "Neznámy"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:194
msgid "Unlock licensed articles"
msgstr "Odomknúť spoplatnené články"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:148
msgid "Unpublish"
msgstr "Zrušiť zverejnenie"

#: src/components/medialist/forms/FormEditAuthor.js:577
msgid "Unsaved changes"
msgstr "Neuložené zmeny"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:152
msgid "Unschedule"
msgstr "Zrušiť načasované zverejnenie"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:149
msgid "Unsubscribe"
msgstr "Odhlásiť sa z odberu"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:47
msgid "Unsubscribe from emails"
msgstr "Odhlásenie z odberu e-mailov"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:311
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:322
msgid "Unsubscribe news source"
msgstr "Neodoberať zdroj"

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:138
msgid "Update recipient"
msgstr "Aktualizovať príjemcu"

#: src/components/medialist/content/MedialistDashboard.js:75
#: src/components/medialist/content/MedialistDashboard.js:108
msgid "Updated"
msgstr "Aktualizované"

#: src/components/medialist/content/MedialistActionsBar/ContactsImportTitle.tsx:8
msgid "Upload either a manually completed template or a formatted contact list file. Once you import contacts, they will automatically appear in the Import. You can also add the contacts to one of the existing lists."
msgstr "Nahrajte buď ručne vyplnenú šablónu, alebo naformátovaný súbor so zoznamom kontaktov. Po importe kontaktov sa automaticky zobrazia v Importe. Kontakty môžete tiež pridať do jedného z existujúcich zoznamov."

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:26
msgid "Upload File"
msgstr "Nahrať súbor"

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:20
msgid "Upload Image"
msgstr "Nahrať obrázok"

#: src/components/medialist/content/MedialistActionsBar/withModalUploadMedialist.tsx:8
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:240
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:241
msgid "Upload medialist"
msgstr "Nahrať medialist"

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:23
msgid "Upload Video"
msgstr "Nahrať video"

#: src/components/settings/SettingsLogo/SettingsLogo.js:95
msgid "Upload your company logo, which will then be displayed in email reports, exports and in the application itself, instead of the {appName} logo."
msgstr "Nahrajte logo Vašej spoločnosti, ktoré sa potom objaví v emailových reportoch, exportoch a i v samotnej aplikácii namiesto loga {appName}."

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformTitle.tsx:8
msgid "Upload your contact list and we'll transform it to fit our medialist for you."
msgstr "Nahrajte svoj zoznam kontaktov a my ho pre vás transformujeme tak, aby vyhovoval nášmu medialistu."

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:58
msgid "Upload your contact list, and we’ll format it to fit perfectly into our medialist for you."
msgstr "Nahrajte svoj zoznam kontaktov a my ho pre vás naformátujeme tak, aby dokonale zapadol do nášho medialistu."

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:58
msgid "Upload your file"
msgstr "Nahrajte svoj súbor"

#: src/components/misc/UploadWatcher/UploadWatcher.js:18
msgid "Uploading has not finished. Please do not refresh or close this page."
msgstr "Prebieha nahrávanie súboru. Prosíme, neobnovujte ani nezatvárajte toto okno."

#: src/components/misc/UploadWatcher/UploadWatcher.js:46
msgid "Uploading: {lastProgress}%"
msgstr "Nahrávanie: {lastProgress}%"

#: src/components/medialist/forms/modules/FormArray.js:131
msgid "Url"
msgstr "Url"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:190
#: src/components/ReusableFeed/FormAddArticle.tsx:31
msgid "URL"
msgstr "URL"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:38
msgid "Use another email address"
msgstr "Použiť inú emailovú adresu"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:76
msgid "Use Google account as sender"
msgstr "Použite účet Google ako odosielateľa"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:82
msgid "Use Microsoft 365 account as sender"
msgstr "Použite konto Microsoft 365 ako odosielateľa"

#: src/components/staff/admin/workspace/Workspace.js:369
#: src/components/staff/admin/workspace/Workspace.js:389
#: src/components/staff/admin/workspace/Workspace.js:410
#: src/components/staff/admin/workspace/Workspace.js:430
#: src/components/staff/admin/workspace/Workspace.js:449
#: src/components/staff/admin/workspace/Workspace.js:470
#: src/components/staff/admin/workspace/Workspace.js:491
#: src/components/staff/admin/workspace/Workspace.js:524
#: src/components/staff/admin/workspace/Workspace.js:550
#: src/components/staff/admin/workspace/Workspace.js:569
#: src/components/staff/admin/workspace/Workspace.js:588
#: src/components/staff/admin/workspace/Workspace.js:639
#: src/components/staff/admin/workspace/Workspace.js:660
#: src/components/staff/admin/workspace/Workspace.js:686
msgid "Used"
msgstr "Využitých"

#: src/pages/staff/admin/users/[userId]/index.js:12
#: src/components/staff/admin/DailyAccess/Table.js:24
#: src/components/staff/admin/DailyAccess/Content.js:32
msgid "User"
msgstr "Užívateľ"

#: src/components/tariff/TariffLimits/TariffLimits.js:274
#: src/components/staff/admin/workspace/Workspace.js:677
msgid "User accounts limit"
msgstr "Limit užívateľských účtov"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:53
#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:59
msgid "User emails"
msgstr "Emaily užívateľov"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:104
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:67
msgid "User management"
msgstr "Správa užívateľov"

#: src/components/staff/admin/user/User.js:224
msgid "User settings"
msgstr "Nastavenie užívateľa"

#: src/components/emailing/forms/FormSenderSettings.js:89
msgid "Username"
msgstr "Username"

#: src/pages/staff/admin/customers/[customerId]/users.js:12
#: src/components/staff/admin/workspace/Workspace.js:858
#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:69
#: src/components/staff/admin/customers/Customer.js:173
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:77
#: src/components/staff/admin/customer/users/Users.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:34
#: src/components/forms/dashboard/Search/SearchUsers.js:36
msgid "Users"
msgstr "Užívatelia"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:27
msgid "Utilizes company profiles for more tailored content."
msgstr "Využíva firemné profily na vytvorenie prispôsobenejšieho obsahu."

#: src/components/emailing/forms/FormSenderSettings.js:168
msgid "Validate"
msgstr "Overiť"

#: src/components/emailing/forms/FormSenderSettings.js:134
msgid "Value"
msgstr "Hodnota"

#: src/components/staff/admin/customer/bio/CustomerBio.js:95
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:58
msgid "VAT"
msgstr "DIČ"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:227
msgid "Verification"
msgstr "Overenie"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:101
msgid "Verification email sent."
msgstr "Verifikačný email bol odoslaný."

#. placeholder {0}: values.email
#: src/components/emailing/content/EmailingSettingsContent.js:93
msgid "Verification email was sent to {0}. Please check your inbox."
msgstr "Verifikačný email bol odoslaný na adresu {0}. Skontrolujte si doručenú poštu."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:26
msgid "Verify your email"
msgstr "Verifikujte váš email"

#: src/components/emailing/forms/FormSenderSettings.js:208
msgid "Verifying your email address with SMTP or DNS can improve email deliverability, protect against spoofing, improve sender reputation, and provide better analytics. It demonstrates legitimacy and helps email providers ensure that emails are not spam."
msgstr "Overenie vašej emailovej adresy pomocou SMTP alebo DNS môže zlepšiť doručiteľnosť emailov, chrániť pred falšovaním, zlepšiť reputáciu odosielateľa a poskytnúť lepšiu analytiku. Preukazuje legitímnosť a pomáha poskytovateľom emailových služieb zabezpečiť, aby emaily neboli označené ako spam."

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:87
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:48
msgid "Video"
msgstr "Video"

#: src/components/newsroom/content/posts/NewsroomPosts.js:119
#: src/components/newsroom/content/posts/NewsroomPosts.js:123
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:23
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:32
#: src/components/misc/ActionsBar/View/ViewMenu.js:323
#: src/components/misc/ActionsBar/View/View.js:16
msgid "View"
msgstr "Zobraziť"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:84
msgid "View changes"
msgstr "Zobraziť zmeny"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:44
msgid "View full article"
msgstr "Zobraziť celý článok"

#: src/components/OurChart/OurChartAdvanced.js:253
msgid "View in full screen"
msgstr "Zobraziť na celej obrazovke"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:58
msgid "View Newsroom"
msgstr "Zobraziť Newsroom"

#: src/components/feed/InspectorToolbar/InspectorToolbar.js:120
msgid "View preview"
msgstr "Zobraziť náhľad"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:41
msgid "View Screenshot"
msgstr "Zobraziť screenshot"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:47
msgid "View Video"
msgstr "Zobraziť video"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:41
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:47
msgid "View Web"
msgstr "Zobraziť web"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:35
msgid "Viewing this press publication incurs an additional fee as per the Table of Fees approved by the Minister of Culture and National Heritage."
msgstr "Prezeranie tejto tlačovej publikácie je spoplatnené podľa sadzobníka poplatkov schváleného Ministerstvom kultúry a národného dedičstva."

#: src/components/monitoring/FeedList/FeedListItem/SocialInteractions/SocialInteractions.js:24
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:95
msgid "views"
msgstr "zhliadnutí"

#: src/components/newsroom/content/posts/NewsroomPosts.js:163
msgid "Views"
msgstr "Zobrazenia"

#: src/components/misc/ActionsBar/Selector/Selector.js:34
msgid "Visible"
msgstr "Zobrazené"

#: src/components/newsroom/content/posts/NewsroomPosts.js:261
#: src/components/newsroom/content/dashboard/ChartVisits.js:86
#: src/components/newsroom/components/PostsList/PostsList.js:209
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:74
msgid "Visits"
msgstr "Návštevy"

#: src/components/newsroom/content/dashboard/ChartVisits.js:49
msgid "Visits (last 30 days / total):"
msgstr "Návštevy (za posledných 30 dní / spolu):"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:107
msgid "Warning"
msgstr "Upozornenie"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:46
msgid "Warning via <0>SMS</0>, <1>email</1> or <2>notification</2>"
msgstr "Upozornenie cez <0>SMS</0>, <1>email</1> alebo <2>notifikáciu</2>"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:57
msgid "We are the only ones in the Czech Republic to monitor <0>text mentions in the broadcast</0> for selected channels."
msgstr "Ako jediní v SR monitorujeme i <0>textové zmienky na obrazovke</0> pre vybrané kanály."

#: src/pages/user/reset-password/success.tsx:8
msgid "We have sent password reset link to your email."
msgstr "Na váš email bol zaslaný odkaz na zmenu hesla."

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:29
msgid "We have sent you an activation link. To activate Emailing and to confirm your email address please open the link."
msgstr "Odoslali sme vám aktivačný odkaz. Ak chcete aktivovať Emailing a potvrdiť svoju emailovú adresu, otvorte odkaz."

#: src/components/emailing/content/EmailingSettingsContent.js:28
msgid "We haven't been granted access to send emails on your behalf. Please try again and make sure to grant us access."
msgstr "Nebol nám udelený prístup na odosielanie e-mailov vo vašom mene. Skúste to znova a uistite sa, že ste nám udelili prístup."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:75
msgid "We noticed that your post lacks an introduction or perex. This section is crucial as it provides a brief overview of your post and entices readers to continue. Consider adding a short paragraph that summarizes your main points or sets the context for your post."
msgstr "Všimli sme si, že vášmu príspevku chýba úvod alebo perex. Táto časť je kľúčová, pretože poskytuje stručný prehľad vášho príspevku a láka čitateľov pokračovať. Zvážte pridanie krátkeho odseku, ktorý zhrnie vaše hlavné body alebo nastaví kontext vášho príspevku."

#: src/components/emailing/forms/FormSenderSettings.js:226
msgid "We recommend that you verify your email address"
msgstr "Odporúčame vám overiť vašu emailovú adresu"

#: src/components/page/auth/UserInactive/UserInactive.js:20
msgid "We will contact you shortly, once we setup your account."
msgstr "Hneď ako pre vás všetko nastavíme, budeme vás kontaktovať."

#: src/pages/404.js:24
#: src/app/not-found-content.tsx:33
msgid "We're sorry, but the requested page was not found. It is possible that the page was either removed or moved somewhere else. Please make sure you entered the correct URL address."
msgstr "Je nám ľúto, ale požadovaná stránka nebola nájdená. Uistite sa, že ste neurobili chybu v URL adrese. Je možné, že bola stránka premiestnená alebo odstránená."

#. placeholder {0}: topics.getTopicNameById(missingArticle.topicMonitorId)
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:170
msgid "We've added the article to the the topic \"{0}\" and adjusted its settings. The article will appear in your feed shortly."
msgstr "Článok sme pridali pod tému \"{0}\" a upravili nastavenie témy. Článok sa čoskoro objaví v sekcii Príspevky."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:110
msgid "We've discovered more content in the media landscape related to your topics and areas of interest."
msgstr "Objavili sme ďalšie články pojednávajúce o témach a oblastiach, ktoré vás zaujímajú."

#: src/components/medialist/forms/FormEditAuthor.js:842
msgid "Website"
msgstr "Web"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:141
msgid "Website URL"
msgstr "Odkaz na váš web"

#: src/helpers/charts/makeGranularityMenu.js:18
#: src/helpers/charts/getGranularityLabel.js:6
msgid "Weeks"
msgstr "Týždne"

#: src/components/emailing/content/sender/EmailingSenderContent.js:48
msgid "What is Emailing used for?"
msgstr "Na čo sa Emailing používa?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:250
msgid "What is Newsroom?"
msgstr "Čo je to Newsroom?"

#: src/components/emailing/content/sender/EmailingSenderContent.js:49
msgid "While our Emailing tool is designed to send press and PR messages to journalists, its functionality goes beyond that. You can use it for various types of communication, opening up possibilities beyond traditional media outreach."
msgstr "Emailing je primárne určený na rozosielanie tlačových a PR správ novinárom. Môžete ho však využiť i k ďalšim typom komunikácie, ktoré otvárajú možnosti presahujúce tradičné oslovovanie médií."

#: src/components/staff/admin/workspace/Workspace.js:799
msgid "Whitelisted domains"
msgstr "Povolené domény"

#. placeholder {0}: targetDashboard.name
#: src/store/models/dashboards/DashboardItem/DashboardItem.js:323
msgid "Widget copied to \"{0}\"."
msgstr "Widget bol skopírovaný do \"{0}\"."

#. placeholder {0}: targetDashboard.name
#: src/store/models/dashboards/DashboardItem/DashboardItem.js:314
msgid "Widget moved to \"{0}\"."
msgstr "Widget bol presunutý do \"{0}\"."

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:138
msgid "Widget will be removed"
msgstr "Widget bude odstránený."

#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:203
msgid "With contact"
msgstr "S kontaktom"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:38
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:72
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:112
msgid "With inflection"
msgstr "So skloňovaním"

#: src/components/layout/AuthWrapper/constants/features.slides.js:215
msgid "With Medialist, you don't send your media output to randomly selected journalists. You only send it to those who are most likely to publish it."
msgstr "S Medialistom neposielate svoje mediálne výstupy náhodne vybraným novinárom, ale oslovíte iba tých, ktorí vám správu s veľkou pravdepodobnosťou uverejnia."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:32
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:37
#: src/components/layout/MntrActiveFilters/modules/Note.js:11
msgid "With note"
msgstr "S poznámkou"

#. placeholder {0}: values.unverified_recipients_limit
#: src/components/emailing/forms/FormSenderSettings.js:227
msgid "Without a verified email address, your emails risk being marked as spam and rejected by providers, potentially damaging your reputation. You will also be limited to sending an email to only {0} recipients at a time."
msgstr "Bez overenej emailovej adresy hrozí, že vaše emaily budú označené ako spam a poskytovatelia ich odmietnu, čo môže poškodiť vašu povesť. Budete tiež obmedzení na odosielanie emailov len {0} príjemcom naraz."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:29
msgid "Without limit"
msgstr "Bez obmedzenia"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:49
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:54
#: src/components/layout/MntrActiveFilters/modules/Note.js:15
msgid "Without note"
msgstr "Bez poznámky"

#: src/components/staff/admin/customers/Customers.js:26
msgid "without sending registration email"
msgstr "bez zaslania registračného emailu"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:154
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:166
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:86
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:92
#: src/components/layout/MntrActiveFilters/modules/Tags.js:46
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterTags.js:19
msgid "Without tags"
msgstr "Bez štítku"

#: src/pages/404.js:14
#: src/app/not-found-content.tsx:23
msgid "Woop woop woop woop, page not found"
msgstr "Woop woop woop woop, stránka nenájdená"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:24
#: src/components/help/search/Content/RulesSearch.tsx:16
msgid "Word Search"
msgstr "Vyhľadávanie slov"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:98
#: src/components/help/search/Content/RulesDistance.tsx:16
msgid "Words to distance"
msgstr "Slová do vzdialenosti"

#: src/pages/staff/admin/workspaces/[workspaceId]/index.js:12
#: src/components/staff/admin/workspace/WorkspaceChangelog.js:21
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:77
#: src/components/staff/admin/DailyAccess/Table.js:27
#: src/components/staff/admin/DailyAccess/Content.js:32
#: src/components/page/auth/Expired/Expired.js:53
#: src/components/layout/Header/UserMenu/UserMenu.tsx:101
msgid "Workspace"
msgstr "Workspace"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:210
msgid "Workspace admin"
msgstr "Workspace admin"

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:55
msgid "Workspace created."
msgstr "Workspace bol vytvorený."

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:37
msgid "Workspace name"
msgstr "Názov workspacu"

#: src/components/staff/admin/workspace/Workspace.js:280
msgid "Workspace settings"
msgstr "Nastavenie workspacu"

#: src/pages/staff/admin/customers/[customerId]/workspaces.js:12
#: src/components/staff/admin/user/WorkspacesTable.js:61
#: src/components/staff/admin/user/User.js:307
#: src/components/staff/admin/customers/Customer.js:150
#: src/components/staff/admin/customer/workspaces/Workspaces.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:26
#: src/components/forms/dashboard/Search/SearchWorkspaces.js:43
msgid "Workspaces"
msgstr "Workspace"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:65
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:204
msgid "worst"
msgstr "najhoršie"

#: src/components/settings/SettingsLogo/SettingsLogo.js:76
msgid "Would you like to customize the appearance of the app, email reports and exports with your own logo? Contact us at <0>{salesEmail}</0>"
msgstr "Priali by ste si prispôsobiť vzhľad aplikácie, emailových reportov a exportov Vaším vlastným logom? Kontaktujte nás na <0>{salesEmail}</0>"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:61
msgid "Write the main content of your article that you want to create. The main content is considered as the primary theme or topic of your article."
msgstr "Napíšte hlavnú obsah vašej článku, ktorý chcete vytvoriť. Hlavný obsah sa považuje za primárnu tému alebo predmet vášho článku."

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:19
#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:20
msgid "Write with AI assistant"
msgstr "Písať s asistentom AI"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:39
msgid "Write without AI assistant"
msgstr "Písať bez asistenta AI"

#: src/components/newsroom/components/AiTools/AiGenerateTitles.tsx:79
msgid "Write your own"
msgstr "Napíšte vlastný"

#: src/components/tariff/TariffLimits/TariffLimits.js:132
#: src/components/staff/admin/workspace/Workspace.js:420
msgid "Yearly authors export limit"
msgstr "Ročný limit na počet vyexportovaných autorov"

#: src/helpers/charts/makeGranularityMenu.js:34
#: src/helpers/charts/getGranularityLabel.js:12
msgid "Years"
msgstr "Roky"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/metaDataDate.js:30
msgid "yesterday"
msgstr "včera"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:114
#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/MediaDetail.js:72
#: src/components/tvr/Content/TvrStories/TvrStory/TvrStory.js:52
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:100
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:115
msgid "Yesterday"
msgstr "Včera"

#: src/components/emailing/modules/withModalRemoveRecipients.tsx:60
msgid "You are about to remove the selected recipients. However, you can keep some of them by clicking on the recipients."
msgstr "Chystáte sa odstrániť vybraných príjemcov. Môžete však niektorých z nich ponechať kliknutím na príjemcov."

#: src/components/emailing/content/mediaCoverage/EmptyFeedMessage.tsx:9
msgid "You can add articles to media coverage"
msgstr "Môžete pridať články do mediálneho pokrytia"

#: src/components/exportList/Content/HeadingExport/HeadingExport.js:30
msgid "You can add items in Articles section."
msgstr "Môžete pridávať položky v sekcii Články."

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:88
msgid "You can create your first campaign by clicking the button below."
msgstr "Svoju prvú kampaň môžete vytvoriť kliknutím na tlačidlo nižšie."

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:133
msgid "You can create your first email by clicking the button below."
msgstr "Svoj prvý email môžete vytvoriť kliknutím na tlačidlo nižšie."

#: src/components/monitoring/WorkspaceArticles/Intro.js:30
msgid "You can create your own articles here. They will be added to <0>your feed only</0>."
msgstr "Tu môžete pridávať vlastné články. Tie sa následne zobrazia v sekcii Príspevky. <0>Uvidíte ich iba Vy</0>."

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:51
msgid "You can edit recipients in email settings."
msgstr "Príjemcov môžete upraviť v nastaveniach emailu."

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:149
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:70
msgid "You can reset your filter by clicking the button below."
msgstr "Filter môžete resetovať kliknutím na tlačidlo nižšie."

#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:20
msgid "You can safely close this window as the process will continue in the background. Once the import is complete, we will notify you. If any issues occur, you will receive a notification and an email detailing the errors."
msgstr "Toto okno môžete bezpečne zavrieť, pretože proces bude pokračovať na pozadí. Keď bude import dokončený, upozorníme vás. Ak nastanú nejaké problémy, dostanete upozornenie a e-mail s podrobnosťami o chybách."

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:91
msgid "You can use an external link to the article or {appName} link."
msgstr "Môžete použiť externý odkaz alebo odkaz z aplikácie {appName}."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:244
msgid "You don't have a Newsroom yet, but you can create a new one right now."
msgstr "Zatiaľ nemáte žiaden Newsroom, ale môžete si ho vytvoriť jednoducho práve teraz."

#: src/components/monitoring/WorkspaceArticles/Intro.js:26
msgid "You don't have any articles yet, but you can create one right now."
msgstr "Zatiaľ ste nevytvorili žiaden článok, ale môžete to vyskúšať hneď teraz."

#: src/components/emailing/content/sender/EmailingSenderContent.js:20
msgid "You don't have Emailing set up yet. It only takes a few minutes to set it up."
msgstr "Zatiaľ nemáte Emailing nastavený. Nastavenie zaberie len pár minút."

#: src/components/widgets/modules/stats/StatsBySource.js:35
#: src/components/widgets/components/PermissionErrorHint/PermissionErrorHint.js:13
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:36
msgid "You don't have permission to view"
msgstr "Nemáte práva na zobrazenie"

#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:55
msgid "You have no topics created"
msgstr "Nemáte vytvorené žiadne témy"

#: src/components/notifications/AppNotifications/AppNotifications.js:25
#: src/components/layout/Header/AppNotifications/AppNotifications.js:173
msgid "You have not received any notifications yet."
msgstr "Zatiaľ ste neobdržali žiadne notifikácie."

#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:52
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:52
msgid "You have not saved any settings"
msgstr "Nemáte uložené žiadne nastavenia"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:106
msgid "You have reached 30-day limit on the number of translated articles."
msgstr "Dosiahli ste limit na počet preložených článkov za mesiac."

#: src/components/tariff/TariffLimits/TariffLimits.js:59
#: src/components/exportList/ExportLimit/ExportLimit.js:19
msgid "You have reached 30-day limit. You cannot export any new articles."
msgstr "Dosiahli ste 30-denný limit. Nové články tak nemôžete exportovať."

#: src/components/tariff/TariffLimits/TariffLimits.js:220
#: src/components/exportList/ExportLimit/ExportLimit.js:30
msgid "You have reached 30-day limit. You cannot export any new social media mentions."
msgstr "Dosiahli ste 30-denný limit. Nové zmienky zo soc. sietí tak nemôžete exportovať."

#: src/store/models/ExportStore.js:238
msgid "You have reached the 30-day limit on the number of exported articles. Exported file doesn't contain all the selected articles."
msgstr "Dosiahli ste limit na počet exportovaných článkov za mesiac. Súbor s exportom tak neobsahuje všetky zvolené články."

#: src/store/models/ExportStore.js:240
msgid "You have reached the 30-day limit on the number of exported social media mentions. Exported file doesn't contain all selected items."
msgstr "Dosiahli ste limit na počet exportovaných zmienok zo soc. sietí za mesiac. Súbor s exportom tak neobsahuje všetky zvolené výstupy."

#: src/components/monitoring/WorkspaceArticles/Limits.js:59
msgid "You have reached the 30-day limit on the number of OCR pages."
msgstr "Dosiahli ste 30-denný limit na na počet strán pre OCR:"

#: src/components/monitoring/WorkspaceArticles/Limits.js:75
msgid "You have reached the 30-day limit on the number of transcribed seconds."
msgstr "Dosiahli ste 30-denný limit na počet sekúnd na prepis."

#: src/store/models/ExportStore.js:246
msgid "You have reached the 30-day limit on the number of translated articles. Exported file doesn't contain all the selected articles."
msgstr "Dosiahli ste limit na počet preložených článkov za mesiac. Súbor s exportom tak obsahuje niektoré články nepreložené do zvoleného jazyka."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:92
msgid "You have reached the limit of recipients per email"
msgstr "Dosiahli ste limit príjemcov na jeden e-mail"

#: src/components/layout/Header/MessageLimit/MessageLimit.js:19
msgid "You have reached the limit on found articles"
msgstr "Dosiahli ste limit na počet monitorovaných článkov"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:104
msgid "You have reached the limit on the number of dashboards."
msgstr "Dosiahli ste limit na počet dashboardov."

#: src/store/models/ExportStore.js:229
msgid "You have reached the limit on the number of exported articles. Exported file doesn't contain all the selected articles."
msgstr "Presiahli ste limit na počet exportovaných článkov. Súbor s exportom tak neobsahuje všetky zvolené články."

#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:79
msgid "You have reached the limit on the number of Newsrooms."
msgstr "Dosiahli ste limit na počet Newsroomov."

#: src/store/models/emailing/emailEdit/EmailEditStore/recipients/EmailRecipientsStore/EmailRecipientsStore.js:122
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:48
msgid "You have reached the limit on the number of recipients."
msgstr "Dosiahli ste limit na počet príjemcov."

#: src/components/emailing/content/EmailingSettingsContent.js:22
msgid "You have successfully authorized our application to use the external service."
msgstr "Úspešne ste autorizovali našu aplikáciu na používanie externej služby."

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:123
msgid "You have unsaved changes."
msgstr "Máte neuložené zmeny."

#: src/components/tariff/TariffLimits/TariffLimits.js:23
msgid "You may not see the latest articles."
msgstr "Nové články vám unikajú."

#: src/components/layout/Header/MessageLimit/MessageLimit.js:13
msgid "You may not see the latest articles. We recommend that you change your keyword settings or limit your watched media in the Topics section."
msgstr "Najnovšie články sa vám tak nemusia v prehľade noviniek zobraziť. Doporučujeme vám v sekcii Témy zmeniť nastavenie kľúčových slov alebo obmedziť sledované médiá."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:470
msgid "You will be able to edit the link later to match your own domain."
msgstr "Odkaz budete môcť upraviť aj neskôr tak, aby zodpovedal Vašej doméne."

#: src/components/emailing/modules/PreviewEmail/RecipientsIsEmpty.tsx:27
#: src/components/emailing/components/EmailRecipientsList/RenderAllRecipients.tsx:34
msgid "You will see your recipients here"
msgstr "Tu uvidíte vybraných príjemcov"

#: src/helpers/modal/withModalRequestFeature.tsx:40
#: src/components/misc/PromoBox/PromoBox.js:135
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:12
msgid "You'll be contacted by our Sales management."
msgstr "Bude vás kontaktovať náš zástupca."

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:17
msgid "Your account does not have access to any workspace."
msgstr "Váš účet nemá prístup do žiadneho workspacu."

#: src/components/page/auth/Expired/Expired.js:49
msgid "Your account has expired"
msgstr "Platnosť Vašeho účtu vypršala"

#: src/components/page/auth/UserInactive/UserInactive.js:14
msgid "Your account is being prepared"
msgstr "Účet sa pripravuje"

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:118
msgid "Your email <0>{0}</0> is already unsubscribed from our email list. There is nothing you need to do to stop receiving emails from {host}"
msgstr "Váš e-mail <0>{0}</0> je už odhlásený z nášho e-mailového zoznamu. Nemusíte urobiť nič, aby ste prestali dostávať e-maily od {host}"

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:85
msgid "Your email <0>{0}</0> successfully unsubscribed from our email list. You will no longer receive emails from us."
msgstr "Váš e-mail <0>{0}</0> sa úspešne odhlásil z nášho e-mailového zoznamu. Už od nás nebudete dostávať e-maily."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:65
msgid "Your email has been verified. Now you can fully enjoy our platform."
msgstr "Váš email bol overený. Teraz môžete naplno využívať našu platformu."

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:76
msgid "Your email successfully unsubscribed"
msgstr "Váš e-mail bol úspešne odhlásený"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:109
msgid "Your email was already unsubscribed"
msgstr "Váš e-mail už bol odhlásený"

#: src/components/emailing/content/EmailingSettingsContent.js:71
#: src/components/emailing/content/EmailingCampaignsContent.tsx:28
msgid "Your Emailing is not fully set up and verified"
msgstr "Váš Emailing ešte nie je plne nastavený a overený"

#: src/components/emailing/content/EmailingSettingsContent.js:74
msgid "Your Emailing is not fully set up and verified. This can decrease the trust level and deliverability. You can fully set up and verify your Emailing in the settings. If you need help, please contact our support."
msgstr "Váš Emailing ešte nie je plne nastavený a overený. To môže znížiť úroveň dôveryhodnosti a doručiteľnosti. Emailing môžete plne nastaviť a overiť v nastaveniach. Ak potrebujete pomoc, obráťte sa na našu podporu."

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:96
msgid "Your HTML code"
msgstr "Váš HTML kód"

#: src/components/layout/Header/MessageDirty/MessageDirty.js:10
msgid "Your news feed is being updated"
msgstr "Váš súhrn článkov sa aktualizuje"

#: src/store/models/account/user/UserStore.js:247
msgid "Your password has been changed successfully."
msgstr "Vaše heslo bolo úspešne zmenené."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:66
msgid "Your post is missing a title. A clear, concise title helps readers understand what your post is about at a glance. Please add a title that accurately represents your content."
msgstr "Vášmu príspevku chýba názov. Jasný a výstižný názov pomáha čitateľom na prvý pohľad pochopiť, o čom je váš príspevok. Pridajte názov, ktorý presne vystihuje váš obsah."
