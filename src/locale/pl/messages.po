msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-07-31 13:12+0200\n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"Mime-Version: 1.0\n"
"X-Generator: Poedit 3.6\n"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:7
msgid "error"
msgstr "<i><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ale mamy pewne problemy z naszym asystentem AI. Wiemy, że to frustruj<PERSON>ce, gdy coś nie działa zgodnie z oczekiwaniami.<br> Spróbuj ponownie za jakiś czas.</i>"

#. placeholder {0}: data.word_count
#: src/components/monitoring/FeedList/FeedListItem/FeedListItem.js:305
msgid "(full text; {0} words)"
msgstr "(pełny tekst; {0} słów)"

#: src/components/staff/admin/workspace/Workspace.js:781
msgid "(TVR) Allow automatic transcripts in monitoring"
msgstr "(TVR) Zezwalaj na automatyczne transkrypcje w monitoringu"

#: src/components/staff/admin/workspace/Workspace.js:790
msgid "(TVR) Allow reruns in monitoring"
msgstr "(TVR) Zezwalaj na powtórki w monitoringu"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+access%7D+other+%7B%23+accesses%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:85
msgid "# access"
msgid_plural "# accesses"
msgstr[0] "# dostęp"
msgstr[1] "# dostępy"
msgstr[2] "# dostępów"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(item.article_count)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+article%7D+other+%7B%23+articles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:43
#: src/helpers/charts/formatters.js:55
#: src/components/reports/history/HistoryTable.js:199
#: src/components/analytics/AnalyticsContent.js:122
msgid "# article"
msgid_plural "# articles"
msgstr[0] "# materiał"
msgstr[1] "# materiały"
msgstr[2] "# materiałów"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+attached+article%7D+other+%7B%23+attached+articles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:50
msgid "# attached article"
msgid_plural "# attached articles"
msgstr[0] "# załączony materiał"
msgstr[1] "# załączone materiały"
msgstr[2] "# załączone materiałów"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+author%7D+other+%7B%23+authors%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:57
msgid "# author"
msgid_plural "# authors"
msgstr[0] "# autor"
msgstr[1] "# autorzy"
msgstr[2] "# autorów"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: inspector.data.versions_count
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+change%7D+other+%7B%23+changes%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:78
#: src/components/monitoring/Inspector/InspectorMonitora/StatusBar/ArticleHistoryAction.js:22
msgid "# change"
msgid_plural "# changes"
msgstr[0] "# zmiana"
msgstr[1] "# zmiany"
msgstr[2] "# zmian"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+comment%7D+other+%7B%23+comments%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:89
msgid "# comment"
msgid_plural "# comments"
msgstr[0] "# komentarz"
msgstr[1] "# komentarze"
msgstr[2] "# komentarzy"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+dislike%7D+other+%7B%23+dislikes%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:109
msgid "# dislike"
msgid_plural "# dislikes"
msgstr[0] "# nie lubię"
msgstr[1] "# nie lubię"
msgstr[2] "# nie lubię"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+email%7D+other+%7B%23+emails%7D%7D&pluralize_on=0
#: src/components/emailing/helpers/emailing.plurals.js:3
msgid "# email"
msgid_plural "# emails"
msgstr[0] "# mail"
msgstr[1] "# maile"
msgstr[2] "# maili"

#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(data.social_shares)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+interaction%7D+other+%7B%23+interactions%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:76
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:78
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:119
msgid "# interaction"
msgid_plural "# interactions"
msgstr[0] "# interakcja"
msgstr[1] "# interakcje"
msgstr[2] "# interakcji"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+like%7D+other+%7B%23+likes%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:69
msgid "# like"
msgid_plural "# likes"
msgstr[0] "# polubienie"
msgstr[1] "# polubienia"
msgstr[2] "# polubień"

#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(inspector.data.article_mentions_count)
#. placeholder {0}: parseInt(data.article_mentions_count)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+mention%7D+other+%7B%23+mentions%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:90
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:103
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:81
#: src/components/monitoring/Inspector/InspectorMonitora/StatusBar/ArticleMentionsActions.js:25
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:342
msgid "# mention"
msgid_plural "# mentions"
msgstr[0] "# wzmianka"
msgstr[1] "# wzmianki"
msgstr[2] "# wzmianek"

#. placeholder {0}: parseInt(jobs.length - 1)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+more%7D+other+%7B%23+more%7D%7D&pluralize_on=0
#: src/helpers/getAuthorJobs.js:16
msgid "# more"
msgid_plural "# more"
msgstr[0] "# więcej"
msgstr[1] "# więcej"
msgstr[2] "# więcej"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: parseInt(value)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+output%7D+other+%7B%23+outputs%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:71
#: src/helpers/charts/formatters.js:69
msgid "# output"
msgid_plural "# outputs"
msgstr[0] "# treści"
msgstr[1] "# treści"
msgstr[2] "# treści"

#. placeholder {0}: parseInt(value, 10)
#. placeholder {0}: payload.page_count
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+page%7D+other+%7B%23+pages%7D%7D&pluralize_on=0
#: src/components/monitoring/WorkspaceArticles/Limits.js:51
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:145
msgid "# page"
msgid_plural "# pages"
msgstr[0] "# strona"
msgstr[1] "# strony"
msgstr[2] "# stron"

#. placeholder {0}: parseInt(value)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+person%7D+other+%7B%23+people%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:83
msgid "# person"
msgid_plural "# people"
msgstr[0] "# osoba"
msgstr[1] "# osoby"
msgstr[2] "# osób"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+retweet%7D+other+%7B%23+retweets%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:79
msgid "# retweet"
msgid_plural "# retweets"
msgstr[0] "# retweet"
msgstr[1] "# retweety"
msgstr[2] "# retweetów"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+share%7D+other+%7B%23+shares%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:119
msgid "# share"
msgid_plural "# shares"
msgstr[0] "# udostępnienie"
msgstr[1] "# udostępnienia"
msgstr[2] "# udostępnień"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+view%7D+other+%7B%23+views%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:99
msgid "# view"
msgid_plural "# views"
msgstr[0] "# wyświetlenie"
msgstr[1] "# wyświetlenia"
msgstr[2] "# wyświetleń"

#. placeholder {0}: account.workspace.limits.media_archive_depth_limit
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+year%7D+other+%7B%23+years%7D%7D&pluralize_on=0
#: src/components/tariff/TariffLimits/TariffLimits.js:263
msgid "# year"
msgid_plural "# years"
msgstr[0] "# rok"
msgstr[1] "# lata"
msgstr[2] "# lat"

#. placeholder {0}: item.recipients.length - shortEmailList.length
#. placeholder {0}: items.length - MAX_ITEMS
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%2B%23+more%7D+other+%7B%2B%23+more%7D%7D&pluralize_on=0
#: src/components/reports/history/HistoryTable.js:373
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:73
msgid "+# more"
msgid_plural "+# more"
msgstr[0] "+# więcej"
msgstr[1] "+# więcej"
msgstr[2] "+# więcej"

#. placeholder {0}: data.identical_articles.length
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%2B%23+other%7D+other+%7B%2B%23+other%7D%7D&pluralize_on=0
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:128
msgid "+# other"
msgid_plural "+# other"
msgstr[0] "+# inny"
msgstr[1] "+# inne"
msgstr[2] "+# innych"

#. placeholder {0}: 1
#. placeholder {0}: self.selector.selected.size
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7BArticle+Removed%7D+other+%7BArticles+Removed%7D%7D&pluralize_on=0
#: src/store/models/monitoring/Inspector/Inspector.ts:497
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:659
#: src/store/models/emailing/campaignDetail/CampaignDetailStore/CampaignDetailStore.js:108
msgid "Article Removed"
msgid_plural "Articles Removed"
msgstr[0] "Materiał usunięty"
msgstr[1] "Materiały usunięte"
msgstr[2] "Materiały usunięte"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7Barticle%7D+other+%7Barticles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:64
msgid "article"
msgid_plural "articles"
msgstr[0] "materiał"
msgstr[1] "materiały"
msgstr[2] "materiałów"

#. placeholder {0}: 1
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7BItem+Removed%7D+other+%7BItems+Removed%7D%7D&pluralize_on=0
#: src/store/models/tvr/tvr.js:274
msgid "Item Removed"
msgid_plural "Items Removed"
msgstr[0] "Pozycja usunięta"
msgstr[1] "Pozycje usunięte"
msgstr[2] "Pozycje usunięte"

#: src/components/forms/dashboard/Search/SearchDeclensions.js:51
msgid "{appName} will search"
msgstr "{appName} wyszuka"

#: src/components/tariff/MonitoredMedia/MonitoredMedia.js:206
msgid "{countriesWithActiveMedia} countries with monitoring enabled"
msgstr "{countriesWithActiveMedia} kraje z włączonym monitoringiem"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:107
msgid "{i} (current)"
msgstr "{i} (aktualny)"

#. js-lingui:icu=%7BolderCount%2C+plural%2C+one+%7B%2B+%23+older%7D+other+%7B%2B+%23+older%7D%7D&pluralize_on=olderCount
#: src/components/feed/InspectorToolbar/ToolbarPagination.js:32
msgid "+ # older"
msgid_plural "+ # older"
msgstr[0] "+ # starszy"
msgstr[1] "+ # starsi"
msgstr[2] "+ # starszych"

#. js-lingui:icu=%7BprocessedCount%2C+plural%2C+one+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+article+has+been+updated.%7D+other+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+articles+have+been+updated.%7D%7D&pluralize_on=processedCount
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:517
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:754
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:800
msgid "You have reached the limit for this action. {processedCount} article has been updated."
msgid_plural "You have reached the limit for this action. {processedCount} articles have been updated."
msgstr[0] "Wyczerpałeś limit dla tej akcji. {processedCount} materiał został zaktualizowany."
msgstr[1] "Wyczerpałeś limit dla tej akcji. {processedCount} materiały zostały zaktualizowane."
msgstr[2] "Wyczerpałeś limit dla tej akcji. {processedCount} materiałów zostało zaktualizowanych."

#. js-lingui:icu=%7BprocessedCount%2C+plural%2C+one+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+author+has+been+updated.%7D+other+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+authors+have+been+updated.%7D%7D&pluralize_on=processedCount
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:553
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:590
#: src/store/models/authors/AuthorsStore.js:528
#: src/store/models/authors/AuthorsStore.js:581
#: src/store/models/authors/AuthorsStore.js:657
#: src/store/models/authors/AuthorsStore.js:709
msgid "You have reached the limit for this action. {processedCount} author has been updated."
msgid_plural "You have reached the limit for this action. {processedCount} authors have been updated."
msgstr[0] "Wyczerpałeś limit dla tej akcji. {processedCount} autor został zaktualizowany."
msgstr[1] "Wyczerpałeś limit dla tej akcji. {processedCount} autorzy zostali zaktualizowani."
msgstr[2] "Wyczerpałeś limit dla tej akcji. {processedCount} autorów zostało zaktualizowanych."

#. js-lingui:icu=%7Bvalue%2C+plural%2C+one+%7B%23+post%7D+other+%7B%23+posts%7D%7D&pluralize_on=value
#: src/helpers/charts/formatters.js:97
msgid "# post"
msgid_plural "# posts"
msgstr[0] "# post"
msgstr[1] "# posty"
msgstr[2] "# postów"

#. js-lingui:icu=%7Bvalue%2C+plural%2C+one+%7B%23+visit%7D+other+%7B%23+visits%7D%7D&pluralize_on=value
#: src/helpers/charts/formatters.js:62
msgid "# visit"
msgid_plural "# visits"
msgstr[0] "# odwiedziny"
msgstr[1] "# odwiedziny"
msgstr[2] "# odwiedziny"

#: src/pages/newsroom/index.js:62
msgid "<0>Accurate</0> data is part of analytics."
msgstr "<0>Dokładne</0> dane są częścią analizy."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:88
msgid "help.engagementRate"
msgstr "<0>Wskaźnik zaangażowania to metryka używana do oceny średniej liczby interakcji, które post otrzymuje na jednego obserwującego. Pomaga w relatywnym porównaniu wzmianek z różnych kanałów i źródeł. Za pomocą wskaźnika zaangażowania możesz: </0><1><2>sprawdzić, które posty mają najlepsze i najgorsze wyniki, </2><3>porównać stopień zaangażowania, który generujesz na różnych sieciach społecznościowych, </3><4>porównać swoje wyniki z konkurencją, </4><5>ocenić influencerów. </5></1>"

#: src/pages/authors/index.js:69
msgid "<0>Export</0> detailed lists of authors"
msgstr "<0>Eksportuj</0> szczegółowe listy autorów"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:21
msgid "<0>Immediate notifications</0> about mentions on <1>TV and radio</1>. Be in the swim of things. Non-stop."
msgstr "<0>Natychmiastowe powiadomienia</0> o wzmiankach w <1>TV i radiu</1>. Bądź na bieżąco. Non-stop."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:72
msgid "help.influenceScore"
msgstr "<0>Wynik wpływu (Influence Score) to liczba (od 1 do 10) obliczana dla każdej wzmianki w mediach społecznościowych. Jego wartość jest głównie oparta na dwóch parametrach:</0><1><2>jak prawdopodobne jest, że dana wzmianka zostanie zauważona,</2><3>ile razy wzmianka została wyświetlona, udostępniona lub zretweetowana.</3></1><4>Wierzymy, że ta wartość pomoże Ci odkryć wzmianki, autorów i strony internetowe, które są najpopularniejsze i najbardziej wpływowe. W ten sposób możesz uzyskać dodatkowy wskaźnik do analizy swoich kampanii marketingowych.</4>"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:38
msgid "<0>Non-stop</0> monitoring of selected TVs and radios"
msgstr "<0>Non-stop</0> monitorowanie wybranych stacji telewizyjnych i radiowych"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesSearch.tsx:18
msgid "help.search.wordSearch.description"
msgstr "<0>Szybki przegląd</0><1><2><3><4><5>Podany wyraz</5><6>Co {appName} zrobi</6><7>Wyszukuje materiały zawierające</7></4></3><8><9><10>Pingwin</10><11>odmieni słowo, diakrytyka i wielkość liter nie mają znaczenia</11><12>Pingwin, PINGWIN, pingwin, Pingwinów, pingwiny, pingwin</12></9><13><14>\"Pingwin\"</14><15>nie odmieni słowa, diakrytyka ma znaczenie, wielkość liter nie ma znaczenia</15><16>Pingwin, PINGWIN, pingwin</16></13><17><18>\"!Pingwin\"</18><19>nie odmieni słowa, diakrytyka i wielkość liter mają znaczenie</19><20>Pingwin</20></17></8></2></1><21>Z odmianą</21><22>Jeśli wpiszemy do pola wyszukiwania: <23>Pingwin</23></22><24>{appName} wyszuka wszystkie materiały, które zawierają słowo <25>Pingwin</25> w dowolnej formie. Znalezione zostaną więc materiały, które zawierają słowo <26>Pingwinów</26> (odmienia słowo), <27>PINGWIN</27> (wielkość liter nie ma znaczenia) lub <28>pingwin</28> (diakrytyka nie ma znaczenia).</24><29>Zalecamy w ten sposób wyszukiwać wszystkie słowa, które w normalnym tekście są odmieniane. Są to typowo ogólne słowa (pingwin), imiona własne (Michał) lub obce nazwy (facebook).</29><30>Dokładne dopasowanie</30><31>Jeśli wpiszemy do pola wyszukiwania: <32>\"Pingwin\"</32> (umieścimy słowo w cudzysłowie)</31><33>{appName} wyszuka wszystkie materiały, które zawierają słowo <34>Pingwin</34>, ale tylko w podanej formie (tzn. bez odmiany). Znalezione zostaną więc materiały, które zawierają słowo <35>Pingwin</35> lub<36>PINGWIN</36> (wielkość liter nie ma znaczenia).</33><37>{appName} nie wyszuka tych materiałów, które zawierają tylko odmienione słowo <38>Pingwinów</38> lub słowo <39>pingwin</39> bez diakrytyki.</37><40>Zalecamy w ten sposób wyszukiwać nazwy firm i produktów (\"McDonald's\"), domeny internetowe (\"{appName}.pl\"), dokładne dopasowanie słowa (\"najlepszy\") lub skróty (\"USA\").</40><41>Dokładne dopasowanie, w tym uwzględni wielkość liter</41><42>Jeśli wpiszemy do pola wyszukiwania: <43>\"!Pingwin\"</43> (umieścimy słowo w cudzysłowie z wykrzyknikiem na początku)</42><44>{appName} wyszuka wszystkie materiały, które zawierają słowo <45>Pingwin</45>, ale tylko w podanej formie, w tym wielkość liter. Jest to najbardziej rygorystyczna wersja.</44><46>{appName} nie wyszuka tych materiałów, które zawierają np. tylko słowo <47>pingwin</47> pisane małymi literami.</46><48><49>Zalecamy w ten sposób wyszukiwać nazwy firm i produktów (\"!Lista\") lub skróty (\"!WHO\").</49></48>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesOperators.tsx:18
msgid "help.search.operators.description"
msgstr "<0>Szybki przegląd</0><1><2><3><4>Wprowadzony wyraz</4><5>{appName} wyszuka</5></3></2><6><7><8>pingwin AND foka</8><9>Materiały zawierające oba słowa <10>pingwin</10> i <11>foka</11>.</9></7><12><13>pingwin foka</13><14>Materiały zawierające oba słowa <15>pingwin</15> i <16>foka</16>. Spacja między słowami działa tak samo jak operator AND.</14></12><17><18>pingwin OR foka</18><19>Materiały zawierające co najmniej jedno ze słów <20>pingwin</20> lub <21>foka</21>.</19></17><22><23>pingwin -foka</23><24>Materiały zawierające słowo <25>pingwin</25>, ale nie zawierające słowa <26>foka</26>.</24></22></6></1><27>AND</27><28>Jeśli chcemy wyszukać materiały, które zawierają kilka słów lub fraz jednocześnie, wprowadzamy wszystkie wymagane słowa i oddzielamy je albo spacją, albo słowem <29>AND</29> (pisane wielkimi literami).</28><30>Jeśli wpiszemy do pola wyszukiwania: <31>pingwin foka Warszawskie+zoo \"!PGE\"</31></30><32>To jest to samo, co gdybyśmy napisali: <33>pingwin AND foka AND Warszawskie+zoo AND \"!PGE\"</33></32><34>OR</34><35>Jeśli chcemy wyszukać materiały, które zawierają co najmniej jedno z wprowadzonych słów lub fraz, wprowadzamy wszystkie wymagane słowa i oddzielamy je słowem <36>OR</36> (pisane wielkimi literami).</35><37>Przykład: <38>pingwin OR foka OR Warszawskie+zoo OR \"!PGE\"</38></37><39>NOT</39><40>Jeśli chcemy usunąć z wyników wyszukiwania materiały, które zawierają niektóre słowa lub frazy, wpisujemy po wyszukiwanym wyrażeniu listę zabronionych słów i fraz i przed każdym z nich stawiamy znak minus.</40><41>Przykład: <42>pingwin -foka -Warszawskie+zoo -\"!PGE\"</42></41><43>Nawiasy</43><44>Operatory wyszukiwania można łączyć według potrzeb. W przypadku bardziej skomplikowanych wyrażeń często musimy określić również kolejność, w której chcemy, aby operatory wyszukiwania były oceniane. W tym celu używamy nawiasów, które działają podobnie jak w matematyce.</44><45>Jeśli wpiszemy do pola wyszukiwania: <46>\"!Billa\" AND (sklep OR sieć OR punkt sprzedaży OR supermarket OR hipermarket)</46></45><47>{appName} wyszuka wszystkie materiały zawierające słowo <48>Billa</48> (tylko w wprowadzonej formie, włącznie z wielkością liter) w połączeniu z co najmniej jednym ze słów <49>sklep</49>, <50>sieć</50>, …</47><51>Na koniec przykład, jak skomplikowane wyrażenia można skomponować w aplikacji z operatorów wyszukiwania: <52>osiedle (dom OR budynek OR konstrukcja) AND (balkon OR (plastikowe+okna -\"!Velux\")) AND Warszawa+Wola~5 -(Mokotów OR Wilanów)</52></51>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesPhrase.tsx:18
msgid "help.search.phrase.description"
msgstr "<0>Szybki przegląd</0><1><2><3><4>Wprowadzony wyraz</4><5>Wyszukuje materiały zawierające</5></3></2><6><7><8>Mądry+Michał</8><9>Mądry Michał, MĄDRY MICHAŁ, mądry michał, Mądrego Michała, madry michal, mądry-michał</9></7><10><11>\"Mądry Michał\"</11><12>Mądry Michał, MĄDRY MICHAŁ, mądry michał</12></10><13><14>\"Mądry-Michał\"</14><15>Mądry-Michał, MĄDRY-MICHAŁ, mądry-michał</15></13><16><17>\"!Mądry Michał\"</17><18>Mądry Michał</18></16></6></1><19>Z odmianą</19><20>Jeśli wpiszemy do pola wyszukiwania: <21>Mądry+Michał</21> (słowa oddzielamy znakiem plus, tak aby nie było między nimi spacji)</20><22>{appName} wyszuka wszystkie materiały, które zawierają wyrażenie <23>Mądry Michał</23> (tzn. te słowa po sobie w tej kolejności) w dowolnej formie. Znalezione zostaną więc materiały, które zawierają wyrażenie <24>Mądrego Michała</24> (odmiana słowa), <25>MĄDRY MICHAŁ</25> (rozmiar liter nie ma znaczenia), <26>madry michal</26> (diakrytyka nie ma znaczenia) lub <27>mądry-michał</27> (między słowami może być separator, np. przecinek lub myślnik).</22><28>Zalecamy wyszukiwanie w ten sposób imion osób (Michał+Nowy), nazw firm i organizacji (Ministerstwo+Rozwoju+Lokalnego) lub wyrażeń (monitoring+mediów).</28><29>Dokładne dopasowanie</29><30>Jeśli wpiszemy do pola wyszukiwania: <31>\"Mądry Michał\"</31> (umieszczamy całe wyrażenie w cudzysłowie)</30><32>{appName} wyszuka wszystkie materiały, które zawierają wyrażenie <33>Mądry Michał</33>, ale tylko w podanej formie. Znalezione zostaną więc materiały, które zawierają <34>Mądry Michał</34> lub <35>MĄDRY MICHAŁ</35> (rozmiar liter nie ma znaczenia).</32><36>{appName} nie znajdzie tych materiałów, które zawierają tylko odmienione wyrażenie <37>Mądrego Michała</37>, wyrażenie <38>madry michal</38> bez diakrytyki lub wyrażenie <39>mądry-michał</39> z separatorem.</36><40>Zalecamy wyszukiwanie w ten sposób nazw firm i produktów (\"{appName} Media\"), skrótów (\"MFF UK\") lub dokładnego dopasowania wyrażeń (\"być albo nie być\").</40><41>Dokładne dopasowanie z separatorem</41><42>Podczas szukania dokładnego dopasowania, należy wprowadzić do cudzysłowu również separatory, które mogą występować między słowami - myślniki, podkreślenia, małpy, itp. Dotyczy to następujących znaków: & @ _ + - ' #</42><43>Jeśli wpiszemy do pola wyszukiwania: <44>\"Mądry-Michał\"</44></43><45>{appName} wyszuka wszystkie materiały, które zawierają wyrażenie <46>Mądry-Michał</46> z separatorem.</45><47>{appName} nie znajdzie tych materiałów, które zawierają tylko wyrażenie <48>Mądry Michał</48> bez separatora lub <49>Mądry&Michał</49> z innym separatorem niż podaliśmy.</47><50>Typowe wyrażenia, przy których nie zapomnij o separatorze to \"Ernst & Young\", \"info@{appName}.pl\", \"Mi+Te\" lub \"X-Men\".</50><51>Dokładne dopasowanie, włącznie z rozmiarem liter</51><52>Jeśli wpiszemy do pola wyszukiwania: <53>\"!Mądry Michał\"</53> (umieszczamy całe wyrażenie w cudzysłowie i wykrzyknik za pierwszym cudzysłowem)</52><54>{appName} wyszuka wszystkie materiały, które zawierają wyrażenie <55>Mądry Michał</55>, ale tylko w podanej formie, włącznie z rozmiarem liter. Jest to najbardziej rygorystyczna wersja.</54><56>{appName} nie znajdzie tych materiałów, które zawierają np. tylko wyrażenie <57>mądry michał</57> pisane małymi literami.</56><58>Zalecamy wyszukiwanie w ten sposób nazw firm i produktów (\"!Złota łyżeczka\") lub skrótów (\"!AV CR\").</58>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesDistance.tsx:18
msgid "help.search.distance.description"
msgstr "<0>Szybki przegląd</0><1><2><3><4>Podane wyrażenie</4><5>Wyszukuje materiały zawierające</5></3></2><6><7><8>foka+pingwin~5</8><9>pingwin pogłaskał fokę</9></7><10><11>tukan+foka+pingwin~10</11><12>tukany chciały zjeść pingwina, ale foka interweniowała</12></10><13><14>\"foka tukan\"~5</14><15>\"Foka!\" powiedział tukan i odleciał.</15></13><16><17>\"tukan foka pingwin\"~5</17><18>Na zdjęciu od lewej: pingwin, foka, tukan.</18></16></6></1><19>Z odmianą</19><20>Jeśli wpiszemy do pola wyszukiwania: <21>foka+pingwin~5</21> (słowa oddzielamy znakiem plus, a za nimi wpisujemy tyldę i liczbę)</20><22>{appName} wyszuka wszystkie materiały, które zawierają słowa <23>foka</23> i <24>pingwin</24> w dowolnej kolejności i w odległości nie większej niż 5 słów od siebie. Podane słowa są automatycznie odmieniane, a wielkość liter lub diakrytyka nie mają znaczenia.</22><25>Zalecamy wyszukiwanie w ten sposób słów, które są ze sobą powiązane i w tekście materiału będą blisko siebie (firma+Facebook~7).</25><26>Dokładne dopasowanie</26><27>Jeśli wpiszemy do pola wyszukiwania: <28>\"foka pingwin\"~5</28> (słowa umieszczamy w cudzysłowie, a za drugim cudzysłowem wpisujemy tyldę i liczbę)</27><29>{appName} wyszuka wszystkie materiały, które zawierają słowa <30>foka</30> i <31>pingwin</31> w dowolnej kolejności i w odległości nie większej niż 5 słów od siebie. Obie podane słowa są wyszukiwane tylko w podanej formie, tzn. nie są odmieniane, a diakrytyka ma znaczenie.</29>"

#: src/pages/newsroom/index.js:35
msgid "<0>Share press releases</0> and other external and internal communication with <1>Newsroom</1> and have an accurate overview of traffic directly in the application."
msgstr "<0>Udostępniaj komunikaty prasowe</0> oraz inną komunikację zewnętrzną lub wewnętrzną za pomocą <1>Newsroomu</1> i uzyskaj dokładną analizę aktywności bezpośrednio w aplikacji."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:36
msgid "help.ave"
msgstr "<0>Współczynnik AVE (Advertising Value Equivalent) reprezentuje finansową ocenę działań medialnych. Jest to ekwiwalent tego, ile kosztowałoby miejsce zdobyte treścią przeliczone na wartość powierzchni reklamowej według cennika danego medium.</0><1>Do maszynowego obliczania AVE używamy następujących zmiennych:</1><2><3>jednostkowa cena reklamy w danym medium (np. cena za stronę standardową w prasie / 1s nadanej wiadomości w TV lub radiu)</3><4>rozmiar materiału prasowego / długość reportażu w TV lub radiu</4><5>zakres informacji poświęconej tematowi w ramach postu</5></2>"

#. js-lingui-explicit-id
#: src/components/layout/Header/MessageDirty/MessageDirty.js:12
msgid "message.dirty.description"
msgstr "<0>Wyświetlane dane mogą nie odpowiadać Twoim aktualnym ustawieniom, ponieważ jeden lub więcej tematów zostało zmienionych.</0><1>Załaduj stronę ponownie za kilka minut.</1>"

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:105
msgid "help.socialInteractions"
msgstr "<0>Liczba interakcji społecznościowych (like, udostępnienie, komentarz, wyświetlenie, retweet) przy wzmiankach.</0>"

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:59
msgid "help.socialInteractionsOnline"
msgstr "<0>Liczba interakcji społecznościowych (like, share, komentarz) przy materiałach na Facebooku.</0><1>Statystyka jest aktualizowana raz na 24 godziny.</1>"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:11
msgid "missingBasics"
msgstr "<div>Dziękujemy za przesłanie posta. Przeprowadziliśmy wstępną kontrolę, aby upewnić się, że spełnia on nasze podstawowe wymagania.</div><br> <strong>Oto, co odkryliśmy:</strong>"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:15
msgid "responseInfo"
msgstr ""
"<div>Ten proces może potrwać kilka chwil, ponieważ wykonujemy następujące kroki:</div>\n"
"<ol>\n"
"<li><strong>Wstępne skanowanie:</strong> Szybkie sprawdzenie ogólnej struktury i formatu treści.</li>\n"
"<li><strong>Dogłębna analiza:</strong> Dokładne zbadanie szczegółów, języka i kontekstu przesłania.</li>\n"
"<li><strong>Ocena jakości:</strong> Ocenianie różnych aspektów, takich jak przejrzystość, spójność i trafność.</li>\n"
"<li><strong>Wykrywanie błędów:</strong> Identyfikowanie potencjalnych problemów, niespójności lub obszarów wymagających poprawy.</li>\n"
"<li><strong>Sugestie dotyczące optymalizacji:</strong> Przygotowywanie rekomendacji w celu ulepszenia treści, jeśli zajdzie taka potrzeba.</li>\n"
"</ol>\n"
"<div>Nasza sztuczna inteligencja ciężko pracuje, aby zapewnić Ci dokładne i pomocne informacje zwrotne. Doceniamy Twoją cierpliwość podczas tej kompleksowej analizy. Wyniki będą wkrótce dostępne.</div>"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:124
#: src/components/newsroom/components/PostsList/PostsList.js:98
msgid "<No title yet>"
msgstr "<Brak tytułu>"

#: src/store/models/admin/customer/CustomerStore.js:220
msgid "<user already exists>"
msgstr "<użytkownik już istnieje>"

#: src/components/tariff/TariffLimits/TariffLimits.js:26
#: src/components/staff/admin/workspace/Workspace.js:359
msgid "30-day article limit"
msgstr "30-dniowy limit materiałów"

#: src/components/tariff/UsageTracker/UsageTracker.js:13
msgid "30-day limit"
msgstr "30-dniowy limit"

#: src/components/tariff/TariffLimits/TariffLimits.js:63
#: src/components/staff/admin/workspace/Workspace.js:378
msgid "30-day limit on exported articles"
msgstr "30-dniowy limit wyeksportowanych materiałów"

#: src/components/tariff/TariffLimits/TariffLimits.js:224
#: src/components/staff/admin/workspace/Workspace.js:559
msgid "30-day limit on exported social media mentions"
msgstr "30-dniowy limit liczby wyeksportowanych wzmianek z social media"

#: src/components/tariff/TariffLimits/TariffLimits.js:241
#: src/components/staff/admin/workspace/Workspace.js:514
msgid "30-day limit on licensed article downloads"
msgstr "30-dniowy limit na pobieranie licencjonowanych artykułów"

#: src/components/staff/admin/workspace/Workspace.js:629
msgid "30-day limit on OCR pages"
msgstr "30-dniowy limit stron OCR"

#: src/components/tariff/TariffLimits/TariffLimits.js:203
#: src/components/staff/admin/workspace/Workspace.js:540
msgid "30-day limit on social media mentions"
msgstr "30-dniowy limit na liczbę wzmianek w social media"

#: src/components/staff/admin/workspace/Workspace.js:650
msgid "30-day limit on transcribed seconds"
msgstr "30-dniowy limit na liczbę sekund transkrypcji"

#: src/components/staff/admin/workspace/Workspace.js:399
msgid "30-day limit on translated articles with Google Translate"
msgstr "30-dniowy limit na liczbę materiałów przetłumaczonych przez Google Translate"

#: src/components/medialist/forms/FormEditAuthor.js:755
msgid "About Author"
msgstr "O autorze"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:132
msgid "Above avg."
msgstr "Powyżej średniej"

#: src/components/tariff/Permissions/Permissions.js:45
msgid "Access"
msgstr "Dostęp"

#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:52
msgid "Access comprehensive articles via {appName}’s media monitoring, covering online, traditional, and social media content."
msgstr "Zyskaj dostęp do pełnych materiałów za pośrednictwem aplikacji {appName}, obejmujący treści online, media tradycyjne oraz social media."

#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:49
msgid "Access Full Articles via Media Monitoring"
msgstr "Pełny dostęp do materiałów dzięki monitorowaniu mediów"

#: src/components/dashboards/PageExpiredSharedDashboard/PageExpiredSharedDashboard.js:37
msgid "Access to this dashboard has expired."
msgstr "Dostęp do tego pulpitu nawigacyjnego wygasł."

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:22
msgid "Account info"
msgstr "Informacje o koncie"

#: src/components/misc/Changelog/ChangelogTableRow.js:114
msgid "Account manager"
msgstr "Manager konta"

#: src/components/staff/admin/customer/bio/CustomerBio.js:112
msgid "Account managers"
msgstr "Menedżerowie kont"

#: src/components/settings/SettingsHeader/SettingsHeader.js:8
msgid "Account settings"
msgstr "Ustawienia konta"

#: src/components/settings/SettingsTheme/SettingsTheme.js:11
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:87
msgid "Account theme"
msgstr "Motyw konta"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:57
msgid "Activate"
msgstr "Aktywuj"

#: src/components/staff/admin/user/User.js:235
msgid "Activated"
msgstr "Aktywowany"

#: src/components/emailing/content/EmailingSettingsContent.js:76
#: src/components/emailing/content/EmailingCampaignsContent.tsx:32
msgid "Activated senders without verification:"
msgstr "Aktywowani nadawcy bez weryfikacji:"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:173
#: src/components/staff/admin/user/User.js:138
#: src/components/staff/admin/customer/users/UsersTable.js:116
#: src/components/forms/dashboard/Search/SearchUsers.js:99
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:151
msgid "Active"
msgstr "Aktywny"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:80
msgid "Active Article Language"
msgstr "Aktywny język materiałów"

#: src/components/staff/admin/workspace/ToggleActiveMedia.js:29
msgid "Active only"
msgstr "Tylko aktywne"

#: src/components/reports/Content/ReportsList/FormToggleActive/FormToggleActive.js:34
msgid "Active report"
msgstr "Aktywny newsletter"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:93
msgid "Active Source Country"
msgstr "Aktywne kraje źródłowe"

#: src/components/medialist/content/MedialistAuthorCreate.js:16
msgid "Activity Overview"
msgstr "Przegląd aktywności"

#: src/components/topics/Content/TopicsList/FormAddKeyword/FormAddKeyword.tsx:54
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:591
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:61
#: src/components/medialist/forms/modules/FormArray.js:198
#: src/components/medialist/forms/modules/FormArray.js:220
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:38
#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:190
#: src/components/emailing/forms/FormEmailRecipients.js:120
#: src/components/ReusableFeed/FormAddArticle.tsx:42
msgid "Add"
msgstr "Dodaj"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:178
msgid "Add a sender to activate Emailing."
msgstr "Dodaj nadawcę, aby aktywować Mailing."

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:92
msgid "Add all to selection"
msgstr "Dodaj wszystko do zaznaczenia"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:96
msgid "Add annotation"
msgstr "Dodaj adnotację"

#: src/helpers/modal/withModalAddArticle/withModalAddArticle.tsx:17
#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:95
#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:156
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:90
#: src/components/emailing/content/mediaCoverage/AddArticleToMediaCoverage.tsx:10
msgid "Add article"
msgstr "Dodaj artykuł"

#: src/components/emailing/content/mediaCoverage/AddArticleToMediaCoverage.tsx:13
msgid "Add article media coverage"
msgstr "Dodaj artykuł do monitoringu mediów"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:237
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:219
msgid "Add article to topic"
msgstr "Dodaj materiał do tematu"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:537
#: src/components/newsroom/content/post/AttachmentsList.js:89
msgid "Add Attachment"
msgstr "Dodaj załącznik"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:129
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:100
msgid "Add authors to list"
msgstr "Dodaj autorów do listy"

#: src/components/newsroom/forms/FormNewsroomPost/CategoriesSelector.js:71
msgid "Add Category"
msgstr "Dodaj kategorię"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:118
msgid "Add content"
msgstr "Dodaj treść"

#: src/components/dashboards/DashboardSelector/CreateDashboard.js:20
msgid "Add Dashboard"
msgstr "Dodaj pulpit"

#: src/components/reports/Content/ReportsList/AddDay.js:25
msgid "Add day"
msgstr "Dodaj dzień"

#: src/components/staff/admin/workspace/Workspace.js:827
msgid "Add domains separated by a comma (domain1.com, domain2.com)"
msgstr "Dodaj domeny oddzielone przecinkiem (domain1.com, domain2.com)"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:79
msgid "Add Gallery"
msgstr "Dodaj galerię"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:66
msgid "Add Image"
msgstr "Dodaj obraz"

#: src/components/topics/Content/TopicsList/FormAddKeyword/FormAddKeyword.tsx:45
msgid "Add Keyword"
msgstr "Dodaj słowo kluczowe"

#: src/components/newsroom/forms/FormNewsroomSettings/LanguageSection.tsx:91
msgid "Add language variant"
msgstr "Dodaj wariant językowy"

#: src/components/newsroom/content/posts/ChooseTemplates.tsx:109
msgid "Add main message"
msgstr "Dodaj główną wiadomość"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:57
msgid "Add manually"
msgstr "Dodaj ręcznie"

#: src/components/newsroom/components/AiTools/AiGenerateContent.tsx:117
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:137
msgid "Add missing data"
msgstr "Dodaj brakujące dane"

#: src/components/emailing/components/EmailRecipientsList/RecipientsButton.tsx:37
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:164
msgid "Add Missing Info"
msgstr "Dodaj brakujące informacje"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:64
msgid "Add new keypoint"
msgstr "Dodaj nowy kluczowy punkt"

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:111
msgid "Add new mediatypes"
msgstr "Dodaj nowe typy mediów"

#: src/components/newsroom/content/modules/CustomQuotes.tsx:84
msgid "Add new quote"
msgstr "Dodaj nowy cytat"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:52
msgid "Add new sender"
msgstr "Dodaj nowego nadawcę"

#: src/components/topics/Content/TopicsList/TopicsList.js:63
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:44
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:70
msgid "Add New Topic"
msgstr "Dodaj nowy temat"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:28
#: src/components/medialist/forms/FormEditAuthor.js:710
msgid "Add note"
msgstr "Dodaj notatkę"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:37
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:57
msgid "Add note to article"
msgstr "Dodaj notatkę do materiału"

#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:55
msgid "Add recipient"
msgstr "Dodaj odbiorcę"

#: src/components/emailing/modules/PreviewEmail/RecipientsIsEmpty.tsx:32
#: src/components/emailing/content/tabs/AddRecipients.tsx:78
msgid "Add recipients"
msgstr "Dodaj odbiorców"

#: src/components/emailing/content/Signature.tsx:113
#: src/components/emailing/content/Signature.tsx:116
msgid "Add signature"
msgstr "Dodaj podpis"

#: src/components/emailing/forms/FormEmailRecipients.js:112
msgid "Add single authors, author’s lists or emails"
msgstr "Dodaj pojedynczych autorów, listy autorów lub e-maile"

#: src/components/newsroom/content/modules/CustomQuotes.tsx:35
msgid "Add specific quotes you want to include in your article, along with the name of the person being quoted. We will use these quotes exactly as provided.\""
msgstr "Dodaj konkretne cytaty, które chcesz uwzględnić w swoim artykule, wraz z nazwiskiem osoby, którą cytujesz. Użyjemy tych cytatów dokładnie tak, jak zostały podane.”"

#: src/components/reports/Content/ReportsList/AddTime.js:27
msgid "Add time"
msgstr "Dodaj czas"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/ModalAddDashboardFooter.tsx:35
msgid "Add to Dashboard"
msgstr "Dodaj do pulpitu"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:179
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:210
msgid "Add to export"
msgstr "Dodaj do eksportu"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:38
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:94
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:150
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:207
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:130
msgid "Add to filters"
msgstr "Dodaj do filtrów"

#: src/components/medialist/forms/FormEditAuthor.js:670
#: src/components/medialist/content/withAddToBasketPopup.js:44
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorBaskets/AddAuthorToBasket.js:26
msgid "Add to list"
msgstr "Dodaj do listy"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:263
msgid "Add to next report"
msgstr "Dodaj do następnego raportu"

#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:282
msgid "Add to report"
msgstr "Dodaj do raportu"

#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:91
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Keywords.js:52
msgid "Add to search"
msgstr "Dodaj do wyszukiwania"

#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:58
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:60
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:895
msgid "Add Topic"
msgstr "Dodaj temat"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:41
#: src/components/settings/SettingsUserManagement/AddUsers.tsx:23
msgid "Add users"
msgstr "Dodaj użytkowników"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:42
msgid "Add users to workspace"
msgstr "Dodaj użytkowników do obszaru roboczego"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:101
msgid "Add Video"
msgstr "Dodaj wideo"

#: src/components/dashboards/Content.js:89
#: src/components/dashboards/Content.js:90
msgid "Add Widget"
msgstr "Dodaj widget"

#: src/store/models/ExportStore.js:316
#: src/store/models/monitoring/Inspector/Inspector.ts:449
msgid "Added to export."
msgstr "Dodane do eksportu."

#: src/store/models/monitoring/Inspector/Inspector.ts:422
msgid "Added to next report."
msgstr "Dodane do raportu."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:29
msgid "Additional settings"
msgstr "Dodatkowe ustawienia"

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorAddress/AuthorAddress.js:8
msgid "Address"
msgstr "Adres"

#: src/constants/analytics.js:143
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:42
#: src/components/misc/ActionsBar/View/ViewMenu.js:237
msgid "Adjusted Reach"
msgstr "Dotarcie Skorygowane"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:160
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:162
#: src/components/staff/admin/workspace/Workspace.js:162
#: src/components/staff/admin/user/getUserAttributes.js:9
#: src/components/staff/admin/user/User.js:88
#: src/components/reports/history/HistoryTable.js:452
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:60
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:62
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:406
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:413
#: src/components/medialist/forms/FormEditAuthor.js:396
#: src/components/medialist/forms/FormEditAuthor.js:542
#: src/components/layout/Header/UserMenu/UserMenu.tsx:200
#: src/app/components/monitoring-navigation.tsx:314
msgid "Admin"
msgstr "Admin"

#: src/components/reports/Content/ReportsList/ReportsForm.js:331
#: src/components/forms/dashboard/ExportResend/ExportResend.js:133
msgid "Advanced attachment settings"
msgstr "Zaawansowane ustawienia załącznika"

#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:15
msgid "Advanced export settings"
msgstr "Zaawansowane ustawienia eksportu"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:79
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:174
msgid "Advanced settings"
msgstr "Zaawansowane ustawienia"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:58
msgid "Advanced template settings"
msgstr "Zaawansowane ustawienia szablonu"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:255
msgid "Advertising Value Equivalency"
msgstr "Równoważność wartości reklamowej"

#: src/constants/analytics.js:101
#: src/constants/analytics.js:621
#: src/constants/analytics.js:755
#: src/components/layout/AuthWrapper/constants/features.slides.js:191
msgid "Advertising Value Equivalent (AVE)"
msgstr "Równoważnik wartości reklamowej (AVE)"

#: src/constants/analytics.js:99
msgid "Advertising Value Equivalent (AVE) by sentiment"
msgstr "Równowartość reklamowa (AVE) według sentymentu"

#: src/components/staff/admin/workspace/Workspace.js:920
#: src/components/settings/SettingsTariff/SettingsTariff.js:37
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:48
msgid "Agency media"
msgstr "Media agencyjne"

#: src/components/newsroom/components/AiTools/AiCheckPostResult.tsx:88
msgid "AI check"
msgstr "AI Sprawdzenie"

#: src/components/newsroom/components/AiTools/AiCheckLoadingInfo.tsx:28
msgid "AI Checkup information"
msgstr "Informacje o AI kontroli"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:218
msgid "Align Center"
msgstr "Wyśrodkuj"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:230
msgid "Align Justify"
msgstr "Wyjustuj"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:212
msgid "Align Left"
msgstr "Wyrównaj do lewej"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:224
msgid "Align Right"
msgstr "Wyrównaj do prawej"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:83
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:37
#: src/components/reports/history/HistoryTable.js:86
#: src/components/reports/history/HistoryTable.js:96
#: src/components/reports/history/HistoryTable.js:331
#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:123
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:144
#: src/components/misc/portable/PortableExport/CounterTitle.js:8
#: src/components/misc/ActionsBar/Selector/Selector.js:51
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:146
#: src/components/analytics/AnalyticsContent.js:156
#: src/components/analytics/AnalyticsContent.js:166
msgid "All"
msgstr "Wszystkie"

#: src/store/models/ExportStore.js:318
msgid "All articles are already in export."
msgstr "Wszystkie materiały są już w eksporcie."

#: src/components/exportList/Content/Content.tsx:97
#: src/app/components/monitoring-navigation.tsx:130
msgid "All articles will be removed from export."
msgstr "Wszystkie materiały zostaną usunięte z eksportu."

#: src/components/misc/portable/PortableExport/CounterTitle.js:10
msgid "All except"
msgstr "Wszystko oprócz"

#: src/components/layout/AuthWrapper/constants/features.slides.js:400
msgid "All features of the browser app are accessible on a mobile device. The app keeps you informed even when you are drinking a morning cup of coffee."
msgstr "Wszystkie funkcje aplikacji przeglądarkowej są dostępne na urządzeniu mobilnym. Aplikacja informuje Cię o wszystkim, nawet gdy pijesz poranną kawę."

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
msgid "all mediatypes for"
msgstr "wszystkie typy mediów dla"

#: src/store/models/Megalist/MegalistFilter.js:49
#: src/components/topics/Content/TopicsList/MegalistToolbar/MediatypeFilterPopup.js:11
msgid "All Sources"
msgstr "Wszystkie źródła"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitorsReports.js:40
msgid "All topics"
msgstr "Wszystkie tematy"

#: src/components/medialist/forms/FormEditAuthor.js:576
msgid "All unsaved changes will be lost. Do you really want to cancel the changes?"
msgstr "Wszystkie niezapisane zmiany zostaną utracone. Czy na pewno chcesz anulować zmiany?"

#: src/components/staff/admin/workspace/Workspace.js:743
msgid "Allow adjusted reach (PL)"
msgstr "Zezwól na dostosowany zasięg (PL)"

#: src/components/staff/admin/workspace/Workspace.js:716
msgid "Allow automatic sentiment"
msgstr "Zezwól na automatyczną detekcję sentymentu"

#: src/components/staff/admin/workspace/Workspace.js:725
msgid "Allow automatic summarization"
msgstr "Zezwól na automatyczne podsumowanie"

#: src/components/staff/admin/workspace/Workspace.js:734
msgid "Allow AVE"
msgstr "Zezwól na AVE"

#: src/components/staff/admin/workspace/Workspace.js:752
msgid "Allow AVE Coefficient (for media analysis)"
msgstr "Zezwól na współczynnik AVE (dla analizy mediów)"

#: src/components/staff/admin/workspace/Workspace.js:707
msgid "Allow custom logo"
msgstr "Zezwól na własne logo"

#: src/components/staff/admin/workspace/Workspace.js:598
msgid "Allow english social media"
msgstr "Zezwól na posty z soc. media w języku angielskim"

#: src/components/staff/admin/workspace/Workspace.js:772
msgid "Allow forcing articles to email reports"
msgstr "Zezwól na funkcję \"Wyślij materiał w następnym raporcie\""

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:93
msgid "Allow search engines to index this blog (including inclusion of articles in media monitoring and analysis of online mentions)"
msgstr "Zezwól wyszukiwarkom na indeksowanie tego bloga (w tym na uwzględnienie artykułów w monitoringu mediów i analizie wzmianek online)"

#: src/components/staff/admin/workspace/Workspace.js:609
msgid "Allow users to create own articles"
msgstr "Zezwól użytkownikom na tworzenie własnych materiałów"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:31
msgid "Allowing detailed tracking of distribution campaigns."
msgstr "Umożliwia szczegółowe śledzenie kampanii dystrybucyjnych."

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:63
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:86
#: src/components/staff/admin/customer/expenses/ExpenseTable.js:81
#: src/components/staff/admin/customer/expenses/DetailExpenseModal.js:38
msgid "Amount"
msgstr "Kwota"

#: src/components/emailing/content/EmailingSettingsContent.js:32
msgid "An email sender record with this address already exists. Please check your existing records or try again."
msgstr "Rekord nadawcy e-maila z tym adresem już istnieje. Sprawdź istniejące rekordy lub spróbuj ponownie."

#: src/pages/_error.js:36
msgid "An error {statusCode} occurred on server"
msgstr "Na serwerze wystąpił błąd {statusCode}"

#: src/pages/_error.js:37
msgid "An error occurred on client"
msgstr "Wystąpił błąd na kliencie"

#: src/components/emailing/content/EmailingSettingsContent.js:17
msgid "An error occurred while authorizing our application to use the external service."
msgstr "Wystąpił błąd podczas autoryzacji naszej aplikacji do korzystania z usługi zewnętrznej."

#: src/components/staff/admin/user/getUserAttributes.js:19
msgid "Analyst"
msgstr "Analityk"

#: src/store/models/dashboards/DashboardPreview.js:87
#: src/pages/analytcs.js:16
#: src/components/widgets/modules/stats/WidgetStats.js:67
#: src/components/widgets/modules/analytics/WidgetAnalytics.js:29
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:102
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:27
#: src/components/layout/AuthWrapper/constants/features.slides.js:165
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:16
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:16
#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:36
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:36
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormSubtype/FormSubtype.js:56
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:20
#: src/components/analytics/AnalyticsContent.js:105
#: src/app/components/monitoring-navigation.tsx:81
msgid "Analytics"
msgstr "Analityka"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:140
msgid "AND"
msgstr "I"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderVideo/HeaderVideo.js:43
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderSocial/HeaderSocial.js:25
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:37
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:69
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationHeader.js:25
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationHeader.js:29
#: src/components/feed/InspectorToolbar/InspectorToolbar.js:134
msgid "Annotation"
msgstr "Adnotacja"

#: src/pages/user/yoy-analysis.js:34
msgid "Annual Media Analysis"
msgstr "Roczna analiza mediów"

#: src/components/notifications/Content.js:28
msgid "App"
msgstr "Aplikacja"

#: src/components/staff/admin/workspace/Workspace.js:932
#: src/components/settings/SettingsTariff/SettingsTariff.js:45
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:55
msgid "Application permissions"
msgstr "Uprawnienia aplikacji"

#: src/components/settings/SettingsApplication/SettingsApplication.js:19
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:98
msgid "Application settings"
msgstr "Ustawienia aplikacji"

#: src/components/newsroom/components/AiTools/AiGenerateContent.tsx:138
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:161
msgid "Apply"
msgstr "Zastosować"

#: src/components/medialist/content/FeedMedialist/FeedMedialistItem.js:229
msgid "archive"
msgstr "archiwum"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:22
msgid "Archive"
msgstr "Archiwum"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:129
msgid "Are you ready to send the email?"
msgstr "Czy jesteś gotowy wysłać e-mail?"

#. placeholder {0}: item.filename
#. placeholder {0}: file.name
#: src/components/newsroom/content/post/AttachmentsList.js:66
#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:42
msgid "Are you sure you want to delete {0}?"
msgstr "Czy na pewno chcesz usunąć {0}?"

#: src/components/emailing/content/SignaturePopup.tsx:36
msgid "Are you sure you want to delete signature?"
msgstr "Czy na pewno chcesz usunąć podpis?"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:397
msgid "Are you sure you want to delete this blog post?"
msgstr "Czy na pewno chcesz usunąć ten wpis na blogu?"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:87
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:100
msgid "Are you sure you want to delete this email?"
msgstr "Czy na pewno chcesz usunąć ten mail?"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:122
msgid "Are you sure you want to delete this sender?"
msgstr "Czy na pewno chcesz usunąć tego nadawcę?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:662
msgid "Are you sure you want to delete your Newsroom? This action will delete all articles and settings."
msgstr "Czy na pewno chcesz usunąć swój Newsroom? Ta akcja usunie wszystkie materiały i ustawienia."

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:299
msgid "Are you sure you want to publish the changes?"
msgstr "Czy na pewno chcesz opublikować zmiany?"

#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:110
msgid "Are you sure you want to remove all recipients from this report?"
msgstr "Czy na pewno chcesz usunąć wszystkich odbiorców z tego raportu?"

#: src/components/staff/admin/workspace/UsersTable/RemoveUsers.tsx:27
msgid "Are you sure you want to remove these users from the workspace?"
msgstr "Czy na pewno chcesz usunąć tych użytkowników z przestrzeni roboczej?"

#: src/components/newsroom/content/posts/NewsroomPosts.js:206
msgid "Are you sure you want to remove this article?"
msgstr "Czy na pewno chcesz usunąć ten materiał?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:679
msgid "Are you sure you want to remove this Newsroom?"
msgstr "Czy na pewno chcesz usunąć ten Newsroom?"

#: src/components/staff/admin/workspace/UsersTable/RemoveUser.js:30
#: src/components/staff/admin/user/WorkspacesTable.js:143
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:400
msgid "Are you sure you want to remove this user from the workspace?"
msgstr "Czy na pewno chcesz usunąć tego użytkownika z obszaru roboczego?"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:300
msgid "Are you sure you want to set this post to draft?"
msgstr "Czy na pewno chcesz oznaczyć ten post jako wersję roboczą?"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:205
msgid "area"
msgstr "powierzchnia"

#: src/components/OurChart/OurChartAdvanced.js:148
msgid "Area"
msgstr "Powierzchnia"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:72
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:56
#: src/components/misc/MntrEditor/extensions/ExtensionArticle.tsx:33
msgid "Article"
msgstr "Materiał"

#. placeholder {0}: item.title
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:364
msgid "Article '<0>{0}</0>' will be removed."
msgstr "Materiał '<0>{0}</0>' zostanie usunięty."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:575
msgid "Article '<0>{title}</0>' will be removed."
msgstr "Materiał '<0>{title}</0>' zostanie usunięty."

#: src/components/misc/ActionsBar/View/ViewMenu.js:140
msgid "Article Area"
msgstr "Obszar materiału"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:53
msgid "Article can still be attached later"
msgstr "Artykuł można dołączyć później"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:34
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:48
msgid "Article clipping"
msgstr "Fragment materiału"

#: src/store/models/OurChart.js:531
#: src/store/models/OurChart.js:563
#: src/store/models/OurChart.js:788
#: src/constants/stats.ts:11
#: src/constants/analytics.js:26
#: src/constants/analytics.js:1097
#: src/components/widgets/modules/stats/WidgetStats.js:203
#: src/components/widgets/modules/stats/WidgetStats.js:216
#: src/components/widgets/modules/stats/WidgetStats.js:229
#: src/components/monitoring/FeedChart/FeedChart.js:28
#: src/components/medialist/content/MedialistInspector/AuthorChart/AuthorChart.js:65
msgid "Article count"
msgstr "Liczba materiałów"

#: src/components/topics/Content/TopicsList/KeywordStatsTable.js:23
msgid "Article count for the last 30 days"
msgstr "Liczba materiałów z ostatnich 30 dni"

#: src/constants/analytics.js:192
msgid "Article count vs. GRP vs. AVE"
msgstr "Liczba materiałów vs. GRP vs. AVE"

#: src/store/models/monitoring/Inspector/Inspector.ts:778
msgid "Article has been copied to the clipboard."
msgstr "Materiał został skopiowany do schowka."

#: src/store/models/monitoring/WorkspaceArticles.js:219
msgid "Article has been removed."
msgstr "Materiał został usunięty."

#: src/store/models/monitoring/WorkspaceArticles.js:193
msgid "Article has been updated."
msgstr "Materiał został zaktualizowany."

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:215
msgid "Article has no annotations assigned. Select the text to add."
msgstr "Materiał nie ma przypisanych żadnych adnotacji. Wybierz tekst, który chcesz dodać."

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:63
msgid "Article history"
msgstr "Historia materiału"

#: src/store/models/monitoring/Inspector/Inspector.ts:451
msgid "Article is already in export."
msgstr "Materiał jest już w eksporcie."

#: src/components/article/Content.js:17
msgid "Article link has expired"
msgstr "Link do materiału wygasł"

#: src/components/layout/MntrActiveFilters/modules/ArticleMentions.js:12
msgid "Article mentions"
msgstr "Wzmianki o materiale"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:146
msgid "Article numbering"
msgstr "Numeracja materiałów"

#: src/store/models/admin/customer/CustomerStore.js:303
msgid "Article recreation started successfully."
msgstr "Materiały są ponownie generowane."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:181
msgid "Article screenshot"
msgstr "Zrzut ekranu materiałów"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:37
#: src/components/layout/MntrActiveFilters/modules/EmptyTags.js:23
msgid "Article Tags"
msgstr "Tagi materiałów"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:441
msgid "Article Text"
msgstr "Tekst materiału"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:83
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:129
msgid "Article transcript"
msgstr "Transkrypcja materiału"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:101
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:381
msgid "Article Type"
msgstr "Typ materiału"

#: src/components/ReusableFeed/FormAddArticle.tsx:30
msgid "Article URL"
msgstr "URL artykułu"

#: src/store/models/monitoring/Inspector/Inspector.ts:751
#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:58
#: src/components/monitoring/FeedList/FeedListItem/FeedListItem.js:138
msgid "Article URL has been copied to the clipboard. Without a login, it will be accessible for 30 days."
msgstr "Adres URL materiału został skopiowany do schowka. Bez logowania będzie dostępny przez 30 dni."

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:70
msgid "Article view"
msgstr "Wyświetlanie materiału"

#: src/store/models/monitoring/Inspector/Inspector.ts:551
msgid "Article was reported"
msgstr "Materiał został zgłoszony"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:278
msgid "Article was successfully published on your Newsroom page"
msgstr "Artykuł został pomyślnie opublikowany"

#: src/store/models/dashboards/DashboardPreview.js:75
#: src/components/trash/Content.js:55
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:95
#: src/components/newsroom/content/posts/NewsroomPosts.js:80
#: src/components/medialist/constants/medialist.tabNavigation.js:27
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:20
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:18
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:109
#: src/components/layout/MntrActiveFilters/modules/MedialistArticles.js:18
#: src/components/forms/dashboard/Search/SearchNewsroom.js:31
#: src/components/exportList/History/HistoryTable/HistoryTable.js:63
#: src/components/exportList/Content/HeadingExport/HeadingExport.js:36
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:30
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:30
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:58
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:17
#: src/app/components/monitoring-navigation.tsx:71
msgid "Articles"
msgstr "Wyniki"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:525
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:808
msgid "Articles updated successfully."
msgstr "Materiały zostały pomyślnie zaktualizowane."

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:38
msgid "Assess competitors and trends to refine your strategy."
msgstr "Analizuj mocne i słabe strony konkurencji, obserwuj trendy i doskonal swoją strategię."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:28
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:64
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:260
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:361
#: src/components/monitoring/FeedActionsBar/withAddTagPopup/AddTagPopupContent.js:16
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:50
msgid "Assign tag"
msgstr "Przypisz tag"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:34
msgid "Assign tag to article"
msgstr "Przypisz tag do materiału"

#: src/components/medialist/forms/FormEditAuthor.js:626
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorTags/AddAuthorTag.js:19
msgid "Assign tag to author"
msgstr "Przypisz tag do autora"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:25
msgid "Assistant creates a draft of the email content based on your specific needs"
msgstr "Asystent tworzy szkic treści e-maila na podstawie Twoich specyficznych potrzeb"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:192
msgid "Attached articles"
msgstr "Załączone artykuły"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:238
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:52
msgid "Attachment"
msgstr "Załącznik"

#: src/components/reports/history/HistoryTable.js:173
#: src/components/newsroom/content/post/AttachmentsList.js:81
msgid "Attachments"
msgstr "Załączniki"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:68
msgid "Audio"
msgstr "Audio"

#: src/constants/analytics.js:1094
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:485
#: src/components/newsroom/content/modules/CustomQuotes.tsx:69
#: src/components/monitoring/Inspector/InspectorMonitora/AuthorsList/AuthorsList.js:21
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:258
msgid "Author"
msgstr "Autor"

#: src/components/medialist/constants/medialist.tabNavigation.js:12
msgid "Author Detail"
msgstr "Szczegóły autora"

#: src/components/medialist/content/AuthorBasketsMenu.js:26
#: src/components/medialist/content/AuthorBasketSelectorButton.js:8
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:19
msgid "Author Lists"
msgstr "Listy autorów"

#: src/pages/authors/index.js:89
msgid "Author tags"
msgstr "Tagi autora"

#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:874
msgid "Author Tags"
msgstr "Tagi autorów"

#: src/components/medialist/forms/FormEditAuthor.js:233
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:122
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:67
msgid "Author type"
msgstr "Typ autora"

#: src/store/models/authors/AuthorsStore.js:1079
msgid "Author was deleted."
msgstr "Autor został usunięty."

#: src/store/models/authors/AuthorsStore.js:1168
msgid "Author was reported."
msgstr "Autor został zgłoszony."

#: src/components/medialist/forms/FormEditAuthor.js:316
#: src/components/medialist/forms/FormEditAuthor.js:481
msgid "Author will be deleted."
msgstr "Autor zostanie usunięty."

#: src/components/layout/MntrFiltersBar/forms/FormAuthor/FormAuthor.tsx:18
msgid "Author's name"
msgstr "Imię autora"

#: src/components/medialist/forms/FormEditAuthor.js:997
msgid "Author's shortname"
msgstr "Skrót nazwiska autora"

#: src/components/medialist/forms/FormEditAuthor.js:834
#: src/components/medialist/forms/FormEditAuthor.js:992
msgid "Author's shortnames"
msgstr "Skróty nazwisk autora"

#: src/components/monitoring/Inspector/InspectorMonitora/AuthorsList/AuthorsList.js:21
#: src/components/medialist/content/MedialistDashboard.js:82
#: src/components/medialist/content/MedialistDashboard.js:115
#: src/components/layout/Sidebar/modules/AuthorsNavigation/AuthorsNavigation.js:20
#: src/components/forms/dashboard/Search/SearchAuthors.js:39
#: src/components/emailing/forms/FormEmailRecipients.js:131
#: src/components/emailing/components/EmailRecipientsList/RenderAllRecipients.tsx:40
msgid "Authors"
msgstr "Autorzy"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:560
#: src/store/models/authors/AuthorsStore.js:535
msgid "Authors added."
msgstr "Autorzy zostali dodani."

#: src/components/emailing/content/tabs/AddRecipients.tsx:69
msgid "Authors lists"
msgstr "Listy autorów"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:598
#: src/store/models/authors/AuthorsStore.js:603
msgid "Authors removed."
msgstr "Autorzy zostali usunięci."

#: src/store/models/authors/AuthorsStore.js:664
msgid "Authors updated successfully."
msgstr "Autorzy zostali pomyślnie zaktualizowani."

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:94
msgid "Authors with types “agency”, “publisher” or “editorial office” can’t use merge tags  *|LAST_NAME|*,  *|VOKATIV_L|*. If you want to apply these merge tags to the author, change their type to “author” or “blogger” and add the last name."
msgstr "Autorzy o typie „agencja”, „wydawca” lub „redakcja” nie mogą używać znaczników scalania *|LAST_NAME|*, *|VOKATIV_L|*. Jeśli chcesz zastosować te znaczniki scalania do autora, zmień jego typ na „autor” lub „bloger” i dodaj nazwisko."

#: src/components/staff/admin/user/User.js:62
msgid "Autologin link"
msgstr "Link do autologinu"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:178
msgid "Automatic summary"
msgstr "Automatyczne podsumowanie"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:270
#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:74
msgid "Automatic transcript"
msgstr "Automatyczna transkrypcja"

#: src/components/tariff/TariffLimits/TariffLimits.js:99
msgid "Automatic translations 30-day limit"
msgstr "30-dniowy limit na liczbę przetłumaczonych materiałów"

#: src/constants/stats.ts:6
#: src/constants/analytics.js:994
#: src/components/widgets/modules/stats/WidgetStats.js:154
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:387
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:252
#: src/components/misc/ActionsBar/View/ViewMenu.js:203
msgid "AVE"
msgstr "AVE"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:237
msgid "AVE and sentiment"
msgstr "AVE i sentyment"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:39
msgid "AVE Coefficient"
msgstr "Współczynnik AVE"

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:23
msgid "formatNumber.B"
msgstr "mld."

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:165
#: src/components/notifications/AppNotifications/AppNotifications.js:18
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:197
#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepTitleAndCommunicationPlan.tsx:106
#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:82
#: src/components/newsroom/content/posts/ChooseTemplates.tsx:98
#: src/components/newsroom/components/NewsroomHeading/NewsroomHeading.js:20
#: src/components/misc/ActionsBar/View/ViewMenu.js:40
#: src/components/misc/ActionsBar/View/ViewMenu.js:260
#: src/components/medialist/content/MedialistActionsBar/FormTransformContacts.tsx:30
#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:73
#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:97
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:49
#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:31
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:145
#: src/components/layout/Header/AppNotifications/AppNotifications.js:110
#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:163
#: src/components/emailing/content/CreateEmailContent.js:296
msgid "Back"
msgstr "Wstecz"

#: src/components/page/auth/ResetPassword/ResetPasswordFooter.js:29
msgid "Back to Log In"
msgstr "Wróć do logowania"

#: src/components/page/auth/ResetPassword/ResetPasswordFooter.js:19
msgid "Back to settings"
msgstr "Wróć do ustawień"

#: src/pages/_error.js:61
#: src/pages/404.js:29
#: src/pages/user/yoy-analysis.js:79
#: src/pages/user/reactivate-24.js:79
#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:28
#: src/components/page/auth/UserInactive/UserInactive.js:25
#: src/components/page/auth/Expired/Expired.js:119
#: src/components/layout/ErrorCustom/ErrorCustom.js:13
#: src/app/not-found-content.tsx:35
msgid "Back to the main page"
msgstr "Wróć do głównej strony"

#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:68
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:107
msgid "Background Color"
msgstr "Kolor tła"

#: src/constants/analytics.js:529
#: src/constants/analytics.js:547
#: src/constants/analytics.js:565
#: src/constants/analytics.js:584
#: src/constants/analytics.js:602
#: src/constants/analytics.js:620
#: src/constants/analytics.js:638
#: src/constants/analytics.js:658
#: src/components/OurChart/OurChartAdvanced.js:141
msgid "Bar"
msgstr "Słupkowy"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:46
msgid "Basic settings"
msgstr "Podstawowe ustawienia"

#: src/components/layout/MntrActiveFilters/modules/Paywalled.js:6
msgid "Behind paywall"
msgstr "Za paywallem"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:141
msgid "Below avg."
msgstr "Poniżej średniej"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:81
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:220
msgid "best"
msgstr "najlepszy"

#: src/constants/analytics/primeScoreCharts.ts:95
msgid "Best PRIMe mediatypes"
msgstr "Najlepsze mediatypy PRIMe"

#: src/components/staff/admin/customer/bio/CustomerBio.js:106
msgid "Billing email"
msgstr "Mail do faktury"

#: src/components/medialist/forms/FormEditAuthor.js:855
#: src/components/medialist/forms/FormEditAuthor.js:1023
msgid "Bio"
msgstr "Bio"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:273
msgid "Blockquote"
msgstr "Cytat"

#: src/store/models/newsroom/blogs/posts/NewsroomPostsStoreArrItem.ts:106
msgid "Blog post was successfully deleted."
msgstr "Post na blogu został pomyślnie usunięty."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:123
msgid "Bold"
msgstr "Pogrubienie"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:158
msgid "Brackets"
msgstr "Nawiasy"

#: src/pages/brand-tracking.tsx:29
#: src/components/layout/Sidebar/SidebarNavigation.tsx:169
#: src/app/components/monitoring-navigation.tsx:301
msgid "Brand Tracking"
msgstr "Śledzenie marki"

#: src/components/monitoring/Inspector/InspectorMonitora/KeywordsPagination/KeywordsPagination.js:256
msgid "Browse keywords"
msgstr "Przeglądaj słowa kluczowe"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:257
msgid "Bullet list"
msgstr "Lista punktowana"

#: src/components/newsroom/components/PostsList/PostsList.js:162
msgid "By"
msgstr "Przez"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:48
msgid "by source"
msgstr "według źródła"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:152
msgid "By submitting the form, you agree to our <0>terms</0>."
msgstr "Przesyłając formularz, zgadzasz się z naszymi <0>warunkami</0>."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:61
msgid "Call to Action (CTA):"
msgstr "Wezwanie do działania (CTA):"

#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:136
msgid "Campaign"
msgstr "Kampania"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:54
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:92
msgid "Campaign will be removed"
msgstr "Kampania zostanie usunięta"

#: src/components/forms/dashboard/Search/SearchEmailingCampaigns.js:26
#: src/components/emailing/sidebar/EmailingSidebarDashboard.js:17
#: src/components/emailing/content/EmailingCampaignsContent.tsx:49
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:44
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:57
msgid "Campaigns"
msgstr "Kampanie"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:139
msgid "Can unsubscribe"
msgstr "Możesz zrezygnować z subskrypcji"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:76
msgid "Can't find an article in your feed? Enter a link to the article you are looking for and select a topic."
msgstr "Nie możesz znaleźć materiału w swoim kanale? Wprowadź link do materiału, którego szukasz i wybierz temat."

#: src/components/reports/Content/ReportsList/ReportsForm.js:350
#: src/components/misc/VideoPlayer/getCropAction.js:7
#: src/components/misc/MntrForm/MntrForm.tsx:516
#: src/components/medialist/forms/FormEditAuthor.js:559
#: src/components/medialist/forms/FormEditAuthor.js:571
#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:86
msgid "Cancel"
msgstr "Anuluj"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:273
msgid "Cancel choice"
msgstr "Anuluj wybór"

#: src/components/misc/Changelog/ChangelogTableRow.js:247
msgid "Cancel revert"
msgstr "Anuluj cofanie"

#: src/components/misc/UploadWatcher/UploadWatcher.js:40
msgid "Cancel Upload"
msgstr "Anuluj przesyłanie"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:35
msgid "Cannot delete articles, run manual sentiment, create/edit topics or reports, change account settings, delete TV/radio stories, or edit CRM info."
msgstr "Brak dostępu do kanałów monitorowania, wyszukiwania w archiwum, analiz, ustawień tematów lub raportów, komunikacji kryzysowej, listy mediów ani ustawień użytkownika."

#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/MediaDetail.js:120
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:279
msgid "Captured on the screen"
msgstr "Przechwycone na ekranie"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:67
msgid "Categories"
msgstr "Kategorie"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:29
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNewsroomCategory.js:24
#: src/components/layout/MntrActiveFilters/modules/NewsroomCategory.js:21
msgid "Category"
msgstr "Kategoria"

#. placeholder {0}: item.name
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:146
msgid "Category <0>{0}</0> will be removed."
msgstr "Kategoria <0>{0}</0> zostanie usunięta."

#: src/components/newsroom/forms/FormCreateCategory/FormCreateCategory.js:25
msgid "Category name"
msgstr "Nazwa kategorii"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:47
msgid "Change email"
msgstr "Zmień adres mailowy"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:27
#: src/components/settings/SettingsUserManagement/UpdateRole.tsx:20
msgid "Change role"
msgstr "Zmień rolę"

#: src/components/misc/Changelog/ChangelogTable.js:36
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:394
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChangeType.js:24
msgid "Change Type"
msgstr "Zmień Typ"

#: src/pages/topics/[topicId]/changelog.js:13
#: src/pages/staff/admin/workspaces/[workspaceId]/changelog.js:12
#: src/pages/reports/[reportId]/changelog.js:13
#: src/components/staff/admin/workspace/Workspace.js:147
msgid "Changelog"
msgstr "Historia zmian"

#: src/store/models/admin/customer/CustomerStore.js:163
#: src/store/models/admin/customer/CustomerStore.js:177
#: src/store/models/admin/customer/CustomerStore.js:186
#: src/store/models/admin/customer/CustomerStore.js:231
#: src/store/models/admin/customer/CustomerStore.js:258
#: src/store/models/admin/customer/CustomerStore.js:286
msgid "Changes successfully saved."
msgstr "Zmiany zostały pomyślnie zapisane."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:27
msgid "Channel"
msgstr "Kanał"

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:241
msgid "Channels"
msgstr "Kanały"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:169
msgid "Chart"
msgstr "Wykres"

#: src/components/OurChart/OurChartAdvanced.js:128
msgid "Chart Settings"
msgstr "Ustawienia wykresu"

#: src/components/OurChart/OurChartAdvanced.js:137
msgid "Chart Type"
msgstr "Typ wykresu"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:238
msgid "Check"
msgstr "Sprawdzać"

#: src/components/emailing/content/tabs/AddRecipients.tsx:80
msgid "Choose authors list or tag:"
msgstr "Wybierz listę autorów lub tag:"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:35
msgid "Choose how to add/edit your signature"
msgstr "Wybierz sposób dodania/edytowania podpisu"

#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:44
msgid "Clear Color"
msgstr "Wyczyść kolor"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:139
msgid "Clear formatting"
msgstr "Wyczyść formatowanie"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/EntityInfoBox.js:220
msgid "click to open the detail"
msgstr "kliknij, aby otworzyć szczegóły"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:342
msgid "Click to see options"
msgstr "Kliknij, aby zobaczyć opcje"

#: src/components/forms/inspector/FormMediaEditor.js:124
msgid "Clip duration"
msgstr "Czas trwania klipu"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:127
#: src/components/reports/history/Compose.js:36
#: src/components/misc/portable/PortableResend/PortableResend.js:59
#: src/components/misc/portable/PortableResend/PortableResend.js:99
#: src/components/misc/portable/PortableExport/PortableExport.js:55
#: src/components/misc/portable/PortableExport/PortableExport.js:95
#: src/components/misc/MntrHint/MntrHint.js:77
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:111
#: src/components/misc/Mntr/ButtonGroup.tsx:51
msgid "Close"
msgstr "Zamknij"

#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:129
msgid "Collapse"
msgstr "Zwiń"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:53
msgid "Color palette"
msgstr "Paleta kolorów"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:208
msgid "Colors"
msgstr "Kolory"

#: src/constants/analytics/primeScoreCharts.ts:115
#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:60
msgid "Column"
msgstr "Kolumna"

#: src/components/newsroom/content/posts/NewsroomPosts.js:126
msgid "Compact"
msgstr "Kompaktowy"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:66
msgid "Company"
msgstr "Firma"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:93
msgid "Company (Name or CRN)"
msgstr "Firma (NIP, KRS lub REGON)"

#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:79
msgid "Company detail"
msgstr "Szczegóły firmy"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:72
#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:26
msgid "Company info"
msgstr "Informacje o firmie"

#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:893
msgid "Compare Topic"
msgstr "Porównaj temat"

#: src/pages/sign-up-completion.tsx:30
#: src/pages/staff/sign-up-completion.js:26
msgid "Completion"
msgstr "Zakończenie rejestracji"

#: src/components/emailing/forms/FormSenderSettings.js:260
msgid "Configuring DKIM (DomainKeys Identified Mail) enhances the integrity and authenticity of your emails, reducing the likelihood of them being marked as spam:"
msgstr "Konfiguracja DKIM (DomainKeys Identified Mail) zwiększa integralność i autentyczność Twoich maili, zmniejszając prawdopodobieństwo, że zostaną oznaczone jako spam:"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:51
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:319
msgid "Confirm"
msgstr "Potwierdź"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:93
msgid "Confirm new password"
msgstr "Potwierdź nowe hasło"

#: src/helpers/store/apiClient.js:240
msgid "Connection with the server was lost. Please try again."
msgstr "Połączenie z serwerem zostało przerwane. Proszę spróbować ponownie."

#: src/components/layout/Sidebar/SidebarNavigation.tsx:263
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:160
msgid "Contact"
msgstr "Kontakt"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:123
msgid "Contact information"
msgstr "Dane kontaktowe"

#: src/components/medialist/forms/FormEditAuthor.js:761
#: src/components/medialist/content/MedialistDashboard.js:88
#: src/components/medialist/content/MedialistDashboard.js:121
#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:56
msgid "Contacts"
msgstr "Kontakty"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:178
msgid "Contacts cannot be imported from this file"
msgstr "Nie można zaimportować kontaktów z tego pliku"

#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:19
msgid "Contacts import in progress"
msgstr "Trwa import kontaktów"

#: src/pages/user/reset-password/new.tsx:48
#: src/pages/user/reset-password/index.tsx:29
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:167
#: src/components/page/auth/SignUp/SignUp.js:58
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:610
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:246
#: src/components/newsroom/content/newsroom/NewsroomCreateBlog.js:19
#: src/components/misc/Wizard/WizardChoice.tsx:150
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:143
#: src/components/medialist/forms/FormEditAuthor.js:300
#: src/components/medialist/forms/FormEditAuthor.js:318
#: src/components/medialist/forms/FormEditAuthor.js:464
#: src/components/medialist/forms/FormEditAuthor.js:483
#: src/components/medialist/forms/FormEditAuthor.js:578
#: src/components/emailing/forms/FormAddCampaign.tsx:20
#: src/components/emailing/content/CreateEmailContent.js:354
msgid "Continue"
msgstr "Kontynuuj"

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:58
msgid "Continue to import"
msgstr "Kontynuuj import"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:229
#: src/components/medialist/forms/FormEditAuthor.js:116
#: src/components/medialist/forms/FormEditAuthor.js:120
#: src/components/medialist/forms/FormEditAuthor.js:872
#: src/components/medialist/forms/FormEditAuthor.js:1048
#: src/components/emailing/forms/FormSenderSettings.js:69
msgid "Copied to the clipboard."
msgstr "Skopiowane do schowka."

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:64
msgid "Copy article to clipboard"
msgstr "Skopiuj materiał do schowka"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:12
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:13
#: src/components/exportList/History/HistoryTable/HistoryTable.js:115
msgid "Copy link to clipboard"
msgstr "Skopiuj link do schowka"

#: src/components/staff/admin/workspace/UsersTable/CopyPassword.js:27
msgid "Copy password"
msgstr "Skopiuj hasło"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:117
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:76
msgid "Copy public URL to clipboard"
msgstr "Skopiuj publiczny URL do schowka"

#: src/components/reports/history/HistoryTable.js:436
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:96
msgid "Copy recipients to clipboard"
msgstr "Skopiuj odbiorców do schowka"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:34
#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:66
msgid "Copy share link"
msgstr "Skopiuj link do udostępnienia"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:70
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:82
msgid "Copy to another campaign"
msgstr "Skopiuj do innej kampanii"

#: src/helpers/modal/withModalEmailPreview.js:102
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:585
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:644
#: src/components/medialist/forms/FormEditAuthor.js:773
#: src/components/medialist/forms/FormEditAuthor.js:801
#: src/components/medialist/forms/FormEditAuthor.js:869
#: src/components/medialist/forms/FormEditAuthor.js:1045
#: src/components/emailing/forms/FormSenderSettings.js:65
msgid "Copy to clipboard"
msgstr "Kopiuj do schowka"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:112
msgid "Copy to Dashboard"
msgstr "Kopiuj do pulpitu"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:238
#: src/components/misc/ActionsBar/View/ViewMenu.js:187
msgid "Cost per Point (CCP) - how much does one second of advertising cost for each GRP point (AVE = CPP * GRP * duration)"
msgstr "Cost per Point (CCP) - ile kosztuje jedna sekunda reklamy za każdy punkt GRP (AVE = CPP * GRP * czas trwania)"

#: src/constants/analytics.js:864
msgid "Countries"
msgstr "Kraje"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:124
msgid "countries with enabled mediatype"
msgstr "kraje z włączonym typem mediów"

#: src/constants/analytics.js:851
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:74
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:360
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:76
#: src/components/medialist/forms/FormEditAuthor.js:246
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:271
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:291
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:80
msgid "Country"
msgstr "Kraj"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:63
msgid "Cover page"
msgstr "Strona tytułowa"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:233
#: src/components/misc/ActionsBar/View/ViewMenu.js:182
msgid "CPP"
msgstr "CPP"

#: src/components/medialist/forms/FormEditAuthor.js:601
msgid "Create"
msgstr "Utwórz"

#: src/components/page/auth/Login/Login.tsx:75
msgid "Create an account for free"
msgstr "Załóż darmowe konto"

#: src/components/emailing/content/NewEmailWizardButton.tsx:15
msgid "Create an email"
msgstr "Utwórz e-mail"

#: src/components/medialist/content/withAddToBasketPopup.js:52
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorBaskets/AddAuthorToBasket.js:59
msgid "Create and add to new list"
msgstr "Utwórz i dodaj do nowej listy"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:68
#: src/components/monitoring/FeedActionsBar/withAddTagPopup/AddTagPopupContent.js:49
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorTags/AddAuthorTag.js:51
msgid "Create and assign new tag"
msgstr "Utwórz i przypisz nowy tag"

#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:48
#: src/app/components/monitoring-navigation.tsx:213
msgid "Create article"
msgstr "Utwórz materiał"

#: src/pages/workspace-articles.js:64
#: src/components/monitoring/WorkspaceArticles/withWorkspaceArticleModal.js:9
msgid "Create Article"
msgstr "Utwórz materiał"

#: src/components/medialist/content/MedialistDashboard.js:144
#: src/components/medialist/content/AuthorBasketsMenu.js:51
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:249
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:250
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:168
msgid "Create author"
msgstr "Dodaj autora"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:76
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:77
msgid "Create Folder"
msgstr "Utwórz folder"

#: src/components/medialist/content/AuthorBasketsMenu.js:151
msgid "Create new list"
msgstr "Utwórz nową listę"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:69
msgid "Create new Newsroom"
msgstr "Utwórz nowy Newsroom"

#: src/components/newsroom/content/newsroom/NewsroomCreateBlog.js:18
msgid "Create Newsroom"
msgstr "Utwórz Newsroom"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:240
msgid "Create Own Article"
msgstr "Utwórz własny materiał"

#: src/components/newsroom/content/posts/NewPostWizardButton.tsx:16
msgid "Create post"
msgstr "Utwórz post"

#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:69
msgid "Create Report"
msgstr "Utwórz raport"

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:24
#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:26
msgid "Create workspace"
msgstr "Utwórz przestrzeń roboczą"

#: src/pages/authors/index.js:61
msgid "Create your own <0>lists</0> and <1>tags</1>"
msgstr "Twórz własne <0>listy</0> i <1>tagi</1>"

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:44
msgid "Created"
msgstr "Utworzono"

#: src/store/models/dashboards/DashboardPreview.js:134
#: src/pages/crisis-communication.js:10
#: src/pages/crisis-communication-story/[articleId].js:10
#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:19
#: src/components/layout/Sidebar/SidebarNavigation.tsx:129
#: src/components/layout/Sidebar/SidebarNavigation.tsx:255
#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:26
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:37
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:23
#: src/app/components/monitoring-navigation.tsx:258
msgid "Crisis communication"
msgstr "Komunikacja kryzysowa"

#: src/store/models/monitoring/Inspector/EntityKnowledgeBaseStore/EntityKnowledgeBaseStore.js:23
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:55
msgid "CRN"
msgstr "Regon"

#. placeholder {0}: option.reg_no
#. placeholder {0}: data.reg_no
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:117
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorAddress/AuthorAddress.js:14
msgid "CRN: {0}"
msgstr "Regon: {0}"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:124
#: src/components/misc/MntrEditor/modals/withModalCTAButton.js:8
#: src/components/misc/MntrEditor/extensions/ExtensionCTAButton.js:23
msgid "CTA Button"
msgstr "Przycisk CTA"

#: src/components/staff/admin/user/User.js:286
#: src/components/settings/SettingsApplication/SettingsApplication.js:42
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:408
msgid "Currency"
msgstr "Waluta"

#: src/components/settings/SettingsApplication/SettingsApplication.js:33
msgid "Currency in which to calculate AVE."
msgstr "Waluta, w której oblicza się AVE."

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:81
msgid "Current password"
msgstr "Aktualne hasło"

#: src/components/newsroom/modals/withModalCustomAiRewrite.tsx:11
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:645
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:65
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:49
#: src/components/emailing/content/CreateEmailContent.js:548
msgid "Custom"
msgstr "Niestandardowy"

#: src/components/emailing/content/promo/PromoEmailing.js:22
msgid "Custom branding"
msgstr "Własny branding"

#: src/components/settings/SettingsTheme/SettingsThemePreview/LogoColorPicker/LogoColorPicker.js:70
#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:65
msgid "Custom color"
msgstr "Własny kolor"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:171
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:135
#: src/components/misc/MntrEditor/modals/withModalHtmlCode.js:17
#: src/components/misc/MntrEditor/extensions/ExtensionHtmlCode.js:23
msgid "Custom HTML Code"
msgstr "Własny kod HTML"

#: src/components/newsroom/modals/withModalCustomAiRewrite.tsx:24
msgid "Custom insruction"
msgstr "Niestandardowa instrukcja"

#: src/components/settings/SettingsLogo/SettingsLogo.js:63
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:78
msgid "Custom logo"
msgstr "Własne logo"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:176
msgid "Custom meta/script/style for <head> section"
msgstr "Własny kod meta/script/style dla sekcji <head>"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:83
msgid "Custom selection"
msgstr "Własna lista"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:353
msgid "Custom Slug"
msgstr "Niestandardowy URL slug"

#: src/components/reports/Content/ReportsList/ReportsForm.js:306
msgid "Custom subject (optional)"
msgstr "Niestandardowy temat (opcjonalnie)"

#: src/pages/staff/admin/customers/index.js:12
#: src/components/staff/admin/customers/Customers.js:20
#: src/components/layout/Sidebar/SidebarNavigation.tsx:188
#: src/components/layout/Header/UserMenu/UserMenu.tsx:219
#: src/components/forms/dashboard/Search/SearchCustomers.js:54
#: src/app/components/monitoring-navigation.tsx:317
msgid "Customers"
msgstr "Klienci"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:313
msgid "Customization"
msgstr "Personalizacja"

#: src/components/misc/VideoPlayer/getCropAction.js:7
msgid "Cut clip"
msgstr "Wytnij klip"

#: src/components/forms/inspector/FormMediaEditor.js:117
msgid "Cut from"
msgstr "Cięcie od"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:433
msgid "Cut media"
msgstr "Wytnij media"

#: src/components/forms/inspector/FormMediaEditor.js:120
msgid "Cut to"
msgstr "Cięcie do"

#: src/pages/staff/admin/workspaces/[workspaceId]/daily-access.js:12
#: src/pages/staff/admin/users/[userId]/daily-access.js:12
#: src/components/staff/admin/workspace/Workspace.js:153
#: src/components/staff/admin/user/User.js:77
msgid "Daily Access"
msgstr "Dostęp do aplikacji codziennie"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:54
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:70
#: src/components/misc/ActionsBar/View/ViewMenu.js:166
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:106
msgid "Daily listenership"
msgstr "Dzienna słuchalność"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:71
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:45
msgid "daily users"
msgstr "użytkowników dziennie"

#: src/components/misc/ActionsBar/View/ViewMenu.js:66
msgid "Daily users"
msgstr "Codzienni użytkownicy"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:151
msgid "Dark"
msgstr "Ciemny"

#: src/components/settings/SettingsTheme/SettingsThemePreview/SettingsThemePreview.js:193
msgid "Dark mode preview"
msgstr "Podgląd trybu ciemnego"

#: src/pages/dashboard/index.js:18
#: src/pages/dashboard/shared/[dashboardKey].js:15
#: src/app/components/monitoring-navigation.tsx:91
msgid "Dashboard"
msgstr "Pulpit"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:19
msgid "Dashboard sharing"
msgstr "Udostępnianie pulpitu nawigacyjnego"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:56
msgid "Dashboard will be removed."
msgstr "Pulpit zostanie usunięty."

#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:47
msgid "Dashboard will be shared in read-only form (non-interactive) with currently displayed data. Link expiration is 30 days."
msgstr "Pulpit będzie udostępniony w formie tylko do odczytu (nieinteraktywnej) z aktualnie wyświetlanymi danymi. Link wygasa po 30 dniach."

#: src/components/tariff/TariffLimits/TariffLimits.js:150
#: src/components/staff/admin/workspace/Workspace.js:460
msgid "Dashboards limit"
msgstr "Limit pulpitów"

#: src/components/staff/admin/DailyAccess/Table.js:21
#: src/components/reports/history/HistoryTable.js:146
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:180
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:81
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:263
#: src/components/exportList/History/HistoryTable/HistoryTable.js:48
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:177
msgid "Date"
msgstr "Data"

#: src/components/misc/Changelog/ChangelogTable.js:30
msgid "Date & User"
msgstr "Data i użytkownik"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:238
msgid "Date and time must be in the future"
msgstr "Data i godzina muszą być w przyszłości"

#. placeholder {0}: format(effectiveMinDate, DATE_FORMAT)
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:35
msgid "Date cannot be earlier than {0}"
msgstr "Data nie może być wcześniejsza niż {0}"

#. placeholder {0}: format(effectiveMaxDate, DATE_FORMAT)
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:38
msgid "Date cannot be later than {0}"
msgstr "Data nie może być późniejsza niż {0}"

#: src/constants/analytics.js:827
msgid "Day of the week"
msgstr "Dzień tygodnia"

#: src/helpers/charts/makeGranularityMenu.js:10
#: src/helpers/charts/getGranularityLabel.js:16
msgid "Days"
msgstr "Dni"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:289
msgid "DD.MM.YYYY"
msgstr "DD.MM.RRRR"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:32
#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:51
msgid "Deduplicate articles"
msgstr "Usuwanie duplikatów materiałów"

#: src/components/staff/admin/workspace/Workspace.js:763
msgid "Deduplicate feed articles"
msgstr "Usuń duplikaty materiałów w feedzie"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:36
msgid "Deduplication will remove same or similar articles from the report according to your settings. It will not remove any article from the feed."
msgstr "Deduplikacja usunie te same lub podobne materiały z newslettera zgodnie z Twoimi ustawieniami. Nie usunie żadnego materiału z kanału."

#: src/components/newsroom/content/posts/NewsroomPosts.js:133
msgid "Default"
msgstr "Domyślny"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:62
msgid "Define the action you want recipients to take."
msgstr "Zdefiniuj działanie, które mają podjąć odbiorcy."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:37
msgid "Define the subject line to set the focus and tone."
msgstr "Zdefiniuj temat, aby określić skupienie i ton."

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:137
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:78
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:160
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:63
#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:87
#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:60
#: src/components/newsroom/content/posts/NewsroomPosts.js:201
#: src/components/newsroom/content/post/AttachmentsList.js:52
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:101
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:357
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:388
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:37
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:35
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:279
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:75
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:37
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:35
#: src/components/medialist/forms/modules/FormArray.js:119
#: src/components/medialist/forms/modules/FormArray.js:165
#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:33
#: src/components/emailing/content/SignaturePopup.tsx:31
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:85
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:96
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:117
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:53
msgid "Delete"
msgstr "Usuń"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:348
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:360
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:385
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:313
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:388
msgid "Delete Article"
msgstr "Usuń materiał"

#: src/components/newsroom/content/post/AttachmentsList.js:56
msgid "Delete attachment"
msgstr "Usuń załącznik"

#: src/components/medialist/forms/FormEditAuthor.js:311
#: src/components/medialist/forms/FormEditAuthor.js:317
#: src/components/medialist/forms/FormEditAuthor.js:476
#: src/components/medialist/forms/FormEditAuthor.js:482
msgid "Delete author"
msgstr "Usuń autora"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:393
msgid "Delete blog post"
msgstr "Usuń wpis na blogu"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:133
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:142
msgid "Delete category"
msgstr "Usuń kategorię"

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:37
msgid "Delete file"
msgstr "Usuń plik"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:286
msgid "Delete Folder"
msgstr "Usuń folder"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:375
msgid "Delete from media coverage"
msgstr "Usuń z monitoringu mediów"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:131
#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:140
msgid "Delete Item"
msgstr "Usuń element"

#: src/components/medialist/content/AuthorBasketsMenu.js:126
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:108
msgid "Delete list"
msgstr "Usuń listę"

#: src/components/medialist/content/AuthorBasketsMenu.js:134
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:123
msgid "Delete list?"
msgstr "Usunąć listę?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:659
msgid "Delete Newsroom"
msgstr "Usuń Newsroom"

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:147
msgid "Delete recipient"
msgstr "Usuń odbiorcę"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:121
msgid "Delete Sender"
msgstr "Usuń nadawcę"

#: src/components/emailing/content/SignaturePopup.tsx:34
msgid "Delete signature"
msgstr "Usuń podpis"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:302
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:311
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:192
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:201
msgid "Delete tag"
msgstr "Usuń tag"

#: src/pages/trash.js:16
msgid "Deleted Articles"
msgstr "Usunięte materiały"

#. placeholder {0}: feed.totalCount
#: src/components/trash/Content.js:41
msgid "Deleted Articles ({0})"
msgstr "Usunięte materiały ({0})"

#: src/components/trash/Content.js:49
msgid "Deleted articles will appear here when deleted in the Articles section."
msgstr "Usunięte materiały pojawią się tutaj po usunięciu w sekcji Materiały."

#: src/components/reports/history/RecipientsTableRow.js:40
#: src/components/reports/history/HistoryTable.js:84
#: src/components/reports/history/HistoryTable.js:118
#: src/components/reports/history/HistoryTable.js:328
msgid "Delivered"
msgstr "Dostarczony"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:204
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:230
msgid "Delivery rate"
msgstr "Wskaźnik dostawy"

#: src/components/reports/history/HistoryTable.js:162
msgid "Delivery stats"
msgstr "Statystyki dostaw"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:20
msgid "Demo"
msgstr "Demo"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:111
msgid "Demographic Data"
msgstr "Dane demograficzne"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:47
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:96
#: src/components/feed/InspectorToolbar/InspectorToolbar.js:145
msgid "Demographics"
msgstr "Demografia"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:42
msgid "Describe who will receive the email (demographics, interests)."
msgstr "Opisz, kto otrzyma e-mail (dane demograficzne, zainteresowania)."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:59
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:373
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:223
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:43
msgid "Description"
msgstr "Opis"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:44
#: src/components/layout/MntrFiltersBar/modules/MenuFilterToggleAllButtons.js:76
msgid "Deselect all"
msgstr "Odznacz wszystko"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:23
msgid "Designed for PR professionals, generates a press release structure."
msgstr "Zapewnione dla profesjonalistów PR, generuje strukturę informacji prasowej."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:80
msgid "Detailed instructions for email text creation"
msgstr "Szczegółowe instrukcje dotyczące tworzenia tekstu e-maila"

#: src/pages/newsroom/index.js:53
msgid "Detailed traffic<0/> <1>analytics</1>"
msgstr "Szczegółowy<0/> <1>analiza ruchu</1>"

#: src/constants/analytics/primeScoreCharts.ts:31
msgid "Development of PRIMe by rating"
msgstr "Rozwój PRIMe według oceny"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:149
msgid "Deviation from the average"
msgstr "Odchylenie od średniej. Im wyższa wartość, tym ciekawsze wyniki."

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:89
msgid "Disable"
msgstr "Wyłącz"

#: src/components/staff/admin/workspace/Workspace.js:225
#: src/components/staff/admin/workspace/Workspace.js:958
#: src/components/staff/admin/user/User.js:167
#: src/components/staff/admin/user/User.js:336
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:279
msgid "Discard"
msgstr "Odrzuć zmiany"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:227
msgid "Discard changes"
msgstr "Odrzuć zmiany"

#: src/components/monitoring/Inspector/InspectorMonitora/DiscussionThreadBar/DiscussionThreadBar.js:20
#: src/components/layout/MntrActiveFilters/modules/DiscussionThread.js:12
msgid "Discussion thread"
msgstr "Wątek dyskusyjny"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:98
msgid "Display empty categories"
msgstr "Wyświetl puste kategorie"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:205
msgid "Display the article"
msgstr "Wyświetl materiały"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:154
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:49
#: src/components/misc/ActionsBar/View/ViewMenu.js:116
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:75
msgid "Distribution amount"
msgstr "Nakład"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:149
msgid "DNS settings are invalid."
msgstr "Ustawienia DNS są nieprawidłowe."

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:147
msgid "DNS settings are valid."
msgstr "Ustawienia DNS są prawidłowe."

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:108
msgid "Do not add new media to the medium"
msgstr "Nie dodawaj nowych mediów do medium"

#: src/components/medialist/forms/FormEditAuthor.js:299
#: src/components/medialist/forms/FormEditAuthor.js:316
#: src/components/medialist/forms/FormEditAuthor.js:463
#: src/components/medialist/forms/FormEditAuthor.js:481
msgid "Do you really want to continue?"
msgstr "Czy na pewno chcesz kontynuować?"

#. placeholder {0}: menuItem.name
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:294
msgid "Do you really want to delete '<0>{0}</0>'?"
msgstr "Czy na pewno chcesz usunąć '<0>{0}</0>'?"

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:200
msgid "Do you want to add author addresses from this list?"
msgstr "Czy chcesz dodać adresy autorów z tej listy?"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:84
msgid "Do you want to start with AI assistant?"
msgstr "Chcesz zacząć z asystentem AI?"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:124
msgid "Do you wish to reactivate this recipient?"
msgstr "Czy chcesz ponownie aktywować tego odbiorcę?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:55
#: src/components/emailing/forms/FormSenderSettings.js:267
msgid "Domain"
msgstr "Domena"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:489
msgid "Domain change"
msgstr "Zmiana domeny"

#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:130
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:24
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:23
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:184
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:30
#: src/components/forms/dashboard/Export/ExportForm.js:100
#: src/components/exportList/History/HistoryTable/HistoryTable.js:96
#: src/components/exportList/Content/Content.tsx:78
#: src/components/OurChart/OurChartAdvanced.js:181
msgid "Download"
msgstr "Pobierz"

#: src/components/forms/inspector/FormMediaEditor.js:141
msgid "Download clip"
msgstr "Pobierz klip"

#: src/components/staff/admin/workspace/Workspace.js:126
msgid "Download settings (.xlsx)"
msgstr "Pobierz ustawienia (.xlsx)"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:82
msgid "Download template"
msgstr "Pobierz szablon"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewImage/PreviewImage.js:60
msgid "Drag 'n' drop image or click to select files"
msgstr "Kliknij lub przeciągnij obraz, aby wybrać pliki"

#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryAdapter.js:59
msgid "Drag 'n' drop some images here, or click to select files"
msgstr "Przeciągnij tutaj obrazy lub kliknij, aby wybrać pliki"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:66
msgid "Due date"
msgstr "Termin płatności"

#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:36
#: src/components/medialist/content/AuthorBasketsMenu.js:117
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:101
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:59
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:71
msgid "Duplicate"
msgstr "Duplikować"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:74
msgid "Duplicate widget"
msgstr "Duplikuj Widget"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:214
#: src/components/misc/ActionsBar/View/ViewMenu.js:174
msgid "Duration"
msgstr "Czas trwania"

#: src/components/layout/AuthWrapper/constants/features.slides.js:307
msgid "Dynamic platform for creating, curating, and sharing captivating content."
msgstr "Twórz i udostępniaj wizualnie porywające informacje prasowe dzięki naszej nowoczesnej platformie."

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:89
#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:124
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:51
#: src/components/misc/MntrEditor/extensions/ExtensionImageGallery.js:53
#: src/components/emailing/content/SignaturePopup.tsx:22
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:65
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:46
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:34
msgid "Edit"
msgstr "Edytuj"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:443
msgid "Edit article"
msgstr "Edytuj materiał"

#: src/components/newsroom/content/posts/NewsroomPosts.js:194
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:169
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:170
msgid "Edit Article"
msgstr "Edytuj materiał"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:34
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:37
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:77
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:80
msgid "Edit Campaign"
msgstr "Edytuj kampanię"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/modalEditCategory.js:10
#: src/components/layout/Sidebar/modules/NewsroomNavigation/modalEditCategory.js:24
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:113
msgid "Edit category"
msgstr "Edytuj kategorię"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:37
msgid "Edit dashboard"
msgstr "Edytuj pulpit"

#: src/components/topics/Content/TopicsList/Keyword/Keyword.js:62
msgid "Edit keyword"
msgstr "Edytuj słowo kluczowe"

#: src/components/medialist/content/AuthorBasketsMenu.js:102
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:19
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:87
msgid "Edit list"
msgstr "Edytuj listę"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:63
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:135
#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:60
msgid "Edit mediatypes"
msgstr "Edytuj typy mediów"

#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:56
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:106
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:131
#: src/components/medialist/forms/FormEditAuthor.js:710
msgid "Edit note"
msgstr "Edytuj notatkę"

#: src/components/medialist/forms/FormEditAuthor.js:282
#: src/components/medialist/forms/FormEditAuthor.js:448
msgid "Edit profile"
msgstr "Edytuj profil"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:67
msgid "Edit recipient"
msgstr "Edytuj odbiorcę"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:48
msgid "Edit Sender"
msgstr "Edytuj nadawcę"

#: src/components/newsroom/content/posts/NewsroomPosts.js:300
#: src/components/newsroom/content/dashboard/NewsroomBlogSettings.js:23
msgid "Edit settings"
msgstr "Edytuj ustawienia"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:26
msgid "Edit signature"
msgstr "Edytuj podpis"

#: src/components/layout/Sidebar/modules/SidebarTags/modalEditTags.js:10
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:279
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:168
msgid "Edit tag"
msgstr "Edytuj tag"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:32
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:58
msgid "Edit topic"
msgstr "Edytuj temat"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:59
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:63
msgid "Edit widget"
msgstr "Edytuj Widget"

#: src/components/medialist/forms/modules/FormArray.js:94
msgid "Editorial Office"
msgstr "Redakcja"

#: src/components/medialist/forms/FormEditAuthor.js:848
#: src/components/medialist/forms/FormEditAuthor.js:1012
msgid "Editorial offices and positions"
msgstr "Redakcje i stanowiska"

#: src/components/medialist/content/MedialistAuthorCreate.js:24
msgid "Eg. sent press releases, profile edits, published articles related to your press releases."
msgstr "Np. wysłane komunikaty prasowe, edycje profilu, opublikowane materiały związane z Twoimi komunikatami prasowymi."

#: src/pages/user/reset-password/index.tsx:20
#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:116
#: src/components/staff/admin/user/User.js:246
#: src/components/staff/admin/customer/users/UsersTable.js:68
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:61
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:224
#: src/components/reports/history/RecipientsTableHeader.js:30
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:133
#: src/components/page/auth/SignUp/SignUp.js:37
#: src/components/page/auth/Login/Login.tsx:40
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:128
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:74
#: src/components/medialist/forms/FormEditAuthor.js:793
#: src/components/medialist/forms/FormEditAuthor.js:911
#: src/components/medialist/forms/FormEditAuthor.js:916
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:33
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:100
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:111
#: src/components/emailing/content/EmailDetailEmailContent.js:37
#: src/components/emailing/content/CreateEmailContent.js:262
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:50
msgid "Email"
msgstr "Mail"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:26
msgid "Email address was verified"
msgstr "Adres mailowy został zweryfikowany"

#. placeholder {0}: campaign.name
#: src/components/emailing/content/CampaignAutocompleteList.tsx:41
msgid "Email copied to {0}"
msgstr "Mail skopiowany do {0}"

#: src/store/models/ExportStore.js:126
msgid "Email has been successfully sent."
msgstr "Mail został pomyślnie wysłany."

#: src/components/emailing/content/CreateEmailContent.js:77
msgid "Email is locked and cannot be edited. If you want to edit the email, return it to the draft state."
msgstr "Mail jest zablokowany i nie można go edytować. Jeśli chcesz edytować mail, przywróć go do stanu szkicu."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:45
msgid "Email is missing"
msgstr "Brakuje e-maila"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:12
msgid "Email is required"
msgstr "Mail jest wymagany"

#: src/components/emailing/content/CreateEmailContent.js:72
msgid "Email is sending"
msgstr "E-mail jest wysyłany"

#: src/helpers/modal/withModalEmailPreview.js:120
msgid "Email preview"
msgstr "Podgląd maila"

#: src/components/reports/Content/ReportsList/ReportsHeading/ReportsHeading.js:10
msgid "Email reports ({counter})"
msgstr "Newslettery ({counter})"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:142
msgid "Email subject"
msgstr "Temat wiadomości e-mail"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:36
msgid "Email Subject:"
msgstr "Temat e-maila:"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:62
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:74
msgid "Email successfully duplicated"
msgstr "Mail został pomyślnie zduplikowany"

#: src/components/emailing/content/CreateEmailContent.js:134
msgid "Email was saved"
msgstr "Mail został zapisany"

#: src/components/emailing/content/CreateEmailContent.js:153
msgid "Email was sent"
msgstr "Mail został wysłany"

#: src/components/emailing/content/CreateEmailContent.js:124
msgid "Email was set to draft"
msgstr "Mail został ustawiony jako szkic"

#: src/components/emailing/content/CreateEmailContent.js:74
msgid "Email will be sent at: {scheduledDate}"
msgstr "Mail zostanie wysłany o: {scheduledDate}"

#: src/components/staff/admin/user/User.js:115
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:384
msgid "Email with further instructions has been sent."
msgstr "Email z dalszymi instrukcjami został wysłany."

#: src/pages/emailing/settings.tsx:16
#: src/pages/emailing/index.tsx:14
#: src/pages/emailing/campaign/[campaignId]/recipients.tsx:16
#: src/pages/emailing/campaign/[campaignId]/media-coverage.tsx:18
#: src/pages/emailing/campaign/[campaignId]/index.tsx:19
#: src/pages/emailing/campaign/[campaignId]/email/create.tsx:15
#: src/pages/emailing/campaign/[campaignId]/email/edit/[emailId]/index.tsx:12
#: src/pages/emailing/campaign/[campaignId]/email/[emailId]/recipients.tsx:16
#: src/pages/emailing/campaign/[campaignId]/email/[emailId]/index.tsx:16
#: src/components/layout/Sidebar/SidebarNavigation.tsx:158
#: src/components/layout/AuthWrapper/constants/features.slides.js:259
#: src/components/emailing/content/promo/PromoEmailing.js:17
#: src/components/emailing/components/FunnelStats/FunnelStats.tsx:79
#: src/app/components/monitoring-navigation.tsx:290
msgid "Emailing"
msgstr "Wysyłanie maili"

#: src/components/reports/history/Compose.js:71
#: src/components/forms/dashboard/Search/SearchEmailingEmailMessages.js:27
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:106
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:54
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:158
msgid "Emails"
msgstr "Maile"

#: src/components/medialist/forms/modules/MainEmailHelperText.js:6
msgid "Emails from <0>Emailing</0> will be sent to this address."
msgstr "Na ten adres będą wysyłane maile z <0>Mailingu</0>."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:488
msgid "Embed"
msgstr "Osadzić"

#: src/components/misc/ActionsBar/View/ViewMenu.js:281
msgid "Emoji reactions"
msgstr "Reakcje emoji"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:83
msgid "Empty Body"
msgstr "Pusty tekst artykułu"

#: src/app/components/monitoring-navigation.tsx:123
msgid "Empty export"
msgstr "Opróżnij eksport"

#: src/app/components/monitoring-navigation.tsx:129
msgid "Empty export?"
msgstr "Opróżnić eksport?"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:74
msgid "Empty Perex"
msgstr "Pusty perex"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:65
msgid "Empty Title"
msgstr "Pusty tytuł"

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:89
msgid "Enable"
msgstr "Włącz"

#: src/components/notifications/Permissions.js:58
msgid "Enable notifications"
msgstr "Włącz powiadomienia"

#: src/components/tariff/AgencyMedia/AgencyMedia.js:69
#: src/components/misc/ActionsBar/View/ViewMenu.js:52
#: src/components/misc/ActionsBar/View/ViewMenu.js:272
msgid "Enabled"
msgstr "Włączone"

#: src/components/emailing/forms/FormSenderSettings.js:112
msgid "Encryption method"
msgstr "Metoda szyfrowania"

#: src/components/settings/SettingsTheme/ThemePicker.tsx:99
msgid "Enforce primary color as header"
msgstr "Wymuś podstawowy kolor jako nagłówek"

#: src/constants/analytics.js:315
#: src/constants/analytics.js:424
#: src/constants/analytics.js:489
#: src/constants/analytics.js:511
#: src/constants/analytics.js:949
#: src/constants/analytics.js:964
#: src/components/monitoring/FeedList/FeedListItem/MetaData/modules/MetaDataEngagement/MetaDataEngagement.js:19
#: src/components/misc/ActionsBar/View/ViewMenu.js:305
msgid "Engagement rate"
msgstr "Wskaźnik zaangażowania"

#: src/constants/analytics.js:336
msgid "Engagement rate by mention type"
msgstr "Wskaźnik zaangażowania według typu wzmianki"

#: src/constants/analytics.js:334
#: src/constants/analytics.js:443
#: src/constants/analytics.js:509
msgid "Engagement rate by sentiment"
msgstr "Wskaźnik zaangażowania według sentymentu"

#: src/constants/analytics.js:445
msgid "Engagement rate by social network"
msgstr "Wskaźnik zaangażowania według sieci społecznościowej"

#: src/components/analytics/SocialMedia.js:25
msgid "Engagement summary"
msgstr "Podsumowanie zaangażowania"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:62
#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:18
msgid "Enter a word or phrase"
msgstr "Wprowadź słowo lub frazę"

#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:53
msgid "Enter text here..."
msgstr "Wpisz tekst tutaj..."

#: src/components/emailing/forms/FormSenderSettings.js:83
msgid "Enter the hostname of your SMTP server"
msgstr "Wprowadź nazwę hosta serwera SMTP"

#: src/components/emailing/forms/FormSenderSettings.js:85
msgid "Enter the hostname of your SMTP server. This is usually in the format of \"smtp.yourdomain.com\"."
msgstr "Wprowadź nazwę hosta serwera SMTP. Zwykle jest w formacie „smtp.twojadomena.pl“."

#: src/components/emailing/forms/FormSenderSettings.js:98
msgid "Enter the password to login to SMTP server"
msgstr "Wprowadź hasło do logowania do serwera SMTP"

#: src/components/emailing/forms/FormSenderSettings.js:100
msgid "Enter the password to login to SMTP server. This is usually the same password you use for your email."
msgstr "Wprowadź hasło do logowania do serwera SMTP. Zazwyczaj jest to to samo hasło, którego używasz do logowania do swojego e-maila."

#: src/components/emailing/forms/FormSenderSettings.js:90
msgid "Enter the username to login to SMTP server"
msgstr "Wprowadź username do logowania do serwera SMTP"

#. placeholder {0}: initialValues.email
#: src/components/emailing/forms/FormSenderSettings.js:92
msgid "Enter the username to login to SMTP server. If left blank, \"{0}\" is used by default."
msgstr "Wprowadź username, aby zalogować się do serwera SMTP. Jeśli pozostanie puste, domyślnie używane jest \"{0}\"."

#: src/pages/user/reset-password/index.tsx:17
msgid "Enter your email. We'll send you instructions on how to reset your password."
msgstr "Wprowadź swój mail. Wyślemy Ci instrukcje jak zresetować hasło."

#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:61
msgid "Error detail"
msgstr "Szczegóły błędu"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:163
msgid "Estimated number of distributed copies (print and digital)."
msgstr "Szacunkowa liczba rozdystrybuowanych egzemplarzy (druk i cyfrowe)."

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:17
msgid "Eternal"
msgstr "Wieczny"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:32
msgid "Everything enabled"
msgstr "Wszystko włączone"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:24
msgid "Everything went well."
msgstr "Wszystko poszło dobrze."

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:44
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:78
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:118
msgid "Exact match"
msgstr "Dokładne dopasowanie"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:84
msgid "Exact match with separator"
msgstr "Dokładne dopasowanie z separatorem"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:50
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:90
msgid "Exact match, including letter size"
msgstr "Dokładne dopasowanie, włącznie z wielkością liter"

#: src/pages/newsroom/index.js:72
msgid "Example Newsroom"
msgstr "Przykładowy Newsroom"

#: src/components/misc/VideoPlayer/Controls.js:143
msgid "Exit fullscreen"
msgstr "Zakończ tryb pełnoekranowy"

#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:83
msgid "Expense type"
msgstr "Rodzaj wydatku"

#: src/pages/staff/admin/customers/[customerId]/expenses.js:12
#: src/components/staff/admin/customer/expenses/Expenses.js:26
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:50
msgid "Expenses"
msgstr "Wydatki"

#: src/components/page/auth/Expired/Expired.js:24
msgid "expired"
msgstr "wygasł"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:12
msgid "Expired"
msgstr "Wygasł"

#: src/components/staff/admin/workspace/Workspace.js:316
msgid "Expires at"
msgstr "Wygasa"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:30
msgid "Expires on: {formattedDate}"
msgstr "Wygasa: {formattedDate}"

#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:219
#: src/components/exportList/History/History.js:28
#: src/components/exportList/Content/Content.tsx:58
#: src/components/exportList/Content/Content.tsx:58
#: src/app/components/monitoring-navigation.tsx:102
msgid "Export"
msgstr "Eksport"

#: src/components/misc/portable/PortableExport/PortableExport.js:64
#: src/components/misc/portable/PortableExport/PortableExport.js:104
msgid "Export all articles"
msgstr "Eksportuj wszystkie materiały"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:142
msgid "Export article"
msgstr "Eksportuj materiał"

#: src/components/misc/portable/PortableExport/PortableExport.js:62
#: src/components/misc/portable/PortableExport/PortableExport.js:102
msgid "Export articles"
msgstr "Eksport materiałów"

#: src/components/staff/admin/workspace/Workspace.js:329
msgid "Export basket mode"
msgstr "Tryb koszyka eksportowego"

#: src/pages/export/history.js:12
#: src/components/exportList/History/History.js:35
msgid "Export History"
msgstr "Historia eksportu"

#: src/store/models/ExportStore.js:311
#: src/store/models/monitoring/Inspector/Inspector.ts:447
msgid "Export is full."
msgstr "Koszyk eksportu jest pełny. Nie można do niego dodać więcej materiałów."

#: src/components/exportList/Content/HeadingExport/HeadingExport.js:21
msgid "Export list is empty"
msgstr "Lista eksportu jest pusta"

#: src/components/medialist/forms/FormEditAuthor.js:362
#: src/components/medialist/forms/FormEditAuthor.js:502
msgid "Export XLSX"
msgstr "Eksportuj XLSX"

#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:17
#: src/app/components/monitoring-navigation.tsx:117
msgid "Exports to download"
msgstr "Eksporty do pobrania"

#: src/pages/external-analytics.tsx:29
#: src/components/layout/Sidebar/SidebarNavigation.tsx:179
msgid "External analytics"
msgstr "Zewnętrzna analityka"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:41
msgid "External Communication Manager"
msgstr "Menadżer Komunikacji Zewnętrznej"

#: src/components/misc/MntrEditor/forms/FormEmbedSocialUrl/FormEmbedFacebookUrl.tsx:29
msgid "Facebook Post URL"
msgstr "URL postu na Facebooku"

#: src/components/emailing/content/EmailingSettingsContent.js:30
msgid "Failed to fetch your email address from the service provider. Please try again or contact support if the issue persists."
msgstr "Nie udało się pobrać Twojego adresu e-mail od dostawcy usługi. Spróbuj ponownie lub skontaktuj się z pomocą techniczną, jeśli problem będzie się powtarzał."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:41
msgid "Feature has been requested."
msgstr "Zażądano funkcjonalności."

#: src/components/forms/dashboard/Export/ExportForm.js:66
msgid "File format"
msgstr "Format pliku"

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:24
msgid "Files"
msgstr "Pliki"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:207
msgid "Fill from URL"
msgstr "Wypełnij z URL"

#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:105
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:122
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:136
#: src/components/layout/MntrFiltersBar/forms/FormFilterItems/FormFilterItems.js:38
#: src/components/layout/MntrFiltersBar/forms/FormFilterAuthorTitles/FormFilterAuthorTitles.js:13
#: src/components/layout/MntrFiltersBar/forms/FormChannelSearch/FormChannelSearch.js:31
#: src/components/emailing/content/CampaignAutocomplete.tsx:25
msgid "Filter"
msgstr "Filtruj"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:104
msgid "Filter by absolute score"
msgstr "Filtruj według absolutnego wyniku"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorActivity.js:27
msgid "Filter by activity"
msgstr "Filtruj według aktywności"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterArticleType.js:26
msgid "Filter by article type"
msgstr "Filtruj według typu materiału"

#: src/components/layout/MntrFiltersBar/forms/FormAuthor/FormAuthor.tsx:13
msgid "Filter by author"
msgstr "Filtruj według autora"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTypeMultiselect.js:32
msgid "Filter by author type"
msgstr "Filtruj według typu autora"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/withModalMedialistArticlesFilter.tsx:21
msgid "Filter by author's articles"
msgstr "Filtruj według materiałów autora"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterContactInformationMultiselect.js:32
msgid "Filter by contact"
msgstr "Filtruj według kontaktu"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:98
#: src/components/layout/MntrFiltersBar/modules/MenuFilterCountryMultiselect.js:29
msgid "Filter by country"
msgstr "Filtruj według kraju"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:239
msgid "Filter by date"
msgstr "Filtruj według daty"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorFocusAreasMultiselect.js:32
msgid "Filter by focus area"
msgstr "Filtruj według obszaru zainteresowań"

#: src/components/layout/MntrFiltersBar/forms/FormFilterAuthorTitles/FormFilterAuthorTitles.js:32
msgid "Filter by job position"
msgstr "Filtruj według stanowiska"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterLanguageTVR.js:24
#: src/components/layout/MntrFiltersBar/modules/MenuFilterLanguageMultiselect.js:26
msgid "Filter by language"
msgstr "Filtruj według języka"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSourceTVR.js:24
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:115
msgid "Filter by media"
msgstr "Filtruj według medium"

#: src/components/emailing/forms/FormFilter.js:35
msgid "Filter by name"
msgstr "Filtruj według nazwy"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:50
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:80
msgid "Filter by rank"
msgstr "Filtruj według rankingu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:187
msgid "Filter by reach"
msgstr "Filtruj według zasięgu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:141
msgid "Filter by relevance"
msgstr "Filtruj według trafności"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSentimentMultiselect.js:27
msgid "Filter by sentiment"
msgstr "Filtruj według sentymentu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:59
msgid "Filter sources"
msgstr "Filtruj źródła"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:120
#: src/components/layout/MntrFiltersBar/modals/withModalPageNumbers.js:15
msgid "Filter specific pages"
msgstr "Filtruj konkretne strony"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:354
msgid "Filter tags"
msgstr "Filtruj tagi"

#: src/components/layout/Sidebar/modules/SidebarTopics/SidebarTopicsFolders.js:76
msgid "Filter topics"
msgstr "Filtruj tematy"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:56
msgid "Final version"
msgstr "Wersja końcowa"

#. placeholder {0}: account.enums.analytics.export_charts_file_format.find( ({ id }) => id === fileFormatId, ).text
#: src/components/misc/Capture/Capture.js:304
msgid "finalizing {0} file for download"
msgstr "finalizowanie {0} pliku do pobrania"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:67
#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:59
msgid "Find out more"
msgstr "Dowiedz się więcej"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:228
msgid "Find similar articles"
msgstr "Znajdź podobne materiały"

#. js-lingui-explicit-id
#: src/components/misc/ActionsBar/Selector/Selector.js:45
msgid "selector.first"
msgstr "Pierwszych"

#: src/components/page/auth/SignUp/SignUp.js:20
msgid "First Name"
msgstr "Imię"

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:21
msgid "First Step"
msgstr "Pierwszy krok"

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:141
msgid "Focus area"
msgstr "Obszar zainteresowania"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:301
#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:53
msgid "Font Size"
msgstr "Rozmiar czcionki"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:30
msgid "For each functionality choose one of three levels:"
msgstr "Dla każdej funkcjonalności wybierz jeden z trzech poziomów:"

#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:68
msgid "For example"
msgstr "Na przykład"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:81
msgid "For example, \"1,4-6\" will filter out 1,4,5,6"
msgstr "Na przykład: „1,4-6” odfiltruje strony 1,4,5,6"

#: src/components/dashboards/PageExpiredSharedDashboard/PageExpiredSharedDashboard.js:40
msgid "For renewal, contact account admin."
msgstr "Aby odnowić, skontaktuj się z administratorem konta."

#. placeholder {0}: self.filters.topic_monitors[0].text
#: src/store/models/OurChart.js:853
msgid "for topic: {0}"
msgstr "dla tematu: {0}"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:39
msgid "Forbidden:"
msgstr "Zabronione:"

#: src/components/layout/AuthWrapper/constants/features.slides.js:146
msgid "Foreign Media"
msgstr "Zagraniczne media"

#: src/components/page/auth/Login/Login.tsx:86
msgid "Forgot password?"
msgstr "Zapomniałeś hasła?"

#: src/components/exportList/History/HistoryTable/HistoryTable.js:51
msgid "Format"
msgstr "Format"

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:18
msgid "Formatting is finished successfully. The prepared file is downloaded automatically. Edit it manually if needed and upload it to the medialist in the next step."
msgstr "Formatowanie zostało pomyślnie zakończone. Przygotowany plik zostanie pobrany automatycznie. Jeśli to konieczne, edytuj go ręcznie i prześlij do medialisty w następnym kroku."

#: src/components/reports/Content/ReportsList/ReportsForm.js:111
msgid "Frequency of report dispatch"
msgstr "Częstotliwość wysyłki"

#: src/components/reports/Content/ReportsList/ReportsForm.js:279
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:56
msgid "From"
msgstr "Od"

#: src/components/forms/dashboard/ExportResend/ExportResend.js:90
msgid "From email"
msgstr "Mail nadawcy"

#: src/components/misc/ActionsBar/View/ViewMenu.js:101
msgid "Frontpage promo"
msgstr "Promocja na stronie głównej"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:32
msgid "Full access:"
msgstr "Pełny dostęp:"

#: src/components/misc/ActionsBar/View/ViewMenu.js:148
msgid "Full page ad price"
msgstr "Cena za pełnostronnicową reklamę"

#: src/components/misc/VideoPlayer/Controls.js:143
msgid "Fullscreen"
msgstr "Pełny ekran"

#: src/components/tariff/Permissions/Permissions.js:40
msgid "Functionality"
msgstr "Funkcjonalność"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:93
msgid "Generate structure"
msgstr "Generuj strukturę"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:149
msgid "Generate text"
msgstr "Generuj tekst"

#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:40
msgid "Generating link"
msgstr "Tworzenie linku"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:115
msgid "Get a comprehensive view of the topics that matter to you."
msgstr "Uzyskaj kompleksowy przegląd tematów, które są dla ciebie ważne."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:102
msgid "Get a more complete view of the topics that interest you"
msgstr "Uzyskaj bardziej kompleksowy widok na tematy, które Cię interesują"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:34
msgid "Get access to our media archive."
msgstr "Zdobądź dostęp do naszego archiwum mediów."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:71
msgid "Go back to Emailing"
msgstr "Wróć do Mailingu"

#: src/components/staff/admin/workspace/Workspace.js:403
msgid "Google Translate price: 1 article = 2.5 Kč = 0.09 €"
msgstr "Google Translate cena: 1 materiał = 2.5 Kč = 0.09 €"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:268
msgid "Gross Rating Point"
msgstr "Gross Rating Point"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:107
msgid "Group articles"
msgstr "Grupuj materiały"

#: src/constants/stats.ts:16
#: src/constants/analytics.js:1010
#: src/components/widgets/modules/stats/WidgetStats.js:150
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:265
#: src/components/misc/ActionsBar/View/ViewMenu.js:211
msgid "GRP"
msgstr "GRP"

#: src/components/monitoring/Inspector/InspectorMonitora/HashTagsList/HashTagsList.js:24
msgid "Hashtags"
msgstr "Hashtagi"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:33
msgid "Hasn't started yet"
msgstr "Jeszcze się nie zaczęło"

#: src/pages/sign-up-completion.tsx:42
#: src/components/page/auth/SignUp/SignUp.js:81
msgid "Have an account? Sign in"
msgstr "Masz konto? Zaloguj się"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:11
msgid "Head of External and Internal Communication"
msgstr "Kierownik ds. Komunikacji Zewnętrznej i Wewnętrznej"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:225
msgid "Header"
msgstr "Nagłówek"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:166
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:172
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:178
msgid "Heading"
msgstr "Nagłówek"

#: src/pages/user/reactivate-24.js:37
msgid "Hello!<0/><1/>Thank you for your interest in trying Mediaboard with all the features. Please confirm by clicking on the button below. We will contact you as soon as possible.<2/><3/>Kind regards,<4/><5/>{appName} team"
msgstr "Witaj!<0/><1/>Dziękujemy za zainteresowanie wypróbowaniem Mediaboard ze wszystkimi funkcjami. Prosimy o potwierdzenie, klikając przycisk poniżej. Skontaktujemy się z Tobą jak najszybciej.<2/><3/>Z poważaniem,<4/><5/>Zespół {appName}"

#: src/pages/user/yoy-analysis.js:37
msgid "Hello!<0/><1/>Thank you for your interest! Please confirm by clicking on the button below. We will contact you as soon as possible.<2/><3/>Kind regards,<4/><5/>{appName} team"
msgstr "Witaj!<0/><1/>Dziękujemy za Twoje zainteresowanie! Prosimy o potwierdzenie, klikając przycisk poniżej. Skontaktujemy się z Tobą jak najszybciej.<2/><3/>Z poważaniem,<4/><5/>Zespół {appName}"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:30
msgid "Hello!<0/><1/>Thanks for your interest in our Emailing platform. To activate it you need to verify your email first. To do so just check, that the email you have entered is correct and click the activation button.<2/><3/><4/><5/>Best regards,<6/><7/>{appName} team"
msgstr "Witaj!<0/><1/>Dziękujemy za zainteresowanie naszą platformą do wysyłki maili. Aby ją aktywować, musisz najpierw zweryfikować swój adres mailowy. Wystarczy sprawdzić, czy wprowadzony adres jest poprawny, a następnie kliknąć przycisk aktywacji.<2/><3/><4/><5/>Z poważaniem,<6/><7/>{appName} zespół"

#: src/helpers/modal/withModalHelp.tsx:17
#: src/components/layout/Sidebar/SidebarNavigation.tsx:215
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:20
#: src/components/OurChart/OurChartAdvanced.js:268
msgid "Help"
msgstr "Pomoc"

#: src/components/medialist/content/MedialistAuthorCreate.js:20
msgid "Here you will see all the activity related to this author."
msgstr "Tutaj zobaczysz wszystkie działania związane z tym autorem."

#: src/components/emailing/components/FunnelStats/StatBlock.tsx:121
msgid "Here you will see newsroom analytics affected by the campaign"
msgstr "Tutaj zobaczysz analizy newsroomu, na które wpłynęła kampania"

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:159
msgid "hide"
msgstr "ukryj"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:246
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:210
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:249
msgid "Hide"
msgstr "Ukryj"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:557
msgid "Hide header and footer"
msgstr "Ukryj nagłówek i stopkę"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:63
msgid "hide stats"
msgstr "ukryj statystyki"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:230
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:241
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:144
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:153
msgid "Hide tag"
msgstr "Ukryj tag"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:229
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:190
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:202
msgid "Hide topic"
msgstr "Ukryj temat"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:210
msgid "Hide topics in folder"
msgstr "Ukryj tematy w folderze"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:65
msgid "Highlight Only"
msgstr "Tylko podświetl"

#: src/components/tariff/Permissions/Permissions.js:51
msgid "Hints"
msgstr "Podpowiedzi"

#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:28
#: src/components/exportList/Content/Content.tsx:72
msgid "History"
msgstr "Historia"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:84
msgid "Homepage url"
msgstr "URL strony głównej"

#: src/components/emailing/forms/FormSenderSettings.js:82
msgid "Host"
msgstr "Host"

#: src/components/emailing/forms/FormSenderSettings.js:123
msgid "Hostname"
msgstr "Nazwa hosta"

#: src/components/staff/admin/workspace/Workspace.js:504
msgid "How many years back is the user allowed to search in media archive."
msgstr "Ile lat wstecz użytkownik może przeszukiwać archiwum mediów."

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:88
msgid "How permissions work"
msgstr "Jak działają uprawnienia"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:135
msgid "How to help the AI generate a more satisfying and detailed email"
msgstr "Jak pomóc sztucznej inteligencji wygenerować bardziej satysfakcjonujący i szczegółowy e-mail"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:218
msgid "How to use {appName}"
msgstr "Jak korzystać z aplikacji {appName}"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:111
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:91
msgid "HTML"
msgstr "HTML"

#: src/pages/user/yoy-analysis.js:59
#: src/pages/user/reactivate-24.js:59
msgid "I am interested"
msgstr "Jestem zainteresowany"

#: src/components/staff/admin/user/WorkspacesTable.js:74
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:74
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:80
msgid "ID"
msgstr "ID"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:43
msgid "Ideal for those with their own content ready."
msgstr "Idealne dla tych, którzy mają gotową treść."

#: src/components/staff/admin/workspace/Workspace.js:382
msgid "If set to 0, then: no export basket, no exporting or email sending from feed or export basket."
msgstr "Jeśli jest ustawione na 0, to: nie ma koszyka eksportu, nie można eksportować ani wysyłać maili z kanału."

#: src/helpers/modal/withModalReportArticle.tsx:23
msgid "If the article has a bad transcript or screenshot, please report the problem and our staff will look into it and fix the issue."
msgstr "Jeśli materiał ma złą transkrypcję lub zrzut ekranu, zgłoś proszę problem, a nasi pracownicy zajmą się nim."

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:92
msgid "If this was a mistake or if you'd like to re-subscribe at any time, please contact us at"
msgstr "Jeśli to była pomyłka lub jeśli chcesz ponownie zasubskrybować w dowolnym momencie, skontaktuj się z nami pod adresem"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:64
msgid "If this was a mistake or you'd prefer to stay available for our emails, no further action is needed."
msgstr "Jeśli to była pomyłka lub wolisz pozostać dostępny na nasze e-maile, nie są potrzebne żadne dalsze działania."

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:39
msgid "If you don't remember your current password, you can <0>reset it</0> or contact us at <1>{salesEmail}</1>."
msgstr "Jeśli nie pamiętasz swojego obecnego hasła, możesz <0>zresetować je</0> lub skontaktować się z nami pod adresem <1>{salesEmail}</1>."

#: src/components/page/auth/Expired/Expired.js:63
msgid "If you liked our service and would like to purchase the account, send us an email to <0>{salesEmail}</0>"
msgstr "Jeśli nasza usługa się Tobie spodobała i chciałbyś zakupić pełną wersję, skontaktuj się z nami wysyłając mail na adres <0>{salesEmail}</0>"

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:20
msgid "If you would like to purchase a workspace account, send us an email to <0>{salesEmail}</0>"
msgstr "Jeśli jesteś zainteresowany zakupem konta workspace, wyślij do nas mail na adres <0>{salesEmail}</0>"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:126
msgid "If you'd like to re-subscribe, please contact us at"
msgstr "Jeśli chcesz ponownie zasubskrybować, skontaktuj się z nami pod adresem"

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:56
msgid "If you'd like to unsubscribe from emails, sent via mediaboard.com, simply click the button below. Your email <0>{0}</0> will no longer receive new emails form us."
msgstr "Jeśli chcesz zrezygnować z subskrypcji e-maili wysyłanych za pośrednictwem mediaboard.com, po prostu kliknij przycisk poniżej. Twój e-mail <0>{0}</0> nie będzie już otrzymywał nowych wiadomości od nas."

#: src/components/reports/history/Content.js:38
msgid "If your report wasn't delivered, make sure to check your spam folder and your promotions inbox."
msgstr "Jeśli twój newsletter nie został dostarczony, sprawdź folder ze spamem."

#: src/store/models/dashboards/DashboardPreview.js:147
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:49
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:47
#: src/components/misc/MntrEditor/extensions/ExtensionImageGallery.js:49
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:70
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:70
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewImage/PreviewImage.js:29
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:38
msgid "Image"
msgstr "Obraz"

#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:61
msgid "Images"
msgstr "Obrazy"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:70
msgid "Import"
msgstr "Importować"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:181
#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:84
#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:13
#: src/components/medialist/content/MedialistActionsBar/ContactsImportTitle.tsx:7
msgid "Import contacts"
msgstr "Importuj kontakty"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:54
msgid "Import options"
msgstr "Opcje importu"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:42
msgid "Import to"
msgstr "Importuj do"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:75
msgid "Import your already formatted contact list or manually completed template."
msgstr "Importuj swoją już sformatowaną listę kontaktów lub ręcznie wypełniony szablon."

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:106
msgid "Import your contacts to medialist"
msgstr "Importuj swoje kontakty do medialisty"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:92
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:37
msgid "Imprint"
msgstr "Stopka redakcyjna"

#. placeholder {0}: formatDate(lowerDate, 'd. M. yyyy')
#. placeholder {1}: formatDate(upperDate, 'd. M. yyyy')
#: src/helpers/getTitleWithDateFromTo.js:5
msgid "in period from {0} to {1}"
msgstr "w okresie od {0} do {1}"

#: src/components/newsroom/content/posts/NewsroomPosts.js:297
msgid "In the settings you can edit basic information about the Newsroom, appearance, web address, etc."
msgstr "W ustawieniach możesz edytować podstawowe informacje o Newsroomie, wygląd, adres internetowy itp."

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:13
msgid "In three years with Mediaboard, our experience has been exceptional. Their professionalism, wide range of services, and top-notch quarterly and annual analyses are highly valuable. We recommend Mediaboard for quality and reliability."
msgstr "Nasze trzyletnie doświadczenie z Mediaboard jest wyjątkowe. Ich profesjonalizm, szeroki zakres usług oraz doskonałe kwartalne i roczne analizy są dla nas niezwykle cenne. Polecamy Mediaboard ze względu na wysoką jakość i niezawodność."

#: src/components/settings/SettingsApplication/SettingsApplication.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:106
msgid "In-app currency"
msgstr "Waluta używana w aplikacji"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:173
#: src/components/staff/admin/user/User.js:138
#: src/components/staff/admin/customer/users/UsersTable.js:116
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoStaticItem.js:14
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoItem.js:31
#: src/components/forms/dashboard/Search/SearchUsers.js:99
msgid "Inactive"
msgstr "Nieaktywny"

#: src/components/reports/Content/ReportsList/FormToggleActive/FormToggleActive.js:36
msgid "Inactive report"
msgstr "Nieaktywny newsletter"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:22
msgid "Include all the key points you want to specifically mention in your article. These should be the essential details, arguments, or highlights that support and enhance the main content.\""
msgstr "Uwzględnij wszystkie kluczowe punkty, które chcesz konkretnie wspomnieć w swoim artykule. Powinny to być istotne szczegóły, argumenty lub wyróżnienia, które wspierają i wzbogacają główną treść.”"

#: src/components/tariff/TariffLimits/TariffLimits.js:51
#: src/components/tariff/TariffLimits/TariffLimits.js:88
#: src/components/tariff/TariffLimits/TariffLimits.js:123
#: src/components/tariff/TariffLimits/TariffLimits.js:143
#: src/components/tariff/TariffLimits/TariffLimits.js:159
#: src/components/tariff/TariffLimits/TariffLimits.js:176
#: src/components/tariff/TariffLimits/TariffLimits.js:195
#: src/components/tariff/TariffLimits/TariffLimits.js:212
#: src/components/tariff/TariffLimits/TariffLimits.js:233
#: src/components/tariff/TariffLimits/TariffLimits.js:250
#: src/components/tariff/TariffLimits/TariffLimits.js:283
#: src/components/tariff/TariffLimits/SingleValueLimit/SingleValueLimit.js:20
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:114
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:85
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:102
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:110
msgid "Increase limit"
msgstr "Zwiększ limit"

#: src/components/tariff/TariffLimits/TariffLimits.js:50
#: src/components/tariff/TariffLimits/TariffLimits.js:87
#: src/components/tariff/TariffLimits/TariffLimits.js:122
#: src/components/tariff/TariffLimits/TariffLimits.js:142
#: src/components/tariff/TariffLimits/TariffLimits.js:158
#: src/components/tariff/TariffLimits/TariffLimits.js:175
#: src/components/tariff/TariffLimits/TariffLimits.js:194
#: src/components/tariff/TariffLimits/TariffLimits.js:211
#: src/components/tariff/TariffLimits/TariffLimits.js:232
#: src/components/tariff/TariffLimits/TariffLimits.js:249
#: src/components/tariff/TariffLimits/TariffLimits.js:282
#: src/components/tariff/TariffLimits/SingleValueLimit/SingleValueLimit.js:18
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:84
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:101
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:109
msgid "Increase limit?"
msgstr "Zwiększyć limit?"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:52
msgid "Indicate the desired tone (formal, casual) and style (informative, promotional)."
msgstr "Wskaż pożądany ton (formalny, nieformalny) i styl (informacyjny, promocyjny)."

#: src/components/monitoring/FeedList/FeedListItem/MetaData/modules/MetaDataScore/MetaDataScore.js:38
msgid "influence score"
msgstr "wynik wpływu"

#: src/constants/stats.ts:21
#: src/constants/analytics.js:262
#: src/constants/analytics.js:371
#: src/constants/analytics.js:451
#: src/constants/analytics.js:483
#: src/constants/analytics.js:644
#: src/constants/analytics.js:659
#: src/constants/analytics.js:929
#: src/constants/analytics.js:944
#: src/components/widgets/modules/stats/WidgetStats.js:162
#: src/components/misc/ActionsBar/View/ViewMenu.js:297
msgid "Influence score"
msgstr "Wynik wpływu"

#: src/constants/analytics.js:283
#: src/constants/analytics.js:530
msgid "Influence score by mention type"
msgstr "Wynik wpływu (Influence Score) według typu wzmianki"

#: src/constants/analytics.js:281
#: src/constants/analytics.js:390
#: src/constants/analytics.js:481
msgid "Influence score by sentiment"
msgstr "Wynik wpływu (Influence Score) według sentymentu"

#: src/constants/analytics.js:392
msgid "Influence score by social network"
msgstr "Wynik wpływu (Influence Score) według sieci społecznościowej"

#: src/components/emailing/content/sender/EmailingSenderContent.js:17
msgid "Initial Emailing settings"
msgstr "Początkowe ustawienia Mailingu"

#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:124
msgid "Insert"
msgstr "Wstaw"

#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:155
msgid "Insert button label to view preview"
msgstr "Wprowadź etykietę przycisku, aby wyświetlić podgląd"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:125
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:169
msgid "Insert HTML code to view preview"
msgstr "Wprowadź kod HTML, aby zobaczyć podgląd"

#: src/components/emailing/content/CreateEmailContent.js:408
msgid "Insert internal name of email"
msgstr "Wprowadź wewnętrzną nazwę maila"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:359
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:363
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:385
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:409
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:413
msgid "Insert link"
msgstr "Wstaw link"

#: src/components/emailing/content/CreateEmailContent.js:449
msgid "Insert subject"
msgstr "Wprowadź temat"

#: src/components/medialist/forms/FormEditAuthor.js:736
#: src/components/medialist/forms/FormEditAuthor.js:1031
msgid "Insert text..."
msgstr "Wprowadź tekst..."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:121
msgid "Instructions"
msgstr "Instrukcje"

#: src/constants/analytics.js:416
msgid "Interactions by sentiment"
msgstr "Interakcje według sentymentu"

#: src/constants/analytics.js:418
msgid "Interactions by social network"
msgstr "Interakcje według sieci społecznej"

#: src/constants/analytics.js:227
#: src/constants/analytics.js:639
#: src/constants/analytics.js:774
#: src/components/layout/AuthWrapper/constants/features.slides.js:199
msgid "Interactions on social networks"
msgstr "Interakcje na sieciach społecznościowych"

#: src/constants/analytics.js:225
msgid "Interactions on social networks by sentiment"
msgstr "Interakcje w sieciach społecznościowych według sentymentu"

#: src/components/emailing/content/CreateEmailContent.js:405
msgid "Internal name of email"
msgstr "Wewnętrzna nazwa maila"

#: src/components/emailing/forms/FormEmailRecipients.js:37
msgid "Invalid"
msgstr "Nieprawidłowy"

#: src/components/article/Content.js:13
msgid "Invalid article link"
msgstr "Nieprawidłowy link do materiału"

#. placeholder {0}: format( startOfYear(today), DATE_FORMAT, )
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:83
msgid "Invalid date format. Expected format is {0}"
msgstr "Nieprawidłowy format daty. Oczekiwany format to {0}"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:16
#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:10
#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:33
msgid "Invalid email format"
msgstr "Nieprawidłowy format maila"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:118
msgid "Invalid page number"
msgstr "Nieprawidłowy numer strony"

#: src/components/forms/inspector/FormMediaEditor.js:76
#: src/components/forms/inspector/FormMediaEditor.js:79
msgid "Invalid time format. Enter hh:mm:ss"
msgstr "Nieprawidłowy format czasu. Wprowadź hh:mm:ss"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:57
msgid "Invoice no."
msgstr "Numer faktury"

#: src/pages/staff/admin/customers/[customerId]/invoices.js:12
#: src/components/staff/admin/customer/invoices/Invoices.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:42
msgid "Invoices"
msgstr "Faktury"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:56
msgid "Irrelevant"
msgstr "Nieistotne"

#: src/components/misc/Changelog/ChangelogTableRow.js:156
msgid "Irreversible"
msgstr "Nieodwracalne"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:104
msgid "Is overdue"
msgstr "Po terminie"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:296
msgid "Is there a problem with the article?"
msgstr "Czy jest jakiś problem z materiałem?"

#. placeholder {0}: data.publication.issue
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:225
msgid "Issue: {0}"
msgstr "Numer: {0}"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:72
msgid "Issued via"
msgstr "Wydane przez"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:368
msgid "It appears above the description on the search results page."
msgstr "Pojawia się nad opisem na stronie z wynikami wyszukiwania."

#: src/pages/_error.js:49
msgid "It looks like you're trying to access a malformed URL. Please review it and try again."
msgstr "Wygląda na to, że próbujesz uzyskać dostęp do nieprawidłowego adresu URL. Proszę go sprawdzić i spróbować ponownie."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:131
msgid "Italic"
msgstr "Kursywa"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:144
msgid "Item '<0>{title}</0>' will be removed."
msgstr "Element '<0>{title}</0>' zostanie usunięty."

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:179
msgid "Job position"
msgstr "Stanowisko pracy"

#: src/components/medialist/forms/modules/FormArray.js:107
msgid "Job Position"
msgstr "Stanowisko pracy"

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:21
msgid "formatNumber.k"
msgstr "tys."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:68
msgid "Keep original"
msgstr "Zachować oryginał"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:20
msgid "Key points list"
msgstr "Lista kluczowych punktów"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:56
msgid "Key Points:"
msgstr "Kluczowe punkty:"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:40
msgid "Keyword"
msgstr "Słowo kluczowe"

#: src/helpers/modal/withModalTvrTopics.tsx:53
#: src/constants/analytics.js:1054
#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/KeywordsListMedia/KeywordsListMedia.js:22
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:53
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:119
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:255
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:337
msgid "Keywords"
msgstr "Słowa kluczowe"

#: src/components/misc/ResendSettings/SaveResendSettings/FormSaveResendSettings.js:21
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:122
#: src/components/misc/ExportSettings/SaveExportSettings/FormSaveExportSettings.js:21
#: src/components/emailing/forms/FormAddCampaign.tsx:15
#: src/components/dashboards/DashboardSelector/FormEditDashboard.js:26
msgid "Label"
msgstr "Etykieta"

#: src/constants/analytics.js:870
#: src/components/staff/admin/user/User.js:278
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:274
#: src/components/misc/ActionsBar/View/ViewMenu.js:333
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:310
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:329
#: src/components/layout/Header/UserMenu/UserMenu.tsx:119
msgid "Language"
msgstr "Język"

#: src/constants/analytics.js:883
msgid "Languages"
msgstr "Języki"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:106
msgid "Languages & connected newsrooms"
msgstr "Jazyki i połączone newsroomy"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:236
msgid "Last access"
msgstr "Ostatni dostęp"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:122
#: src/components/staff/admin/customer/users/UsersTable.js:74
msgid "Last login"
msgstr "Ostatnie logowanie"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:147
msgid "Last month"
msgstr "Poprzedni miesiąc"

#: src/components/page/auth/SignUp/SignUp.js:29
msgid "Last Name"
msgstr "Nazwisko"

#: src/components/newsroom/components/PostsList/PostsList.js:166
#: src/components/newsroom/components/PostsList/PostsList.js:181
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:152
msgid "Last update"
msgstr "Ostatnia aktualizacja"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:128
msgid "Last week"
msgstr "Poprzedni tydzień"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:160
msgid "Last year"
msgstr "Poprzedni rok"

#: src/components/emailing/forms/FormSenderSettings.js:106
msgid "Leave blank to use the default port"
msgstr "Pozostaw puste, aby użyć domyślnego portu"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:141
msgid "Light"
msgstr "Jasny"

#: src/components/settings/SettingsTheme/SettingsThemePreview/SettingsThemePreview.js:193
msgid "Light mode preview"
msgstr "Podgląd trybu jasnego"

#: src/components/medialist/forms/FormEditAuthor.js:504
msgid "limit"
msgstr "limit"

#: src/store/models/monitoring/MedialistMapItem/MedialistMapItem.js:260
#: src/store/models/monitoring/MedialistMapItem/MedialistMapItem.js:285
#: src/store/models/authors/AuthorsStore.js:410
#: src/store/models/authors/AuthorsStore.js:435
msgid "Limit exceeded. Sucessfully exported {generated} of {requested} requested authors."
msgstr "Przekroczono limit. Pomyślnie wyeksportowano {generated} z {requested} żądanych autorów."

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:652
msgid "Limit reached. You have selected too many articles."
msgstr "Osiągnięto limit. Wybrałeś zbyt wiele materiałów."

#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:35
msgid "Limits"
msgstr "Limity"

#: src/components/OurChart/OurChartAdvanced.js:162
msgid "Line"
msgstr "Liniowy"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:76
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:325
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:131
msgid "Link"
msgstr "Link"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:40
#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:69
#: src/components/staff/admin/user/User.js:67
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:224
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:15
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:16
#: src/components/exportList/History/HistoryTable/HistoryTable.js:111
msgid "Link has been copied to the clipboard."
msgstr "Link został skopiowany do schowka."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:116
msgid "Link to other language"
msgstr "Link do innego języka"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:95
msgid "Link to the article"
msgstr "Link do materiału"

#: src/components/emailing/content/EmailingSettingsContent.js:31
msgid "Linking with Google account timed out. Please, try again."
msgstr "Łączenie z kontem Google przekroczyło limit czasu. Spróbuj ponownie."

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:240
#: src/components/monitoring/Inspector/InspectorMonitora/Links/Links.js:14
msgid "Links"
msgstr "Linki"

#: src/components/medialist/content/AuthorBasketsMenu.js:135
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:124
msgid "List {label} will be removed."
msgstr "Lista {label} zostanie usunięta."

#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:25
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:31
#: src/components/forms/baskets/FormNewBasket.js:26
msgid "List name"
msgstr "Nazwa listy"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:26
msgid "List of tags"
msgstr "Lista tagów"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:99
msgid "List of topics"
msgstr "Lista tematów"

#: src/components/medialist/forms/FormEditAuthor.js:660
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:97
msgid "Lists"
msgstr "Listy"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:46
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:20
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:20
msgid "Load"
msgstr "Załaduj"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:54
msgid "Load from"
msgstr "Załaduj z"

#: src/components/monitoring/FeedList/LoadMore/LoadMore.js:27
msgid "Load more"
msgstr "Załaduj więcej"

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:140
msgid "Load more..."
msgstr "Załaduj więcej..."

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:46
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:48
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:48
msgid "Load settings"
msgstr "Załaduj listę"

#: src/components/tvr/Content/Content.js:82
#: src/components/trash/Content.js:39
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:70
#: src/components/reports/history/HistoryTable.js:491
#: src/components/notifications/Permissions.js:84
#: src/components/notifications/AppNotifications/AppNotifications.js:21
#: src/components/newsroom/content/dashboard/ChartVisits.js:127
#: src/components/monitoring/WorkspaceArticles/Intro.js:20
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:57
#: src/components/monitoring/FeedList/LoadMore/LoadMore.js:27
#: src/components/monitoring/FeedChart/FeedChart.js:47
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:47
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:55
#: src/components/misc/MntrEditor/forms/FormMediaUpload/UploadProgress.js:31
#: src/components/medialist/content/MedialistHeading.js:14
#: src/components/medialist/content/MedialistInspector/AuthorChart/AuthorChart.js:84
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:31
#: src/components/exportList/Content/HeadingExport/HeadingExport.js:25
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:40
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:48
#: src/components/dashboards/DashboardSelector/DashboardSelector.js:21
#: src/components/analytics/AnalyticsContent.js:38
#: src/components/OurChart/OurChartAdvanced.js:316
msgid "Loading..."
msgstr "Ładowanie..."

#: src/components/layout/Header/UserMenu/UserMenu.tsx:228
msgid "Log back in"
msgstr "Zaloguj się ponownie"

#: src/components/page/auth/Login/Login.tsx:55
#: src/components/page/auth/Login/Login.tsx:69
#: src/components/page/auth/Expired/Expired.js:104
msgid "Log In"
msgstr "Zaloguj się"

#: src/components/staff/admin/user/User.js:192
#: src/components/staff/admin/customer/users/UsersTable.js:129
msgid "Login as this user"
msgstr "Zaloguj się jako ten użytkownik"

#: src/components/staff/admin/workspace/Workspace.js:249
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:137
msgid "Login into this workspace"
msgstr "Zaloguj się do tej przestrzeni roboczej"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:187
msgid "Login link"
msgstr "Link do logowania"

#: src/components/page/auth/Login/Login.tsx:63
msgid "Login to {appName}"
msgstr "Zaloguj się do {appName}"

#: src/components/page/auth/Login/Login.tsx:64
msgid "Login to {appName}, the next generation media monitoring tool."
msgstr "Zaloguj się do {appName}, narzędzia do monitoringu mediów nowej generacji. Obserwuj, mierz i analizuj swoją komunikację."

#: src/components/page/auth/Expired/Expired.js:75
msgid "Login to different workspace"
msgstr "Zaloguj się do innego obszaru roboczego"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:187
msgid "Logout"
msgstr "Wyloguj się"

#: src/helpers/auth.js:47
msgid "Logout performed in another window."
msgstr "Wylogowanie wykonane w innym oknie."

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:22
msgid "formatNumber.M"
msgstr "mln."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:155
msgid "Magazine cover pages"
msgstr "Okładki czasopism"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:59
msgid "Main message"
msgstr "Główna wiadomość"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:48
msgid "Main message & key points"
msgstr "Główna wiadomość i kluczowe punkty"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:46
msgid "Main Objective:"
msgstr "Główny cel:"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:61
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:65
msgid "Mainstream sources"
msgstr "Główne źródła"

#: src/pages/_error.js:44
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:422
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:156
#: src/components/misc/MntrEditor/forms/FormEmbedSocialUrl/FormEmbedFacebookUrl.tsx:44
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:88
msgid "Malformed URL"
msgstr "Nieprawidłowy URL"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:104
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:107
msgid "Manage hidden tags"
msgstr "Zarządzaj ukrytymi tagami"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:95
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:98
msgid "Manage hidden topics"
msgstr "Zarządzaj ukrytymi tematami"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:231
msgid "Management summaries"
msgstr "Zarządzanie podsumowaniami"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:49
msgid "Manual writing"
msgstr "Pisanie ręczne"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:36
msgid "Manually add the information for your signature, which will appear in every email or customize it using your own HTML."
msgstr "Ręcznie dodaj informacje do swojego podpisu, który pojawi się w każdym e-mailu, lub dostosuj go za pomocą własnego HTML."

#: src/components/emailing/content/promo/PromoEmailing.js:27
msgid "Mass mailing"
msgstr "Masowa wysyłka"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:488
msgid "Max. file size:"
msgstr "Maks. rozmiar pliku:"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:225
msgid "Media analysis"
msgstr "Analiza mediów"

#: src/components/tariff/TariffLimits/TariffLimits.js:261
#: src/components/staff/admin/workspace/Workspace.js:500
msgid "Media archive depth limit"
msgstr "Głębokość archiwum multimedialnego"

#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:120
msgid "Media Coverage"
msgstr "Zasięg medialny"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:330
#: src/components/misc/ActionsBar/View/ViewMenu.js:49
#: src/components/misc/ActionsBar/View/ViewMenu.js:344
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:37
msgid "Media data"
msgstr "Dane medialne"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:92
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:168
msgid "Media data (GRP, OTS, AVE, PRIMe)"
msgstr "Dane medialne (GRP, OTS, AVE, PRIMe)"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:16
msgid "Media Monitoring"
msgstr "Monitoring mediów"

#: src/constants/analytics.js:79
#: src/constants/analytics.js:585
#: src/constants/analytics.js:717
#: src/components/layout/AuthWrapper/constants/features.slides.js:183
msgid "Media reach (GRP)"
msgstr "Zasięg mediów (GRP)"

#: src/constants/analytics.js:77
msgid "Media reach (GRP) by sentiment"
msgstr "Zasięg mediów (GRP) według sentymentu"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:222
msgid "Media services"
msgstr "Usługi medialne"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:43
msgid "Mediaboard transformed our communication at Coca-Cola HBC! A daily essential for top-notch media monitoring, with a user-friendly interface and insightful analytics. Their exceptional customer support makes it a joy to work with Mediaboard."
msgstr "Mediaboard przekształcił naszą komunikację w Coca-Cola HBC! To niezbędne narzędzie do codziennego monitoringu mediów, z przyjaznym dla użytkownika interfejsem i dogłębną analizą. Ich wyjątkowa obsługa klienta sprawia, że praca z Mediaboardem to czysta przyjemność."

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:29
msgid "Mediakit"
msgstr "Mediakit"

#: src/store/models/dashboards/DashboardPreview.js:99
#: src/pages/authors/index.js:32
#: src/pages/authors/index.js:41
#: src/pages/authors/create.js:10
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:110
#: src/components/layout/Sidebar/SidebarNavigation.tsx:137
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:36
#: src/components/layout/AuthWrapper/constants/features.slides.js:214
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:44
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:44
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:29
#: src/app/components/monitoring-navigation.tsx:269
msgid "Medialist"
msgstr "Medialist"

#: src/constants/analytics.js:793
#: src/components/topics/Content/TopicsList/MegalistModal.js:52
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:70
#: src/components/topics/Content/TopicsList/MegalistToolbar/MediatypeFilterPopup.js:8
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:247
msgid "Mediatype"
msgstr "Typ mediów"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:91
msgid "mediatype for all countries"
msgstr "typ mediów dla wszystkich krajów"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:67
msgid "Mention any personalization details (name, company)."
msgstr "Podaj wszelkie dane personalizacyjne (imię, firma)."

#: src/constants/analytics.js:233
#: src/constants/analytics.js:342
#: src/constants/analytics.js:889
#: src/constants/analytics.js:909
#: src/constants/analytics.js:1312
#: src/components/widgets/modules/stats/WidgetStats.js:241
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:51
msgid "Mentions"
msgstr "Wzmianki"

#: src/constants/analytics.js:254
#: src/constants/analytics.js:363
msgid "Mentions by sentiment"
msgstr "Wzmianki według sentymentu"

#: src/constants/analytics.js:365
#: src/constants/analytics.js:923
#: src/constants/analytics.js:1325
msgid "Mentions by social network"
msgstr "Wzmianki według sieci społecznościowej"

#: src/constants/analytics.js:256
#: src/constants/analytics.js:903
msgid "Mentions by type"
msgstr "Wzmianki według typu"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:586
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:96
msgid "Merge Tags"
msgstr "Scal tagi"

#: src/pages/staff/admin/customers/[customerId]/merged-customers.js:12
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomers.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:60
msgid "Merged customers"
msgstr "Połączeni klienci"

#: src/components/staff/admin/customer/bio/CustomerBio.js:79
msgid "Merged to"
msgstr "Połączone z"

#: src/components/misc/ActionsBar/View/ViewMenu.js:199
msgid "Metrics"
msgstr "Metryki"

#: src/components/misc/portable/PortableResend/PortableResend.js:93
#: src/components/misc/portable/PortableExport/PortableExport.js:88
msgid "Minimize"
msgstr "Minimalizuj"

#: src/components/tariff/MonitoredMedia/MissedArticles/MissedArticles.js:9
msgid "Missed articles"
msgstr "Pominięte materiały"

#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:54
#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:74
msgid "Missing article"
msgstr "Brakujący materiał"

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:39
msgid "Missing data"
msgstr "Brakujące dane"

#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:46
msgid "Missing recipient info"
msgstr "Brak informacji o odbiorcy"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:298
#: src/components/layout/AuthWrapper/constants/features.slides.js:399
msgid "Mobile Apps"
msgstr "Aplikacje mobilne"

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:41
msgid "Modified"
msgstr "Zmodyfikowane"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoItem.js:87
msgid "Monitor a wide range of social media platforms including Facebook, LinkedIn, Instagram, TikTok, X.com, and YouTube."
msgstr "Monitoruj szeroki zakres platform social media, w tym Facebooka, LinkedIn, Instagrama, TikToka, X.com i YouTube."

#: src/components/layout/AuthWrapper/constants/features.slides.js:23
msgid "Monitor newspapers, magazines, radios, TV stations or the entire online world. Reach out to media, react, track, analyze, and build your brand."
msgstr "Monitoruj prasę, czasopisma, radia, stacje telewizyjne czy cały świat online. Nawiąż kontakt z mediami, reaguj, śledź, analizuj i buduj swoją markę."

#: src/components/topics/Content/TopicsList/MediaCard.js:21
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:127
#: src/components/staff/admin/workspace/Workspace.js:877
#: src/components/settings/SettingsTariff/SettingsTariff.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:41
msgid "Monitored media"
msgstr "Monitorowane media"

#: src/components/notifications/Content.js:34
#: src/components/monitoring/Monitoring.js:109
#: src/components/monitoring/Monitoring.js:110
#: src/components/monitoring/Monitoring.js:165
#: src/components/layout/Sidebar/SidebarNavigation.tsx:119
#: src/components/layout/AuthWrapper/constants/features.slides.js:22
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:10
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:10
#: src/app/components/monitoring-navigation.tsx:65
msgid "Monitoring"
msgstr "Monitorowanie"

#: src/components/staff/admin/customer/expenses/ExpenseTable.js:77
msgid "Month"
msgstr "Miesiąc"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:107
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:65
msgid "monthly sessions"
msgstr "wizyty miesięcznie"

#: src/components/misc/ActionsBar/View/ViewMenu.js:82
msgid "Monthly sessions"
msgstr "Wizyty miesięczne"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:87
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:55
msgid "monthly users"
msgstr "użytkowników miesięcznie"

#: src/components/misc/ActionsBar/View/ViewMenu.js:74
msgid "Monthly users"
msgstr "Użytkownicy miesięcznie"

#: src/helpers/charts/makeGranularityMenu.js:26
#: src/helpers/charts/getGranularityLabel.js:9
msgid "Months"
msgstr "Miesiące"

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:159
msgid "more"
msgstr "więcej"

#: src/components/OurChart/OurChartAdvanced.js:247
msgid "More"
msgstr "Więcej"

#: src/constants/analytics.js:1338
msgid "Most common terms"
msgstr "Najczęstsze terminy"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:96
msgid "Move article"
msgstr "Przenieś materiał"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:119
msgid "Move to Dashboard"
msgstr "Przenieś do pulpitu"

#: src/pages/workspace-articles.js:51
#: src/components/monitoring/WorkspaceArticles/Intro.js:23
#: src/app/components/monitoring-navigation.tsx:204
msgid "My Articles"
msgstr "Moje materiały"

#: src/components/medialist/content/AuthorInfoDetail.js:72
msgid "My author"
msgstr "Mój autor"

#: src/components/medialist/content/OwnAuthorsListSelectorButton.js:9
#: src/components/medialist/content/AuthorBasketsMenu.js:41
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:158
msgid "My authors"
msgstr "Moi autorzy"

#: src/components/topics/Content/TopicsList/FormSaveMegalist/FormSaveMegalist.js:8
#: src/components/staff/admin/workspace/Workspace.js:288
#: src/components/staff/admin/user/WorkspacesTable.js:71
#: src/components/staff/admin/user/User.js:256
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:71
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:52
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:62
#: src/components/medialist/forms/FormEditAuthor.js:211
#: src/components/medialist/forms/FormEditAuthor.js:212
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:28
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:53
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:45
msgid "Name"
msgstr "Imię"

#. js-lingui-explicit-id
#: src/components/dashboards/DashboardSelector/FormCreateDashboard.js:25
msgid "name.nazev"
msgstr "Nazwa"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:57
msgid "Name for expression (optional)"
msgstr "Nazwa dla wyrażenia (opcjonalnie)"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:341
msgid "Need help? Ask AI assistant. Select a sentence or paragraph"
msgstr "Potrzebujesz pomocy? Zapytaj asystenta AI. Wybierz zdanie lub akapit."

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:147
msgid "Need help? Check our <0>tutorial</0> or contact us."
msgstr "Potrzebujesz pomocy? Sprawdź nasz <0>poradnik</0> lub skontaktuj się z nami."

#: src/components/medialist/content/MedialistDashboard.js:94
#: src/components/medialist/content/MedialistDashboard.js:127
msgid "New"
msgstr "Nowy"

#. js-lingui-explicit-id
#: src/components/layout/Header/AppNotifications/AppNotifications.js:133
msgid "new.notifications"
msgstr "Nowe"

#: src/components/emailing/modules/withModalAddCampaign.tsx:20
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:91
#: src/components/emailing/content/EmailingCampaignsContent.tsx:59
msgid "New Campaign"
msgstr "Nowa kampania"

#: src/components/newsroom/forms/FormCreateCategory/FormCreateCategory.js:29
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:72
msgid "New Category"
msgstr "Nowa kategoria"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:29
#: src/components/emailing/content/NewEmailWizardButton.tsx:13
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:72
msgid "New Email"
msgstr "Nowy mail"

#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:38
#: src/components/forms/baskets/FormNewBasket.js:10
msgid "New list"
msgstr "Nowa lista"

#: src/pages/user/reset-password/success.tsx:7
#: src/pages/user/reset-password/new.tsx:54
#: src/components/staff/admin/user/User.js:267
#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:87
msgid "New password"
msgstr "Nowe hasło"

#: src/store/models/admin/customer/CustomerStore.js:227
msgid "New passwords have been copied to the clipboard."
msgstr "Nowe hasła zostały skopiowane do schowka."

#: src/components/newsroom/content/posts/NewPostWizardButton.tsx:13
msgid "New post"
msgstr "Nowy post"

#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:71
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:70
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:103
msgid "New report"
msgstr "Nowy newsletter"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:78
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:264
#: src/components/forms/tags/FormNewTag/FormNewTag.js:10
msgid "New Tag"
msgstr "Nowy tag"

#: src/components/topics/Content/TopicsList/TopicsList.js:36
msgid "New topic"
msgstr "Nowy temat"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:55
msgid "New value"
msgstr "Nowa wartość"

#: src/pages/newsroom/index.js:24
#: src/pages/newsroom/index.js:33
#: src/pages/newsroom/create.js:17
#: src/pages/newsroom/[blogId]/settings.js:15
#: src/pages/newsroom/[blogId]/index.js:16
#: src/pages/newsroom/[blogId]/post/[postId].js:10
#: src/components/newsroom/forms/FormNewsroomSettings/LanguageSection.tsx:61
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:112
#: src/components/layout/Sidebar/SidebarNavigation.tsx:147
#: src/components/layout/AuthWrapper/constants/features.slides.js:306
#: src/components/emailing/components/FunnelStats/FunnelStats.tsx:80
#: src/app/components/monitoring-navigation.tsx:279
msgid "Newsroom"
msgstr "Redakcja"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:85
msgid "Newsroom Articles"
msgstr "Artykuły w newsroomie"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:254
msgid "Newsroom is a blogging platform that allows you to easily share your external and internal communication (e.g. press releases, announcements, etc.). You can read more about the Newsroom <0>on our website</0>."
msgstr "Newsroom to platforma blogowa, która umożliwia łatwe udostępnianie komunikacji zewnętrznej i wewnętrznej (np. komunikaty prasowe, ogłoszenia itp.). Więcej o Newsroomie możesz przeczytać <0>na naszej stronie internetowej</0>."

#: src/components/staff/admin/workspace/Workspace.js:481
msgid "Newsroom limit"
msgstr "Limit na liczbę Newsroomów"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:305
msgid "Newsroom settings"
msgstr "Ustawienia Newsroomu"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:69
msgid "Next page"
msgstr "Następna strona"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:40
msgid "No access to monitoring feeds, archive search, analytics, topic or report settings, crisis communications, medialist, or user settings."
msgstr "Brak dostępu do kanałów monitorowania, wyszukiwania w archiwum, analiz, ustawień tematów lub raportów, komunikacji kryzysowej, listy mediów ani ustawień użytkownika."

#: src/components/emailing/content/mediaCoverage/EmptyFeedMessage.tsx:8
msgid "No articles yet"
msgstr "Brak artykułów"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:81
msgid "No campaigns yet"
msgstr "Jeszcze nie masz żadnych kampanii"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:189
msgid "No categories yet."
msgstr "Nie masz jeszcze żadnych kategorii."

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:109
msgid "No companies found"
msgstr "Nie znaleziono firm"

#: src/components/staff/admin/user/WorkspacesTable.js:164
#: src/components/staff/admin/customer/workspaces/Workspaces.js:53
#: src/components/staff/admin/customer/users/UsersTable.js:148
#: src/components/staff/admin/customer/users/Users.js:53
#: src/components/staff/admin/customer/invoices/Invoices.js:49
#: src/components/staff/admin/customer/expenses/Expenses.js:46
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomers.js:49
msgid "No data"
msgstr "Brak danych"

#: src/helpers/charts/highcharts.js:20
msgid "No data to display"
msgstr "Brak danych do wyświetlenia"

#: src/components/trash/Content.js:43
msgid "No Deleted Articles"
msgstr "Brak usuniętych materiałów"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:47
msgid "No detailed data to track."
msgstr "Brak szczegółowych danych do śledzenia."

#: src/components/reports/Content/ReportsList/ReportsHeading/ReportsHeading.js:9
msgid "No email reports created"
msgstr "Nie utworzono żadnych raportów mailowych"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:145
msgid "No emails found"
msgstr "Nie znaleziono żadnych maili"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:127
msgid "No emails yet"
msgstr "Nie masz jeszcze żadnych wiadomości mailowych"

#: src/components/newsroom/content/posts/NewsroomPosts.js:291
msgid "No posts yet"
msgstr "Nie masz jeszcze żadnych postów."

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:66
msgid "No recipients found"
msgstr "Nie znaleziono żadnych odbiorców"

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:50
msgid "No recipients yet"
msgstr "Jeszcze nie dodano odbiorców"

#: src/components/reports/Content/ReportsList/ReportsList.js:85
msgid "No reports are assigned to the topic."
msgstr "Do tematu nie są przypisane żadne newslettery."

#: src/store/models/Megalist/MegalistFilter.js:42
#: src/helpers/withTranslatePopup/TranslatePopupContent.js:89
#: src/helpers/withMenuPopup/MntrMenuPopupContent.js:58
#: src/components/widgets/modules/tvr/WidgetTvr.js:78
#: src/components/widgets/modules/medialist/WidgetMedialist.js:127
#: src/components/widgets/modules/feed/WidgetFeedSimple.js:75
#: src/components/staff/admin/customers/Customers.js:37
#: src/components/misc/MntrMultiSelect/MultiSelect.js:22
#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:16
#: src/components/medialist/content/MedialistHeading.js:15
#: src/components/medialist/content/MedialistInspector/Feed/Feed.js:25
#: src/components/medialist/content/FeedMedialist/FeedMedialistEmpty/FeedMedialistEmpty.js:8
#: src/components/layout/Sidebar/modules/SidebarTopics/SidebarTopicsFolders.js:118
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:371
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitorsReports.js:64
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitors.js:86
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:280
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:34
#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTitle.js:99
#: src/components/forms/dashboard/Search/SearchEmailingEmailMessages.js:32
#: src/components/forms/dashboard/Search/SearchEmailingCampaigns.js:31
#: src/components/emailing/content/CampaignAutocompleteList.tsx:23
msgid "No results found"
msgstr "Nie znaleziono żadnych wyników"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:177
msgid "No senders"
msgstr "Brak nadawców"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:129
msgid "No subject"
msgstr "Bez tematu"

#: src/components/emailing/helpers/displayEmailingTitle.js:18
#: src/components/emailing/helpers/displayEmailingTitle.js:21
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:149
#: src/components/emailing/components/RecipientsFeed/EmailMessagesList.js:13
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:123
msgid "No title"
msgstr "Bez tytułu"

#: src/components/topics/Content/TopicsHeading/TopicsHeading.js:9
msgid "No topics created"
msgstr "Nie utworzyłeś żadnego tematu"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:209
msgid "No users"
msgstr "Brak użytkowników"

#: src/components/staff/admin/workspace/Workspace.js:862
msgid "No users assigned to this workspace"
msgstr "Do tej przestrzeni roboczej nie są przypisani żadni użytkownicy"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:187
msgid "No users found"
msgstr "Nie znaleziono użytkowników"

#: src/components/newsroom/content/dashboard/ChartVisits.js:59
msgid "No visits yet"
msgstr "Jeszcze nie ma żadnych wizyt."

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:14
msgid "No workspace"
msgstr "Brak obszaru roboczego"

#: src/components/staff/admin/user/User.js:311
msgid "No workspaces assigned to this user"
msgstr "Brak przypisanych obszarów roboczych dla tego użytkownika"

#. js-lingui-explicit-id
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:542
msgid "filetypes.none"
msgstr "żadne"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:90
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:43
#: src/components/misc/ActionsBar/Selector/Selector.js:58
msgid "None"
msgstr "Nic"

#. js-lingui-explicit-id
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:43
msgid "attachment.none"
msgstr "Brak"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:152
msgid "NOT"
msgstr "NIE"

#: src/components/reports/history/RecipientsTableRow.js:49
#: src/components/reports/history/HistoryTable.js:82
#: src/components/reports/history/HistoryTable.js:111
#: src/components/reports/history/HistoryTable.js:325
msgid "Not delivered"
msgstr "Nie dostarczono"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:151
msgid "Not verified"
msgstr "Niezweryfikowano"

#: src/store/models/dashboards/DashboardPreview.js:156
#: src/components/staff/admin/workspace/Workspace.js:339
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:36
#: src/components/medialist/forms/FormEditAuthor.js:703
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:63
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:63
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:35
msgid "Note"
msgstr "Notatka"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:128
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:368
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:83
msgid "Notes"
msgstr "Notatki"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:30
msgid "Notification about mention <0>within 3 minutes</0>"
msgstr "Powiadomienie o wzmiance <0>w ciągu 3 minut</0>"

#: src/components/layout/Header/AppNotifications/AppNotifications.js:99
#: src/components/layout/Header/AppNotifications/AppNotifications.js:107
msgid "Notification Settings"
msgstr "Ustawienia powiadomień"

#: src/components/notifications/ContentTvr.js:39
#: src/components/notifications/ContentTopics.js:24
#: src/components/notifications/AppNotifications/AppNotifications.js:21
#: src/components/layout/Header/AppNotifications/AppNotifications.js:152
msgid "Notifications"
msgstr "Powiadomienia"

#: src/constants/analytics.js:55
#: src/constants/analytics.js:566
#: src/constants/analytics.js:678
msgid "Number of articles"
msgstr "Liczba materiałów"

#: src/constants/analytics/primeScoreCharts.ts:57
msgid "Number of articles by PRIMe relevant vs irrelevant"
msgstr "Liczba artykułów według PRIMe istotne vs nieistotne"

#: src/constants/analytics.js:53
msgid "Number of articles by sentiment"
msgstr "Liczba materiałów według sentymentu"

#: src/components/monitoring/Inspector/InspectorMonitora/SocialParentText/SocialParentHeader.js:94
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:41
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:132
#: src/components/misc/ActionsBar/View/ViewMenu.js:289
msgid "Number of followers"
msgstr "Liczba obserwujących"

#: src/store/models/OurChart.js:188
msgid "Number of mentions"
msgstr "Liczba wzmianek"

#: src/components/tvr/Content/Content.js:61
msgid "Number of outputs"
msgstr "Liczba wyników"

#: src/components/reports/Content/ReportsList/ReportsTopMentionsMode.js:21
msgid "Number of TOP stories"
msgstr "Liczba TOP tematów"

#: src/components/misc/Changelog/ChangelogTable.js:33
msgid "Object"
msgstr "Obiekt"

#: src/components/monitoring/WorkspaceArticles/Limits.js:57
msgid "OCR"
msgstr "OCR"

#: src/components/monitoring/FeedList/FeedListItem/FeedListOlderDivider/FeedListOlderDivider.js:24
msgid "Older articles"
msgstr "Starsze materiały"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:142
msgid "on frontpage"
msgstr "na stronie głównej"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:91
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:125
msgid "on this continent"
msgstr "na tym kontynencie"

#: src/components/misc/ActionsBar/View/ViewMenu.js:62
#: src/components/layout/AuthWrapper/constants/features.slides.js:47
msgid "Online"
msgstr "Online"

#: src/constants/analytics.js:1287
msgid "Online categories"
msgstr "Kategorie - Online"

#: src/store/models/Megalist/MegalistFilter.js:34
msgid "Only Selected"
msgstr "Tylko wybrane"

#: src/store/models/Megalist/MegalistFilter.js:38
msgid "Only Unselected"
msgstr "Tylko niewybrane"

#: src/components/medialist/forms/FormEditAuthor.js:892
msgid "Only you can see all the data you entered and the changes made."
msgstr "Tylko Ty możesz zobaczyć wszystkie wprowadzone dane i dokonane zmiany."

#: src/components/newsroom/content/posts/NewsroomPosts.js:185
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:159
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderVideo/HeaderVideo.js:52
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderSocial/HeaderSocial.js:34
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:105
#: src/components/monitoring/Inspector/InspectorEntityKnowledgeBase/InspectorKnowledgeBaseHeader.js:12
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:51
#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthor.js:55
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:20
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:54
msgid "Open"
msgstr "Otwórz"

#: src/components/staff/admin/customers/Customer.js:187
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:102
msgid "Open customer detail"
msgstr "Otwórz szczegóły klienta"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:102
msgid "Open In Feed"
msgstr "Otwórz w kanałach"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/EmbedFacebook/EmbedFacebook.tsx:49
msgid "Open on Facebook"
msgstr "Otwórz na Facebooku"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:209
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:225
msgid "Open rate"
msgstr "Wskaźnik otwarcia"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:199
#: src/components/staff/admin/customer/users/UsersTable.js:138
msgid "Open user detail"
msgstr "Otwórz szczegóły użytkownika"

#: src/components/staff/admin/user/WorkspacesTable.js:154
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:146
msgid "Open workspace detail"
msgstr "Otwórz szczegóły obszaru roboczego"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:281
msgid "Opportunity to see"
msgstr "Możliwość zobaczenia"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:190
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:320
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:360
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:371
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:387
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:421
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:441
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:510
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:230
#: src/components/emailing/forms/FormSenderSettings.js:89
#: src/components/emailing/forms/FormSenderSettings.js:105
msgid "optional"
msgstr "opcjonalne"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:146
msgid "OR"
msgstr "LUB"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:70
msgid "Or use an external service"
msgstr "Lub skorzystaj z usługi zewnętrznej"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:91
msgid "Order articles"
msgstr "Sortuj materiały"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:265
msgid "Ordered list"
msgstr "Uporządkowana lista"

#: src/helpers/withTranslatePopup/TranslatePopupContent.js:70
msgid "Original"
msgstr "Oryginał"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:45
msgid "Original value"
msgstr "Oryginalna wartość"

#: src/constants/analytics.js:1125
#: src/constants/analytics.js:1205
#: src/constants/analytics.js:1227
#: src/constants/analytics.js:1247
#: src/constants/analytics.js:1267
#: src/constants/analytics.js:1286
#: src/constants/analytics.js:1305
#: src/constants/analytics.js:1324
#: src/constants/analytics.js:1337
#: src/constants/analytics/primeScoreCharts.ts:135
#: src/components/notifications/ContentTvr.js:118
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:57
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:57
#: src/app/components/monitoring-navigation.tsx:252
msgid "Other"
msgstr "Inne"

#: src/store/models/Megalist/Megalist.js:48
msgid "Other Regions"
msgstr "Ogólnopolskie"

#: src/components/staff/admin/workspace/Workspace.js:671
msgid "Other settings"
msgstr "Inne ustawienia"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:155
msgid "Others"
msgstr "Inne"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:278
#: src/components/misc/ActionsBar/View/ViewMenu.js:219
msgid "OTS"
msgstr "OTS"

#: src/pages/_error.js:55
msgid "Our team has been notified. We're sorry for the inconvenience."
msgstr "Nasz zespół został powiadomiony. Przepraszamy za niedogodności."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:57
msgid "Outline the main content or details you want included."
msgstr "Zarysuj główną treść lub szczegóły, które chcesz uwzględnić."

#: src/components/medialist/constants/medialist.tabNavigation.js:20
msgid "Overview"
msgstr "Przegląd"

#: src/components/staff/admin/workspace/Workspace.js:603
msgid "Own content"
msgstr "Własna zawartość"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:86
msgid "Own selection"
msgstr "Własny wybór"

#. js-lingui-explicit-id
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:92
msgid "page.shortened"
msgstr "str."

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:64
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:76
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:72
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:220
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupPagesContent.js:27
#: src/components/misc/Pagination/Pagination.js:26
#: src/components/misc/Pagination/Pagination.js:39
#: src/components/misc/Pagination/Pagination.js:45
#: src/components/misc/Pagination/Pagination.js:76
#: src/components/misc/Pagination/Pagination.js:84
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:118
msgid "Page"
msgstr "Strona"

#: src/components/tvr/Inspector/InspectorMedia/PaginationMedia/PaginationMedia.js:30
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPagination.js:48
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:258
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupPagesContent.js:18
#: src/components/layout/MntrActiveFilters/modules/PageNumbers.js:22
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:123
msgid "Pages"
msgstr "Strony"

#. placeholder {0}: data.publication.pages.length
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:238
msgid "Pages ({0} total)"
msgstr "Strony ({0} w sumie)"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:60
msgid "Paid"
msgstr "Zapłacone"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:160
msgid "Paragraph"
msgstr "Akapit"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:423
msgid "Parse PDF"
msgstr "Analizuj PDF"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:140
msgid "Partner Code (optional)"
msgstr "Kod partnera (opcjonalnie)"

#: src/pages/user/reset-password/new.tsx:32
#: src/components/page/auth/SignUp/SignUp.js:42
#: src/components/page/auth/Login/Login.tsx:46
#: src/components/emailing/forms/FormSenderSettings.js:96
msgid "Password"
msgstr "Hasło"

#: src/pages/user/reset-password/new.tsx:39
msgid "Password again"
msgstr "Hasło ponownie"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:119
msgid "Password change"
msgstr "Zmiana hasła"

#: src/components/staff/admin/workspace/UsersTable/CopyPassword.js:34
msgid "Password copied to the clipboard."
msgstr "Hasło skopiowane do schowka."

#: src/components/misc/VideoPlayer/CropControls.js:127
#: src/components/misc/VideoPlayer/Controls.js:98
msgid "Pause"
msgstr "Pauza"

#: src/components/reports/history/RecipientsTableRow.js:58
msgid "Pending"
msgstr "Oczekuje"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:30
msgid "people"
msgstr "odbiorców"

#: src/helpers/formatNumber.js:29
msgid "per month"
msgstr "na miesiąc"

#: src/components/OurChart/OurChartAdvanced.js:155
msgid "Percent Share"
msgstr "Procentowy udział"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:137
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:562
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:564
#: src/components/misc/ActionsBar/View/ViewMenu.js:326
msgid "Perex"
msgstr "Lead"

#: src/components/exportList/History/HistoryTable/HistoryTable.js:60
msgid "Period"
msgstr "Za okres"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:39
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:44
msgid "Periodicity"
msgstr "Okresowość"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:60
msgid "Permissions"
msgstr "Uprawnienia"

#: src/components/medialist/forms/FormEditAuthor.js:841
#: src/components/medialist/forms/FormEditAuthor.js:1002
#: src/components/medialist/forms/FormEditAuthor.js:1007
msgid "Personal Website"
msgstr "Strona internetowa"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:66
msgid "Personalization:"
msgstr "Personalizacja:"

#: src/components/page/auth/SignUp/SignUp.js:49
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:132
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:80
#: src/components/medialist/forms/FormEditAuthor.js:766
#: src/components/medialist/forms/FormEditAuthor.js:900
#: src/components/medialist/forms/FormEditAuthor.js:906
msgid "Phone"
msgstr "Telefon"

#: src/constants/analytics.js:677
#: src/constants/analytics.js:697
#: src/constants/analytics.js:716
#: src/constants/analytics.js:735
#: src/constants/analytics.js:754
#: src/constants/analytics.js:773
#: src/constants/analytics.js:792
#: src/constants/analytics.js:811
#: src/constants/analytics.js:826
#: src/constants/analytics.js:844
#: src/constants/analytics.js:863
#: src/constants/analytics.js:882
#: src/constants/analytics.js:902
#: src/constants/analytics.js:922
#: src/constants/analytics.js:943
#: src/constants/analytics.js:963
#: src/constants/analytics.js:978
#: src/constants/analytics.js:992
#: src/constants/analytics.js:1008
#: src/constants/analytics.js:1023
#: src/constants/analytics.js:1038
#: src/constants/analytics/primeScoreCharts.ts:94
msgid "Pie"
msgstr "Ciasto"

#: src/helpers/formatNumber.js:39
msgid "pieces"
msgstr "sztuk"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:71
msgid "Plain"
msgstr "Prosty"

#: src/components/staff/admin/workspace/Workspace.js:309
msgid "Plan"
msgstr "Plan"

#: src/components/emailing/content/promo/PromoEmailing.js:18
msgid "Platform for email communication with journalists."
msgstr "Platforma do komunikacji mailowej z dziennikarzami."

#: src/components/misc/VideoPlayer/CropControls.js:127
#: src/components/misc/VideoPlayer/Controls.js:98
msgid "Play"
msgstr "Odtwórz"

#: src/components/emailing/content/sender/EmailingSenderContent.js:34
msgid "Please add a sender address that will be used for sending emails."
msgstr "Proszę podać adres nadawcy, który będzie używany do wysyłania maili."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:545
msgid "Please copy and insert this code into your website. Modify the width and height values of the iframe according to your requirements. Additionally, it’s possible to hide the header and footer if necessary."
msgstr "Skopiuj i wklej ten kod do kodu źródłowego swojej strony internetowej. Szerokość i wysokość iframe'u możesz dostosować do swoich potrzeb. Możesz również ukryć nagłówek i stopkę, jeśli to konieczne."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:95
msgid "Please remove some recipients."
msgstr "Proszę usunąć niektórych odbiorców."

#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:168
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:81
msgid "Please select Image"
msgstr "Proszę wybrać obraz"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepTitleAndCommunicationPlan.tsx:60
msgid "Please select the title and review the communication plan. If it does not meet your expectations, restart the process."
msgstr "Proszę wybrać tytuł i przejrzeć plan komunikacyjny. Jeśli nie spełnia on twoich oczekiwań, uruchom proces ponownie."

#: src/components/emailing/forms/FormSenderSettings.js:105
msgid "Port"
msgstr "Port"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:133
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:100
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:68
msgid "Position"
msgstr "Pozycja"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostPreview.tsx:80
msgid "Post preview"
msgstr "Podgląd postu"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:351
msgid "Post settings"
msgstr "Ustawienia posta"

#: src/constants/analytics.js:1132
#: src/constants/analytics.js:1157
#: src/constants/analytics.js:1212
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:37
msgid "Posts"
msgstr "Posty"

#: src/components/topics/Content/TopicsList/KeywordExtraQueryAdvanced/KeywordExtraQueryAdvanced.tsx:26
msgid "Predefined queries"
msgstr "Predefiniowane zapytania"

#: src/components/misc/Capture/Capture.js:283
msgid "Preparing export..."
msgstr "Przygotowujemy eksport..."

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:86
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:28
#: src/components/reports/history/HistoryTable.js:406
#: src/components/reports/Content/ReportsList/ReportPreview.js:18
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:20
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:115
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:140
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:140
#: src/components/forms/dashboard/ExportResend/ExportResend.js:163
#: src/components/emailing/content/CreateEmailContent.js:278
msgid "Preview"
msgstr "Podgląd"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:142
msgid "Preview & Publish"
msgstr "Podgląd i publikacja"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:110
msgid "Preview images"
msgstr "Podgląd obrazów"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:66
msgid "Previous page"
msgstr "Poprzednia strona"

#: src/components/staff/SignUp.js:17
#: src/components/staff/admin/workspace/Workspace.js:296
#: src/components/staff/admin/DailyAccess/Table.js:30
msgid "Primary app"
msgstr "Aplikacja"

#: src/constants/analytics/primeScoreCharts.ts:122
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:420
#: src/components/layout/MntrActiveFilters/modules/PrimeFilter.tsx:25
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:46
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:33
#: src/components/analytics/AnalyticsContent.js:152
#: src/components/analytics/AnalyticsContent.js:179
msgid "PRIMe"
msgstr "PRIMe"

#: src/constants/analytics/primeScoreCharts.ts:116
msgid "PRIMe in mediatype"
msgstr "PRIMe w typie mediów"

#: src/components/widgets/modules/stats/WidgetStats.js:190
msgid "PRIMe negative total value"
msgstr "PRIMe ujemna całkowita wartość"

#: src/components/widgets/modules/stats/WidgetStats.js:183
msgid "PRIMe positive total value"
msgstr "PRIMe pozytywna całkowita wartość"

#: src/constants/analytics/primeScoreCharts.ts:76
msgid "PRIMe scale"
msgstr "Skala PRIMe"

#: src/constants/analytics/primeScoreCharts.ts:9
#: src/constants/analytics/primeScoreCharts.ts:37
#: src/constants/analytics/primeScoreCharts.ts:69
#: src/constants/analytics/primeScoreCharts.ts:82
#: src/constants/analytics/primeScoreCharts.ts:102
msgid "PRIMe score"
msgstr "Wynik PRIMe"

#: src/components/widgets/modules/stats/WidgetStats.js:176
msgid "PRIMe total average"
msgstr "PRIMe średnia całkowita"

#: src/components/widgets/modules/stats/WidgetStats.js:169
msgid "PRIMe total value"
msgstr "PRIMe całkowita wartość"

#: src/components/misc/ActionsBar/View/ViewMenu.js:112
#: src/components/layout/AuthWrapper/constants/features.slides.js:57
#: src/components/OurChart/OurChartAdvanced.js:260
msgid "Print"
msgstr "Drukuj"

#: src/constants/analytics.js:1306
msgid "Print categories"
msgstr "Kategorie - Drukuj"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:89
msgid "Professional"
msgstr "Profesjonalny"

#: src/constants/analytics.js:1154
msgid "Profile"
msgstr "Profil"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:113
#: src/components/misc/MntrEditor/modals/withModalPromoBox.js:8
#: src/components/misc/MntrEditor/extensions/ExtensionPromoBox.js:33
msgid "Promo Box"
msgstr "Promo box"

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:66
msgid "Provide additional feedback..."
msgstr "Zapewnij dodatkowe informacje zwrotne..."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:33
msgid "Provide information such as:"
msgstr "Podaj informacje takie jak:"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:74
msgid "Providing information about your company allows the AI assistant to generate more accurate and tailored content for your newsroom articles. This ensures the text aligns closely with your brand's identity and messaging"
msgstr "Podanie informacji o Twojej firmie pozwala asystentowi AI na generowanie bardziej dokładnych i dostosowanych treści dla artykułów w Twojej redakcji. Dzięki temu tekst ściśle odpowiada tożsamości i przekazowi Twojej marki"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:291
msgid "Publication Date"
msgstr "Data publikacji"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:95
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:142
msgid "Publish"
msgstr "Opublikuj"

#: src/components/newsroom/forms/FormNewsroomPost/PostStatus.js:49
msgid "Publish date set to"
msgstr "Data publikacji ustawiona na"

#: src/components/newsroom/forms/FormNewsroomPost/PostStatus.js:73
msgid "Publish date set to {scheduledFormatted}"
msgstr "Data publikacji ustawiona na {scheduledFormatted}"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:154
msgid "Publish now"
msgstr "Opublikuj teraz"

#: src/pages/newsroom/index.js:45
msgid "Publish press releases <0>easily and quickly</0>"
msgstr "Opublikuj komunikat prasowy <0>szybko i łatwo</0>"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:155
msgid "Publish this post immediately"
msgstr "Natychmiast opublikuj ten post"

#: src/components/newsroom/components/PostsList/PostsList.js:184
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:30
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:66
msgid "Published"
msgstr "Opublikowane"

#: src/components/staff/admin/user/getUserAttributes.js:14
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:35
#: src/components/layout/Sidebar/SidebarNavigation.tsx:195
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:261
#: src/components/layout/MntrActiveFilters/modules/Publisher.js:13
msgid "Publisher"
msgstr "Wydawca"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:30
msgid "Publisher copyright fees"
msgstr "Opłaty licencyjne wydawcy"

#: src/constants/analytics.js:979
#: src/constants/analytics.js:993
#: src/constants/analytics.js:1009
#: src/constants/analytics.js:1024
#: src/constants/analytics.js:1039
msgid "Publishers"
msgstr "Wydawcy"

#: src/components/layout/Header/AppNotifications/AppNotifications.js:201
msgid "Push Notifications"
msgstr "Powiadomienia push"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:32
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:66
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:106
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:134
msgid "Quick Overview"
msgstr "Szybki przegląd"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:30
msgid "Quickly protect your brand reputation and stakeholder trust."
msgstr "Szybko zabezpiecz reputację swojej marki i zaufanie interesariuszy."

#: src/components/newsroom/content/modules/CustomQuotes.tsx:64
msgid "Quote"
msgstr "Cytat"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:74
#: src/components/newsroom/content/modules/CustomQuotes.tsx:33
msgid "Quotes"
msgstr "Cytaty"

#: src/components/notifications/ContentTvrRequest.js:74
#: src/components/notifications/ContentTvr.js:81
#: src/components/misc/ActionsBar/View/ViewMenu.js:162
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:62
#: src/components/layout/AuthWrapper/constants/features.slides.js:75
msgid "Radio"
msgstr "Radio"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:92
msgid "Rank is primarily based on the reach and the importance of the news source."
msgstr "Ranking opiera się przede wszystkim na zasięgu i znaczeniu źródła informacji."

#: src/components/newsroom/components/AiTools/AiCheckPostResult.tsx:92
msgid "Re-run check"
msgstr "Ponowne uruchomienie kontroli"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:30
msgid "reach"
msgstr "zasięg"

#: src/constants/stats.ts:26
#: src/constants/analytics.js:107
#: src/constants/analytics.js:121
#: src/constants/analytics.js:123
#: src/constants/analytics.js:129
#: src/constants/analytics.js:590
#: src/constants/analytics.js:603
#: src/constants/analytics.js:736
#: src/constants/analytics.js:1025
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:421
#: src/components/misc/ActionsBar/View/ViewMenu.js:227
#: src/components/analytics/TraditionalMedia.js:34
#: src/components/analytics/TraditionalMedia.js:40
msgid "Reach"
msgstr "Zasięg"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:98
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:112
msgid "Reactivate"
msgstr "Reaktywować"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:101
msgid "Reactivate recipient"
msgstr "Reaktywuj odbiorcę"

#: src/components/reports/history/RecipientsTableRow.js:31
msgid "Read"
msgstr "Przeczytany"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:34
msgid "Read only:"
msgstr "Tylko do odczytu:"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:189
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:63
#: src/components/misc/ActionsBar/View/ViewMenu.js:132
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:95
msgid "Readership"
msgstr "Czytelnictwo"

#: src/helpers/modal/withModalReportProblem.tsx:32
#: src/helpers/modal/withModalReportArticle.tsx:46
#: src/components/reports/history/RecipientsTableHeader.js:38
msgid "Reason"
msgstr "Powód"

#: src/components/medialist/content/MedialistDashboard.js:179
msgid "Recently edited authors"
msgstr "Ostatnio edytowani autorzy"

#: src/components/medialist/content/MedialistDashboard.js:158
msgid "Recently viewed authors"
msgstr "Ostatnio przeglądani autorzy"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:70
msgid "Recipient"
msgstr "Odbiorca"

#: src/store/models/reports/recipients/Recipients.js:27
msgid "Recipient added."
msgstr "Odbiorca został dodany."

#: src/components/forms/dashboard/ExportResend/ExportResend.js:79
msgid "Recipient emails"
msgstr "Maile odbiorców"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:83
msgid "Recipient has no email address"
msgstr "Odbiorca nie ma adresu e-mail"

#: src/store/models/reports/recipients/Recipients.js:56
msgid "Recipient removed."
msgstr "Odbiorca został usunięty."

#: src/store/models/reports/recipients/Recipients.js:44
msgid "Recipient updated."
msgstr "Odbiorca został zaktualizowany."

#: src/helpers/modal/withModalTvrTopics.tsx:77
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:246
#: src/components/reports/history/HistoryTable.js:169
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:59
#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:55
#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:63
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:101
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:112
#: src/components/emailing/content/CreateEmailContent.js:269
#: src/components/emailing/content/tabs/RecipientsTab.tsx:23
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:163
msgid "Recipients"
msgstr "Odbiorcy"

#: src/store/models/reports/recipients/Recipients.js:27
msgid "Recipients added."
msgstr "Odbiorcy zostali dodani."

#: src/components/reports/history/HistoryTable.js:52
msgid "Recipients from: {formattedCreated}"
msgstr "Odbiorcy z: {formattedCreated}"

#: src/components/reports/history/HistoryTable.js:432
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:50
msgid "Recipients have been copied to the clipboard."
msgstr "Odbiorcy zostali skopiowani do schowka."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:45
msgid "Recipients limit"
msgstr "Limit odbiorców"

#: src/store/models/reports/recipients/Recipients.js:68
msgid "Recipients removed."
msgstr "Odbiorcy zostali usunięci."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:25
msgid "Recipients with missing information"
msgstr "Odbiorcy z brakującymi informacjami"

#: src/components/forms/dashboard/Export/RecommendedLimit.js:32
msgid "Recomended limit"
msgstr "Zalecany limit"

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:59
msgid "Recommended file types: XLSX, CSV"
msgstr "Zalecane typy plików: XLSX, CSV"

#: src/components/forms/adapters/MntrFileAdapter/MntrFileAdapter.js:70
msgid "Recommended resolution"
msgstr "Zalecana rozdzielczość"

#: src/components/staff/admin/workspace/Workspace.js:136
msgid "Recreate articles"
msgstr "Przywróć materiały"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:566
msgid "Redo"
msgstr "Ponów"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:31
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:110
msgid "Regenerate content"
msgstr "Wygeneruj treść ponownie"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:31
msgid "Regenerate until the email text aligns perfectly with your requirements"
msgstr "Regeneruj, aż tekst e-maila idealnie spełni Twoje wymagania."

#: src/components/staff/admin/customers/Customers.js:27
msgid "Register new user"
msgstr "Zarejestruj nowego użytkownika"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:55
msgid "Relevant"
msgstr "Istotny"

#: src/helpers/modal/withModalRemove.tsx:37
#: src/helpers/modal/withModalRemove.tsx:51
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:142
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:47
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:85
#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:7
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:676
#: src/components/newsroom/content/modules/CustomQuotes.tsx:58
#: src/components/newsroom/content/modules/CustomKeypoints.tsx:49
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:570
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:74
#: src/components/emailing/modules/withModalRemoveRecipients.tsx:24
#: src/components/emailing/modules/withModalRemoveRecipients.tsx:39
#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:88
#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:68
msgid "Remove"
msgstr "Usuń"

#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:105
#: src/components/exportList/Content/Content.tsx:95
#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:56
msgid "Remove All"
msgstr "Usuń wszystko"

#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:34
msgid "Remove all from report"
msgstr "Usuń wszystko z raportu"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:97
msgid "Remove all from selection"
msgstr "Usuń wszystko z wyboru"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:155
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:126
msgid "Remove authors from list"
msgstr "Usuń autorów z listy"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:50
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:88
msgid "Remove Campaign"
msgstr "Usuń kampanię"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:57
msgid "Remove from article"
msgstr "Usuń z materiału"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:338
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:285
msgid "Remove from Export"
msgstr "Usuń z eksportu"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:40
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:99
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:173
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:257
msgid "Remove from filters"
msgstr "Usuń z filtrów"

#: src/components/medialist/content/withRemoveFromBasketPopup.js:34
msgid "Remove from list"
msgstr "Usuń z listy"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:273
msgid "Remove from next report"
msgstr "Usuń z następnego raportu"

#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:282
msgid "Remove from report"
msgstr "Usuń z raportu"

#: src/components/staff/admin/user/WorkspacesTable.js:138
msgid "Remove from workspace"
msgstr "Usuń z obszaru roboczego"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:200
#: src/components/settings/SettingsLogo/SettingsLogo.js:145
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:335
msgid "Remove Image"
msgstr "Usuń obraz"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:359
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:457
msgid "Remove Link"
msgstr "Usuń link"

#: src/components/medialist/forms/modules/FormFieldUploadPhoto.js:53
msgid "Remove Photo"
msgstr "Usuń zdjęcie"

#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:69
msgid "Remove Recipients"
msgstr "Usuń odbiorców"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:73
msgid "Remove report"
msgstr "Usuń raport"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:99
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:274
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:374
#: src/components/monitoring/FeedActionsBar/withRemoveTagPopup/RemoveTagPopupContent.js:11
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:77
msgid "Remove tag"
msgstr "Usuń tag"

#: src/components/staff/admin/workspace/UsersTable/RemoveUser.js:25
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:398
msgid "Remove user"
msgstr "Usuń użytkownika"

#: src/components/staff/admin/workspace/UsersTable/RemoveUsers.tsx:16
msgid "Remove users"
msgstr "Usuń użytkowników"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:132
msgid "Remove widget"
msgstr "Usuń Widget"

#: src/store/models/monitoring/Inspector/Inspector.ts:428
msgid "Removed from next report."
msgstr "Usunięte z raportu."

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:257
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:261
msgid "Rename"
msgstr "Zmień nazwę"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:94
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:36
#: src/components/reports/Content/ReportsList/ReportPreview.js:26
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:28
msgid "Report preview"
msgstr "Podgląd newslettera"

#: src/helpers/modal/withModalReportProblem.tsx:45
#: src/helpers/modal/withModalReportArticle.tsx:70
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:104
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:26
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:289
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:295
#: src/components/medialist/forms/FormEditAuthor.js:381
#: src/components/medialist/forms/FormEditAuthor.js:387
#: src/components/medialist/forms/FormEditAuthor.js:528
#: src/components/medialist/forms/FormEditAuthor.js:534
msgid "Report problem"
msgstr "Zgłoś problem"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:76
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:51
msgid "Report will be removed."
msgstr "Raport zostanie usunięty."

#: src/pages/reports/index.js:15
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:118
#: src/components/reports/ReportChangelog.js:18
#: src/components/reports/history/Content.js:31
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:45
#: src/app/components/monitoring-navigation.tsx:165
msgid "Reports"
msgstr "Newslettery"

#: src/components/layout/AuthWrapper/constants/features.slides.js:353
msgid "Reports and exports"
msgstr "Raporty i eksporty"

#: src/pages/reports/history.js:12
#: src/components/reports/history/Content.js:35
#: src/components/reports/Content/ReportsList/ReportsList.js:37
#: src/app/components/monitoring-navigation.tsx:179
msgid "Reports History"
msgstr "Historia wysyłek"

#. js-lingui-explicit-id
#: src/helpers/modal/withModalRequestFeature.tsx:24
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:57
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:244
msgid "featureRequest.Request"
msgstr "Wypróbuj"

#: src/helpers/modal/withModalRequestFeature.tsx:50
#: src/components/misc/MntrButton/modules/ButtonRequestFeature.js:36
msgid "Request Access?"
msgstr "Poprosić o dostęp?"

#: src/helpers/modal/withModalTvrTopics.tsx:41
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:509
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:510
msgid "Request change"
msgstr "Poproś o zmianę"

#: src/components/notifications/ContentTvrRequest.js:32
#: src/components/notifications/ContentTvr.js:120
msgid "Request Channels"
msgstr "Poproś o kanały"

#: src/components/analytics/AnalyticsContent.js:250
msgid "Request social media?"
msgstr "Poprosić o uruchomienie social media?"

#: src/components/analytics/AnalyticsContent.js:217
msgid "Request traditional media?"
msgstr "Poprosić o uruchomienie tradycyjnych mediów?"

#: src/helpers/store/apiClient.js:153
msgid "Request was cancelled."
msgstr "Żądanie zostało anulowane."

#. js-lingui-explicit-id
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:104
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:78
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:49
#: src/components/misc/PromoBox/PromoBox.js:142
#: src/components/misc/MntrButton/modules/ButtonRequestFeature.js:25
#: src/components/analytics/AnalyticsContent.js:199
#: src/components/analytics/AnalyticsContent.js:232
msgid "featureRequest.Requested"
msgstr "Zażądane"

#: src/components/staff/admin/DailyAccess/Table.js:33
msgid "Requests"
msgstr "Zapytania"

#: src/components/reports/history/HistoryTable.js:65
#: src/components/reports/history/HistoryTable.js:446
msgid "Resend"
msgstr "Wyślij ponownie"

#: src/components/reports/history/Compose.js:42
msgid "Resend email report"
msgstr "Wyślij ponownie raport mailowy"

#: src/components/reports/history/Compose.js:44
msgid "Resend email report from: {formattedCreated}"
msgstr "Wyślij ponownie raport z: {formattedCreated}"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:102
msgid "Resend verification email"
msgstr "Wyślij ponownie mail weryfikacyjny"

#: src/store/models/reports/history/History.js:92
msgid "Resending email report. Check back later."
msgstr "Ponownie wysyłam raport mailowy. Sprawdź później."

#: src/components/reports/history/HistoryTable.js:215
msgid "Resent report"
msgstr "Ponów wysyłkę raportu"

#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:33
#: src/components/medialist/forms/FormEditAuthor.js:591
msgid "Reset"
msgstr "Zresetuj"

#: src/components/medialist/forms/FormEditAuthor.js:295
#: src/components/medialist/forms/FormEditAuthor.js:459
msgid "Reset author profile"
msgstr "Zresetuj profil autora"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:153
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:72
msgid "Reset filter"
msgstr "Zresetuj filtr"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:61
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:241
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:258
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:277
msgid "Reset filters"
msgstr "Zresetuj filtry"

#: src/pages/user/reset-password/index.tsx:36
msgid "Reset Password"
msgstr "Zresetuj hasło"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:69
msgid "Reset selection"
msgstr "Zresetuj wybór"

#: src/helpers/modal/withModalResetAuthor.tsx:25
#: src/helpers/modal/withModalResetAuthor.tsx:39
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:87
msgid "Restore"
msgstr "Przywróć"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:403
msgid "Restore articles"
msgstr "Przywróć materiały"

#: src/components/settings/SettingsTheme/ThemePicker.tsx:122
msgid "Restore default"
msgstr "Przywróć domyślne"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:131
#: src/components/misc/Changelog/ChangelogTable.js:40
msgid "Revert"
msgstr "Przywróć"

#: src/components/misc/Changelog/ChangelogTableRow.js:192
msgid "Revert actions"
msgstr "Cofnij działania"

#: src/components/misc/Changelog/ChangelogTableRow.js:209
msgid "Revert now"
msgstr "Przywróć teraz"

#: src/components/misc/Changelog/ChangelogTableRow.js:163
msgid "Reverted on:"
msgstr "Przywrócono dnia:"

#: src/components/newsroom/components/AiTools/AiGenerateCommunicationPlan.tsx:34
msgid "Review communication plan"
msgstr "Przejrzyj plan komunikacyjny"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:204
msgid "Roadmap"
msgstr "Mapa drogowa"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:65
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:230
msgid "Role"
msgstr "Rola"

#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:59
msgid "Row"
msgstr "Wiersz"

#: src/components/medialist/forms/FormEditAuthor.js:862
#: src/components/medialist/forms/FormEditAuthor.js:1038
msgid "Salutation"
msgstr "Powitanie"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:142
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:229
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:103
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:44
#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:116
#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:215
#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:101
#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:70
#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:122
#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:47
#: src/components/staff/admin/workspace/Workspace.js:234
#: src/components/staff/admin/workspace/Workspace.js:951
#: src/components/staff/admin/user/User.js:176
#: src/components/staff/admin/user/User.js:329
#: src/components/settings/SettingsTheme/ThemePicker.tsx:139
#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:103
#: src/components/settings/SettingsLogo/SettingsLogo.js:164
#: src/components/settings/SettingsApplication/SettingsApplication.js:50
#: src/components/reports/Content/ReportsList/ReportsForm.js:342
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:36
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:150
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:224
#: src/components/newsroom/content/dashboard/NewsroomBlogSettings.js:21
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:591
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:73
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:82
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:21
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:467
#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:86
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:242
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:116
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:191
#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:35
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:170
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:21
#: src/components/misc/Capture/Capture.js:238
#: src/components/medialist/forms/FormEditAuthor.js:601
#: src/components/medialist/forms/FormEditAuthor.js:741
#: src/components/layout/Sidebar/modules/SidebarTopics/FormFolder.tsx:25
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:55
#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:91
#: src/components/forms/tags/FormEditTag/FormEditTag.js:48
#: src/components/emailing/forms/FormSenderSettings.js:193
#: src/components/emailing/content/CreateEmailContent.js:321
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:58
#: src/components/dashboards/DashboardSelector/FormEditDashboard.js:35
msgid "Save"
msgstr "Zapisz"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:147
msgid "Save & Publish"
msgstr "Zapisz i opublikuj"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:151
msgid "Save & Schedule"
msgstr "Zapisz i zaplanuj"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:188
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:61
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:61
msgid "Save as"
msgstr "Zapisz jako"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/ModalAddDashboardFooter.tsx:35
msgid "Save changes"
msgstr "Zapisz zmiany"

#: src/components/misc/Capture/Capture.js:245
#: src/components/OurChart/OurChartAdvanced.js:187
msgid "Save in format"
msgstr "Zapisz w formacie"

#: src/helpers/modal/withModalEmailPreview.js:94
msgid "Save report"
msgstr "Zapisz raport"

#: src/components/topics/Content/TopicsList/FormSaveMegalist/FormSaveMegalist.js:20
msgid "Save selection"
msgstr "Zapisz wybór"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:142
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:51
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:51
msgid "Save settings"
msgstr "Zapisz listę"

#: src/constants/analytics/primeScoreCharts.ts:75
msgid "Scatter"
msgstr "Scatter"

#: src/components/misc/Changelog/ChangelogTableRow.js:212
msgid "Schedule revert"
msgstr "Zaplanuj revert"

#: src/components/newsroom/components/PostsList/PostsList.js:188
msgid "Scheduled"
msgstr "Zaplanowane"

#: src/components/misc/Changelog/ChangelogTableRow.js:172
msgid "Scheduled on:"
msgstr "Zaplanowane na:"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:99
msgid "Scheduled to send at {scheduledDateFormatted}, are you sure you want to delete this email?"
msgstr "Wysyłka zaplanowana na {scheduledDateFormatted}, na pewno chcesz usunąć ten mail?"

#: src/components/emailing/content/CreateEmailContent.js:150
msgid "Scheduled to send email"
msgstr "Zaplanowane wysłanie maila"

#: src/components/misc/ActionsBar/View/ViewMenu.js:247
msgid "Scope of mention"
msgstr "Zakres wzmianki"

#: src/components/tvr/Inspector/InspectorMedia/PaginationMedia/PaginationMedia.js:46
msgid "Screens"
msgstr "Ekrany"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:33
msgid "Screenshot"
msgstr "Zrzut ekranu"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:106
#: src/components/tariff/MonitoredMedia/MonitoredMedia.js:174
#: src/components/monitoring/Inspector/InspectorMonitora/HashTagsList/HashTagsList.js:55
#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:59
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Keywords.js:38
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:139
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/MedialistArticlesFilterSearchQuery.js:55
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/MedialistArticlesFilterSearchQuery.js:84
#: src/components/layout/MntrFiltersBar/forms/FormNote/FormNote.js:33
#: src/components/layout/Header/SearchSuggest/SearchSuggest.js:51
#: src/components/forms/dashboard/Search/SearchForm.js:71
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery.js:56
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery.js:85
msgid "Search"
msgstr "Szukaj"

#: src/components/forms/dashboard/Search/SearchForm.js:75
msgid "Search authors"
msgstr "Szukaj autorów"

#: src/pages/authors/index.js:53
msgid "Search authors by <0>many filters</0>"
msgstr "Wyszukaj autorów za pomocą <0>wielu filtrów</0>"

#: src/components/forms/dashboard/Search/SearchForm.js:79
msgid "Search changelog"
msgstr "Szukaj w historii zmian"

#: src/components/forms/dashboard/Search/SearchForm.js:43
#: src/components/forms/dashboard/Search/SearchAdmin.js:39
msgid "Search customers"
msgstr "Szukaj wśród klientów"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:360
msgid "Search engine metadata"
msgstr "Metadane dla wyszukiwarek"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:58
#: src/components/help/search/Content/RulesPhrase.tsx:16
msgid "Search for phrases"
msgstr "Wyszukiwanie fraz"

#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:28
#: src/components/layout/Header/HeaderWithObserver.tsx:201
#: src/components/layout/Header/HeaderWithObserver.tsx:240
#: src/app/(authorized)/help/search/page.tsx:17
msgid "Search help"
msgstr "Pomoc w wyszukiwaniu"

#: src/components/layout/Header/SearchSuggest/SearchSuggest.js:70
msgid "Search History"
msgstr "Historia wyszukiwania"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:62
msgid "Search in"
msgstr "Szukaj w"

#: src/components/forms/dashboard/Search/SearchForm.js:52
msgid "Search in {topicName}"
msgstr "Szukaj w {topicName}"

#: src/components/forms/dashboard/Search/SearchForm.js:33
msgid "Search in archive"
msgstr "Szukaj w archiwum"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:44
msgid "Search in author"
msgstr "Szukaj w autorze"

#: src/components/forms/dashboard/Search/SearchForm.js:63
msgid "Search in Emailing"
msgstr "Szukaj w Mailingu"

#: src/components/forms/dashboard/Search/SearchForm.js:59
msgid "Search in Newsroom"
msgstr "Szukaj w Newsroomie"

#: src/components/layout/MntrFiltersBar/forms/FormNote/FormNote.js:31
msgid "Search in Notes"
msgstr "Szukaj w notatkach"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:102
#: src/components/forms/dashboard/Search/SearchForm.js:54
msgid "Search in topic"
msgstr "Szukaj w temacie"

#: src/components/forms/dashboard/Search/SearchForm.js:34
#: src/components/forms/dashboard/Search/SearchForm.js:67
#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:36
msgid "Search in topics"
msgstr "Szukaj w tematach"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTitle.js:90
msgid "Search job position"
msgstr "Szukaj stanowiska pracy"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:112
#: src/components/topics/Content/TopicsList/Keyword/KeywordExtraQuery.js:37
#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:47
msgid "Search keywords in conjunction with the phrase"
msgstr "Szukaj słów kluczowych w połączeniu z frazą"

#: src/components/staff/admin/workspace/Workspace.js:172
#: src/components/staff/admin/user/User.js:100
msgid "Search log"
msgstr "Dziennik wyszukiwania"

#: src/components/medialist/forms/FormEditAuthor.js:371
#: src/components/medialist/forms/FormEditAuthor.js:514
#: src/components/medialist/content/FeedMedialist/FeedMedialistItem.js:181
msgid "Search on Google"
msgstr "Szukaj na Google"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:126
#: src/components/help/search/Content/RulesOperators.tsx:16
msgid "Search operators"
msgstr "Operatory wyszukiwania"

#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:41
msgid "Search query"
msgstr "Zapytanie wyszukiwania"

#: src/store/models/Megalist/MegalistFilter.js:46
msgid "Search Results"
msgstr "Wyniki wyszukiwania"

#: src/components/layout/MntrFiltersBar/forms/FormSearchSources/FormSearchSources.js:33
msgid "Search source or publisher"
msgstr "Szukaj źródła lub wydawcy"

#. js-lingui-explicit-id
#: src/components/topics/Content/TopicsList/MegalistSearch.js:42
msgid "megalist.search"
msgstr "Wyszukaj źródło lub wydawnictwo"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:122
msgid "Search users"
msgstr "Wyszukaj użytkowników"

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:25
msgid "Second Step"
msgstr "Drugi krok"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:311
msgid "Section"
msgstr "Sekcja"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:55
#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:79
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:30
#: src/components/misc/ActionsBar/Selector/Selector.js:29
#: src/components/misc/ActionsBar/Selector/Selector.js:70
msgid "Select"
msgstr "Wybierz"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:106
msgid "Select a method"
msgstr "Wybierz metodę"

#: src/helpers/modal/withModalAddArticle/ArticleBoxesPreview.tsx:31
msgid "Select a preview card for the newsroom article to include in the email"
msgstr "Wybierz kartę podglądu artykułu z newsroomu, która zostanie dołączona do e-maila"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:33
#: src/components/layout/MntrFiltersBar/modules/MenuFilterToggleAllButtons.js:59
msgid "Select all"
msgstr "Zaznacz wszystko"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:120
msgid "Select article"
msgstr "Wybierz artykuł"

#: src/components/misc/portable/PortableResend/PortableResend.js:118
#: src/components/misc/portable/PortableExport/PortableExport.js:113
msgid "Select articles to export."
msgstr "Wybierz materiały do eksportu."

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:65
msgid "Select at least one mediatype"
msgstr "Wybierz przynajmniej jeden typ medium"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterContent.tsx:67
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:52
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewSocialEngagement/PreviewSocialEngagement.js:32
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:51
#: src/components/analytics/AnalyticsContent.js:106
msgid "Select at least one topic"
msgstr "Wybierz przynajmniej jeden temat"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:73
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:85
msgid "Select campaign"
msgstr "Wybierz kampanię"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:41
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:48
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:54
msgid "Select category"
msgstr "Wybierz kategorię"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:350
#: src/components/misc/ColorPicker/ColorPickerSelector.js:100
#: src/components/misc/ColorPicker/ColorPicker.js:61
#: src/components/misc/ColorPicker/ColorPicker.js:67
msgid "Select color"
msgstr "Wybierz kolor"

#: src/components/newsroom/forms/FormNewsroomPost/CoverImageUpload.js:85
msgid "Select Cover Image"
msgstr "Wybierz obraz okładki"

#: src/components/topics/Content/TopicsList/KeywordExtraQueryAdvanced/KeywordExtraQueryAdvanced.tsx:25
msgid "Select from our list of predefined queries"
msgstr "Wybierz z naszej listy predefiniowanych zapytań"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:130
#: src/components/settings/SettingsLogo/SettingsLogo.js:129
#: src/components/forms/adapters/MntrFileAdapter/MntrFileAdapter.js:58
msgid "Select Image"
msgstr "Wybierz obraz"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:320
msgid "Select Logo"
msgstr "Wybierz logo"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:108
msgid "Select newsroom"
msgstr "Wybierz redakcję"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:75
msgid "Select pages"
msgstr "Wybierz strony"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:96
msgid "Select sources"
msgstr "Wybierz źródła"

#: src/components/emailing/forms/FormSenderSettings.js:116
msgid "Select the encryption method used by your SMTP server."
msgstr "Wybierz metodę szyfrowania używaną przez Twój serwer SMTP."

#: src/components/newsroom/components/AiTools/AiGenerateTitles.tsx:45
msgid "Select the title"
msgstr "Wybierz tytuł"

#: src/components/widgets/modules/stats/WidgetStats.js:79
#: src/components/widgets/modules/socialEngagement/WidgetSocialEngagement.js:41
#: src/components/widgets/modules/analytics/WidgetAnalytics.js:52
#: src/components/monitoring/Monitoring.js:166
msgid "Select Topic"
msgstr "Wybierz temat"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:85
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormSubtype/FormSubtype.js:81
msgid "Select type"
msgstr "Wybierz typ"

#: src/components/emailing/forms/FormSenderSettings.js:207
msgid "Select verification method"
msgstr "Wybierz metodę weryfikacji"

#: src/helpers/charts/makeGranularityMenu.js:6
msgid "Select view"
msgstr "Wybierz widok"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:106
msgid "Select workspace"
msgstr "Wybierz obszar roboczy"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:79
msgid "Selected"
msgstr "Wybrane"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:122
msgid "Selected articles will be removed."
msgstr "Wybrane materiały zostaną usunięte."

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:96
msgid "Selected merge tags can not be applied to the author"
msgstr "Wybrane tagi scalania nie mogą być zastosowane do autora"

#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:188
msgid "Selected sources"
msgstr "Wybrane źródła"

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:69
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:133
msgid "Selected: {selectedLength}/{MAX_SELECTED_LIMIT}"
msgstr "Wybrane: {selectedLength} z {MAX_SELECTED_LIMIT}"

#: src/store/models/Megalist/Megalist.js:376
msgid "Selection \"{name}\" was removed."
msgstr "Wybór \"{name}\" został usunięty."

#: src/store/models/Megalist/Megalist.js:335
#: src/store/models/Megalist/Megalist.js:354
msgid "Selection saved as \"{name}\"."
msgstr "Wybór został zapisany jako \"{name}\"."

#: src/components/reports/history/Compose.js:84
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:198
#: src/components/forms/dashboard/ExportResend/ExportResend.js:179
#: src/components/exportList/Content/Content.tsx:86
#: src/components/emailing/content/CreateEmailContent.js:325
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:122
msgid "Send"
msgstr "Wyślij"

#: src/components/misc/portable/PortableResend/PortableResend.js:70
#: src/components/misc/portable/PortableResend/PortableResend.js:110
msgid "Send all articles to email"
msgstr "Wyślij wszystkie materiały na mail"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:132
msgid "Send article"
msgstr "Wyślij materiał"

#: src/components/misc/portable/PortableResend/PortableResend.js:66
#: src/components/misc/portable/PortableResend/PortableResend.js:106
msgid "Send article to email"
msgstr "Wyślij materiał na mail"

#: src/components/misc/portable/PortableResend/PortableResend.js:68
#: src/components/misc/portable/PortableResend/PortableResend.js:108
msgid "Send articles to email"
msgstr "Wyślij materiały na mail"

#: src/components/reports/Content/ReportsList/ReportsForm.js:121
msgid "Send empty reports"
msgstr "Wysyłaj również puste newslettery"

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:76
msgid "Send Feedback"
msgstr "Wyślij opinię"

#: src/components/reports/Content/ReportsList/ReportsForm.js:270
msgid "Send in times (optional)"
msgstr "Wysyłaj w określonych godzinach (opcjonalnie)"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:150
msgid "Send now"
msgstr "Wyślij teraz"

#: src/components/reports/Content/ReportsList/ReportsForm.js:131
#: src/components/reports/Content/ReportsList/ReportsForm.js:218
msgid "Send on days"
msgstr "Dni wysyłania"

#: src/components/reports/Content/ReportsList/ReportsForm.js:155
msgid "Send on holidays"
msgstr "Wysyłać również w święta"

#: src/components/reports/Content/ReportsList/ReportsForm.js:166
msgid "Send on times"
msgstr "Czasy wysyłania"

#: src/components/emailing/content/promo/PromoEmailing.js:28
msgid "Send press releases to journalists with one click."
msgstr "Wysyłanie komunikatów prasowych do dziennikarzy jednym kliknięciem."

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:151
msgid "Send this email immediately"
msgstr "Wyślij ten mail natychmiast"

#: src/components/reports/history/RecipientsTableHeader.js:41
msgid "Send this to your IT specialist"
msgstr "Prześlij tę wiadomość do swojego specjalisty IT"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:58
#: src/components/emailing/content/EmailDetailEmailContent.js:17
#: src/components/emailing/content/CreateEmailContent.js:418
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:135
msgid "Sender"
msgstr "Nadawca"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:186
msgid "Senders"
msgstr "Nadawcy"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:58
msgid "Sending a generic email without an attached article provides no useful data for tracking"
msgstr "Wysłanie ogólnego e-maila bez załączonego artykułu nie dostarcza żadnych użytecznych danych do śledzenia"

#: src/components/reports/history/HistoryTable.js:223
msgid "Sent automatically via scheduled report"
msgstr "Wysłany automatycznie"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:69
msgid "Sent to"
msgstr "Wysłano do"

#: src/constants/analytics.js:812
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:101
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Sentiment.js:30
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:349
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:61
#: src/components/OurChart/OurChartAdvanced.js:108
msgid "Sentiment"
msgstr "Sentyment"

#: src/components/emailing/forms/FormEmailRecipients.js:107
msgid "Separate emails with a space, comma, or semicolon"
msgstr "Oddziel maile spacją, przecinkiem lub średnikiem"

#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:50
msgid "Separate regional duplicates"
msgstr "Nie łącz regionalnych duplikatów"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:57
#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:60
msgid "Separated by space, newline, comma, or semicolon."
msgstr "Oddzielone spacją, nową linią, przecinkiem lub średnikiem."

#: src/components/newsroom/content/dashboard/ChartVisits.js:110
msgid "Sessions"
msgstr "Sesje"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:117
msgid "Set annotation"
msgstr "Ustaw adnotację"

#: src/components/layout/AuthWrapper/constants/features.slides.js:354
msgid "Set any number of reports that will be sent to any number of contacts. Everyone receives the correct info at the right time and in the format you choose."
msgstr "Ustaw dowolną ilość raportów, które zostaną wysłane do dowolnej liczby kontaktów. Każdy otrzyma poprawne informacje we właściwym czasie i w formacie, który wybierzesz."

#: src/components/emailing/content/CreateEmailContent.js:372
msgid "Set as Draft"
msgstr "Ustaw jako Szkic"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:93
msgid "Set as primary"
msgstr "Ustaw jako główny"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:163
msgid "Set automatic publishing of this post"
msgstr "Ustaw automatyczną publikację tego posta"

#: src/components/staff/admin/workspace/UsersTable/UpdatePermissions.tsx:36
#: src/components/staff/admin/workspace/UsersTable/UpdatePermissions.tsx:40
msgid "Set permissions"
msgstr "Ustaw uprawnienia"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:96
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:162
msgid "Set publish date"
msgstr "Ustaw datę publikacji"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:123
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:158
msgid "Set send date"
msgstr "Ustaw datę wysłania"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:229
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:233
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:339
msgid "Set sentiment"
msgstr "Ustaw sentyment"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:159
msgid "Set this email to auto-send"
msgstr "Ustaw automatyczne wysyłanie tego e-maila"

#: src/pages/user/settings.js:19
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:387
#: src/components/newsroom/content/posts/NewsroomPosts.js:293
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:57
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:22
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:48
#: src/components/layout/Header/UserMenu/UserMenu.tsx:176
#: src/components/emailing/sidebar/EmailingSidebarDashboard.js:27
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:76
#: src/components/emailing/forms/FormSenderSettings.js:252
#: src/components/emailing/forms/FormSenderSettings.js:283
#: src/components/emailing/content/EmailingSettingsContent.js:54
#: src/components/emailing/content/EmailingCampaignsContent.tsx:40
msgid "Settings"
msgstr "Ustawienia"

#. placeholder {0}: model.name
#: src/store/models/ResendSettings.ts:38
#: src/store/models/ExportSettings.js:26
msgid "Settings \"{0}\" was applied."
msgstr "Zastosowano ustawienia \"{0}\"."

#. placeholder {0}: exportSettings.name
#: src/store/models/ResendSettings.ts:114
#: src/store/models/ExportSettings.js:80
msgid "Settings \"{0}\" was removed."
msgstr "Ustawienia \"{0}\" zostały usunięte."

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:21
msgid "Settings complete"
msgstr "Ustawienia zakończone"

#. placeholder {0}: model.name
#. placeholder {0}: exportSettings.name
#: src/store/models/ResendSettings.ts:93
#: src/store/models/ResendSettings.ts:132
#: src/store/models/ExportSettings.js:62
#: src/store/models/ExportSettings.js:96
msgid "Settings saved as \"{0}\"."
msgstr "Ustawienia zostały zapisane jako \"{0}\"."

#: src/store/models/topics/TopicsStore.js:229
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:197
msgid "Settings saved."
msgstr "Ustawienia zapisane."

#: src/components/dashboards/Content.js:75
#: src/components/dashboards/Content.js:76
#: src/components/dashboards/DashboardSelector/DashboardSelector.js:69
msgid "Share"
msgstr "Udostępnij"

#: src/constants/analytics.js:698
#: src/constants/analytics.js:979
#: src/constants/analytics.js:993
#: src/constants/analytics.js:1009
#: src/constants/analytics.js:1024
#: src/constants/analytics.js:1039
msgid "Share of voice"
msgstr "Share of Voice (SoV)"

#: src/store/models/dashboards/Dashboards.js:523
msgid "Shared link will expire"
msgstr "Link do udostępnienia wygaśnie"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:107
#: src/components/medialist/content/MedialistDashboard.js:135
msgid "Show"
msgstr "Pokaż"

#. placeholder {0}: format.formatAttachedArticles(item.mentioned_article_count)
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:191
msgid "Show {0}"
msgstr "Pokaż {0}"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:14
#: src/components/layout/Header/AppNotifications/AppNotifications.js:186
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:95
#: src/components/OurChart/OurChartAdvanced.js:116
msgid "Show All"
msgstr "Pokaż wszystko"

#. placeholder {0}: filteredRecipients.length - displayLimit
#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:130
msgid "Show all recipients (+{0})"
msgstr "Pokaż wszystkich odbiorców (+{0})"

#: src/components/medialist/forms/FormEditAuthor.js:772
#: src/components/medialist/forms/FormEditAuthor.js:800
msgid "Show and copy to clipboard"
msgstr "Pokaż i skopiuj do schowka"

#: src/components/reports/history/HistoryTable.js:397
#: src/components/exportList/History/HistoryTable/HistoryTable.js:86
msgid "Show articles"
msgstr "Pokaż materiały"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:123
msgid "Show articles in feed"
msgstr "Pokaż materiały w kanałach"

#: src/components/medialist/forms/FormEditAuthor.js:343
#: src/components/medialist/forms/FormEditAuthor.js:351
#: src/components/medialist/forms/FormEditAuthor.js:424
#: src/components/medialist/forms/FormEditAuthor.js:433
#: src/components/medialist/content/MedialistDashboard.js:99
msgid "Show authors"
msgstr "Pokaż autorów"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:133
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:28
msgid "Show changes"
msgstr "Pokaż zmiany"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:23
msgid "Show checked only"
msgstr "Pokaż tylko zaznaczone"

#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:21
msgid "Show history for this report"
msgstr "Pokaż historię tego raportu"

#: src/components/monitoring/Inspector/DemographicsData/modules/EntriesList/EntriesListContent.js:93
msgid "Show less"
msgstr "Pokaż mniej"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:95
msgid "Show Less"
msgstr "Pokaż mniej"

#: src/components/monitoring/Inspector/DemographicsData/modules/EntriesList/EntriesListContent.js:93
#: src/components/medialist/content/MedialistDashboard.js:162
#: src/components/medialist/content/MedialistDashboard.js:183
msgid "Show more"
msgstr "Pokaż więcej"

#: src/components/medialist/forms/FormEditAuthor.js:356
#: src/components/medialist/forms/FormEditAuthor.js:439
msgid "Show newsrooms"
msgstr "Pokaż redakcje"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:65
msgid "show stats"
msgstr "pokaż statystyki"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:32
msgid "Show unchecked only"
msgstr "Pokaż tylko niezaznaczone"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:52
msgid "showing {counterFrom} out of {counterTo}"
msgstr "pokazano {counterFrom} z {counterTo}"

#: src/pages/sign-up.tsx:11
#: src/pages/sign-up-completion.tsx:30
#: src/pages/staff/sign-up.js:11
#: src/pages/staff/sign-up-completion.js:26
#: src/components/staff/SignUp.js:30
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:174
#: src/components/page/auth/SignUp/SignUp.js:74
msgid "Sign Up"
msgstr "Zarejestruj się"

#: src/components/emailing/content/Signature.tsx:104
msgid "Signature"
msgstr "Podpis"

#: src/components/layout/MntrActiveFilters/modules/SimilarArticle.js:12
msgid "Similar to"
msgstr "Podobne do"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:62
msgid "Similarity"
msgstr "Podobieństwo"

#: src/components/emailing/content/promo/PromoEmailing.js:23
msgid "Simple setting of the appearance of the email template."
msgstr "Proste ustawienie wyglądu szablonu maila."

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:124
msgid "Skip"
msgstr "Pomiń"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:60
msgid "Skip error lines"
msgstr "Pomiń błędne linie"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:249
msgid "SMS alerts"
msgstr "Powiadomienia SMS"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:134
msgid "SMTP settings are invalid."
msgstr "Ustawienia SMTP są nieprawidłowe."

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:132
msgid "SMTP settings are valid."
msgstr "Ustawienia SMTP są prawidłowe."

#: src/components/misc/ActionsBar/View/ViewMenu.js:269
#: src/components/misc/ActionsBar/View/ViewMenu.js:353
msgid "Social data"
msgstr "Dane social media"

#: src/store/models/dashboards/DashboardPreview.js:110
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:51
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:51
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:32
msgid "Social Engagement"
msgstr "Zaangażowanie w social media"

#: src/constants/analytics.js:198
#: src/constants/analytics.js:289
#: src/constants/analytics.js:398
#: src/constants/analytics.js:1040
#: src/components/misc/ActionsBar/View/ViewMenu.js:93
msgid "Social interactions"
msgstr "Interakcje w social media"

#: src/constants/stats.ts:31
#: src/components/widgets/modules/stats/WidgetStats.js:143
msgid "Social Interactions"
msgstr "Interakcje w social media"

#: src/constants/analytics.js:309
#: src/constants/analytics.js:548
msgid "Social interactions by mention type"
msgstr "Interakcje w social media według typu wzmianki"

#: src/constants/analytics.js:307
msgid "Social interactions by sentiment"
msgstr "Interakcje w social media według sentymentu"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:179
#: src/components/staff/admin/workspace/Workspace.js:534
#: src/components/medialist/forms/FormEditAuthor.js:819
#: src/components/medialist/forms/FormEditAuthor.js:922
#: src/components/layout/AuthWrapper/constants/features.slides.js:87
#: src/components/exportList/ExportLimit/ExportLimit.js:28
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:29
#: src/components/analytics/AnalyticsContent.js:149
#: src/components/analytics/AnalyticsContent.js:228
msgid "Social Media"
msgstr "Social Media"

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:37
msgid "Social Media in {appName}"
msgstr "Social Media w aplikacji {appName}"

#: src/components/tariff/TariffLimits/TariffLimits.js:186
msgid "Social media topics limit"
msgstr "Limit tematów (soc. media)"

#: src/components/staff/admin/workspace/Workspace.js:578
msgid "Social media topics limit (Sentione price = 500 Kč per topic)"
msgstr "Limit liczby tematów (social media) (Koszt Sentione = 500 Kč/temat)"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:147
msgid "Social post"
msgstr "Post na portalu społecznościowym"

#: src/components/medialist/content/AuthorContactInformation.js:38
msgid "Social profiles"
msgstr "Profile społecznościowe"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:56
#: src/components/misc/ActionsBar/View/ViewMenu.js:124
msgid "Sold amount"
msgstr "Sprzedana ilość"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:173
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:85
msgid "Sold amount (print+digital)"
msgstr "Sprzedane (druk+el.)"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:652
msgid "Some articles may not be deleted."
msgstr "Niektóre materiały mogły nie zostać usunięte."

#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:155
msgid "Some data are missing in the generated content. Add them manually before proceeding."
msgstr "W wygenerowanej treści brakuje niektórych danych. Dodaj je ręcznie przed kontynuowaniem."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:23
msgid "Some recipients are missing information for merge tags or email. Please add the missing information or replace the recipients by clicking on them."
msgstr "Niektórym odbiorcom brakuje informacji do znaczników scalania lub e-maila. Proszę dodać brakujące informacje lub zastąpić odbiorców, klikając na nich."

#: src/helpers/store/apiClient.js:249
msgid "Something failed while preparing a server request. Our team was notified."
msgstr "Podczas przygotowywania zapytania do serwera wystąpił błąd. Nasz zespół został powiadomiony."

#: src/pages/_error.js:44
msgid "Something's gone wrong"
msgstr "Coś poszło nie tak"

#: src/components/misc/ActionsBar/Sort/SortExport.js:19
#: src/components/misc/ActionsBar/Sort/Sort.js:21
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:468
#: src/components/layout/MntrFiltersBar/modules/MenuFilterOrderBy.js:23
msgid "Sort"
msgstr "Sortuj"

#: src/components/misc/ActionsBar/Sort/SortExport.js:25
#: src/components/misc/ActionsBar/Sort/Sort.js:38
msgid "Sort List"
msgstr "Sortuj listę"

#: src/components/tariff/AgencyMedia/AgencyMedia.js:64
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:371
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:205
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:226
#: src/components/layout/MntrActiveFilters/modules/NewsSource.js:13
#: src/components/exportList/History/HistoryTable/HistoryTable.js:54
msgid "Source"
msgstr "Źródło"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:326
msgid "Source <0>{newsSourceName}</0> will be removed from the topic <1>{topicMonitorName}</1>."
msgstr "Źródło <0>{newsSourceName}</0> zostanie usunięte z tematu <1>{topicMonitorName}</1>."

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:37
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:456
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:510
msgid "Source File"
msgstr "Plik źródłowy"

#: src/store/models/monitoring/Inspector/Inspector.ts:532
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:885
msgid "Source removed"
msgstr "Źródło usunięte"

#: src/components/staff/admin/workspace/Workspace.js:892
msgid "Sources"
msgstr "Źródła"

#: src/components/staff/admin/workspace/Workspace.js:894
#: src/components/staff/admin/workspace/Workspace.js:901
msgid "Sources per client"
msgstr "Źródła na klienta"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:233
msgid "Special tag"
msgstr "Specjalna etykieta"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:47
msgid "Specify the primary goal (inform, persuade, invite, etc.)."
msgstr "Określ główny cel (informować, przekonywać, zapraszać itp.)."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:137
msgid "Spokesperson"
msgstr "Rzecznik prasowy"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:57
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:194
msgid "Start editing"
msgstr "Rozpocznij edycję"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:75
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:213
msgid "Start over"
msgstr "Zacznij od nowa"

#: src/components/forms/inspector/FormMediaEditor.js:82
msgid "Start time must be lower than end time"
msgstr "Czas rozpoczęcia musi być wcześniejszy niż czas zakończenia"

#: src/components/emailing/content/CreateEmailContent.js:584
msgid "Start typing or click + to add more content"
msgstr "Zacznij pisać lub kliknij +, aby dodać więcej treści"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:681
msgid "Start typing or insert image, video…"
msgstr "Zacznij pisać lub wstaw obraz, wideo…"

#: src/components/newsroom/content/posts/ChooseTemplates.tsx:76
msgid "Start with template"
msgstr "Zacznij od szablonu"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:119
#: src/components/staff/admin/user/WorkspacesTable.js:77
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:80
#: src/components/staff/admin/customer/users/UsersTable.js:71
msgid "State"
msgstr "Stan"

#: src/store/models/dashboards/DashboardPreview.js:121
#: src/components/emailing/content/promo/PromoEmailing.js:32
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:23
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:23
#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:44
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:54
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:26
msgid "Statistics"
msgstr "Statystyki"

#: src/components/reports/history/RecipientsTableHeader.js:33
#: src/components/newsroom/content/posts/NewsroomPosts.js:166
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:27
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:407
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNewsroomStatus.js:24
#: src/components/layout/MntrActiveFilters/modules/NewsroomStatus.js:21
msgid "Status"
msgstr "Status"

#: src/components/reports/history/HistoryTable.js:80
#: src/components/reports/history/HistoryTable.js:104
#: src/components/reports/history/HistoryTable.js:322
msgid "Status unknown"
msgstr "Status nieznany"

#: src/components/layout/AuthWrapper/constants/features.slides.js:260
msgid "Streamline communication efforts and maximize your PR impact."
msgstr "Komunikuj się z mediami łatwo i efektywnie, zwiększając skuteczność swojego PR."

#: src/components/reports/history/HistoryTable.js:149
#: src/components/reports/history/Compose.js:62
#: src/components/forms/dashboard/ExportResend/ExportResend.js:102
#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:92
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:86
#: src/components/emailing/content/EmailDetailEmailContent.js:27
#: src/components/emailing/content/CreateEmailContent.js:446
msgid "Subject"
msgstr "Temat"

#: src/components/misc/MntrForm/MntrForm.tsx:525
msgid "Submit"
msgstr "Zatwierdź"

#: src/constants/stats.ts:36
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:79
#: src/components/forms/dashboard/ExportResend/ExportResend.js:124
msgid "Summary"
msgstr "Podsumowanie"

#: src/components/staff/admin/customer/expenses/DetailExpenseModal.js:35
msgid "Supplier"
msgstr "Dostawca"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:210
msgid "Support"
msgstr "Wsparcie"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:472
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:538
msgid "Supported file types:"
msgstr "Obsługiwane typy plików:"

#. placeholder {0}: item.label
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:245
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:157
msgid "Tag <0>{0}</0> will be hidden."
msgstr "Tag <0>{0}</0> zostanie ukryty."

#. placeholder {0}: item.label
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:315
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:205
msgid "Tag <0>{0}</0> will be removed."
msgstr "Tag <0>{0}</0> zostanie usunięty."

#: src/components/forms/tags/FormNewTag/FormNewTag.js:26
#: src/components/forms/tags/FormEditTag/FormEditTag.js:25
#: src/components/forms/tags/FormEditTag/FormEditTag.js:28
msgid "Tag name"
msgstr "Nazwa tagu"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:761
#: src/store/models/authors/AuthorsStore.js:716
msgid "Tag removed successfully."
msgstr "Tag został pomyślnie usunięty."

#: src/constants/analytics.js:845
#: src/constants/analytics.js:1069
#: src/components/medialist/forms/FormEditAuthor.js:616
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:90
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:79
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:72
#: src/components/emailing/content/tabs/AddRecipients.tsx:70
msgid "Tags"
msgstr "Tagi"

#: src/components/medialist/forms/FormEditAuthor.js:610
msgid "Tags, lists and note"
msgstr "Tagi, listy i notatka"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:41
msgid "Target Audience:"
msgstr "Grupa docelowa:"

#: src/components/settings/SettingsTariff/SettingsTariff.js:22
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:27
msgid "Tariff information"
msgstr "Informacje o taryfie"

#: src/components/reports/Content/ReportsList/ReportsForm.js:317
#: src/components/forms/dashboard/ExportResend/ExportResend.js:107
msgid "Template"
msgstr "Szablon"

#: src/components/emailing/forms/FormSenderSettings.js:167
msgid "Test DNS Settings"
msgstr "Przetestuj ustawienia DNS"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:377
msgid "Text"
msgstr "Tekst"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:207
msgid "Text align"
msgstr "Wyrównanie tekstu"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:281
#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:76
#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:36
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:113
msgid "Text Color"
msgstr "Kolor tekstu"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:155
msgid "Text format"
msgstr "Format tekstu"

#: src/components/page/auth/Expired/Expired.js:60
msgid "Thank you for trying out {appName}."
msgstr "Dziękujemy za wypróbowanie aplikacji {appName}."

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:91
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:113
msgid "Thank you for your feedback!"
msgstr "Dziękujemy za Twoją opinię!"

#: src/components/page/auth/UserInactive/UserInactive.js:17
msgid "Thank you for your interest in using {appName}."
msgstr "Dziękujemy za zainteresowanie aplikacją {appName}."

#: src/pages/user/yoy-analysis.js:67
#: src/pages/user/reactivate-24.js:67
msgid "Thank you for your interest. We will contact you soon.<0/><1/>Have a great day,<2/><3/>{appName} team"
msgstr "Dziękujemy za zainteresowanie. Skontaktujemy się z Tobą wkrótce.<0/><1/>Miłego dnia,<2/><3/>zespół {appName}"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:100
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:131
msgid "Thank you!"
msgstr "Dziękujemy!"

#: src/store/models/monitoring/Inspector/Inspector.ts:910
msgid "The article already belongs to the topic."
msgstr "Materiał już należy do tematu."

#: src/store/models/monitoring/WorkspaceArticles.js:164
msgid "The article has been uploaded and is currently being processed. After that it will be added to your feed. You can see the processing status in My Articles."
msgstr "Materiał został przesłany i jest obecnie przetwarzany. Po tym zostanie dodany do Twojego kanału. Status przetwarzania możesz zobaczyć w sekcji Moje Materiały."

#: src/store/models/monitoring/Inspector/Inspector.ts:907
msgid "The article was added to the topic. Please reload the feed to see the changes."
msgstr "Materiał został dodany do tematu. Proszę odświeżyć kanał, aby zobaczyć zmiany. Jeśli go nie widzisz, sprawdź ustawienia filtrów."

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:392
msgid "The article will be removed from your media coverage view. If the article also exists in your feed, it will remain there and will not be deleted."
msgstr "Artykuł zostanie usunięty z widoku monitoringu mediów. Jeśli artykuł znajduje się również w Twoim kanale, pozostanie tam i nie zostanie usunięty."

#: src/components/medialist/forms/FormEditAuthor.js:299
#: src/components/medialist/forms/FormEditAuthor.js:463
msgid "The author's profile will be reset to its original values."
msgstr "Profil autora zostanie przywrócony do pierwotnych wartości."

#: src/store/models/monitoring/Inspector/MediaEditor/MediaEditorStore.js:55
msgid "The clip is being prepared. It may take a while. When the clip is ready for download, you will receive a notification."
msgstr "Klip jest przygotowywany. To może chwilę potrwać. Gdy klip będzie gotowy do pobrania, otrzymasz powiadomienie."

#: src/components/emailing/forms/FormSenderSettings.js:239
msgid "The DNS Verification Is Unavailable"
msgstr "Weryfikacja za pomocą DNS jest niedostępna"

#: src/components/emailing/forms/FormSenderSettings.js:240
msgid "The DNS verification settings for this email are not accessible. We suggest opting for SMTP (Simple Mail Transfer Protocol) as an alternative way of verification. If you need any additional information or help, our support team is here to assist you."
msgstr "Ustawienia weryfikacji DNS dla tego maila nie są dostępne. Jako alternatywę sugerujemy wybór protokołu SMTP (Simple Mail Transfer Protocol). Jeśli potrzebujesz dodatkowych informacji lub pomocy, nasz zespół wsparcia jest do Twojej dyspozycji."

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:37
msgid "The email content can be automatically adjusted to include personalized details for each recipient"
msgstr "Treść e-maila może być automatycznie dostosowana, aby zawierała spersonalizowane dane dla każdego odbiorcy."

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:113
msgid "The email is currently empty. Please add some content to the email."
msgstr "Mail jest obecnie pusty. Proszę dodać treść."

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:28
msgid "The following summary was generated by a machine and may not accurately represent the original content."
msgstr "Poniższe podsumowanie zostało wygenerowane przez maszynę i może nie odzwierciedlać dokładnie oryginalnej treści."

#: src/store/models/ExportStore.js:251
msgid "The full article text cannot be downloaded as you have reached your limit. To adjust this limit, please contact support."
msgstr "Nie można pobrać pełnej treści artykułu, ponieważ osiągnięto limit. Aby zmienić ten limit, skontaktuj się z pomocą techniczną."

#: src/components/tariff/TariffLimits/TariffLimits.js:31
msgid "The limit applies to the number of articles found in the last 30 days generated by set keywords. If you have reached the limit for the number of found articles, <0>edit keywords</0> or contact us to increase the limit."
msgstr "Limit dotyczy liczby materiałów znalezionych w ciągu ostatnich 30 dni, które wygenerowały ustawione słowa kluczowe w tematach. Jeśli osiągnąłeś limit liczby znalezionych materiałów, <0>edytuj słowa kluczowe</0> lub skontaktuj się z nami, aby zwiększyć limit."

#: src/components/tariff/TariffLimits/TariffLimits.js:68
msgid "The limit applies to the number of exported articles in the last 30 days (topics, archive or report attachments). If you have reached the limit for the number of exported articles, you must wait until the limit is restored or contact us to increase the limit."
msgstr "Limit dotyczy liczby eksportowanych materiałów w ciągu ostatnich 30 dni (w ramach tematów, archiwum lub załączników raportu). Jeśli osiągnąłeś limit liczby eksportowanych materiałów, musisz poczekać, aż limit zostanie przywrócony, w zależności od Twojego poprzedniego eksportu. Możesz też skontaktować się z nami, aby zwiększyć limit."

#: src/components/tariff/TariffLimits/TariffLimits.js:104
msgid "The limit applies to the number of translated articles in the last 30 days (topics, archive, report or report attachments). If you are interested in increasing this limit, please contact us."
msgstr "Limit dotyczy liczby przetłumaczonych materiałów w ciągu ostatnich 30 dni (w ramach tematów, archiwum, raportów lub załączników do raportów). Jeśli jesteś zainteresowany zwiększeniem tego limitu, skontaktuj się z nami."

#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:20
msgid "The list of already exported articles can be downloaded without limitation."
msgstr "Listę już wyeksportowanych materiałów można pobrać bez ograniczeń."

#: src/store/models/authors/Baskets/AuthorBasketDefinitionsStoreArrItem.ts:54
msgid "The list was successfully duplicated."
msgstr "Lista została pomyślnie zduplikowana."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:84
msgid "The main content of your post appears to be empty. The body is where you elaborate on your ideas, present your arguments, or share your story. Please add substantial content to your post to engage your readers and convey your message effectively."
msgstr "Główna treść Twojego posta wydaje się być pusta. Treść to miejsce, w którym rozwijasz swoje idee, przedstawiasz swoje argumenty lub dzielisz się swoją historią. Dodaj do swojego posta znaczną treść, aby zaangażować czytelników i skutecznie przekazać swoją wiadomość."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:20
msgid "help.grp"
msgstr "Celem wpływu medialnego jest lepsze prezentowanie rzeczywistego obrazu monitorowanego podmiotu niż całkowita  liczba materiałów, która dociera do najszerszej grupy słuchaczy, widzów i czytelników. Bazuje przede wszystkim na czytelnictwie (prasa), słuchalności (radio), oglądalności (TV) i miesięcznej liczbie odwiedzin stron internetowych (online). Jednostką pomiaru wpływu medialnego są punkty GRP (Gross Rating Points), przy czym jeden punkt GRP odpowiada jednemu procentowi populacji powyżej piętnastu lat (np. dla grupy 90 000 osób w Czechach, dla grupy 45 000 osób na Słowacji). Chodzi o czytelników, słuchaczy lub widzów, którzy mogli mieć styczność z opublikowanym materiałem. Odbiorca, który mógł przeczytać, obejrzeć lub odsłuchać więcej niż jeden materiał, jest liczony wielokrotnie. OTS (Opportunity to See) wskazuje, ile razy średnio członek grupy docelowej miał możliwość przeczytania lub obejrzenia materiału. W przypadku grupy docelowej wszystkich mieszkańców powyżej piętnastu lat: OTS = GRP / 100."

#: src/pages/authors/index.js:43
msgid "The most <0>extensive</0> and the most <1>actual</1> medialist of journalists, publishers & other authors, in which you will find detailed information including contacts."
msgstr "<0>Najobszerniejsza</0> i <1>najbardziej aktualna</1> lista mediów dziennikarzy, wydawców i innych autorów, w której znajdziesz szczegółowe informacje, w tym kontakty."

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:31
msgid "The primary sender is used as the default sender for emails. You can change this when you create an email."
msgstr "Podstawowy nadawca jest używany jako domyślny nadawca dla maili. Możesz to zmienić podczas tworzenia maila."

#. placeholder {0}: senderItem.unverified_recipients_limit
#. placeholder {1}: senderItem.verified_recipients_limit
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:68
msgid "The recipient limit is set to {0}. For a higher limit of {1} recipients, enable DNS or SMTP verification."
msgstr "Limit odbiorców jest ustawiony na {0}. Aby zwiększyć limit do {1} odbiorców, włącz weryfikację DNS lub SMTP."

#. placeholder {0}: appSettings.appName
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:136
msgid "The summary was created with the {0} application."
msgstr "Podsumowanie zostało stworzone za pomocą aplikacji {0}."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:493
msgid "The user is responsible for the content uploaded to the {appName} application. By uploading files, you confirm that you own the rights to the file or that the file is licensed under <0>CC0</0>."
msgstr "Użytkownik jest odpowiedzialny za treść przesyłaną do aplikacji {appName}. Przesyłając pliki, potwierdzasz, że posiadasz prawa do pliku lub że plik jest licencjonowany na mocy <0>CC0</0>."

#: src/components/layout/Header/UserMenu/UserMenu.tsx:137
msgid "Theme"
msgstr "Motyw"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:81
msgid "There are no keywords assigned to this topic"
msgstr "Do tego tematu nie są przypisane żadne słowa kluczowe"

#: src/pages/404.js:18
#: src/app/not-found-content.tsx:27
msgid "There's nothing here..."
msgstr "Nie ma tu nic..."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:107
msgid "These settings allow not only change language of the newsroom, but to link newsrooms together. Pair them in different languages for quick and seamless transitions."
msgstr "Te ustawienia pozwalają nie tylko zmienić język newsroomu, ale także połączyć newsroomy ze sobą. Sparuj je w różnych językach, aby szybko i bezproblemowo przechodzić między nimi."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:184
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:229
msgid "This field is required"
msgstr "To pole jest wymagane"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:222
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:305
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:418
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:75
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:78
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:83
msgid "This field is required."
msgstr "To pole jest wymagane."

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:380
msgid "This is a summary of the page's content. It appears below the headline on the search results page."
msgstr "To jest streszczenie treści strony. Pojawia się pod nagłówkiem na stronie z wynikami wyszukiwania."

#: src/components/emailing/forms/FormSenderSettings.js:109
msgid "This is the port number that your SMTP server uses to send email. If you're not sure, leave it blank to use the default port."
msgstr "To jest numer portu, którego twój serwer SMTP używa do wysyłania maili. Jeśli nie jesteś pewien, pozostaw go pusty, aby użyć portu domyślnego."

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:134
msgid "This list is empty"
msgstr "Ta lista jest pusta"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:65
msgid "This month"
msgstr "Bieżący miesiąc"

#: src/components/misc/Capture/Capture.js:300
msgid "this should take only a couple of seconds"
msgstr "operacja powinna zająć tylko kilka sekund"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:269
msgid "This template was custom tailored for you. For further customization please contact our <0>support</0>."
msgstr "Ten szablon został stworzony specjalnie dla Ciebie. Aby go dalej dostosować, skontaktuj się z naszym <0>wsparciem</0>."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:52
msgid "This week"
msgstr "Bieżący tydzień"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:78
msgid "This year"
msgstr "Bieżący rok"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:151
msgid "Threshold"
msgstr "Próg (%)"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:192
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:316
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:320
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:70
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:99
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:190
msgid "Time"
msgstr "Czas"

#: src/components/forms/inspector/FormMediaEditor.js:85
#: src/components/forms/inspector/FormMediaEditor.js:88
msgid "Time must not exceed media length"
msgstr "Czas nie może przekroczyć długości medium"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:70
msgid "Timed"
msgstr "Czasowy"

#: src/constants/analytics.js:54
#: src/constants/analytics.js:78
#: src/constants/analytics.js:100
#: src/constants/analytics.js:122
#: src/constants/analytics.js:142
#: src/constants/analytics.js:191
#: src/constants/analytics.js:226
#: src/constants/analytics.js:255
#: src/constants/analytics.js:282
#: src/constants/analytics.js:308
#: src/constants/analytics.js:335
#: src/constants/analytics.js:364
#: src/constants/analytics.js:391
#: src/constants/analytics.js:417
#: src/constants/analytics.js:444
#: src/constants/analytics.js:482
#: src/constants/analytics.js:510
#: src/constants/analytics/primeScoreCharts.ts:30
#: src/constants/analytics/primeScoreCharts.ts:56
msgid "Timeline"
msgstr "Oś czasu"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:51
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:506
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:507
#: src/components/newsroom/content/posts/NewsroomPosts.js:156
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:34
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:218
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormWidgetTitle/FormWidgetTitle.js:17
msgid "Title"
msgstr "Tytuł"

#. js-lingui-explicit-id
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:362
msgid "metadata.title"
msgstr "Tytuł"

#: src/components/reports/Content/ReportsList/ReportsForm.js:289
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:85
msgid "To"
msgstr "Do"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:33
msgid "To change your password, enter your current password and then the new password."
msgstr "Aby zmienić hasło, wprowadź swoje obecne hasło, a następnie nowe hasło."

#: src/components/emailing/forms/FormSenderSettings.js:286
msgid "To ensure the successful delivery of emails from our system, it's necessary to configure your SMTP server with the following details:"
msgstr "Aby zapewnić skuteczne dostarczanie maili z naszego systemu, konieczne jest skonfigurowanie serwera SMTP z następującymi danymi:"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:27
msgid "To hide some tags from the list, uncheck these tags. The user can add hidden tags back to their feed again at any time if necessary."
msgstr "Aby ukryć niektóre tagi z listy, odznacz je. Użytkownik może w dowolnym momencie ponownie dodać ukryte tagi do swojego kanału."

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:100
msgid "To hide some topics from the list, uncheck these topics. The user can add hidden topics back to their feed again at any time if necessary."
msgstr "Aby ukryć niektóre tematy z listy, odznacz je. Użytkownik może w każdej chwili ponownie dodać ukryte tematy do swojego kanału, jeśli będzie to konieczne."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:492
msgid "To set up your own domain (e.g. companyname.com), please contact our team. We will be happy to help you set up your domain. We have also written a detailed guide for you."
msgstr "Aby skonfigurować własną domenę (np. blog.firma.pl), skontaktuj się z naszym zespołem. Chętnie pomożemy Ci skonfigurować domenę."

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:39
msgid "To view all mentions, it is necessary to activate social media monitoring."
msgstr "Aby zobaczyć wszystkie wzmianki, konieczne jest aktywowanie monitoringu social media."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/metaDataDate.js:19
msgid "today"
msgstr "dzisiaj"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:110
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:39
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:100
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:327
msgid "Today"
msgstr "Dzisiaj"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:51
msgid "Tone and Style:"
msgstr "Ton i styl:"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:88
msgid "Tone of voice"
msgstr "Ton głosu artykułu"

#: src/constants/analytics.js:1126
msgid "Top authors"
msgstr "Top autorzy"

#: src/constants/analytics.js:1228
msgid "Top hashtags"
msgstr "Top hashtagi"

#: src/constants/analytics.js:1206
msgid "Top profiles"
msgstr "Top profile"

#: src/constants/analytics.js:1248
msgid "Top publishers"
msgstr "Top wydawcy"

#: src/constants/analytics.js:1268
msgid "Top sources"
msgstr "Top źródła"

#: src/constants/analytics/primeScoreCharts.ts:136
msgid "Top sources by overall PRIMe"
msgstr "Źródła według całkowitego PRIMe"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:200
msgid "Top stories"
msgstr "Top tematy"

#: src/helpers/charts/tableTemplates.js:74
#: src/components/exportList/History/HistoryTable/HistoryTable.js:57
msgid "Topic"
msgstr "Temat"

#. placeholder {0}: item.data.name
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:206
msgid "Topic <0>{0}</0> will be hidden."
msgstr "Temat <0>{0}</0> będzie ukryty."

#. placeholder {0}: item.data.name
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:147
msgid "Topic <0>{0}</0> will be removed."
msgstr "Temat <0>{0}</0> zostanie usunięty."

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:147
msgid "Topic Name"
msgstr "Nazwa tematu"

#: src/pages/topics/index.js:24
#: src/components/topics/Content/TopicChangelog.js:18
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:113
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:253
#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:26
#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:80
#: src/components/notifications/ContentTopics.js:29
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:32
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:33
#: src/components/layout/MntrActiveFilters/modules/TvrTopics.js:10
#: src/components/layout/MntrActiveFilters/modules/EmptyTopics.js:21
#: src/app/components/monitoring-navigation.tsx:154
msgid "Topics"
msgstr "Tematy"

#: src/components/topics/Content/TopicsHeading/TopicsHeading.js:10
msgid "Topics ({counter})"
msgstr "Tematy ({counter})"

#. placeholder {0}: menuItem.topic_monitors .map((item) => { // @ts-expect-error TODO refactor topics to TS return item.label }) .join(', ')
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:233
msgid "Topics <0>{0}</0> will be hidden."
msgstr "Tematy <0>{0}</0> będą ukryte."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:142
msgid "Topics and keywords"
msgstr "Tematy i słowa kluczowe"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:291
msgid "Topics in this folder will be displayed separately and won't be deleted."
msgstr "Tematy w tym folderze nie zostaną usunięte, ale będą wyświetlane oddzielnie."

#: src/components/tariff/TariffLimits/TariffLimits.js:167
#: src/components/staff/admin/workspace/Workspace.js:439
msgid "Topics limit"
msgstr "Limit tematów"

#: src/components/monitoring/Inspector/InspectorMonitora/KeywordsPagination/KeywordsPagination.js:304
msgid "total"
msgstr "łącznie"

#: src/helpers/charts/tableTemplates.js:55
#: src/helpers/charts/tableTemplates.js:97
#: src/helpers/charts/tableTemplates.js:136
#: src/components/widgets/modules/stats/StatsBySource.js:120
#: src/components/tvr/Content/Content.js:92
msgid "Total"
msgstr "Łącznie"

#. placeholder {0}: humanizeNumber(data)
#: src/constants/analytics.js:215
msgid "Total {0} interactions"
msgstr "Łącznie {0} interakcji"

#. placeholder {0}: humanizeNumber(data)
#: src/constants/analytics.js:471
msgid "Total influence score: {0}"
msgstr "Całkowity wynik wpływu: {0}"

#. placeholder {0}: formatter( this.points.reduce((sum, { y }) => sum + y, 0), unit, )
#: src/components/OurChart/HighchartsRenderer.js:645
msgid "Total: {0}"
msgstr "Razem: {0}"

#: src/components/emailing/content/promo/PromoEmailing.js:33
msgid "Track delivery and opening statistics."
msgstr "Śledź statystyki dostawy i otwarcia."

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:20
msgid "Track online, traditional and social media with {appName} for a complete view of your brand and trends - never miss a beat."
msgstr "Śledź media online, tradycyjne i społecznościowe za pomocą {appName}, aby uzyskać pełny obraz swojej marki i trendów - nigdy niczego nie przegap."

#: src/components/layout/AuthWrapper/constants/features.slides.js:166
msgid "Tracking, analysis, and reporting are an integral part of PR. Use comprehensible charts that make data analysis easier. Compare your media output with your competition."
msgstr "Śledzenie, analiza i raportowanie są nieodłączną częścią PR. Korzystaj z zrozumiałych wykresów, które ułatwiają analizę danych. Porównuj swoje wyniki medialne z konkurencją."

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:159
#: src/components/staff/admin/workspace/Workspace.js:353
#: src/components/layout/AuthWrapper/constants/features.slides.js:41
#: src/components/exportList/ExportLimit/ExportLimit.js:17
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:19
#: src/components/analytics/AnalyticsContent.js:146
#: src/components/analytics/AnalyticsContent.js:195
msgid "Traditional Media"
msgstr "Tradycyjne media"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:25
msgid "Traditional Media w/o percentage change"
msgstr "Tradycyjne media (bez zmiany procentowej)"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:532
msgid "Transcribe the source file"
msgstr "Transkrypcja pliku źródłowego"

#: src/components/monitoring/WorkspaceArticles/Limits.js:69
msgid "Transcribed seconds"
msgstr "Transkrypcja sekund"

#: src/components/monitoring/WorkspaceArticles/Limits.js:73
msgid "Transcript"
msgstr "Transkrypcja"

#: src/components/medialist/content/MedialistActionsBar/FormTransformContacts.tsx:41
msgid "Transform"
msgstr "Przekształć"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:53
msgid "Transform & import"
msgstr "Przekształć i importuj"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:137
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:17
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformTitle.tsx:7
msgid "Transform contact list"
msgstr "Przekształć listę kontaktów"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:134
msgid "Transformation failed"
msgstr "Transformacja nie powiodła się"

#: src/helpers/withTranslatePopup/TranslatePopupContent.js:49
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:75
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:77
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:110
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:189
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:194
msgid "Translate"
msgstr "Przetłumacz"

#: src/components/layout/Sidebar/SidebarNavigation.tsx:243
msgid "Translations"
msgstr "Tłumaczenia"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:569
msgid "Transparent background"
msgstr "Przezroczyste tło"

#: src/app/components/monitoring-navigation.tsx:242
msgid "Trash"
msgstr "Kosz"

#: src/constants/analytics.js:1053
#: src/constants/analytics.js:1068
msgid "Treemap"
msgstr "Mapa drzewa"

#: src/components/staff/admin/user/User.js:120
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:391
msgid "Trigger password reset"
msgstr "Uruchom resetowanie hasła"

#: src/components/notifications/Permissions.js:74
msgid "Try again"
msgstr "Spróbuj ponownie"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:82
#: src/components/misc/PromoBox/PromoBox.js:144
msgid "Try for free"
msgstr "Wypróbuj za darmo"

#: src/pages/user/reactivate-24.js:34
msgid "Try out Mediaboard"
msgstr "Wypróbuj Mediaboard"

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:57
msgid "Try social media monitoring"
msgstr "Wypróbuj monitoring social media"

#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:18
msgid "Turn off these notifications"
msgstr "Wyłącz te powiadomienia"

#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:27
msgid "Turn on these notifications"
msgstr "Włącz te powiadomienia"

#: src/components/notifications/ContentTvrRequest.js:41
#: src/components/notifications/ContentTvr.js:46
#: src/components/misc/ActionsBar/View/ViewMenu.js:162
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:41
#: src/components/layout/AuthWrapper/constants/features.slides.js:65
msgid "TV"
msgstr "TV"

#: src/components/emailing/forms/FormSenderSettings.js:123
msgid "TXT record"
msgstr "Rekord TXT"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:58
msgid "Type a coefficient"
msgstr "Wpisz współczynnik"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:38
msgid "Type your keypoint"
msgstr "Wpisz swój kluczowy punkt"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:56
msgid "Type your main message"
msgstr "Wpisz swoją główną wiadomość"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:125
msgid "Type your subject or other instructions. Clearly outline the main message or information you want to convey.Provide instructions on how to structure the information, for example: use bullet points or numbered lists."
msgstr "Wpisz temat lub inne instrukcje. Wyraźnie zarysuj główną wiadomość lub informacje, które chcesz przekazać. Podaj instrukcje, jak strukturyzować informacje, na przykład: użyj punktów lub list numerowanych."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:175
msgid "Unable to extract data from the URL."
msgstr "Nie udało się wyodrębnić danych z podanego URL."

#: src/components/emailing/content/EmailingSettingsContent.js:29
msgid "Unable to retrieve access token from the OAuth2 provider. This may be due to a network issue or provider outage. Please try again later."
msgstr "Nie można pobrać tokena dostępu od dostawcy OAuth2. Może to być spowodowane problemem z siecią lub awarią dostawcy. Spróbuj ponownie później."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:558
msgid "Undo"
msgstr "Cofnij"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:79
msgid "Unique visits"
msgstr "Unikalne wizyty"

#: src/components/newsroom/content/dashboard/ChartVisits.js:54
#: src/components/newsroom/content/dashboard/ChartVisits.js:96
#: src/components/newsroom/components/PostsList/PostsList.js:209
msgid "Unique Visits"
msgstr "Unikalne wizyty"

#: src/components/reports/history/RecipientsTableRow.js:68
msgid "Unknown"
msgstr "Nieznany"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:194
msgid "Unlock licensed articles"
msgstr "Odblokuj licencjonowane artykuły"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:148
msgid "Unpublish"
msgstr "Cofnij publikację"

#: src/components/medialist/forms/FormEditAuthor.js:577
msgid "Unsaved changes"
msgstr "Niezapisane zmiany"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:152
msgid "Unschedule"
msgstr "Nieplanowany"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:149
msgid "Unsubscribe"
msgstr "Wypisz się"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:47
msgid "Unsubscribe from emails"
msgstr "Wypisz się z subskrypcji e-maili"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:311
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:322
msgid "Unsubscribe news source"
msgstr "Anuluj subskrypcję źródła wiadomości"

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:138
msgid "Update recipient"
msgstr "Aktualizuj odbiorcę"

#: src/components/medialist/content/MedialistDashboard.js:75
#: src/components/medialist/content/MedialistDashboard.js:108
msgid "Updated"
msgstr "Zaktualizowane"

#: src/components/medialist/content/MedialistActionsBar/ContactsImportTitle.tsx:8
msgid "Upload either a manually completed template or a formatted contact list file. Once you import contacts, they will automatically appear in the Import. You can also add the contacts to one of the existing lists."
msgstr "Prześlij ręcznie wypełniony szablon lub sformatowany plik z listą kontaktów. Po zaimportowaniu kontakty automatycznie pojawią się w Imporcie. Możesz także dodać kontakty do jednej z istniejących list."

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:26
msgid "Upload File"
msgstr "Prześlij plik"

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:20
msgid "Upload Image"
msgstr "Prześlij obraz"

#: src/components/medialist/content/MedialistActionsBar/withModalUploadMedialist.tsx:8
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:240
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:241
msgid "Upload medialist"
msgstr "Prześlij medialist"

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:23
msgid "Upload Video"
msgstr "Prześlij wideo"

#: src/components/settings/SettingsLogo/SettingsLogo.js:95
msgid "Upload your company logo, which will then be displayed in email reports, exports and in the application itself, instead of the {appName} logo."
msgstr "Prześlij logo Twojej firmy, które następnie będzie wyświetlane w raportach mailowych, eksportach i w samej aplikacji, zamiast loga {appName}."

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformTitle.tsx:8
msgid "Upload your contact list and we'll transform it to fit our medialist for you."
msgstr "Prześlij swoją listę kontaktów, a my przekształcimy ją tak, aby pasowała do naszej listy medialnej."

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:58
msgid "Upload your contact list, and we’ll format it to fit perfectly into our medialist for you."
msgstr "Prześlij swoją listę kontaktów, a my sformatujemy ją tak, aby idealnie pasowała do naszej listy medialnej."

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:58
msgid "Upload your file"
msgstr "Prześlij swój plik"

#: src/components/misc/UploadWatcher/UploadWatcher.js:18
msgid "Uploading has not finished. Please do not refresh or close this page."
msgstr "Przesyłanie nie zostało zakończone. Proszę nie odświeżać ani nie zamykać tej strony."

#: src/components/misc/UploadWatcher/UploadWatcher.js:46
msgid "Uploading: {lastProgress}%"
msgstr "Przesyłanie: {lastProgress}%"

#: src/components/medialist/forms/modules/FormArray.js:131
msgid "Url"
msgstr "Url"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:190
#: src/components/ReusableFeed/FormAddArticle.tsx:31
msgid "URL"
msgstr "URL"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:38
msgid "Use another email address"
msgstr "Użyj innego adresu mailowego"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:76
msgid "Use Google account as sender"
msgstr "Użyj konta Google jako nadawcy"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:82
msgid "Use Microsoft 365 account as sender"
msgstr "Użyj konta Microsoft 365 jako nadawcy"

#: src/components/staff/admin/workspace/Workspace.js:369
#: src/components/staff/admin/workspace/Workspace.js:389
#: src/components/staff/admin/workspace/Workspace.js:410
#: src/components/staff/admin/workspace/Workspace.js:430
#: src/components/staff/admin/workspace/Workspace.js:449
#: src/components/staff/admin/workspace/Workspace.js:470
#: src/components/staff/admin/workspace/Workspace.js:491
#: src/components/staff/admin/workspace/Workspace.js:524
#: src/components/staff/admin/workspace/Workspace.js:550
#: src/components/staff/admin/workspace/Workspace.js:569
#: src/components/staff/admin/workspace/Workspace.js:588
#: src/components/staff/admin/workspace/Workspace.js:639
#: src/components/staff/admin/workspace/Workspace.js:660
#: src/components/staff/admin/workspace/Workspace.js:686
msgid "Used"
msgstr "Używane"

#: src/pages/staff/admin/users/[userId]/index.js:12
#: src/components/staff/admin/DailyAccess/Table.js:24
#: src/components/staff/admin/DailyAccess/Content.js:32
msgid "User"
msgstr "Użytkownik"

#: src/components/tariff/TariffLimits/TariffLimits.js:274
#: src/components/staff/admin/workspace/Workspace.js:677
msgid "User accounts limit"
msgstr "Limit kont użytkowników"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:53
#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:59
msgid "User emails"
msgstr "Maile użytkowników"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:104
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:67
msgid "User management"
msgstr "Zarządzanie użytkownikami"

#: src/components/staff/admin/user/User.js:224
msgid "User settings"
msgstr "Ustawienia użytkownika"

#: src/components/emailing/forms/FormSenderSettings.js:89
msgid "Username"
msgstr "Username"

#: src/pages/staff/admin/customers/[customerId]/users.js:12
#: src/components/staff/admin/workspace/Workspace.js:858
#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:69
#: src/components/staff/admin/customers/Customer.js:173
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:77
#: src/components/staff/admin/customer/users/Users.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:34
#: src/components/forms/dashboard/Search/SearchUsers.js:36
msgid "Users"
msgstr "Użytkownicy"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:27
msgid "Utilizes company profiles for more tailored content."
msgstr "Wykorzystuje profile firmowe do bardziej dostosowanej treści."

#: src/components/emailing/forms/FormSenderSettings.js:168
msgid "Validate"
msgstr "Sprawdź"

#: src/components/emailing/forms/FormSenderSettings.js:134
msgid "Value"
msgstr "Wartość"

#: src/components/staff/admin/customer/bio/CustomerBio.js:95
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:58
msgid "VAT"
msgstr "VAT"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:227
msgid "Verification"
msgstr "Weryfikacja"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:101
msgid "Verification email sent."
msgstr "Mail weryfikacyjny został wysłany."

#. placeholder {0}: values.email
#: src/components/emailing/content/EmailingSettingsContent.js:93
msgid "Verification email was sent to {0}. Please check your inbox."
msgstr "Mail weryfikacyjny został wysłany na adres {0}. Sprawdź swoją skrzynkę odbiorczą."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:26
msgid "Verify your email"
msgstr "Zweryfikuj swój mail"

#: src/components/emailing/forms/FormSenderSettings.js:208
msgid "Verifying your email address with SMTP or DNS can improve email deliverability, protect against spoofing, improve sender reputation, and provide better analytics. It demonstrates legitimacy and helps email providers ensure that emails are not spam."
msgstr "Weryfikacja Twojego adresu mailowego za pomocą SMTP lub DNS może poprawić dostarczalność maili, chronić przed spoofingiem, poprawić reputację nadawcy i zapewnić lepszą analitykę. Dowodzi to legalności i pomaga dostawcom usług mailowych zapewnić, że nie są spamem."

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:87
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:48
msgid "Video"
msgstr "Wideo"

#: src/components/newsroom/content/posts/NewsroomPosts.js:119
#: src/components/newsroom/content/posts/NewsroomPosts.js:123
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:23
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:32
#: src/components/misc/ActionsBar/View/ViewMenu.js:323
#: src/components/misc/ActionsBar/View/View.js:16
msgid "View"
msgstr "Wyświetl"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:84
msgid "View changes"
msgstr "Pokaż zmiany"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:44
msgid "View full article"
msgstr "Zobacz cały artykuł"

#: src/components/OurChart/OurChartAdvanced.js:253
msgid "View in full screen"
msgstr "Wyświetl na pełnym ekranie"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:58
msgid "View Newsroom"
msgstr "Zobacz Newsroom"

#: src/components/feed/InspectorToolbar/InspectorToolbar.js:120
msgid "View preview"
msgstr "Zobacz podgląd"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:41
msgid "View Screenshot"
msgstr "Zobacz zrzut ekranu"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:47
msgid "View Video"
msgstr "Wyświetl wideo"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:41
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:47
msgid "View Web"
msgstr "Wyświetl stronę"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:35
msgid "Viewing this press publication incurs an additional fee as per the Table of Fees approved by the Minister of Culture and National Heritage."
msgstr "Przeglądanie tej publikacji prasowej wiąże się z dodatkową opłatą, zgodną z Tabelą Opłat zatwierdzoną przez Ministra Kultury i Dziedzictwa Narodowego."

#: src/components/monitoring/FeedList/FeedListItem/SocialInteractions/SocialInteractions.js:24
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:95
msgid "views"
msgstr "wyświetlenia"

#: src/components/newsroom/content/posts/NewsroomPosts.js:163
msgid "Views"
msgstr "Wyświetlenia"

#: src/components/misc/ActionsBar/Selector/Selector.js:34
msgid "Visible"
msgstr "Widoczne"

#: src/components/newsroom/content/posts/NewsroomPosts.js:261
#: src/components/newsroom/content/dashboard/ChartVisits.js:86
#: src/components/newsroom/components/PostsList/PostsList.js:209
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:74
msgid "Visits"
msgstr "Wizyty"

#: src/components/newsroom/content/dashboard/ChartVisits.js:49
msgid "Visits (last 30 days / total):"
msgstr "Wizyty (ostatnie 30 dni / łącznie):"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:107
msgid "Warning"
msgstr "Ostrzeżenie"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:46
msgid "Warning via <0>SMS</0>, <1>email</1> or <2>notification</2>"
msgstr "Ostrzeżenie przez <0>SMS</0>, <1>mail</1> lub <2>powiadomienie</2>"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:57
msgid "We are the only ones in the Czech Republic to monitor <0>text mentions in the broadcast</0> for selected channels."
msgstr "Jesteśmy jedynymi w Polsce, którzy monitorują <0>wzmianki tekstowe w transmisji</0> na wybranych kanałach."

#: src/pages/user/reset-password/success.tsx:8
msgid "We have sent password reset link to your email."
msgstr "Na Twój mail został wysłany link do resetowania hasła."

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:29
msgid "We have sent you an activation link. To activate Emailing and to confirm your email address please open the link."
msgstr "Wysłaliśmy Ci link aktywacyjny. Aby aktywować Mailing i potwierdzić swój adres mailowy, otwórz ten link."

#: src/components/emailing/content/EmailingSettingsContent.js:28
msgid "We haven't been granted access to send emails on your behalf. Please try again and make sure to grant us access."
msgstr "Nie otrzymaliśmy dostępu do wysyłania wiadomości e-mail w Twoim imieniu. Spróbuj ponownie i upewnij się, że udzieliłeś nam dostępu."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:75
msgid "We noticed that your post lacks an introduction or perex. This section is crucial as it provides a brief overview of your post and entices readers to continue. Consider adding a short paragraph that summarizes your main points or sets the context for your post."
msgstr "Zauważyliśmy, że Twojemu postowi brakuje wstępu lub perex. Ta sekcja jest kluczowa, ponieważ zapewnia krótki przegląd Twojego posta i zachęca czytelników do kontynuowania. Rozważ dodanie krótkiego akapitu, który podsumowuje Twoje główne punkty lub ustala kontekst dla Twojego posta."

#: src/components/emailing/forms/FormSenderSettings.js:226
msgid "We recommend that you verify your email address"
msgstr "Zalecamy zweryfikowanie Twojego adresu mail"

#: src/components/page/auth/UserInactive/UserInactive.js:20
msgid "We will contact you shortly, once we setup your account."
msgstr "Skontaktujemy się z Tobą wkrótce, jak tylko skonfigurujemy Twoje konto."

#: src/pages/404.js:24
#: src/app/not-found-content.tsx:33
msgid "We're sorry, but the requested page was not found. It is possible that the page was either removed or moved somewhere else. Please make sure you entered the correct URL address."
msgstr "Przykro nam, ale nie znaleziono żądanej strony. Możliwe, że strona została usunięta lub przeniesiona gdzie indziej. Upewnij się, że wprowadziłeś poprawny adres URL."

#. placeholder {0}: topics.getTopicNameById(missingArticle.topicMonitorId)
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:170
msgid "We've added the article to the the topic \"{0}\" and adjusted its settings. The article will appear in your feed shortly."
msgstr "Dodaliśmy materiał do tematu \"{0}\" i dostosowaliśmy jego ustawienia. Materiał pojawi się wkrótce w Twoim kanale."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:110
msgid "We've discovered more content in the media landscape related to your topics and areas of interest."
msgstr "Odkryliśmy więcej treści w mediach związanych z Twoimi tematami i obszarami zainteresowań."

#: src/components/medialist/forms/FormEditAuthor.js:842
msgid "Website"
msgstr "Strona internetowa"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:141
msgid "Website URL"
msgstr "Adres URL Twojej strony"

#: src/helpers/charts/makeGranularityMenu.js:18
#: src/helpers/charts/getGranularityLabel.js:6
msgid "Weeks"
msgstr "Tygodnie"

#: src/components/emailing/content/sender/EmailingSenderContent.js:48
msgid "What is Emailing used for?"
msgstr "Do czego jest używany Mailing?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:250
msgid "What is Newsroom?"
msgstr "Czym jest Newsroom?"

#: src/components/emailing/content/sender/EmailingSenderContent.js:49
msgid "While our Emailing tool is designed to send press and PR messages to journalists, its functionality goes beyond that. You can use it for various types of communication, opening up possibilities beyond traditional media outreach."
msgstr "Nasze narzędzie do wysyłki maili jest zaprojektowane do wysyłania wiadomości prasowych i PR do dziennikarzy, ale jego funkcjonalność wykracza poza to. Możesz go używać do różnych typów komunikacji, otwierając możliwości wykraczające poza tradycyjne nawiązywanie kontaktów z mediami."

#: src/components/staff/admin/workspace/Workspace.js:799
msgid "Whitelisted domains"
msgstr "Dozwolone domeny"

#. placeholder {0}: targetDashboard.name
#: src/store/models/dashboards/DashboardItem/DashboardItem.js:323
msgid "Widget copied to \"{0}\"."
msgstr "Widget skopiowany do \"{0}\"."

#. placeholder {0}: targetDashboard.name
#: src/store/models/dashboards/DashboardItem/DashboardItem.js:314
msgid "Widget moved to \"{0}\"."
msgstr "Widget został przeniesiony do \"{0}\"."

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:138
msgid "Widget will be removed"
msgstr "Widget zostanie usunięty."

#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:203
msgid "With contact"
msgstr "Z kontaktem"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:38
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:72
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:112
msgid "With inflection"
msgstr "Z odmianą"

#: src/components/layout/AuthWrapper/constants/features.slides.js:215
msgid "With Medialist, you don't send your media output to randomly selected journalists. You only send it to those who are most likely to publish it."
msgstr "Z Medialist, nie wysyłasz swoich materiałów medialnych do losowo wybranych dziennikarzy. Wysyłasz je tylko do tych, którzy najprawdopodobniej je opublikują."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:32
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:37
#: src/components/layout/MntrActiveFilters/modules/Note.js:11
msgid "With note"
msgstr "Z notatką"

#. placeholder {0}: values.unverified_recipients_limit
#: src/components/emailing/forms/FormSenderSettings.js:227
msgid "Without a verified email address, your emails risk being marked as spam and rejected by providers, potentially damaging your reputation. You will also be limited to sending an email to only {0} recipients at a time."
msgstr "Bez zweryfikowanego adresu mailowego, istnieje ryzyko, że Twoje wiadomości zostaną oznaczone jako spam i odrzucone przez dostawców, co może potencjalnie zaszkodzić Twojej reputacji. Będziesz również ograniczony do wysyłania maili tylko do {0} odbiorców na raz."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:29
msgid "Without limit"
msgstr "Bez limitu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:49
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:54
#: src/components/layout/MntrActiveFilters/modules/Note.js:15
msgid "Without note"
msgstr "Bez notatki"

#: src/components/staff/admin/customers/Customers.js:26
msgid "without sending registration email"
msgstr "bez wysyłania maila rejestracyjnego"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:154
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:166
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:86
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:92
#: src/components/layout/MntrActiveFilters/modules/Tags.js:46
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterTags.js:19
msgid "Without tags"
msgstr "Bez tagów"

#: src/pages/404.js:14
#: src/app/not-found-content.tsx:23
msgid "Woop woop woop woop, page not found"
msgstr "Woop woop woop woop, strona nie znaleziona"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:24
#: src/components/help/search/Content/RulesSearch.tsx:16
msgid "Word Search"
msgstr "Wyszukiwanie słów"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:98
#: src/components/help/search/Content/RulesDistance.tsx:16
msgid "Words to distance"
msgstr "Słowa z odstępem"

#: src/pages/staff/admin/workspaces/[workspaceId]/index.js:12
#: src/components/staff/admin/workspace/WorkspaceChangelog.js:21
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:77
#: src/components/staff/admin/DailyAccess/Table.js:27
#: src/components/staff/admin/DailyAccess/Content.js:32
#: src/components/page/auth/Expired/Expired.js:53
#: src/components/layout/Header/UserMenu/UserMenu.tsx:101
msgid "Workspace"
msgstr "Przestrzeń robocza"

#: src/components/layout/Header/UserMenu/UserMenu.tsx:210
msgid "Workspace admin"
msgstr "Admin przestrzeni roboczej"

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:55
msgid "Workspace created."
msgstr "Przestrzeń robocza została utworzona."

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:37
msgid "Workspace name"
msgstr "Nazwa przestrzeni roboczej"

#: src/components/staff/admin/workspace/Workspace.js:280
msgid "Workspace settings"
msgstr "Ustawienia przestrzeni roboczej"

#: src/pages/staff/admin/customers/[customerId]/workspaces.js:12
#: src/components/staff/admin/user/WorkspacesTable.js:61
#: src/components/staff/admin/user/User.js:307
#: src/components/staff/admin/customers/Customer.js:150
#: src/components/staff/admin/customer/workspaces/Workspaces.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:26
#: src/components/forms/dashboard/Search/SearchWorkspaces.js:43
msgid "Workspaces"
msgstr "Przestrzenie robocze"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:65
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:204
msgid "worst"
msgstr "najgorszy"

#: src/components/settings/SettingsLogo/SettingsLogo.js:76
msgid "Would you like to customize the appearance of the app, email reports and exports with your own logo? Contact us at <0>{salesEmail}</0>"
msgstr "Czy chciałbyś dostosować wygląd aplikacji, raportów mailowych i eksportów do swojego własnego logo? Skontaktuj się z nami pod adresem <0>{salesEmail}</0>"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:61
msgid "Write the main content of your article that you want to create. The main content is considered as the primary theme or topic of your article."
msgstr "Napisz główną treść swojego artykułu, który chcesz stworzyć. Główna treść jest uważana za główny temat lub temat Twojego artykułu."

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:19
#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:20
msgid "Write with AI assistant"
msgstr "Pisanie z asystentem AI"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:39
msgid "Write without AI assistant"
msgstr "Pisanie bez asystenta AI"

#: src/components/newsroom/components/AiTools/AiGenerateTitles.tsx:79
msgid "Write your own"
msgstr "Napisz samodzielnie"

#: src/components/tariff/TariffLimits/TariffLimits.js:132
#: src/components/staff/admin/workspace/Workspace.js:420
msgid "Yearly authors export limit"
msgstr "Roczny limit na liczbę wyeksportowanych autorów"

#: src/helpers/charts/makeGranularityMenu.js:34
#: src/helpers/charts/getGranularityLabel.js:12
msgid "Years"
msgstr "Lata"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/metaDataDate.js:30
msgid "yesterday"
msgstr "wczoraj"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:114
#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/MediaDetail.js:72
#: src/components/tvr/Content/TvrStories/TvrStory/TvrStory.js:52
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:100
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:115
msgid "Yesterday"
msgstr "Wczoraj"

#: src/components/emailing/modules/withModalRemoveRecipients.tsx:60
msgid "You are about to remove the selected recipients. However, you can keep some of them by clicking on the recipients."
msgstr "Zamierzasz usunąć wybranych odbiorców. Możesz jednak zachować niektórych z nich, klikając na odbiorców."

#: src/components/emailing/content/mediaCoverage/EmptyFeedMessage.tsx:9
msgid "You can add articles to media coverage"
msgstr "Możesz dodać artykuły do monitoringu mediów"

#: src/components/exportList/Content/HeadingExport/HeadingExport.js:30
msgid "You can add items in Articles section."
msgstr "Możesz dodawać elementy w sekcji Materiały."

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:88
msgid "You can create your first campaign by clicking the button below."
msgstr "Swoją pierwszą kampanię możesz utworzyć, klikając przycisk poniżej."

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:133
msgid "You can create your first email by clicking the button below."
msgstr "Swojego pierwszego maila możesz utworzyć, klikając przycisk poniżej."

#: src/components/monitoring/WorkspaceArticles/Intro.js:30
msgid "You can create your own articles here. They will be added to <0>your feed only</0>."
msgstr "Tutaj możesz tworzyć własne materiały. Zostaną one dodane do sekcji Twoje materiały. <0>Zobaczysz je tylko Ty</0>."

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:51
msgid "You can edit recipients in email settings."
msgstr "Możesz edytować odbiorców w ustawieniach maila."

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:149
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:70
msgid "You can reset your filter by clicking the button below."
msgstr "Możesz zresetować swój filtr, klikając przycisk poniżej."

#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:20
msgid "You can safely close this window as the process will continue in the background. Once the import is complete, we will notify you. If any issues occur, you will receive a notification and an email detailing the errors."
msgstr "To okno można bezpiecznie zamknąć, ponieważ proces będzie kontynuowany w tle. Po zakończeniu importu powiadomimy Cię. Jeśli wystąpią jakiekolwiek problemy, otrzymasz powiadomienie i e-mail z szczegółami błędów."

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:91
msgid "You can use an external link to the article or {appName} link."
msgstr "Możesz użyć zewnętrznego linku do materiału lub linku z aplikacji {appName}."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:244
msgid "You don't have a Newsroom yet, but you can create a new one right now."
msgstr "Nie masz jeszcze Newsroomu, ale możesz stworzyć nowy właśnie teraz."

#: src/components/monitoring/WorkspaceArticles/Intro.js:26
msgid "You don't have any articles yet, but you can create one right now."
msgstr "Nie masz jeszcze żadnych materiałów, ale możesz stworzyć jeden teraz."

#: src/components/emailing/content/sender/EmailingSenderContent.js:20
msgid "You don't have Emailing set up yet. It only takes a few minutes to set it up."
msgstr "Nie masz jeszcze skonfigurowanego Mailingu. Konfiguracja zajmuje tylko kilka minut."

#: src/components/widgets/modules/stats/StatsBySource.js:35
#: src/components/widgets/components/PermissionErrorHint/PermissionErrorHint.js:13
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:36
msgid "You don't have permission to view"
msgstr "Nie masz uprawnień do wyświetlania"

#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:55
msgid "You have no topics created"
msgstr "Nie utworzyłeś żadnych tematów"

#: src/components/notifications/AppNotifications/AppNotifications.js:25
#: src/components/layout/Header/AppNotifications/AppNotifications.js:173
msgid "You have not received any notifications yet."
msgstr "Nie otrzymałeś jeszcze żadnych powiadomień."

#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:52
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:52
msgid "You have not saved any settings"
msgstr "Nie masz zapisanych żadnych ustawień"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:106
msgid "You have reached 30-day limit on the number of translated articles."
msgstr "Osiągnąłeś limit przetłumaczonych materiałów na miesiąc."

#: src/components/tariff/TariffLimits/TariffLimits.js:59
#: src/components/exportList/ExportLimit/ExportLimit.js:19
msgid "You have reached 30-day limit. You cannot export any new articles."
msgstr "Osiągnąłeś 30-dniowy limit. Nie możesz eksportować nowych materiałów."

#: src/components/tariff/TariffLimits/TariffLimits.js:220
#: src/components/exportList/ExportLimit/ExportLimit.js:30
msgid "You have reached 30-day limit. You cannot export any new social media mentions."
msgstr "Osiągnąłeś 30-dniowy limit. Nie możesz eksportować nowych wzmianek z social mediów."

#: src/store/models/ExportStore.js:238
msgid "You have reached the 30-day limit on the number of exported articles. Exported file doesn't contain all the selected articles."
msgstr "Osiągnąłeś 30-dniowy limit eksportowanych materiałów. Eksportowany plik nie zawiera wszystkich wybranych materiałów."

#: src/store/models/ExportStore.js:240
msgid "You have reached the 30-day limit on the number of exported social media mentions. Exported file doesn't contain all selected items."
msgstr "Osiągnąłeś 30-dniowy limit eksportowanych wzmianek z social media. Eksportowany plik nie zawiera wszystkich wybranych elementów."

#: src/components/monitoring/WorkspaceArticles/Limits.js:59
msgid "You have reached the 30-day limit on the number of OCR pages."
msgstr "Osiągnąłeś 30-dniowy limit stron OCR."

#: src/components/monitoring/WorkspaceArticles/Limits.js:75
msgid "You have reached the 30-day limit on the number of transcribed seconds."
msgstr "Osiągnąłeś 30-dniowy limit sekund do transkrypcji."

#: src/store/models/ExportStore.js:246
msgid "You have reached the 30-day limit on the number of translated articles. Exported file doesn't contain all the selected articles."
msgstr "Osiągnąłeś limit przetłumaczonych materiałów w ciągu 30 dni. Eksportowany plik nie zawiera wszystkich wybranych materiałów."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:92
msgid "You have reached the limit of recipients per email"
msgstr "Osiągnąłeś limit odbiorców na jeden e-mail"

#: src/components/layout/Header/MessageLimit/MessageLimit.js:19
msgid "You have reached the limit on found articles"
msgstr "Osiągnąłeś limit znalezionych materiałów"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:104
msgid "You have reached the limit on the number of dashboards."
msgstr "Osiągnąłeś limit liczby pulpitów."

#: src/store/models/ExportStore.js:229
msgid "You have reached the limit on the number of exported articles. Exported file doesn't contain all the selected articles."
msgstr "Osiągnąłeś limit eksportowanych materiałów. Wyeksportowany plik nie zawiera wszystkich wybranych materiałów."

#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:79
msgid "You have reached the limit on the number of Newsrooms."
msgstr "Osiągnąłeś limit liczby Newsroomów."

#: src/store/models/emailing/emailEdit/EmailEditStore/recipients/EmailRecipientsStore/EmailRecipientsStore.js:122
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:48
msgid "You have reached the limit on the number of recipients."
msgstr "Osiągnąłeś limit odbiorców."

#: src/components/emailing/content/EmailingSettingsContent.js:22
msgid "You have successfully authorized our application to use the external service."
msgstr "Pomyślnie autoryzowałeś naszą aplikację do korzystania z usługi zewnętrznej."

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:123
msgid "You have unsaved changes."
msgstr "Masz niezapisane zmiany."

#: src/components/tariff/TariffLimits/TariffLimits.js:23
msgid "You may not see the latest articles."
msgstr "Możesz nie widzieć najnowszych materiałów."

#: src/components/layout/Header/MessageLimit/MessageLimit.js:13
msgid "You may not see the latest articles. We recommend that you change your keyword settings or limit your watched media in the Topics section."
msgstr "Możesz nie zobaczyć najnowszych materiałów. Zalecamy zmianę ustawień słów kluczowych lub ograniczenie obserwowanych mediów w sekcji Tematy."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:470
msgid "You will be able to edit the link later to match your own domain."
msgstr "Będziesz mógł edytować link później, aby pasował do Twojej domeny."

#: src/components/emailing/modules/PreviewEmail/RecipientsIsEmpty.tsx:27
#: src/components/emailing/components/EmailRecipientsList/RenderAllRecipients.tsx:34
msgid "You will see your recipients here"
msgstr "Tutaj zobaczysz swoich odbiorców"

#: src/helpers/modal/withModalRequestFeature.tsx:40
#: src/components/misc/PromoBox/PromoBox.js:135
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:12
msgid "You'll be contacted by our Sales management."
msgstr "Nasz przedstawiciel skontaktuje się z Tobą."

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:17
msgid "Your account does not have access to any workspace."
msgstr "Twoje konto nie ma dostępu do żadnej przestrzeni roboczej."

#: src/components/page/auth/Expired/Expired.js:49
msgid "Your account has expired"
msgstr "Twoje konto wygasło"

#: src/components/page/auth/UserInactive/UserInactive.js:14
msgid "Your account is being prepared"
msgstr "Twoje konto jest przygotowywane"

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:118
msgid "Your email <0>{0}</0> is already unsubscribed from our email list. There is nothing you need to do to stop receiving emails from {host}"
msgstr "Twój e-mail <0>{0}</0> jest już wypisany z naszej listy mailingowej. Nie musisz nic robić, aby przestać otrzymywać e-maile od {host}."

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:85
msgid "Your email <0>{0}</0> successfully unsubscribed from our email list. You will no longer receive emails from us."
msgstr "Twój e-mail <0>{0}</0> został pomyślnie wypisany z naszej listy mailingowej. Nie będziesz już otrzymywać od nas e-maili."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:65
msgid "Your email has been verified. Now you can fully enjoy our platform."
msgstr "Twój mail został zweryfikowany. Teraz możesz w pełni korzystać z naszej platformy."

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:76
msgid "Your email successfully unsubscribed"
msgstr "Twój e-mail został pomyślnie wypisany"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:109
msgid "Your email was already unsubscribed"
msgstr "Twój e-mail został już wypisany"

#: src/components/emailing/content/EmailingSettingsContent.js:71
#: src/components/emailing/content/EmailingCampaignsContent.tsx:28
msgid "Your Emailing is not fully set up and verified"
msgstr "Twój Mailing nie jest jeszcze w pełni skonfigurowany i zweryfikowany"

#: src/components/emailing/content/EmailingSettingsContent.js:74
msgid "Your Emailing is not fully set up and verified. This can decrease the trust level and deliverability. You can fully set up and verify your Emailing in the settings. If you need help, please contact our support."
msgstr "Twój Mailing nie jest w pełni skonfigurowany i zweryfikowany. Może to obniżyć poziom zaufania i dostarczalność. Możesz w pełni skonfigurować i zweryfikować swój Mailing w ustawieniach. Jeśli potrzebujesz pomocy, skontaktuj się z naszym wsparciem."

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:96
msgid "Your HTML code"
msgstr "Twój kod HTML"

#: src/components/layout/Header/MessageDirty/MessageDirty.js:10
msgid "Your news feed is being updated"
msgstr "Twój kanał informacyjny jest aktualizowany"

#: src/store/models/account/user/UserStore.js:247
msgid "Your password has been changed successfully."
msgstr "Twoje hasło zostało pomyślnie zmienione."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:66
msgid "Your post is missing a title. A clear, concise title helps readers understand what your post is about at a glance. Please add a title that accurately represents your content."
msgstr "Twojemu wpisowi brakuje tytułu. Jasny, zwięzły tytuł pomaga czytelnikom zrozumieć, o czym jest Twój wpis na pierwszy rzut oka. Dodaj tytuł, który dokładnie odzwierciedla Twoją treść."
