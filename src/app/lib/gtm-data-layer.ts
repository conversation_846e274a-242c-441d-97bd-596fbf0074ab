import { headers } from 'next/headers'
import { getSessionAndSettings } from './get-session-settings'

export async function getGTMDataLayer() {
  const { appSettings, requestIp, sessionInfo } = await getSessionAndSettings()
  const headersList = await headers()
  const host = headersList.get('host') || ''
  const productDomain = host.replace('app.', '')
  const userAgent = headersList.get('user-agent') || ''
  const isMobileApp =
    userAgent.includes('MonitoraApp') ||
    Boolean((global as unknown as { isMobileApp?: boolean }).isMobileApp)

  return {
    buildEnv: process.env.NODE_ENV || 'development',
    ipAddress: requestIp,
    isMobileApp,
    productDomain,
    productId: appSettings.gtmProductId,
    primaryApp: sessionInfo.primaryApp,
  }
}
