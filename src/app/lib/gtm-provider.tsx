'use client'

/**
 * Clean and elegant GTM implementation for /src/app directory
 *
 * This provides a unified GTM solution that:
 * - Includes user data in all events (like the old pushEvent from helpers/gtm.js)
 * - Uses Next.js 15 best practices with React context
 * - Handles user identification and page tracking automatically
 * - Provides consistent API for both interactive and auto events
 */

import { sendGTMEvent } from '@next/third-parties/google'
import { differenceInDays } from 'date-fns'
import cookie from 'js-cookie'
import { usePathname, useSearchParams } from 'next/navigation'
import { createContext, useContext, useEffect, useRef, type ReactNode } from 'react'
import { eventNames } from '~/constants/gtm'
import type { UserProps } from '~/app/types/user'
import type { IWorkspaceStore } from '~/store/models/account/workspace/WorkspaceStore'
import type { IAppSettings } from '~/store/models/appSettings/AppSettings'

interface GTMUserData {
  isAnalyst: boolean
  isImpersonating: boolean
  isPublicAccess: boolean
  isSalesman: boolean
  userId: string
  userLanguage: string
  workspaceId: string
  primaryApp: string
  productId?: string
}

interface GTMContextValue {
  userData: GTMUserData | null
  pushEvent: (eventName: string, actionParams?: unknown) => void
  pushEventAuto: (eventName: string, actionParams?: unknown) => void
  sendEvent: typeof sendGTMEvent
}

// Extend window interface for dataLayer
declare global {
  interface Window {
    dataLayer: unknown[]
    _leady: unknown[]
  }
}

export const GTMContext = createContext<GTMContextValue | null>(null)

interface GTMProviderProps {
  children: ReactNode
  user?: UserProps
  workspace?: IWorkspaceStore
  appSettings?: IAppSettings
  lang?: string
  isImpersonating?: boolean
}

function addUserIdentClient(userData: GTMUserData, email: string) {
  if (typeof window === 'undefined' || !window.dataLayer) return

  window.dataLayer.push({
    ...userData,
    event: 'login',
  })

  if (userData.isSalesman) {
    const MIN = new Date()
    const MAX = new Date(2038, 0, 19, 3, 14, 7)

    cookie.set('leady_opt_out', JSON.stringify([true, MIN, MAX]), {
      domain: location.host.replace('app.', ''),
      expires: differenceInDays(MAX, MIN),
    })
  }

  if (!userData.isImpersonating) {
    window._leady = window._leady || []
    window._leady.push(['identify', email])
  }
}

function createPushEvent(userData: GTMUserData | null) {
  return (eventName: string, actionParams?: unknown) => {
    if (!eventName || typeof window === 'undefined' || !window.dataLayer) {
      return
    }

    // Use window.dataLayer.push directly like the old implementation to include user data
    window.dataLayer.push({
      ...(userData || {}),
      event: eventNames.includes(eventName) ? `custom:${eventName}` : eventName,
      ...(actionParams
        ? {
            actionParams: Array.isArray(actionParams) ? actionParams : [actionParams],
          }
        : { actionParams: null }),
      isInteractive: true,
      _clear: true,
    })
  }
}

function createPushEventAuto(userData: GTMUserData | null) {
  return (eventName: string, actionParams?: unknown) => {
    if (!eventName || typeof window === 'undefined' || !window.dataLayer) {
      return
    }

    // Use window.dataLayer.push directly like the old implementation to include user data
    window.dataLayer.push({
      ...(userData || {}),
      event: eventNames.includes(eventName) ? `custom:${eventName}` : eventName,
      ...(actionParams
        ? {
            actionParams: Array.isArray(actionParams) ? actionParams : [actionParams],
          }
        : { actionParams: null }),
      isInteractive: false,
      _clear: true,
    })
  }
}

export function GTMProvider({ children, user, workspace, appSettings, lang, isImpersonating }: GTMProviderProps) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const previousPathnameRef = useRef<string | null>(null)

  const userData: GTMUserData | null = user && workspace ? {
    isAnalyst: user.is_analyst || false,
    isSalesman: user.is_salesman || false,
    isImpersonating: isImpersonating || false,
    isPublicAccess: false,
    userId: user.uuid,
    userLanguage: lang || 'en',
    workspaceId: workspace.uuid,
    primaryApp: String(appSettings?.primaryApp || 'monitora'),
    productId: appSettings?.gtmProductId,
  } : null

  const contextValue: GTMContextValue = {
    userData,
    pushEvent: createPushEvent(userData),
    pushEventAuto: createPushEventAuto(userData),
    sendEvent: sendGTMEvent,
  }

  useEffect(() => {
    if (userData && user && typeof window !== 'undefined') {
      addUserIdentClient(userData, user.email)
    }
  }, [userData, user])

  useEffect(() => {
    if (previousPathnameRef.current === undefined) {
      previousPathnameRef.current = pathname
      return
    }

    if (previousPathnameRef.current !== pathname) {
      const searchString = searchParams?.toString() || ''
      sendGTMEvent({
        event: 'page_view',
        page_path: pathname,
        page_search: searchString,
        page_url: pathname + (searchString ? `?${searchString}` : ''),
      })
      previousPathnameRef.current = pathname
    }
  }, [pathname, searchParams])

  return (
    <GTMContext.Provider value={contextValue}>
      {children}
    </GTMContext.Provider>
  )
}

export function useGTM(): GTMContextValue {
  const context = useContext(GTMContext)
  if (!context) {
    throw new Error('useGTM must be used within a GTMProvider')
  }
  return context
}
