import { GoogleTagManager } from '@next/third-parties/google'

export function GTMNoScript({ gtmId }: { gtmId: string }) {
  return (
    <noscript>
      <iframe
        src={`https://www.googletagmanager.com/ns.html?id=${gtmId}`}
        height={0}
        width={0}
        style={{ display: 'none', visibility: 'hidden' }}
      />
    </noscript>
  )
}

interface GTMContainerProps {
  gtmId: string
  dataLayer?: Record<string, string | number | boolean>
  withNoScript?: boolean
}

export function GTMContainer({ gtmId, dataLayer, withNoScript = false }: GTMContainerProps) {
  return withNoScript ? (
    <GTMNoScript gtmId={gtmId} />
  ) : (
    <GoogleTagManager gtmId={gtmId} dataLayer={dataLayer} />
  )
}
