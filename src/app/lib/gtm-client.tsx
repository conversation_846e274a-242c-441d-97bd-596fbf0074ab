'use client'

import { sendGTMEvent } from '@next/third-parties/google'
import { differenceInDays } from 'date-fns'
import cookie from 'js-cookie'
import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect, useRef, type ReactNode } from 'react'
import { setGTMData } from './use-gtm'
import type { UserProps } from '~/app/types/user'
import type { IWorkspaceStore } from '~/store/models/account/workspace/WorkspaceStore'
import type { IAppSettings } from '~/store/models/appSettings/AppSettings'

interface GTMClientProps {
  children: ReactNode
  user?: UserProps
  workspace?: IWorkspaceStore
  appSettings?: IAppSettings
  lang?: string
  isImpersonating?: boolean
}

function createGTMData(
  user: UserProps,
  workspace: IWorkspaceStore,
  appSettings?: IAppSettings,
  lang?: string,
  isImpersonating?: boolean
) {
  const host = typeof window !== 'undefined' ? window.location.host : ''
  const userAgent = typeof window !== 'undefined' ? window.navigator.userAgent : ''

  return {
    // User data
    isAnalyst: user.is_analyst || false,
    isSalesman: user.is_salesman || false,
    isImpersonating: isImpersonating || false,
    isPublicAccess: false,
    userId: user.uuid,
    userLanguage: lang || 'en',
    workspaceId: workspace.uuid,
    primaryApp: String(appSettings?.primaryApp || 'monitora'),
    productId: appSettings?.gtmProductId,

    // Environment data (from getGTMDataLayer)
    buildEnv: process.env.NODE_ENV || 'development',
    productDomain: host.replace('app.', ''),
    isMobileApp: userAgent.includes('MonitoraApp') || Boolean((window as any).isMobileApp),
  }
}



function addUserIdent(
  user: UserProps,
  workspace: IWorkspaceStore,
  appSettings?: IAppSettings,
  lang?: string,
  isImpersonating?: boolean
) {
  if (typeof window === 'undefined') return

  const gtmData = createGTMData(user, workspace, appSettings, lang, isImpersonating)

  sendGTMEvent({
    ...gtmData,
    event: 'login',
  })

  if (user.is_salesman) {
    setLeadyOptOut()
  }

  if (!isImpersonating) {
    identifyWithLeady(user.email)
  }
}

function setLeadyOptOut() {
  const MIN = new Date()
  const MAX = new Date(2038, 0, 19, 3, 14, 7)

  cookie.set('leady_opt_out', JSON.stringify([true, MIN, MAX]), {
    domain: location.host.replace('app.', ''),
    expires: differenceInDays(MAX, MIN),
  })
}

function identifyWithLeady(email: string) {
  const leadyWindow = window as unknown as { _leady: unknown[] }
  leadyWindow._leady = leadyWindow._leady || []
  leadyWindow._leady.push(['identify', email])
}

export function GTMClient({ children, user, workspace, appSettings, lang, isImpersonating }: GTMClientProps) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const previousPathnameRef = useRef<string | null>(null)

  useEffect(() => {
    const gtmData = user && workspace
      ? createGTMData(user, workspace, appSettings, lang, isImpersonating)
      : {}

    setGTMData(gtmData)
  }, [user, workspace, appSettings, lang, isImpersonating])

  useEffect(() => {
    if (user && workspace && typeof window !== 'undefined') {
      addUserIdent(user, workspace, appSettings, lang, isImpersonating)
    }
  }, [user, workspace, appSettings, lang, isImpersonating])

  useEffect(() => {
    if (previousPathnameRef.current === undefined) {
      previousPathnameRef.current = pathname
      return
    }

    if (previousPathnameRef.current !== pathname) {
      const searchString = searchParams?.toString() || ''
      sendGTMEvent({
        event: 'page_view',
        page_path: pathname,
        page_search: searchString,
        page_url: pathname + (searchString ? `?${searchString}` : ''),
      })
      previousPathnameRef.current = pathname
    }
  }, [pathname, searchParams])

  return <>{children}</>
}
