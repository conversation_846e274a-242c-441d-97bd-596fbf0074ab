'use client'

import { sendG<PERSON>Event } from '@next/third-parties/google'
import { differenceInDays } from 'date-fns'
import cookie from 'js-cookie'
import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect, useRef, type ReactNode } from 'react'
import { setGTMData } from './use-gtm'
import type { UserProps } from '~/app/types/user'
import type { IWorkspaceStore } from '~/store/models/account/workspace/WorkspaceStore'
import type { IAppSettings } from '~/store/models/appSettings/AppSettings'

interface GTMClientProps {
  children: ReactNode
  user?: UserProps
  workspace?: IWorkspaceStore
  appSettings?: IAppSettings
  lang?: string
  isImpersonating?: boolean
}

function userData(
  user: UserProps,
  workspace: IWorkspaceStore,
  appSettings?: IAppSettings,
  lang?: string,
  isImpersonating?: boolean
) {
  return {
    isAnalyst: user.is_analyst || false,
    isSalesman: user.is_salesman || false,
    isImpersonating: isImpersonating || false,
    isPublicAccess: false,
    userId: user.uuid,
    userLanguage: lang || 'en',
    workspaceId: workspace.uuid,
    primaryApp: String(appSettings?.primaryApp || 'monitora'),
    productId: appSettings?.gtmProductId,
  }
}

function getUserIdentParams(
  user: UserProps,
  workspace: IWorkspaceStore,
  appSettings?: IAppSettings,
  lang?: string,
  isImpersonating?: boolean
) {
  return {
    email: user.email,
    is_analyst: user.is_analyst || false,
    is_salesman: user.is_salesman || false,
    isImpersonating: isImpersonating || false,
    isPublicAccess: false,
    productId: appSettings?.gtmProductId,
    primaryApp: String(appSettings?.primaryApp || 'monitora'),
    uuid: user.uuid,
    workspaceId: workspace.uuid,
    app_language: lang || 'en',
  }
}

interface UserIdentParams {
  email: string
  is_analyst: boolean
  is_salesman: boolean
  isImpersonating: boolean
  isPublicAccess: boolean
  productId?: string
  primaryApp: string
  uuid: string
  workspaceId: string | null
  app_language: string
}



function addUserIdent({
  email,
  is_analyst: isAnalyst,
  is_salesman: isSalesman,
  isImpersonating,
  isPublicAccess,
  productId,
  primaryApp,
  uuid: userId,
  workspaceId,
  app_language: userLanguage,
}: UserIdentParams) {
  if (typeof window === 'undefined') return

  sendGTMEvent({
    isAnalyst,
    isImpersonating,
    isPublicAccess,
    isSalesman,
    userId,
    userLanguage,
    workspaceId,
    primaryApp,
    event: 'login',
    productId,
  })

  if (isSalesman) {
    setLeadyOptOut()
  }

  if (!isImpersonating) {
    identifyWithLeady(email)
  }
}

function setLeadyOptOut() {
  const MIN = new Date()
  const MAX = new Date(2038, 0, 19, 3, 14, 7)

  cookie.set('leady_opt_out', JSON.stringify([true, MIN, MAX]), {
    domain: location.host.replace('app.', ''),
    expires: differenceInDays(MAX, MIN),
  })
}

function identifyWithLeady(email: string) {
  const leadyWindow = window as unknown as { _leady: unknown[] }
  leadyWindow._leady = leadyWindow._leady || []
  leadyWindow._leady.push(['identify', email])
}

export function GTMClient({ children, user, workspace, appSettings, lang, isImpersonating }: GTMClientProps) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const previousPathnameRef = useRef<string | null>(null)

  useEffect(() => {
    const gtmUserData = user && workspace
      ? userData(user, workspace, appSettings, lang, isImpersonating)
      : null

    setGTMData(gtmUserData || {})
  }, [user, workspace, appSettings, lang, isImpersonating])

  useEffect(() => {
    if (user && workspace && typeof window !== 'undefined') {
      const params = getUserIdentParams(user, workspace, appSettings, lang, isImpersonating)
      addUserIdent(params)
    }
  }, [user, workspace, appSettings, lang, isImpersonating])

  useEffect(() => {
    if (previousPathnameRef.current === undefined) {
      previousPathnameRef.current = pathname
      return
    }

    if (previousPathnameRef.current !== pathname) {
      const searchString = searchParams?.toString() || ''
      sendGTMEvent({
        event: 'page_view',
        page_path: pathname,
        page_search: searchString,
        page_url: pathname + (searchString ? `?${searchString}` : ''),
      })
      previousPathnameRef.current = pathname
    }
  }, [pathname, searchParams])

  return <>{children}</>
}
