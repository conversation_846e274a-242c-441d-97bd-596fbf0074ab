'use client'

import { sendG<PERSON>Event } from '@next/third-parties/google'
import { differenceInDays } from 'date-fns'
import cookie from 'js-cookie'
import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect, useRef } from 'react'
import type { UserProps } from '~/app/types/user'
import type { IWorkspaceStore } from '~/store/models/account/workspace/WorkspaceStore'
import type { IAppSettings } from '~/store/models/appSettings/AppSettings'

interface GTMClientProps {
  user?: UserProps
  workspace?: IWorkspaceStore
  appSettings?: IAppSettings
  lang?: string
  isImpersonating?: boolean
}

interface UserIdentParams {
  email: string
  is_analyst: boolean
  is_salesman: boolean
  isImpersonating: boolean
  isPublicAccess: boolean
  productId?: string
  primaryApp: string
  uuid: string
  workspaceId: string | null
  app_language: string
}

function addUserIdentClient({
  email,
  is_analyst: isAnalyst,
  is_salesman: is<PERSON><PERSON>man,
  isImpersonating,
  isPublicAccess,
  productId,
  primaryApp,
  uuid: userId,
  workspaceId,
  app_language: userLanguage,
}: UserIdentParams) {
  if (typeof window === 'undefined' || !window.dataLayer) return

  window.dataLayer.push({
    isAnalyst,
    isImpersonating,
    isPublicAccess,
    isSalesman,
    userId,
    userLanguage,
    workspaceId,
    primaryApp,
    event: 'login',
    productId,
  })

  if (isSalesman) {
    const MIN = new Date()
    const MAX = new Date(2038, 0, 19, 3, 14, 7)

    cookie.set('leady_opt_out', JSON.stringify([true, MIN, MAX]), {
      domain: location.host.replace('app.', ''),
      expires: differenceInDays(MAX, MIN),
    })
  }

  if (!isImpersonating) {
    ;(window as unknown as { _leady: unknown[] })._leady =
      (window as unknown as { _leady: unknown[] })._leady || []
    ;(window as unknown as { _leady: unknown[] })._leady.push(['identify', email])
  }
}

export function GTMClient({ user, workspace, appSettings, lang, isImpersonating }: GTMClientProps) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const previousPathnameRef = useRef<string | null>(null)

  useEffect(() => {
    if (user && workspace && typeof window !== 'undefined') {
      addUserIdentClient({
        email: user.email,
        is_analyst: user.is_analyst || false,
        is_salesman: user.is_salesman || false,
        isImpersonating: isImpersonating || false,
        isPublicAccess: false,
        productId: appSettings?.gtmProductId,
        primaryApp: String(appSettings?.primaryApp || 'monitora'),
        uuid: user.uuid,
        workspaceId: workspace.uuid,
        app_language: lang || 'en',
      })
    }
  }, [user, workspace, appSettings, lang, isImpersonating])

  useEffect(() => {
    if (previousPathnameRef.current === undefined) {
      previousPathnameRef.current = pathname
      return
    }

    if (previousPathnameRef.current !== pathname) {
      const searchString = searchParams?.toString() || ''
      sendGTMEvent({
        event: 'page_view',
        page_path: pathname,
        page_search: searchString,
        page_url: pathname + (searchString ? `?${searchString}` : ''),
      })
      previousPathnameRef.current = pathname
    }
  }, [pathname, searchParams])

  return null
}
