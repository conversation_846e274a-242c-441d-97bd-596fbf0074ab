'use client'

import { sendGTMEvent } from '@next/third-parties/google'
import { differenceInDays } from 'date-fns'
import cookie from 'js-cookie'
import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect, useRef, type ReactNode } from 'react'
import { setGTMData } from './use-gtm'
import type { UserProps } from '~/app/types/user'
import type { IWorkspaceStore } from '~/store/models/account/workspace/WorkspaceStore'
import type { IAppSettings } from '~/store/models/appSettings/AppSettings'

interface GTMClientProps {
  children: ReactNode
  user?: UserProps
  workspace?: IWorkspaceStore
  appSettings?: IAppSettings
  lang?: string
  isImpersonating?: boolean
}

function createUserData(
  user: UserProps,
  workspace: IWorkspaceStore,
  appSettings?: IAppSettings,
  lang?: string,
  isImpersonating?: boolean
) {
  return {
    email: user.email,
    isAnalyst: user.is_analyst || false,
    is<PERSON><PERSON>man: user.is_salesman || false,
    isImpersonating: isImpersonating || false,
    isPublicAccess: false,
    userId: user.uuid,
    userLanguage: lang || 'en',
    workspaceId: workspace.uuid,
    primaryApp: String(appSettings?.primaryApp || 'monitora'),
    productId: appSettings?.gtmProductId,
  }
}

function addUserIdent(userData: ReturnType<typeof createUserData>) {
  if (typeof window === 'undefined') return

  sendGTMEvent({
    ...userData,
    event: 'login',
  })

  if (userData.isSalesman) {
    setLeadyOptOut()
  }

  if (!userData.isImpersonating) {
    identifyWithLeady(userData.email)
  }
}

function setLeadyOptOut() {
  const MIN = new Date()
  const MAX = new Date(2038, 0, 19, 3, 14, 7)

  cookie.set('leady_opt_out', JSON.stringify([true, MIN, MAX]), {
    domain: location.host.replace('app.', ''),
    expires: differenceInDays(MAX, MIN),
  })
}

function identifyWithLeady(email: string) {
  const leadyWindow = window as unknown as { _leady: unknown[] }
  leadyWindow._leady = leadyWindow._leady || []
  leadyWindow._leady.push(['identify', email])
}

export function GTMClient({ children, user, workspace, appSettings, lang, isImpersonating }: GTMClientProps) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const previousPathnameRef = useRef<string | null>(null)

  useEffect(() => {
    if (user && workspace) {
      const userData = createUserData(user, workspace, appSettings, lang, isImpersonating)

      setGTMData(userData)

      if (typeof window !== 'undefined') {
        addUserIdent(userData)
      }
    } else {
      setGTMData({})
    }
  }, [user, workspace, appSettings, lang, isImpersonating])

  useEffect(() => {
    if (previousPathnameRef.current === undefined) {
      previousPathnameRef.current = pathname
      return
    }

    if (previousPathnameRef.current !== pathname) {
      const searchString = searchParams?.toString() || ''
      sendGTMEvent({
        event: 'page_view',
        page_path: pathname,
        page_search: searchString,
        page_url: pathname + (searchString ? `?${searchString}` : ''),
      })
      previousPathnameRef.current = pathname
    }
  }, [pathname, searchParams])

  return <>{children}</>
}
