'use client'

import { sendGTMEvent } from '@next/third-parties/google'
import { eventNames } from '~/constants/gtm'

let gtmData: Record<string, unknown> = {}

export function setGTMData(data: Record<string, unknown>) {
  gtmData = data
}

function isValid(eventName: string): boolean {
  return Boolean(eventName && typeof window !== 'undefined')
}

function formatEvent(eventName: string): string {
  return eventNames.includes(eventName) ? `custom:${eventName}` : eventName
}

function formatParams(actionParams?: unknown) {
  return actionParams
    ? { actionParams: Array.isArray(actionParams) ? actionParams : [actionParams] }
    : { actionParams: null }
}

export function pushEvent(eventName: string, actionParams?: unknown) {
  if (!isValid(eventName)) return

  sendGTMEvent({
    ...gtmData,
    event: formatEvent(eventName),
    ...formatParams(actionParams),
    isInteractive: true,
    _clear: true,
  })
}

export function pushEventAuto(eventName: string, actionParams?: unknown) {
  if (!isValid(eventName)) return

  sendGTMEvent({
    ...gtmData,
    event: formatEvent(eventName),
    ...formatParams(actionParams),
    isInteractive: false,
    _clear: true,
  })
}

export function useGTM() {
  return {
    userData: gtmData,
    pushEvent,
    pushEventAuto,
    sendEvent: sendGTMEvent,
  }
}
