'use client'

import { sendGTMEvent } from '@next/third-parties/google'
import { eventNames } from '~/constants/gtm'

function pushEvent(eventName: string, actionParams?: unknown) {
  if (!eventName || typeof window === 'undefined' || !window.dataLayer) {
    return
  }

  sendGTMEvent({
    event: eventNames.includes(eventName) ? `custom:${eventName}` : eventName,
    ...(actionParams
      ? {
          actionParams: Array.isArray(actionParams) ? actionParams : [actionParams],
        }
      : { actionParams: null }),
    isInteractive: true,
    _clear: true,
  })
}

function pushEventAuto(args: unknown) {
  pushEvent(...args, {
    isInteractive: false,
  })
}

export function useGTM() {
  return {
    sendEvent: sendGTMEvent,
    pushEvent: pushEvent,
    pushEventAuto: pushEventAuto,
  }
}
