'use client'

import { sendGTMEvent } from '@next/third-parties/google'
import { eventNames } from '~/constants/gtm'

export interface GTMUserData {
  isAnalyst: boolean | null
  isImpersonating: boolean | null
  isPublicAccess: boolean | null
  isSalesman: boolean | null
  userId: string | null
  userLanguage: string | null
  workspaceId: string | null
  primaryApp: string | null
  productId?: string
}

let currentUserData: GTMUserData | null = null

export function setGTMUserData(userData: GTMUserData | null) {
  currentUserData = userData
}

function isValid(eventName: string): boolean {
  return Boolean(eventName && typeof window !== 'undefined')
}

function formatEvent(eventName: string): string {
  return eventNames.includes(eventName) ? `custom:${eventName}` : eventName
}

function formatParams(actionParams?: unknown) {
  return actionParams
    ? { actionParams: Array.isArray(actionParams) ? actionParams : [actionParams] }
    : { actionParams: null }
}

export function pushEvent(eventName: string, actionParams?: unknown) {
  if (!isValid(eventName)) return

  sendGTMEvent({
    ...(currentUserData || {}),
    event: formatEvent(eventName),
    ...formatParams(actionParams),
    isInteractive: true,
    _clear: true,
  })
}

export function pushEventAuto(eventName: string, actionParams?: unknown) {
  if (!isValid(eventName)) return

  sendGTMEvent({
    ...(currentUserData || {}),
    event: formatEvent(eventName),
    ...formatParams(actionParams),
    isInteractive: false,
    _clear: true,
  })
}

export function useGTM() {
  return {
    userData: currentUserData,
    pushEvent,
    pushEventAuto,
    sendEvent: sendGTMEvent,
  }
}
