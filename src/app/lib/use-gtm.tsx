'use client'

/**
 * Clean GTM hook that works consistently with user data
 *
 * This hook provides the same functionality as the old pushEvent from helpers/gtm.js
 * but with proper React context and TypeScript support. It automatically includes
 * user data in all events when used within a GTMProvider.
 */

import { sendGTMEvent } from '@next/third-parties/google'
import { useContext } from 'react'
import { GTMContext } from './gtm-provider'

export function useGTM() {
  const context = useContext(GTMContext)

  if (!context) {
    // Fallback for components outside GTMProvider - provide basic functionality without user data
    return {
      userData: null,
      pushEvent: (eventName: string, actionParams?: unknown) => {
        if (!eventName || typeof window === 'undefined') return

        sendGTMEvent({
          event: eventName,
          ...(actionParams
            ? {
                actionParams: Array.isArray(actionParams) ? actionParams : [actionParams],
              }
            : { actionParams: null }),
          isInteractive: true,
          _clear: true,
        })
      },
      pushEventAuto: (eventName: string, actionParams?: unknown) => {
        if (!eventName || typeof window === 'undefined') return

        sendGTMEvent({
          event: eventName,
          ...(actionParams
            ? {
                actionParams: Array.isArray(actionParams) ? actionParams : [actionParams],
              }
            : { actionParams: null }),
          isInteractive: false,
          _clear: true,
        })
      },
      sendEvent: sendGTMEvent,
    }
  }

  return context
}
