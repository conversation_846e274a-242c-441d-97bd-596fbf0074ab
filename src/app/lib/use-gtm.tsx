'use client'

/**
 * Clean and elegant GTM implementation for /src/app directory
 *
 * This solves the chaos where:
 * - GTMClient sends user data via window.dataLayer.push() ✅
 * - useGTM pushEvent uses sendGTMEvent() but doesn't include user data ❌
 *
 * Now useGTM consistently includes user data like the old pushEvent from helpers/gtm.js
 */

import { sendGTMEvent } from '@next/third-parties/google'
import { useContext, createContext, type ReactNode } from 'react'
import { eventNames } from '~/constants/gtm'

interface GTMUserData {
  isAnalyst: boolean | null
  isImpersonating: boolean | null
  isPublicAccess: boolean | null
  isSalesman: boolean | null
  userId: string | null
  userLanguage: string | null
  workspaceId: string | null
  primaryApp: string | null
  productId?: string
}

interface GTMContextValue {
  userData: GTMUserData | null
  pushEvent: (eventName: string, actionParams?: unknown) => void
  pushEventAuto: (eventName: string, actionParams?: unknown) => void
  sendEvent: typeof sendGTMEvent
}

const GTMContext = createContext<GTMContextValue | null>(null)

export function GTMProvider({ children, userData }: { children: ReactNode; userData: GTMUserData | null }) {
  const pushEvent = (eventName: string, actionParams?: unknown) => {
    if (!eventName || typeof window === 'undefined' || !window.dataLayer) {
      return
    }

    // Use window.dataLayer.push directly like the old implementation to include user data
    window.dataLayer.push({
      ...(userData || {}),
      event: eventNames.includes(eventName) ? `custom:${eventName}` : eventName,
      ...(actionParams
        ? {
            actionParams: Array.isArray(actionParams) ? actionParams : [actionParams],
          }
        : { actionParams: null }),
      isInteractive: true,
      _clear: true,
    })
  }

  const pushEventAuto = (eventName: string, actionParams?: unknown) => {
    if (!eventName || typeof window === 'undefined' || !window.dataLayer) {
      return
    }

    // Use window.dataLayer.push directly like the old implementation to include user data
    window.dataLayer.push({
      ...(userData || {}),
      event: eventNames.includes(eventName) ? `custom:${eventName}` : eventName,
      ...(actionParams
        ? {
            actionParams: Array.isArray(actionParams) ? actionParams : [actionParams],
          }
        : { actionParams: null }),
      isInteractive: false,
      _clear: true,
    })
  }

  const contextValue: GTMContextValue = {
    userData,
    pushEvent,
    pushEventAuto,
    sendEvent: sendGTMEvent,
  }

  return <GTMContext.Provider value={contextValue}>{children}</GTMContext.Provider>
}

export function useGTM() {
  const context = useContext(GTMContext)

  if (!context) {
    // Fallback for components outside GTMProvider - provide basic functionality without user data
    return {
      userData: null,
      pushEvent: (eventName: string, actionParams?: unknown) => {
        if (!eventName || typeof window === 'undefined' || !window.dataLayer) return

        sendGTMEvent({
          event: eventNames.includes(eventName) ? `custom:${eventName}` : eventName,
          ...(actionParams
            ? {
                actionParams: Array.isArray(actionParams) ? actionParams : [actionParams],
              }
            : { actionParams: null }),
          isInteractive: true,
          _clear: true,
        })
      },
      pushEventAuto: (eventName: string, actionParams?: unknown) => {
        if (!eventName || typeof window === 'undefined' || !window.dataLayer) return

        sendGTMEvent({
          event: eventNames.includes(eventName) ? `custom:${eventName}` : eventName,
          ...(actionParams
            ? {
                actionParams: Array.isArray(actionParams) ? actionParams : [actionParams],
              }
            : { actionParams: null }),
          isInteractive: false,
          _clear: true,
        })
      },
      sendEvent: sendGTMEvent,
    }
  }

  return context
}
