'use client'

/**
 * Clean and elegant GTM implementation for /src/app directory
 *
 * This solves the chaos where:
 * - GTMClient sends user data via window.dataLayer.push() ✅
 * - useGTM pushEvent uses sendGTMEvent() but doesn't include user data ❌
 *
 * Now useGTM consistently includes user data like the old pushEvent from helpers/gtm.js
 */

import { sendGTMEvent } from '@next/third-parties/google'
import { useContext, createContext, type ReactNode } from 'react'
import { eventNames } from '~/constants/gtm'

interface GTMUserData {
  isAnalyst: boolean | null
  isImpersonating: boolean | null
  isPublicAccess: boolean | null
  isSalesman: boolean | null
  userId: string | null
  userLanguage: string | null
  workspaceId: string | null
  primaryApp: string | null
  productId?: string
}

interface GTMContextValue {
  userData: GTMUserData | null
  pushEvent: (eventName: string, actionParams?: unknown) => void
  pushEventAuto: (eventName: string, actionParams?: unknown) => void
  sendEvent: typeof sendGTMEvent
}

const GTMContext = createContext<GTMContextValue | null>(null)

// ============================================================================
// Helper functions to eliminate duplication and improve code clarity
// ============================================================================

/**
 * Validates if an event can be sent to GTM
 */
function isValidEvent(eventName: string): boolean {
  return Boolean(eventName && typeof window !== 'undefined' && window.dataLayer)
}

/**
 * Formats event name according to GTM conventions
 */
function formatEventName(eventName: string): string {
  return eventNames.includes(eventName) ? `custom:${eventName}` : eventName
}

/**
 * Formats action parameters for GTM events
 */
function formatActionParams(actionParams?: unknown) {
  return actionParams
    ? { actionParams: Array.isArray(actionParams) ? actionParams : [actionParams] }
    : { actionParams: null }
}

/**
 * Creates a complete GTM event data object
 */
function createEventData(
  userData: GTMUserData | null,
  eventName: string,
  actionParams?: unknown,
  isInteractive: boolean = true
) {
  return {
    ...(userData || {}),
    event: formatEventName(eventName),
    ...formatActionParams(actionParams),
    isInteractive,
    _clear: true,
  }
}

/**
 * Safely pushes data to GTM dataLayer
 */
function pushToDataLayer(eventData: Record<string, unknown>): void {
  window.dataLayer?.push(eventData)
}

/**
 * Creates a push event function with user data (for GTMProvider context)
 */
function createPushEventFunction(userData: GTMUserData | null, isInteractive: boolean) {
  return (eventName: string, actionParams?: unknown) => {
    if (!isValidEvent(eventName)) return

    const eventData = createEventData(userData, eventName, actionParams, isInteractive)
    pushToDataLayer(eventData)
  }
}

/**
 * Creates a fallback push event function without user data (for components outside GTMProvider)
 */
function createFallbackPushEventFunction(isInteractive: boolean) {
  return (eventName: string, actionParams?: unknown) => {
    if (!isValidEvent(eventName)) return

    sendGTMEvent({
      event: formatEventName(eventName),
      ...formatActionParams(actionParams),
      isInteractive,
      _clear: true,
    })
  }
}

// ============================================================================
// Main GTM Provider and Hook - Clean, DRY implementation
// ============================================================================

/**
 * GTM Provider component that provides GTM context with user data to child components
 */
export function GTMProvider({ children, userData }: { children: ReactNode; userData: GTMUserData | null }) {
  const contextValue: GTMContextValue = {
    userData,
    pushEvent: createPushEventFunction(userData, true),
    pushEventAuto: createPushEventFunction(userData, false),
    sendEvent: sendGTMEvent,
  }

  return <GTMContext.Provider value={contextValue}>{children}</GTMContext.Provider>
}

/**
 * Hook to access GTM functionality with consistent user data
 * Provides fallback functionality for components outside GTMProvider
 */
export function useGTM(): GTMContextValue {
  const context = useContext(GTMContext)

  if (!context) {
    // Fallback for components outside GTMProvider - provide basic functionality without user data
    return {
      userData: null,
      pushEvent: createFallbackPushEventFunction(true),
      pushEventAuto: createFallbackPushEventFunction(false),
      sendEvent: sendGTMEvent,
    }
  }

  return context
}
