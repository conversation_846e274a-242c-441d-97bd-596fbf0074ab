'use client'

import { sendGTMEvent } from '@next/third-parties/google'
import { useContext, createContext, type ReactNode } from 'react'
import { eventNames } from '~/constants/gtm'

interface GTMUserData {
  isAnalyst: boolean | null
  isImpersonating: boolean | null
  isPublicAccess: boolean | null
  isSalesman: boolean | null
  userId: string | null
  userLanguage: string | null
  workspaceId: string | null
  primaryApp: string | null
  productId?: string
}

interface GTMContextValue {
  userData: GTMUserData | null
  pushEvent: (eventName: string, actionParams?: unknown) => void
  pushEventAuto: (eventName: string, actionParams?: unknown) => void
  sendEvent: typeof sendGTMEvent
}

const GTMContext = createContext<GTMContextValue | null>(null)

function isValid(eventName: string): boolean {
  return Boolean(eventName && typeof window !== 'undefined')
}

function formatEvent(eventName: string): string {
  return eventNames.includes(eventName) ? `custom:${eventName}` : eventName
}

function formatParams(actionParams?: unknown) {
  return actionParams
    ? { actionParams: Array.isArray(actionParams) ? actionParams : [actionParams] }
    : { actionParams: null }
}

function createEventData(
  userData: GTMUserData | null,
  eventName: string,
  actionParams?: unknown,
  isInteractive: boolean = true
) {
  return {
    ...(userData || {}),
    event: formatEvent(eventName),
    ...formatParams(actionParams),
    isInteractive,
    _clear: true,
  }
}

function createPushEvent(userData: GTMUserData | null, isInteractive: boolean) {
  return (eventName: string, actionParams?: unknown) => {
    if (!isValid(eventName)) return

    const eventData = createEventData(userData, eventName, actionParams, isInteractive)
    sendGTMEvent(eventData)
  }
}

function createFallbackPushEvent(isInteractive: boolean) {
  return (eventName: string, actionParams?: unknown) => {
    if (!isValid(eventName)) return

    sendGTMEvent({
      event: formatEvent(eventName),
      ...formatParams(actionParams),
      isInteractive,
      _clear: true,
    })
  }
}

export function GTMProvider({ children, userData }: { children: ReactNode; userData: GTMUserData | null }) {
  const contextValue: GTMContextValue = {
    userData,
    pushEvent: createPushEvent(userData, true),
    pushEventAuto: createPushEvent(userData, false),
    sendEvent: sendGTMEvent,
  }

  return <GTMContext.Provider value={contextValue}>{children}</GTMContext.Provider>
}

export function useGTM(): GTMContextValue {
  const context = useContext(GTMContext)

  if (!context) {
    return {
      userData: null,
      pushEvent: createFallbackPushEvent(true),
      pushEventAuto: createFallbackPushEvent(false),
      sendEvent: sendGTMEvent,
    }
  }

  return context
}
