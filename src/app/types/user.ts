export interface UserProps {
  uuid: string
  email: string
  is_active: boolean
  is_2fa_walled: boolean
  name: string
  phone: string
  company_name: string
  app_language: string
  currency: number
  frontend_storage: {
    newsroom: {
      view?: 'compact' | 'default'
    }
    appFeatures: {
      emailing: boolean
      crisisCommunication: boolean
    }
    feedListView: {
      visibleAVE: boolean
      visibleCPP: boolean
      visibleGRP: boolean
      visibleOTS: boolean
      visibleArea: boolean
      visiblePerex: boolean
      visibleReach: boolean
      visibleDuration: boolean
      visibleMediaView: boolean
      visibleDailyUsers: boolean
      visibleReadership: boolean
      visibleSocialView: boolean
      visibleSoldAmount: boolean
      visiblePressAmount: boolean
      visibleSocialEmoji: boolean
      visibleListenership: boolean
      visibleMonthlyUsers: boolean
      visibleSocialShares: boolean
      visibleAdjustedReach: boolean
      visibleEngagementRate: boolean
      visibleFrontpagePromo: boolean
      visibleInfluenceScore: boolean
      visibleSocialAudience: boolean
      visibleArticleLanguage: boolean
      visibleMonthlySessions: boolean
      visiblePrintAdPriceFullPage: boolean
      visibleArticleMetrics: boolean
    }
    dashboard_selected: number | null
  }
  is_salesman: boolean
  is_publisher: boolean
  is_pdf_parser: boolean
  is_analyst: boolean
  feature_requests: boolean[]
  partner_code: string
  auth_tokens_limit: number
  logo_image_url: string
  is_public_access: boolean
}
