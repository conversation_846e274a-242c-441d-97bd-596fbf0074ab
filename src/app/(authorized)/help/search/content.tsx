'use client'

import RulesDistance from '~/components/help/search/Content/RulesDistance'
import RulesOperators from '~/components/help/search/Content/RulesOperators'
import RulesPhrase from '~/components/help/search/Content/RulesPhrase'
import RulesSearch from '~/components/help/search/Content/RulesSearch'
import Box from '~/components/misc/Mntr/Box'
import Flex from '~/components/misc/Mntr/Flex'
import { useGTM } from '~/app/lib/use-gtm'
import events from '~/constants/gtm'

export default function HelpContent({ appName }: { appName: string }) {  
  pushEvent(events.ANALYTICS_LOADED, { test: 'interactive' })}
  HELP_LOADED
  return (
    <Flex column gap={'18px'}>
      <Box>
        <RulesSearch appName={appName} />
      </Box>
      <Box>
        <RulesPhrase appName={appName} />
      </Box>
      <Box>
        <RulesDistance appName={appName} />
      </Box>
      <Box>
        <RulesOperators appName={appName} />
      </Box>
    </Flex>
  )
}
