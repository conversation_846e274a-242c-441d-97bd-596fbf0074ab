'use client'

import Link from 'next/link'

import RulesDistance from '~/components/help/search/Content/RulesDistance'
import RulesOperators from '~/components/help/search/Content/RulesOperators'
import RulesPhrase from '~/components/help/search/Content/RulesPhrase'
import RulesSearch from '~/components/help/search/Content/RulesSearch'
import Box from '~/components/misc/Mntr/Box'
import Flex from '~/components/misc/Mntr/Flex'

export default function HelpContent({ appName }: { appName: string }) {
  return (
    <Flex column gap={'18px'}>
      <Link href="/testing-route">
        Go to <code>/testing-route</code>
      </Link>

      <Box>
        <RulesSearch appName={appName} />
      </Box>
      <Box>
        <RulesPhrase appName={appName} />
      </Box>
      <Box>
        <RulesDistance appName={appName} />
      </Box>
      <Box>
        <RulesOperators appName={appName} />
      </Box>
    </Flex>
  )
}
