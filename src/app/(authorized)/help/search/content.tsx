'use client'

import RulesDistance from '~/components/help/search/Content/RulesDistance'
import RulesOperators from '~/components/help/search/Content/RulesOperators'
import RulesPhrase from '~/components/help/search/Content/RulesPhrase'
import RulesSearch from '~/components/help/search/Content/RulesSearch'
import Box from '~/components/misc/Mntr/Box'
import Flex from '~/components/misc/Mntr/Flex'
import { useGTM } from '~/app/lib/use-gtm'
import events from '~/constants/gtm'
import { useEffect } from 'react'

export default function HelpContent({ appName }: { appName: string }) {
  const { pushEventAuto, hasUserData } = useGTM()

  useEffect(() => {
    if (hasUserData()) {
      pushEventAuto(events.HELP_LOADED)
    }
  }, [hasUserData, pushEventAuto])

  useEffect(() => {
    const interval = setInterval(() => {
      if (hasUserData()) {
        pushEventAuto(events.HELP_LOADED)
        clearInterval(interval)
      }
    }, 100)

    return () => clearInterval(interval)
  }, [hasUserData, pushEventAuto])



  return (
    <Flex column gap={'18px'}>
      <Box>
        <RulesSearch appName={appName} />
      </Box>
      <Box>
        <RulesPhrase appName={appName} />
      </Box>
      <Box>
        <RulesDistance appName={appName} />
      </Box>
      <Box>
        <RulesOperators appName={appName} />
      </Box>
    </Flex>
  )
}
