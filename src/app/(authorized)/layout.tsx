import { redirect } from 'next/navigation'
import { getThemeCookie } from '~/app/actions/app-cookies'
import { getLang } from '~/app/actions/i18n'
import { getInit } from '~/app/actions/init'
import { isImpersonating, unimpersonate } from '~/app/actions/user'
import { switchWorkspace } from '~/app/actions/workspace'
import Header from '~/app/components/header'
import { Layout } from '~/app/components/sidebar-layout'
import { getPermissions } from '~/app/lib/get-permissions'
import { getSessionAndSettings } from '~/app/lib/get-session-settings'
import { getTheme } from '~/app/lib/get-theme'
import { GTMProvider } from '~/app/lib/gtm-provider'
import { ThemeProvider } from '~/app/lib/theme-provider'
import SidebarNavigation from '~/components/layout/Sidebar/SidebarNavigation'
import '~/styles/material-symbols.css'

export default async function AuthorizedLayout({ children }: { children: React.ReactNode }) {
  const { isAuth, ...data } = await getInit()

  if (!isAuth) {
    // TODO: render login
    redirect('/')
  }

  const { enums, user, workspace, workspaces } = await data
  const { frontend_storage } = await user
  const { custom_theme, limits, logo_image_url, permissions } = await workspace
  const { appSettings } = await getSessionAndSettings()
  const darkModeCookie = await getThemeCookie()
  const theme = await getTheme(logo_image_url, custom_theme)
  const lang = await getLang()
  const permissionsWithEnums = getPermissions(permissions, enums.workspaces.app_permission)

  return (
    <ThemeProvider theme={theme}>
      <GTMProvider
        user={user}
        workspace={workspace}
        appSettings={appSettings}
        lang={lang}
        isImpersonating={await isImpersonating()}
      >
        <Header
        appearanceCookie={darkModeCookie}
        appSettings={appSettings}
        appLanguage={lang}
        isImpersonating={await isImpersonating()}
        isLoaded={!!data}
        isMobileSearchVisible={false} // TODO Search
        permissions={permissionsWithEnums}
        // setIsMobileSearchVisible={} // TODO Search
        switchWorkspace={switchWorkspace}
        unimpersonate={unimpersonate}
        user={user}
        workspace={workspace}
        workspaces={workspaces}
      />
      <Layout>
        <SidebarNavigation
          appFeatures={frontend_storage.appFeatures}
          appLanguage={lang}
          appSettings={appSettings}
          hasIframeEmbedUrl={!!workspace.iframe_embed_url}
          limits={limits}
          permissions={permissionsWithEnums}
          user={user}
        />
        {children}
      </Layout>
      </GTMProvider>
    </ThemeProvider>
  )
}
