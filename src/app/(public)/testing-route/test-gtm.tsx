'use client'

import { useGTM } from '~/app/lib/use-gtm'
import events from '~/constants/gtm'

export default function TestGTMButton() {
  const { pushEvent, pushEventAuto, userData } = useGTM()

  return (
    <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', flexDirection: 'column' }}>
      <div>
        <strong>User Data Available:</strong> {userData ? 'Yes' : 'No'}
        {userData && (
          <div style={{ fontSize: '12px', marginTop: '5px' }}>
            User ID: {userData.userId}, Workspace: {userData.workspaceId}
          </div>
        )}
      </div>

      <div style={{ display: 'flex', gap: '10px' }}>
        <button onClick={() => pushEvent(events.ANALYTICS_LOADED, { test: 'interactive' })}>
          Send Interactive Event
        </button>

        <button onClick={() => pushEventAuto(events.ANALYTICS_LOADED, { test: 'auto' })}>
          Send Auto Event
        </button>
      </div>
    </div>
  )
}
