'use client'

import { useGTM } from '~/app/lib/use-gtm'
import events from '~/constants/gtm'

export default function TestGTMButton() {
  const { pushEvent, pushEventAuto } = useGTM()

  return (
    <div style={{ display: 'flex', gap: '10px' }}>
      <button onClick={() => pushEvent(events.ANALYTICS_LOADED, { test: 'interactive' })}>
        Send Interactive Event (with user data)
      </button>
      
      <button onClick={() => pushEventAuto(events.ANALYTICS_LOADED, { test: 'auto' })}>
        Send Auto Event (with user data)
      </button>
    </div>
  )
}
