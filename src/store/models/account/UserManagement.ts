import identityFn from 'lodash/identity'
import { getEnv, Instance, types } from 'mobx-state-tree'

const UserManagement = types
  .model('UserManagement', {
    users: types.frozen(), // User model will be used after unification in next step
  })
  .views((self) => ({
    get apiClient() {
      return getEnv(self).apiClient
    },
  }))
  .actions((self) => ({
    getUsers() {
      return (
        self.apiClient
          .get(`/workspaces/users/`)
          // @ts-expect-error: res
          .then((res) => {
            self.users = res
          })
          .catch(identityFn)
      )
    },
    // @ts-expect-error: res
    setUsers(data) {
      return (
        self.apiClient
          .post(`/workspaces/users/`, { data })
          // @ts-expect-error: res
          .then((res) => {
            const updatedUsers = [
              // @ts-expect-error: res
              ...res.filter((u) => !self.users.some((user) => user.email === u.email)),
              // @ts-expect-error: res
              ...self.users.map((user) => {
                // @ts-expect-error: res
                const updated = res.find((u) => u.email === user.email)
                return updated ? { ...user, ...updated } : user
              }),
            ]

            self.users = updatedUsers
          })
          .catch(identityFn)
      )
    },
    removeUsers(emails: string) {
      return self.apiClient
        .del(`/workspaces/users/`, { data: { emails }, displayNotifications: false })
        .then(() => {
          // @ts-expect-error: res
          const updatedUsers = self.users.filter((user) => !emails.includes(user.email))

          self.users = updatedUsers
        })
        .catch(identityFn)
    },
  }))

export default UserManagement
export interface IUserManagement extends Instance<typeof UserManagement> {}
