import { types } from 'mobx-state-tree'

const FeedListViewStore = types
  .model('FeedListViewStore', {
    visibleMediaView: true,
    visiblePerex: true,
    visibleFrontpagePromo: true,
    visibleInfluenceScore: true,
    visibleEngagementRate: true,
    visibleDailyUsers: true,
    visibleMonthlyUsers: false,
    visibleMonthlySessions: false,
    visibleReach: true,
    visibleAdjustedReach: true,
    visiblePressAmount: true,
    visibleSoldAmount: true,
    visibleReadership: true,
    visibleAVE: true,
    visibleGRP: true,
    visibleOTS: true,
    visibleCPP: true,
    visibleSocialView: true,
    visibleSocialAudience: true,
    visibleSocialShares: true,
    visibleSocialEmoji: true,
    visibleArea: true,
    visibleListenership: true,
    visibleDuration: true,
    visiblePrintAdPriceFullPage: true,
    visibleArticleLanguage: true,
    visibleArticleMetrics: true,
  })
  .actions((self) => ({
    setParam(param, value) {
      self[param] = value
    },
  }))

export default FeedListViewStore
