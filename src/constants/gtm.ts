const events = {
  ANALYTICS_LOADED: 'analytics loaded',
  ANALY<PERSON>CS_SUMBITTED: 'analytics submitted',

  ARTICLE_ADDED_TO_EXPORT: 'article added to export',
  ARTICLE_TAG_ADDED: 'article tag added',
  ARTICLE_NOTE_ADDED: 'article note added',
  ARTICLE_SENTIMENT_ADDED: 'article detail sentiment added',

  ARTICLE_DETAIL_ADDED_TO_EXPORT: 'article detail added to export',
  ARTICLE_DETAIL_TAG_ADDED: 'article detail tag added',
  ARTICLE_DETAIL_NOTE_ADDED: 'article detail note added',
  ARTICLE_DETAIL_LOADED: 'article detail loaded',
  ARTICLE_DETAIL_EXPORTED: 'article detail exported',
  ARTICLE_DETAIL_CLOSED: 'article detail closed',
  ARTICLE_DETAIL_TRANSLATE_ACTIVATED: 'article detail translate activated',
  ARTICLE_DETAIL_TRANSLATED: 'article detail translated',
  ARTICLE_DETAIL_OPENED_EXTERNALLY: 'article detail opened externally',
  ARTICLE_DETAIL_PROBLEM_REPORTED: 'article detail problem reported',
  ARTICLE_DETAIL_SENTIMENT_ADDED: 'article detail sentiment added',

  ARTICLES_LOADED: 'articles loaded',
  ARTICLES_TAG_ADDED: 'articles tag added',
  ARTICLES_ADDED_TO_EXPORT: 'articles added to export',

  CRISIS_COOMUNICTION_LOADED: 'crisis communication loaded',
  CRISIS_COOMUNICTION_DETAIL_LOADED: 'crisis communication detail loaded',
  CRISIS_COOMUNICTION_DETAIL_CLOSED: 'crisis communication detail closed',

  DASHBOARD_LOADED: 'dashboard loaded',
  DASHBOARD_CHANGED: 'dashboard changed',
  DASHBOARD_WIDGET_ADDED: 'dashboard widget added',
  DASHBOARD_WIDGET_EDITED: 'dashboard widget edited',
  DASHBOARD_WIDGET_REMOVED: 'dashboard widget removed',

  EMAILING_LOADED: 'emailing loaded',
  EMAILING_CAMPAIGN_LOADED: 'emailing campaign loaded',
  EMAILING_CAMPAIGN_CREATED: 'emailing campaign created',
  EMAILING_CAMPAIGN_EDITED: 'emailing campaign edited',
  EMAILING_CAMPAIGN_REMOVED: 'emailing campaign removed',
  EMAILING_EMAIL_CREATED: 'emailing email created',
  EMAILING_EMAIL_EDITED: 'emailing email edited',
  EMAILING_EMAIL_SENT: 'emailing email sent',
  EMAILING_EMAIL_SCHEDULED: 'emailing email scheduled',
  EMAILING_EMAIL_DRAFT: 'emailing email set to draft',
  EMAILING_EMAIL_DELETED: 'emailing email removed',
  EMAILING_SETTINGS_LOADED: 'emailing settings loaded',

  EMAILING_EMAIL_CREATE: 'emailing new email opened',
  EMAILING_EMAIL_CREATE_METHOD: 'emailing new email method',
  EMAILING_EMAIL_CREATE_ARTICLE: 'emailing new email article',
  EMAILING_EMAIL_CREATE_INSTRUCTIONS: 'emailing new email instructions',
  EMAILING_EMAIL_CREATE_INSTRUCTIONS_HELP: 'emailing new email instructions help toggled',
  EMAILING_EMAIL_CREATE_PREVIEW: 'emailing new email preview submit',
  EMAILING_EMAIL_CREATE_PREVIEW_REGENERATE: 'emailing new email preview regenerate',
  EMAILING_EMAIL_CREATE_PREVIEW_PLACEHOLDERS: 'emailing new email preview replace placeholders',

  ERROR_MESSAGE_SHOWN: 'error message shown',

  EXPORT_LOADED: 'export loaded',
  EXPORT_REMOVED: 'export removed',
  EXPORT_SENT: 'export sent',
  EXPORT_SENTIMENT_ADDED: 'export sentiment added',
  EXPORT_NOTE_ADDED: 'export note added',
  EXPORT_TAG_ADDED: 'export tag added',
  EXPORT_SELECTED_TAG_ADDED: 'export selected tag added',
  EXPORT_SELECTED_SENTIMENT_ADDED: 'export selected sentiment added',

  EXPORT_DETAIL_SENTIMENT_ADDED: 'export detail sentiment added',
  EXPORT_DETAIL_NOTE_ADDED: 'export detail note added',
  EXPORT_DETAIL_TAG_ADDED: 'export detail tag added',
  
  HELP_LOADED: 'help page loaded',

  LANGUAGE_CHANGED: 'language changed',

  MEDIALIST_LOADED: 'medialist loaded',
  MEDIALIST_AUTHOR_CREATED: 'medialist author created',
  MEDIALIST_AUTHORS_ADDED_TO_LIST: 'medialist authors added to list',
  MEDIALIST_AUTHORS_ADDED_TAG: 'medialist authors added tag',

  NEWSROOM_LOADED: 'newsroom loaded',
  NEWSROOM_CHANGED: 'newsroom changed',
  NEWSROOM_POST_CREATE: 'newsroom new post opened',
  NEWSROOM_POST_CREATE_METHOD: 'newsroom new post method',
  NEWSROOM_POST_CREATE_TEMPLATE: 'newsroom new post template',
  NEWSROOM_POST_CREATE_MESSAGE: 'newsroom new post main message',
  NEWSROOM_POST_CREATE_TITLE: 'newsroom new post title',
  NEWSROOM_POST_CREATE_PREVIEW: 'newsroom new post preview submit',
  NEWSROOM_POST_CREATE_PREVIEW_REGENERATE: 'newsroom new post preview regenerate',
  NEWSROOM_POST_CREATE_PREVIEW_PLACEHOLDERS: 'newsroom new post preview replace placeholders',

  NEWSROOM_POST_CREATED: 'newsroom post created',
  NEWSROOM_POST_EDITED: 'newsroom post edited',
  NEWSROOM_POST_REMOVED: 'newsroom post removed',

  PROMO_MODAL: 'promo modal opened',
  PROMO_CATEGORY: 'promo category clicked',

  REPORTS_LOADED: 'reports loaded',
  REPORT_CREATED: 'report created',
  REPORT_EDITED: 'report edited',
  REPORT_REMOVED: 'report removed',

  SEARCH_OPENED: 'search opened',
  SEARCH_SUBMITTED: 'search submitted',
  SEARCH_IN_AUTHOR_FEED: 'search in author feed',

  SETTINGS_LOADED: 'settings loaded',
  SETTINGS_EDITED: 'settings edited',

  TOPICS_LOADED: 'topics loaded',
  TOPICS_ADDED: 'topics added',
  TOPICS_EDITED: 'topics edited',
  TOPICS_REMOVED: 'topics removed',

  TOPICS_KEYWORD_ADDED: 'topics keyword added',
  TOPICS_KEYWORD_EDITED: 'topics keyword edited',
  TOPICS_KEYWORD_REMOVED: 'topics keyword removed',

  TOPICS_REPORT_CREATED: 'topics report created',
  TOPICS_REPORT_EDITED: 'topics report edited',
  TOPICS_REPORT_REMOVED: 'topics report removed',
}

export const method = {
  MANUAL: 'manual',
  GENERATIVE: 'ai',
}

export const INTERCOM_BLACKLISTED_PATHNAMES = [
  '/dashboard/shared/[dashboardKey]',
  //
]

export const INTERCOM_HIDDEN_PATHNAMES = [
  '/brand-tracking',
  //
]

export const FALLBACK_FAKE_ID = 'FALLBACK_FAKE_ID'
export const MEDIABOARD_CONTAINER = 'GTM-TBQ8WDZD' // shared www./app container, managed by Forecom
export const MEDIABOARD_DEVS_CONTAINER = 'GTM-NWQRSVD' // app only, managed by us

export const eventNames = Object.values(events)
export default events
