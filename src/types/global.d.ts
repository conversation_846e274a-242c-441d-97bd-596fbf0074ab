declare global {
  interface Window {
    // Code coverage data collected during Playwright tests
    // Injected by Istanbul instrumentation
    __coverage__: Record<string, unknown>

    // Intercom workspace ID
    // Injected by Intercom via GTM
    APP_ID: string

    // Function to collect code coverage data during Playwright tests
    // Injected by Istanbul instrumentation
    collectIstanbulCoverage: (coverageJSON: string) => void

    // Flag indicating if app is running in Expo
    // Injected by us in Expo
    isMobileApp: boolean

    // Interface for communicating with React Native WebView
    // Injected when running in Expo mobile environment
    ReactNativeWebView?: {
      postMessage: (message: string) => void
    }
    dataLayer: object[] | undefined
  }
}

export {}
