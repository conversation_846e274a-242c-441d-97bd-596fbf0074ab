import { t } from '@lingui/core/macro'
import { useRouter } from 'next/router'
import { useEffect } from 'react'
import { GTMContainer } from '~/app/lib/gtm-container'
import Head from '~/components/misc/Head/Head'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import SignUpCompletion from '~/components/page/auth/SignUpCompletion/SignUpCompletion'
import { FooterContainer } from '~/components/page/auth/styled/StyledAuth'
import { MEDIABOARD_CONTAINER } from '~/constants/gtm'
import { ObservedFC, observer } from '~/helpers/msts'
import { routerReplace } from '~/helpers/router'

const PageSignUpCompletion: ObservedFC = ({
  appStore: {
    isMobileApp,
    account: { user, signUpCompletion },
  },
}) => {
  const emptyEmail = !signUpCompletion?.email
  const { query } = useRouter()

  useEffect(() => {
    if (emptyEmail) {
      routerReplace({ pathname: '/sign-up', query })
    }
  }, [emptyEmail, query])

  return (
    <>
      <Head title={`${t`Sign Up`} - ${t`Completion`}`} />
      <SignUpCompletion
        backLink="/sign-up"
        handleSubmit={user.signUpSecond}
        footer={
          <FooterContainer px={3}>
            {
              <MntrButton
                fullWidth
                rounded
                /* @ts-expect-error TODO fix the type of href */
                href={{ pathname: '/', query }}
                label={t`Have an account? Sign in`}
                bg="tertiary"
              />
            }
          </FooterContainer>
        }
      />
      {process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER && !isMobileApp && (
        <GTMContainer gtmId={MEDIABOARD_CONTAINER} withNoScript />
      )}
    </>
  )
}

export default observer(PageSignUpCompletion)
