import { t } from '@lingui/core/macro'
import { GTMContainer } from '~/app/lib/gtm-container'
import Head from '~/components/misc/Head/Head'
import Content from '~/components/page/auth/SignUp/SignUp'
import { MEDIABOARD_CONTAINER } from '~/constants/gtm'
import { ObservedFC, observer } from '~/helpers/msts'

const SignUp: ObservedFC = ({ appStore: { isMobileApp } }) => {
  return (
    <>
      <Head title={t`Sign Up`} />
      <Content />
      {process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER && !isMobileApp && (
        <GTMContainer gtmId={MEDIABOARD_CONTAINER} withNoScript />
      )}
    </>
  )
}

export default observer(SignUp)
