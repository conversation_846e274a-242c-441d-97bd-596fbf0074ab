import { Trans } from '@lingui/react/macro'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import * as format from '~/helpers/formatNumber'
import { observer } from '~/helpers/mst'

const DataButton = (props) => {
  return (
    <MntrButton
      isChip
      disabled
      bg="metadata"
      iconBg="transparent"
      iconColor="currentColor"
      {...props}
    />
  )
}

const AuthorMediaData = ({
  appStore: { account },
  data: {
    daily_ru,
    monthly_ru,
    monthly_sessions,
    press_amount,
    sold_amount,
    readership,
    listenership,
    publication_frequency,
    print_ad_price_full_page,
  },
}) => {
  return (
    <Box px="16px" mb="16px">
      <MntrMenuHeading noPadding label={<Trans>Media data</Trans>} />

      <Box ml="-4px">
        {daily_ru > 0 && (
          <DataButton
            icon="data_usage"
            label={
              <>
                {format.humanizeNumber(daily_ru)} <Trans>daily users</Trans>
              </>
            }
          />
        )}
        {monthly_ru > 0 && (
          <DataButton
            icon="data_usage"
            label={
              <>
                {format.humanizeNumber(monthly_ru)} <Trans>monthly users</Trans>
              </>
            }
          />
        )}
        {monthly_sessions > 0 && (
          <DataButton
            icon="data_usage"
            label={
              <>
                {format.humanizeNumber(monthly_sessions)} <Trans>monthly sessions</Trans>
              </>
            }
          />
        )}
        {press_amount > 0 && (
          <DataButton
            icon="data_usage"
            label={
              <>
                <Trans>Distribution amount</Trans>: {format.formatPrintAmount(press_amount)}
              </>
            }
          />
        )}
        {sold_amount > 0 && (
          <DataButton
            icon="data_usage"
            label={
              <>
                <Trans>Sold amount (print+digital)</Trans>: {format.formatPrintAmount(sold_amount)}
              </>
            }
          />
        )}
        {readership > 0 && (
          <DataButton
            icon="data_usage"
            label={
              <>
                <Trans>Readership</Trans>: {format.humanizeNumber(readership)}
              </>
            }
          />
        )}
        {publication_frequency && <DataButton icon="date_range" label={publication_frequency} />}
        {listenership > 0 && (
          <DataButton
            icon="radio"
            label={
              <>
                <Trans>Daily listenership</Trans>: {format.humanizeNumber(listenership)}
              </>
            }
          />
        )}
        {print_ad_price_full_page > 0 && (
          <DataButton
            icon="request_page"
            label={
              <Flex flexDirection="row">
                {format.formatCurrency(print_ad_price_full_page, account.user.currency.text)}/
                <Text textTransform="lowercase">
                  <Trans>Page</Trans>
                </Text>
              </Flex>
            }
          />
        )}
      </Box>
    </Box>
  )
}

export default observer(AuthorMediaData)
