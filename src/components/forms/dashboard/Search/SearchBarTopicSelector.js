import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import get from 'lodash/get'
import { useRouter } from 'next/router'
import MntrSelectAdapter from '~/components/forms/adapters/MntrSelectAdapter/MntrSelectAdapter'
import { Box } from '~/components/misc/Mntr'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import events from '~/constants/gtm'
import cutString from '~/helpers/cutString'
import { useGTM } from '~/app/lib/use-gtm'
import { observer } from '~/helpers/mst'
import { routerPush } from '~/helpers/router'

const SearchBarTopicSelector = ({
  appStore: { topics, filter, searchBar, account },
  topicTitleLimit,
}) => {
  const { pathname, query } = useRouter()
  const { pushEvent } = useGTM()
  const list = []
  const topicsArr = get(filter, 'labels.topic_monitors') || []
  if (account.workspace?.permissions.archive_feed.can_read) {
    list.push({ id: 'monitora::archive', data: { name: t`Archive`.toUpperCase() } })
  }

  if (account.workspace?.permissions.tvr_feed.can_read) {
    list.push({ id: 'monitora::crisis-communication', data: { name: t`Crisis communication` } })
  }

  let value = 'monitora::archive'

  if (topicsArr.length === 1) {
    value = topicsArr[0].value
  }

  if (topicsArr.length > 1) {
    list.push({ id: 'monitora::topics', data: { name: t`Search in topics`.toUpperCase() } })
    value = 'monitora::topics'
  }
  if (pathname === '/crisis-communication') {
    value = 'monitora::crisis-communication'
  }

  if (pathname === '/author/[authorId]') {
    list.push({ id: 'monitora::author', data: { name: t`Search in author`.toUpperCase() } })
    value = 'monitora::author'
  }

  let routeUrl = pathname

  if (pathname === '/crisis-communication' || pathname === '/author/[authorId]') {
    routeUrl = '/'
  }
  const selectorArr = [...list, ...topics.list]

  return (
    <div>
      <Box mx="16px" mb={1}>
        <MntrSelectAdapter
          meta={{}}
          label={
            <Box mb={'-10px'}>
              <MntrMenuHeading label={<Trans>Search in</Trans>} fontSize={12} noPadding />
            </Box>
          }
          name="searchBarTopicSelector"
          items={selectorArr.map((item) => {
            const itemProps = {}
            switch (item.id) {
              case 'monitora::archive':
                itemProps.leftIcon = 'find_in_page'
                break

              case 'monitora::crisis-communication':
                itemProps.leftIcon = 'campaign'
                break

              case 'monitora::author':
                itemProps.leftIcon = 'account_circle'
                break

              case 'monitora::topics':
                itemProps.leftIcon = 'all'
                break

              default:
                itemProps.topicId = item.id
            }

            return {
              value: item.id,
              label: cutString(item.data.name, topicTitleLimit || 40, true),
              ...itemProps,
            }
          })}
          onChange={(value) => {
            pushEvent(events.SEARCH_SUBMITTED, { topicsselector_used: true })

            switch (value) {
              case 'monitora::archive':
                routerPush(
                  `${routeUrl}?${filter.urlWithParam({
                    topic_monitors: undefined,
                    query: searchBar.lastSearchQuery,
                  })}`,
                )
                break

              case 'monitora::crisis-communication':
                routerPush(`/crisis-communication?query=${searchBar.lastSearchQuery}`)
                break

              case 'monitora::author':
                routerPush(
                  `/author/${query.authorId}?${filter.urlWithParam({
                    query: searchBar.lastSearchQuery,
                  })}`,
                )
                break

              case 'monitora::topics':
                routerPush(
                  `${routeUrl}?${filter.urlWithParam({
                    query: searchBar.lastSearchQuery,
                  })}`,
                )
                break

              default:
                routerPush(
                  `${routeUrl}?${filter.urlWithParam({
                    topic_monitors: value,
                    query: searchBar.lastSearchQuery,
                  })}`,
                )
            }
          }}
          input={{ value }}
        />
      </Box>
    </div>
  )
}

export default observer(SearchBarTopicSelector)
