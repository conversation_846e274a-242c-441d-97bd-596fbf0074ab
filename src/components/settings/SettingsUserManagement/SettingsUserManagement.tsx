import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import chunk from 'lodash/chunk'
import each from 'lodash/each'
import { useEffect, useState } from 'react'
import styled, { css } from 'styled-components'
import MntrCheckboxAdapter from '~/components/forms/adapters/MntrCheckboxAdapter/MntrCheckboxAdapter'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { Table, TableBody, Td, Th, Tr } from '~/components/misc/MntrTable/MntrTable'
import Pagination from '~/components/misc/Pagination/Pagination'
import ResponsiveTable from '~/components/misc/ResponsiveTable/ResponsiveTable'
import getUserAttributes from '~/components/staff/admin/user/getUserAttributes'
import RemoveUsers from '~/components/staff/admin/workspace/UsersTable/RemoveUsers'
import formatDate from '~/helpers/date/format'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { observer } from '~/helpers/mst'
import { ObservedFC } from '~/helpers/msts'
import { IUserRoleArrItem } from '~/store/models/account/enums/UserRoleArrItem'
import { IStatusArrItem } from '~/store/models/account/enums/workspaceArticles/StatusArrItem'
import AddUsers from './AddUsers'
import UpdateRole from './UpdateRole'
import withModalUpdateRole, { ROLE_CUSTOM } from './withModalUpdateRole'

interface IStyledTableProps {
  isTablet?: boolean
}

interface ICheckedItem {
  page: number
  key: number
  email: string
  permissions: IPermissionItem
}

interface IUser {
  // will be unified with User in store in next step
  email: string
  is_active: boolean
  last_user_workspace_activity: string | null
  is_analyst: boolean
  is_pdf_parser: boolean
  is_publisher: boolean
  is_salesman: boolean
  permissions: IPermissionItem
  verification_status: number
}

export interface IPermissionItem extends Record<string, number | null> {}

const StyledTable = styled(Table)<IStyledTableProps>`
  border: 1px solid ${({ theme }) => theme.paper.border};
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;

  ${({ isTablet }) =>
    isTablet &&
    css`
      table-layout: auto;
    `}
`

const Header = styled(Tr)`
  height: 60px !important;
  background: ${({ theme }) => theme.table.backgroundDark};
`

const Row = styled(Tr)`
  height: 55px !important;
`

const PaginationWrapper = styled(Flex)`
  text-align: center;
  justify-content: center;
`

const SettingsUserManagement: ObservedFC = ({
  appStore: {
    account: { enums, user: userStore, userManagement, workspace },
    viewport: { isTablet },
    notification,
  },
}) => {
  const canWrite = workspace.permissions.user_management.can_write
  const itemsCountPerPage = 20
  const [page, setPage] = useState(1)
  const [checked, setChecked] = useState<ICheckedItem[]>([])
  const [data, setData] = useState<IUser[][]>(chunk(userManagement.users, itemsCountPerPage))

  const defaultPermissions: IPermissionItem = Object.fromEntries(
    Object.keys(enums.workspaces.app_permissions).map((key) => [key, null]),
  )

  useEffect(() => {
    setData(chunk(userManagement.users, itemsCountPerPage))
  }, [userManagement.users])

  return (
    <Box color="black">
      <MntrPaper id="user-management">
        <MntrPaperToolbar bg="primary" icon={'shield_person'} title={t`User management`} />
        <Flex column p={3} gap={2}>
          <Flex justifyContent="space-between">
            <Box width={'300px'}>
              {/* @ts-expect-error: hovna */}
              <MntrTextFieldAdapter
                meta={{}}
                name="search"
                icon="search"
                onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                  const newList = userManagement.users.filter(
                    (user: IUser) =>
                      user.email &&
                      user.email.toLowerCase().includes(event.currentTarget.value.toLowerCase()),
                  )

                  setData(chunk(newList, itemsCountPerPage))
                }}
                placeholder={t`Search users`}
              />
            </Box>

            {canWrite && (
              <Flex>
                {checked.length === 0 && (
                  <Box>
                    <AddUsers
                      initialValues={{
                        permissions: defaultPermissions,
                      }}
                      onSubmit={(model) => {
                        const data = {
                          emails: model.emails,
                          permissions: {
                            ...(model.permissions || {}),
                            user_role: model.role,
                          },
                        }

                        return userManagement.setUsers(data, true)
                      }}
                    />
                  </Box>
                )}
                {checked.length > 0 && (
                  <Flex gap={1}>
                    <Box>
                      <UpdateRole
                        initialValues={{
                          permissions: defaultPermissions,
                          emails: checked.map((val) => val.email).join(', '),
                        }}
                        onSubmit={(model) => {
                          const data = {
                            emails: checked.map((val) => val.email).join(','),
                            permissions: {
                              ...((model.role === ROLE_CUSTOM
                                ? model.permissions
                                : checked[0].permissions) || {}),
                              user_role: model.role,
                            },
                          }

                          setChecked([])
                          return userManagement.setUsers(data)
                        }}
                      />
                    </Box>
                    <Box>
                      <RemoveUsers
                        onSubmit={() => {
                          setChecked([])
                          userManagement.removeUsers(checked.map((val) => val.email).join(','))
                        }}
                      />
                    </Box>
                  </Flex>
                )}
              </Flex>
            )}
          </Flex>
          {data.length === 0 && (
            <Box>
              <Trans>No users found</Trans>
            </Box>
          )}
          {data.length > 0 && (
            <ResponsiveTable active>
              <StyledTable isTablet={isTablet}>
                <TableBody>
                  <Header>
                    {canWrite && (
                      <Th style={{ width: 30 }}>
                        {/* @ts-expect-error: checkbox */}
                        <MntrCheckboxAdapter
                          checked={userManagement.users?.length === checked.length}
                          onChange={() => {
                            if (userManagement.users.length === checked.length) {
                              setChecked([])
                            } else {
                              const newData: ICheckedItem[] = []

                              data.forEach((val, page) => {
                                each(val, (user, key) =>
                                  newData.push({
                                    page,
                                    key: Number(key),
                                    email: user.email,
                                    permissions: user.permissions,
                                  }),
                                )
                              })

                              setChecked(newData)
                            }
                          }}
                        />
                      </Th>
                    )}
                    <Th style={{ width: 230 }}>
                      <Trans>Email</Trans>
                    </Th>
                    <Th style={{ textAlign: 'center', width: 100 }}>
                      <Trans>Verification</Trans>
                    </Th>
                    <Th style={{ width: 100 }}>
                      <Trans>Role</Trans>
                    </Th>
                    <Th style={{ width: 100 }}>
                      <Trans>Special tag</Trans>
                    </Th>
                    <Th>
                      <Trans>Last access</Trans>
                    </Th>
                    {canWrite && <Th />}
                  </Header>
                  {data[page - 1]?.map((user, key) => {
                    const isChecked = !!checked.find((val) => val.email === user.email)

                    const onChecked = () => {
                      if (isChecked) {
                        setChecked(checked.filter((val) => val.email !== user.email))
                      } else {
                        setChecked(
                          [
                            {
                              page: page - 1,
                              key,
                              email: user.email,
                              permissions: user.permissions,
                            },
                          ].concat(checked),
                        )
                      }
                    }

                    const userRole = enums.workspaces.user_role.find(
                      (role: IUserRoleArrItem) => role.id === user.permissions.user_role,
                    )

                    const userVerification = enums.accounts.user_verification_status.find(
                      (status: IStatusArrItem) => status.id === user.verification_status,
                    )

                    return (
                      <Row key={key}>
                        {canWrite && (
                          <Td>
                            {/* @ts-expect-error: checkbox */}
                            <MntrCheckboxAdapter checked={isChecked} onChange={onChecked} />
                          </Td>
                        )}
                        <Td>{user.email}</Td>
                        <Td style={{ textAlign: 'center' }}>
                          <MntrButton
                            bg="flat"
                            icon={userVerification.icon}
                            iconColor={userVerification.color}
                            tooltip={userVerification.text}
                          />
                        </Td>
                        <Td>
                          <MntrButton
                            disabled={!canWrite}
                            bg="default"
                            isChip
                            label={userRole.text}
                            icon={userRole.icon}
                            iconBg={userRole.color}
                            popupPlacement="bottom-start"
                            popup={(closePopup) => {
                              const menuItems = enums.workspaces.user_role.map(
                                (role: IUserRoleArrItem) => ({
                                  bg: 'flat',
                                  label: role.text,
                                  leftIcon: role.icon,
                                  ...(role.id === ROLE_CUSTOM
                                    ? {
                                        ...withModalUpdateRole({
                                          userRoles: enums.workspaces.user_role,
                                          initialValues: {
                                            emails: user.email,
                                            role: ROLE_CUSTOM,
                                            permissions: {
                                              ...user.permissions,
                                              user_role: ROLE_CUSTOM,
                                            },
                                          },
                                          onSubmit: (model) => {
                                            const data = {
                                              emails: model.emails,
                                              permissions: {
                                                ...(model.permissions || {}),
                                                user_role: model.role,
                                              },
                                            }

                                            return userManagement.setUsers(data)
                                          },
                                        }),
                                      }
                                    : {
                                        onClick: () => {
                                          const data = {
                                            emails: user.email,
                                            permissions: {
                                              ...user.permissions,
                                              user_role: role.id,
                                            },
                                          }

                                          userManagement.setUsers(data)
                                          closePopup()
                                        },
                                      }),
                                }),
                              )

                              // @ts-expect-error: menu
                              return <MntrMenu closePopup={closePopup} menuItems={menuItems} />
                            }}
                          />
                        </Td>
                        <Td>
                          {getUserAttributes(user).map(
                            (i, key) =>
                              i.text && (
                                <MntrButton
                                  key={key}
                                  isChip
                                  disabled
                                  height={'23px'}
                                  bg={i.backgroundColor}
                                  label={i.text}
                                />
                              ),
                          )}
                        </Td>
                        <Td>
                          {user.last_user_workspace_activity
                            ? formatDate(user.last_user_workspace_activity, 'dd. MM. yyyy')
                            : ''}
                        </Td>
                        {canWrite && (
                          <Td style={{ textAlign: 'right' }}>
                            <MntrButton
                              bg="flat"
                              icon="more_vert"
                              popupPlacement="bottom-end"
                              popup={(closePopup) => {
                                const menuItems = [
                                  {
                                    bg: 'flat',
                                    isChip: true,
                                    leftIcon: 'key',
                                    onClick: () => {
                                      return userStore.resetPassword(
                                        { email: user.email },
                                        () => {
                                          notification.add(
                                            t`Email with further instructions has been sent.`,
                                            'success',
                                          )
                                        },
                                        true,
                                      )
                                    },
                                    label: <Trans>Trigger password reset</Trans>,
                                  },
                                  {},
                                  {
                                    bg: 'flat',
                                    leftIcon: 'delete',
                                    leftIconColor: 'error',
                                    label: <Trans>Remove user</Trans>,
                                    ...withModalRemove({
                                      message: t`Are you sure you want to remove this user from the workspace?`,
                                      onSubmit: () => {
                                        userManagement.removeUsers(user.email)
                                      },
                                    }),
                                  },
                                ]

                                // @ts-expect-error: menu
                                return <MntrMenu closePopup={closePopup} menuItems={menuItems} />
                              }}
                            />
                          </Td>
                        )}
                      </Row>
                    )
                  })}
                </TableBody>
              </StyledTable>
            </ResponsiveTable>
          )}
          {userManagement.users?.length > itemsCountPerPage && (
            <PaginationWrapper>
              <Pagination
                activePage={page}
                itemsCountPerPage={itemsCountPerPage}
                totalItemsCount={userManagement.users.length}
                onChange={(page: number) => {
                  setPage(page)
                }}
              />
            </PaginationWrapper>
          )}
        </Flex>
      </MntrPaper>
    </Box>
  )
}

export default observer(SettingsUserManagement)
