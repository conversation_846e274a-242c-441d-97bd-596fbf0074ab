import { t } from '@lingui/core/macro'
import { useState } from 'react'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'

const getCheckbox = (isActive) => {
  return {
    leftIcon: isActive ? 'toggle_on' : 'toggle_off',
    leftIconColor: isActive ? 'secondary' : 'error',
    hoverVariant: isActive ? 'secondary' : 'error',
  }
}

const nestItems = (items, nestedLevel = 1) => {
  return items.map((item) => {
    return {
      ...item,
      nestedLevel,
    }
  })
}

const ViewMenu = ({
  appStore: {
    account,
    monitoring: {
      isOnlineCategoryTypeAvailable,
      isOfflineCategoryTypeAvailable,
      isTVCategoryTypeAvailable,
      isRadioCategoryTypeAvailable,
    },
    viewport: { isMobile },
  },
}) => {
  const [isOpenMediaData, toggleMediaData] = useState(false)
  const feedListView = account.user.frontend_storage.feedListView

  const mediaDataMenu = [
    {
      label: t`Back`,
      leftIcon: 'arrow_back',
      onClick() {
        toggleMediaData(false)
      },
      sticky: true,
    },
    {},
    {
      label: t`Media data`,
    },
    {
      label: t`Enabled`,
      onClick() {
        feedListView.setParam('visibleMediaView', !feedListView.visibleMediaView)
      },
      ...getCheckbox(feedListView.visibleMediaView),
    },
    ...nestItems([
      ...(isOnlineCategoryTypeAvailable
        ? [
            {
              label: t`Online`,
            },
            {
              disabled: !feedListView.visibleMediaView,
              label: t`Daily users`,
              onClick() {
                feedListView.setParam('visibleDailyUsers', !feedListView.visibleDailyUsers)
              },
              ...getCheckbox(feedListView.visibleDailyUsers),
            },
            {
              disabled: !feedListView.visibleMediaView,
              label: t`Monthly users`,
              onClick() {
                feedListView.setParam('visibleMonthlyUsers', !feedListView.visibleMonthlyUsers)
              },
              ...getCheckbox(feedListView.visibleMonthlyUsers),
            },
            {
              disabled: !feedListView.visibleMediaView,
              label: t`Monthly sessions`,
              onClick() {
                feedListView.setParam(
                  'visibleMonthlySessions',
                  !feedListView.visibleMonthlySessions,
                )
              },
              ...getCheckbox(feedListView.visibleMonthlySessions),
            },
            {
              disabled: !feedListView.visibleMediaView,
              label: t`Social interactions`,
              onClick() {
                feedListView.setParam('visibleSocialShares', !feedListView.visibleSocialShares)
              },
              ...getCheckbox(feedListView.visibleSocialShares),
            },
            {
              disabled: !feedListView.visibleMediaView,
              label: t`Frontpage promo`,
              onClick() {
                feedListView.setParam('visibleFrontpagePromo', !feedListView.visibleFrontpagePromo)
              },
              ...getCheckbox(feedListView.visibleFrontpagePromo),
            },
          ]
        : []),
      ...(isOfflineCategoryTypeAvailable
        ? [
            {
              label: t`Print`,
            },
            {
              disabled: !feedListView.visibleMediaView,
              label: t`Distribution amount`,
              onClick() {
                feedListView.setParam('visiblePressAmount', !feedListView.visiblePressAmount)
              },
              ...getCheckbox(feedListView.visiblePressAmount),
            },
            {
              disabled: !feedListView.visibleMediaView,
              label: t`Sold amount`,
              onClick() {
                feedListView.setParam('visibleSoldAmount', !feedListView.visibleSoldAmount)
              },
              ...getCheckbox(feedListView.visibleSoldAmount),
            },
            {
              disabled: !feedListView.visibleMediaView,
              label: t`Readership`,
              onClick() {
                feedListView.setParam('visibleReadership', !feedListView.visibleReadership)
              },
              ...getCheckbox(feedListView.visibleReadership),
            },
            {
              disabled: !feedListView.visibleMediaView,
              label: t`Article Area`,
              onClick() {
                feedListView.setParam('visibleArea', !feedListView.visibleArea)
              },
              ...getCheckbox(feedListView.visibleArea),
            },
            {
              disabled: !feedListView.visibleMediaView || !account.workspace.limits.allow_ave,
              label: t`Full page ad price`,
              onClick() {
                feedListView.setParam(
                  'visiblePrintAdPriceFullPage',
                  !feedListView.visiblePrintAdPriceFullPage,
                )
              },
              ...getCheckbox(feedListView.visiblePrintAdPriceFullPage),
            },
          ]
        : []),
      ...(isTVCategoryTypeAvailable || isRadioCategoryTypeAvailable
        ? [
            {
              label: `${t`TV`}, ${t`Radio`}`,
            },
            {
              disabled: !feedListView.visibleMediaView,
              label: t`Daily listenership`,
              onClick() {
                feedListView.setParam('visibleListenership', !feedListView.visibleListenership)
              },
              ...getCheckbox(feedListView.visibleListenership),
            },
            {
              disabled: !feedListView.visibleMediaView,
              label: t`Duration`,
              onClick() {
                feedListView.setParam('visibleDuration', !feedListView.visibleDuration)
              },
              ...getCheckbox(feedListView.visibleDuration),
            },
            {
              disabled: !feedListView.visibleMediaView || !account.workspace.limits.allow_ave,
              label: t`CPP`,
              buttonGroup: [
                {
                  icon: 'help',
                  size: 'small',
                  tooltip: t`Cost per Point (CCP) - how much does one second of advertising cost for each GRP point (AVE = CPP * GRP * duration)`,
                  bg: 'light',
                },
              ],
              onClick() {
                feedListView.setParam('visibleCPP', !feedListView.visibleCPP)
              },
              ...getCheckbox(feedListView.visibleCPP),
            },
          ]
        : []),
      {
        label: t`Metrics`,
      },
      {
        disabled: !feedListView.visibleMediaView || !account.workspace.limits.allow_ave,
        label: t`AVE`,
        onClick() {
          feedListView.setParam('visibleAVE', !feedListView.visibleAVE)
        },
        ...getCheckbox(feedListView.visibleAVE),
      },
      {
        disabled: !feedListView.visibleMediaView,
        label: t`GRP`,
        onClick() {
          feedListView.setParam('visibleGRP', !feedListView.visibleGRP)
        },
        ...getCheckbox(feedListView.visibleGRP),
      },
      {
        disabled: !feedListView.visibleMediaView,
        label: t`OTS`,
        onClick() {
          feedListView.setParam('visibleOTS', !feedListView.visibleOTS)
        },
        ...getCheckbox(feedListView.visibleOTS),
      },
      {
        disabled: !feedListView.visibleMediaView,
        label: t`Reach`,
        onClick() {
          feedListView.setParam('visibleReach', !feedListView.visibleReach)
        },
        ...getCheckbox(feedListView.visibleReach),
      },
      ...(account.workspace.limits.allow_adjusted_reach_pl
        ? [
            {
              disabled: !feedListView.visibleMediaView,
              label: t`Adjusted Reach`,
              onClick() {
                feedListView.setParam('visibleAdjustedReach', !feedListView.visibleAdjustedReach)
              },
              ...getCheckbox(feedListView.visibleAdjustedReach),
            },
          ]
        : []),
      {
        disabled: !feedListView.visibleMediaView,
        label: t`Scope of mention`,
        onClick() {
          feedListView.setParam('visibleArticleMetrics', !feedListView.visibleArticleMetrics)
        },
        ...getCheckbox(feedListView.visibleArticleMetrics),
      },
    ]),
  ]

  const [isOpenSocialData, toggleSocialData] = useState(false)

  const socialDataMenu = [
    {
      label: t`Back`,
      leftIcon: 'arrow_back',
      onClick() {
        toggleSocialData(false)
      },
      sticky: true,
    },
    {},
    {
      label: t`Social data`,
    },
    {
      label: t`Enabled`,
      onClick() {
        feedListView.setParam('visibleSocialView', !feedListView.visibleSocialView)
      },
      ...getCheckbox(feedListView.visibleSocialView),
    },
    ...nestItems([
      {
        disabled: !feedListView.visibleSocialView,
        label: t`Emoji reactions`,
        onClick() {
          feedListView.setParam('visibleSocialEmoji', !feedListView.visibleSocialEmoji)
        },
        ...getCheckbox(feedListView.visibleSocialEmoji),
      },
      {
        disabled: !feedListView.visibleSocialView,
        label: t`Number of followers`,
        onClick() {
          feedListView.setParam('visibleSocialAudience', !feedListView.visibleSocialAudience)
        },
        ...getCheckbox(feedListView.visibleSocialAudience),
      },
      {
        disabled: !feedListView.visibleSocialView,
        label: t`Influence score`,
        onClick() {
          feedListView.setParam('visibleInfluenceScore', !feedListView.visibleInfluenceScore)
        },
        ...getCheckbox(feedListView.visibleInfluenceScore),
      },
      {
        disabled: !feedListView.visibleSocialView,
        label: t`Engagement rate`,
        onClick() {
          feedListView.setParam('visibleEngagementRate', !feedListView.visibleEngagementRate)
        },
        ...getCheckbox(feedListView.visibleEngagementRate),
      },
    ]),
  ]

  return (
    <MntrMenu
      menuItems={
        isOpenMediaData
          ? mediaDataMenu
          : isOpenSocialData
            ? socialDataMenu
            : [
                {
                  label: t`View`,
                },
                {
                  label: t`Perex`,
                  onClick() {
                    feedListView.setParam('visiblePerex', !feedListView.visiblePerex)
                  },
                  ...getCheckbox(feedListView.visiblePerex),
                },
                {
                  label: t`Language`,
                  onClick() {
                    feedListView.setParam(
                      'visibleArticleLanguage',
                      !feedListView.visibleArticleLanguage,
                    )
                  },
                  ...getCheckbox(feedListView.visibleArticleLanguage),
                },
                !isMobile &&
                  account.workspace.is_traditional_media_active && {
                    label: t`Media data`,
                    onClick() {
                      toggleMediaData(true)
                    },
                    rightIcon: 'arrow_forward',
                    ...getCheckbox(feedListView.visibleMediaView),
                  },
                !isMobile &&
                  account.workspace.is_social_media_active && {
                    label: t`Social data`,
                    onClick() {
                      toggleSocialData(true)
                    },
                    rightIcon: 'arrow_forward',
                    ...getCheckbox(feedListView.visibleSocialView),
                  },
              ]
      }
      width={260}
    />
  )
}

export default observer(ViewMenu)
