import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { useState } from 'react'
import { Box, Flex, Heading, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import events from '~/constants/gtm'
import { useGTM } from '~/app/lib/use-gtm'
import { observer } from '~/helpers/mst'
import viewTransition from '~/helpers/viewTransition'
import MediatypePromoItem from './MediatypePromoItem'
import MediatypePromoStaticItem from './MediatypePromoStaticItem'
import MediatypePromoModal from './modules/MediatypePromoModal'
import { StyledGridWrapper } from './styles/StyledMediaTypePromo'

const MediatypePromo = ({ appStore: { featureRequest, notification, theme }, mediatypePromo }) => {
  const modalBg = theme.paper.background
  const [isModalOpen, setModalOpen] = useState(false)
  const [activeItem, setActiveItem] = useState(null)
  const [featureRequestId, setFeatureRequestId] = useState(null)
  const [messagePostfix, setMessagePostfix] = useState(null)
  const { pushEvent } = useGTM()

  const handleShowPromo = (featureRequestId, messagePostfix, item) => {
    setFeatureRequestId(featureRequestId)
    setMessagePostfix(messagePostfix)
    setActiveItem(item)
    viewTransition(() => setModalOpen(true))

    pushEvent(
      events.PROMO,
      'mediatype_open_promo_modal',
      item.category_type?.id || item.country?.id,
    )
  }

  const handleClosePromo = () => {
    viewTransition(() => setModalOpen(false))
  }

  const handleSubmitPromo = () => {
    handleClosePromo()
    notification.add(t`Feature has been requested.`, 'success')
    pushEvent(
      events.PROMO,
      'mediatype_submit_promo_modal',
      activeItem.category_type?.id || activeItem.country?.id,
    )
    featureRequest.sendRequest(featureRequestId)
  }

  // switch between country and category type
  const renderMediaItem = (item, index) => {
    const propsForItem = item.country
      ? {
          colorConstant: '#0e38b1',
          featureRequestPrefix: 'MEDIATYPE_PROMO',
          iconContent: 'globe',
          labelSecondary: item.country.text,
        }
      : {
          colorConstant: item.category_type.color,
          featureRequestPrefix: 'MEDIATYPE_PROMO_CATEGORY',
          labelSecondary: item.category_type.shorttext,
        }

    return (
      <Flex key={index.toString()}>
        <MediatypePromoItem item={item} {...propsForItem} handleShowPromo={handleShowPromo} />
      </Flex>
    )
  }

  // Check if feature has been requested and render submit button accordingly
  const hasRequested = featureRequest.hasRequested(featureRequestId)

  const buttons = [
    {
      label: hasRequested
        ? t({
            id: 'featureRequest.Requested',
            message: 'Requested',
          })
        : t`Try for free`,
      onClick: handleSubmitPromo,
      isCta: true,
      rounded: true,
      disabled: hasRequested,
    },
  ]

  return (
    <>
      <Box maxWidth="1300px" margin="0 auto" display={['none', 'none', 'block']}>
        {/* Grid of buttons */}
        <StyledGridWrapper>{mediatypePromo.map(renderMediaItem)}</StyledGridWrapper>

        {/* Promo Modal */}
        {isModalOpen && (
          <MediatypePromoModal onClose={handleClosePromo} modalBg={modalBg}>
            <Box position="relative" height="100%">
              <Flex flexDirection="column" gap={3}>
                <Heading color="heading" fontSize={'28px'} lineHeight="1.22em">
                  <Trans>Get a more complete view of the topics that interest you</Trans>
                </Heading>

                <MediatypePromoStaticItem item={activeItem} />

                <Text fontSize={2} mt={-2}>
                  <p>
                    <strong>
                      <Trans>
                        We've discovered more content in the media landscape related to your topics
                        and areas of interest.
                      </Trans>{' '}
                    </strong>
                    <Trans>Get a comprehensive view of the topics that matter to you.</Trans>
                  </p>
                  {messagePostfix && <p>{messagePostfix}</p>}
                </Text>
              </Flex>

              <Box position="absolute" bottom="0" right="0" left="0">
                {buttons.map((button, key) => {
                  return <MntrButton key={key.toString()} {...button} />
                })}
              </Box>
            </Box>
          </MediatypePromoModal>
        )}
      </Box>
    </>
  )
}

export default observer(MediatypePromo)
