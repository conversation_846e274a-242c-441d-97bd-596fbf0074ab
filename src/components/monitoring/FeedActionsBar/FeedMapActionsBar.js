import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { useState } from 'react'
import withAddToBasketPopup from '~/components/medialist/content/withAddToBasketPopup'
import withRemoveFromBasketPopup from '~/components/medialist/content/withRemoveFromBasketPopup'
import ActionsBar from '~/components/misc/ActionsBar/ActionsBar'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import feedTypes from '~/constants/feedTypes'
import events from '~/constants/gtm'
import filterOrShow from '~/helpers/filterOrShow'
import getSentimentIcon from '~/helpers/getSentimentIcon'
import { useGTM } from '~/app/lib/use-gtm'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { observer } from '~/helpers/mst'
import withSubmenuModal from '~/helpers/withSubmenuModal'
import AddTagPopupContent from './withAddTagPopup/AddTagPopupContent'
import withAddTagPopup from './withAddTagPopup/withAddTagPopup'
import RemoveTagPopupContent from './withRemoveTagPopup/RemoveTagPopupContent'
import withRemoveTagPopup from './withRemoveTagPopup/withRemoveTagPopup'

const FeedMapActionsBar = ({
  appStore: {
    account,
    monitoring: { tags, feedMap },
    account: { workspace },
    viewport: { isMobile },
    authors: { baskets },
  },
  actions,
  feedId,
  items,
  isExportList = false,
  withRefineArticles = false,
  withSort,
  withSelectActions,
  withSelectAll,
  withSelectCount,
  withView,
}) => {
  const [errorsTags, setErrorsTags] = useState(undefined)
  const [errorsBaskets, setErrorsBaskets] = useState(undefined)
  const { pushEvent } = useGTM()
  const feed = feedMap.get(feedId)
  if (!feed) {
    return
  }

  const { isEmpty } = feed.selector
  const isArchive = feed.feedType === feedTypes.ARCHIVE
  const isTrash = feed.feedType === feedTypes.TRASH
  const feedActions = []
  const permissions = workspace ? workspace.permissions : undefined
  const { addSentimentToSelected, exportStore, removeFromExport } = feed

  const addTag = (closePopup) => {
    const setTag = (tag) => {
      feed.addTagToSelected(tag, isExportList)
    }

    if (isMobile) {
      return withSubmenuModal({
        modalIcon: 'new_label',
        modalTitle: <Trans>Assign tag</Trans>,
        customContent: (closeModal) => {
          return (
            <AddTagPopupContent
              label={''}
              tags={tags}
              setTag={setTag}
              errors={errorsTags}
              setErrors={setErrorsTags}
              closeParentPopup={closeModal}
              canCreateTag={
                permissions?.archive_feed.can_write ?? permissions?.monitoring_feed.can_write
              }
            />
          )
        },
      })
    }

    return withAddTagPopup({
      tags,
      setTag,
      permissions,
      errors: errorsTags,
      setErrors: setErrorsTags,
      closeParentPopup: closePopup,
    })
  }

  const removeTag = (closePopup) => {
    const removeTag = (tag) => feed.removeTagFromSelected(tag, isExportList)

    if (isMobile) {
      return withSubmenuModal({
        modalIcon: 'label_off',
        modalTitle: <Trans>Remove tag</Trans>,
        customContent: (closeModal) => {
          return (
            <RemoveTagPopupContent
              label={''}
              tags={tags}
              removeTag={removeTag}
              closeParentPopup={closeModal}
            />
          )
        },
      })
    }

    return withRemoveTagPopup({
      tags,
      removeTag,
      closeParentPopup: closePopup,
    })
  }

  const removeArticle = () =>
    withModalRemove({
      message: <Trans>Selected articles will be removed.</Trans>,
      onSubmit: () => {
        feed.removeSelected()
      },
    })

  const addToAuthorsBasket = (closePopup) => {
    const text = t`Add authors to list`
    const icon = 'playlist_add'

    return {
      key: 'add_to_basket',
      bg: 'flat',
      ...(closePopup
        ? { leftIcon: icon, label: text }
        : {
            icon,
            tooltip: text,
          }),
      ...withAddToBasketPopup({
        baskets,
        addToBasket: (basket) => {
          feed.addSelectedToAuthorsBasket(basket)
          closePopup?.()
        },
        errors: errorsBaskets,
        setErrors: setErrorsBaskets,
        canCreateBasket: account.workspace?.permissions.authors_database.can_write,
      }),
    }
  }

  const removeFromAuthorsBasket = (closePopup) => {
    const text = t`Remove authors from list`
    const icon = 'playlist_remove'

    return {
      key: 'remove_from_basket',
      bg: 'flatError',
      ...(closePopup
        ? { leftIcon: icon, label: text }
        : {
            icon,
            tooltip: text,
          }),
      ...withRemoveFromBasketPopup({
        baskets,
        removeFromBasket: (basket) => {
          feed.removeSelectedFromAuthorsBasket(basket)
          closePopup?.()
        },
      }),
    }
  }

  if (!isEmpty) {
    if (filterOrShow(actions, 'download') && permissions?.export.can_read) {
      feedActions.push({
        bg: 'flatSecondary',
        icon: 'download',
        iconColor: 'actionsBar',
        iconColorHover: 'actionsBarHover',
        tooltip: t`Download`,
        zIndex: 100,
        onClick: () => {
          exportStore.markExportOpen(true)
        },
      })
    }

    if (filterOrShow(actions, 'send') && permissions?.export.can_read) {
      feedActions.push({
        bg: 'flatTertiary',
        icon: 'send',
        iconColor: 'actionsBar',
        iconColorHover: 'actionsBarHover',
        tooltip: t`Send`,
        zIndex: 100,
        onClick: () => exportStore.markResendOpen(true),
      })
    }

    if (filterOrShow(actions, 'export') && permissions?.export.can_read) {
      feedActions.push({
        bg: 'flatPrimary',
        iconColor: 'actionsBar',
        iconColorHover: 'actionsBarHover',
        icon: 'inbox',
        tooltip: t`Add to export`,
        zIndex: 100,
        onClick: () => {
          exportStore.addToExportBasket(feed, isExportList)
          pushEvent(events.ARTICLES_ADDED_TO_EXPORT)
        },
      })
    }

    if (!isMobile) {
      if (
        filterOrShow(actions, 'add_sentiment') &&
        (permissions?.archive_feed.can_write || permissions?.monitoring_feed.can_write)
      ) {
        feedActions.push({
          bg: 'flat',
          iconColor: 'actionsBar',
          iconColorHover: 'actionsBarHover',
          icon: 'sentiment_satisfied',
          tooltip: t`Set sentiment`,
          popup: (closePopup) => {
            const menuItems = [
              {
                label: <Trans>Set sentiment</Trans>,
              },
            ]

            account.enums.sentiment.forEach((sentiment) => {
              menuItems.push({
                label: sentiment.text,
                leftIcon: getSentimentIcon(sentiment.id),
                leftIconColor: sentiment.color,
                onClick: () => addSentimentToSelected(sentiment, isExportList),
              })
            })

            return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
          },
        })
      }

      if (
        filterOrShow(actions, 'add_tag') &&
        (permissions?.archive_feed.can_write || permissions?.monitoring_feed.can_write)
      ) {
        feedActions.push({
          bg: 'flat',
          iconColor: 'actionsBar',
          iconColorHover: 'actionsBarHover',
          icon: 'new_label',
          tooltip: t`Assign tag`,
          ...addTag(),
        })
      }

      if (
        filterOrShow(actions, 'remove_tag') &&
        (permissions?.archive_feed.can_write || permissions?.monitoring_feed.can_write)
      ) {
        feedActions.push({
          bg: 'flatError',
          iconColor: 'actionsBar',
          iconColorHover: 'actionsBarHover',
          icon: 'label_off',
          tooltip: t`Remove tag`,
          ...removeTag(),
        })
      }

      if (filterOrShow(actions, 'remove_from_export') && permissions?.export.can_read) {
        feedActions.push({
          bg: 'flat',
          iconColor: 'actionsBar',
          iconColorHover: 'actionsBarHover',
          icon: 'delete',
          tooltip: t`Remove from Export`,
          zIndex: 100,
          onClick: () => removeFromExport(),
        })
      }

      if (filterOrShow(actions, 'add_to_basket') && permissions?.authors_database.can_write) {
        feedActions.push({
          iconColor: 'actionsBar',
          iconColorHover: 'actionsBarHover',
          ...addToAuthorsBasket(),
        })
      }

      if (filterOrShow(actions, 'remove_from_basket') && permissions?.authors_database.can_write) {
        feedActions.push({
          iconColor: 'actionsBar',
          iconColorHover: 'actionsBarHover',
          ...removeFromAuthorsBasket(),
        })
      }

      if (filterOrShow(actions, 'delete') && permissions?.monitoring_feed.can_write && !isArchive) {
        feedActions.push({
          bg: 'flatError',
          iconColor: 'actionsBar',
          iconColorHover: 'actionsBarHover',
          icon: 'delete',
          tooltip: t`Delete Article`,
          zIndex: 100,
          ...removeArticle(),
        })
      }
    } else {
      feedActions.push({
        bg: 'flat',
        iconColor: 'actionsBar',
        iconColorHover: 'actionsBarHover',
        icon: 'more_vert',
        popup: (closePopup) => {
          const menuItems = []

          if (
            filterOrShow(actions, 'add_sentiment') &&
            (permissions?.archive_feed.can_write || permissions?.monitoring_feed.can_write)
          ) {
            const sentimentItems = account.enums.sentiment.map((sentiment) => {
              return {
                label: sentiment.text,
                leftIcon: getSentimentIcon(sentiment.id),
                leftIconColor: sentiment.color,
                onClick: () => addSentimentToSelected(sentiment, isExportList),
              }
            })
            const sentimentLabel = t`Set sentiment`
            const sentimentIcon = 'sentiment_satisfied'

            menuItems.push({
              leftIcon: sentimentIcon,
              label: sentimentLabel,
              ...(isMobile
                ? withSubmenuModal({
                    items: sentimentItems,
                    modalIcon: sentimentIcon,
                    modalTitle: sentimentLabel,
                  })
                : { subMenuItems: sentimentItems }),
            })
          }

          if (
            filterOrShow(actions, 'add_tag') &&
            (permissions?.archive_feed.can_write || permissions?.monitoring_feed.can_write)
          ) {
            menuItems.push({
              leftIcon: 'new_label',
              label: t`Assign tag`,
              placement: 'top-start',
              ...addTag(closePopup),
            })
          }

          if (
            filterOrShow(actions, 'remove_tag') &&
            (permissions?.archive_feed.can_write || permissions?.monitoring_feed.can_write)
          ) {
            menuItems.push({
              hoverVariant: 'error',
              leftIcon: 'label_off',
              label: t`Remove tag`,
              placement: 'top-start',
              ...removeTag(closePopup),
            })
          }

          if (
            filterOrShow(actions, 'delete') &&
            permissions?.monitoring_feed.can_write &&
            !isArchive
          ) {
            menuItems.push({
              hoverVariant: 'error',
              leftIcon: 'delete',
              label: t`Delete Article`,
              ...removeArticle(),
            })
          }

          return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
        },
      })
    }
  }

  if (filterOrShow(actions, 'restore') && !isEmpty && isTrash) {
    feedActions.push({
      bg: 'flatSecondary',
      icon: 'restore_from_trash',
      tooltip: t`Restore articles`,
      onClick: () => feed.restoreSelected(),
    })
  }

  return (
    <ActionsBar
      feedId={feedId}
      groups={{
        l: [
          <Flex position="relative" key="actions-bar-selection-actions">
            {feedActions.map((item, key) => {
              return (
                <Box mr={1} key={key}>
                  <MntrButton {...item} />
                </Box>
              )
            })}
          </Flex>,
        ],
      }}
      items={items}
      withRefineArticles={withRefineArticles}
      withSort={withSort}
      withSelectActions={withSelectActions}
      withSelectAll={withSelectAll}
      withSelectCount={withSelectCount}
      withView={withView}
    />
  )
}

export default observer(FeedMapActionsBar)
