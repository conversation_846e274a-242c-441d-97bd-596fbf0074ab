import { Plural, Trans } from '@lingui/react/macro'
import sum from 'lodash/sum'
import { styled } from 'styled-components'
import { socialShares } from '~/components/OurChart/hints'
import { Box } from '~/components/misc/Mntr'
import categoryTypes from '~/constants/categoryTypes'
import * as format from '~/helpers/formatNumber'
import { observer } from '~/helpers/mst'
import MetaDataMediaItem from './MetaDataMediaItem/MetaDataMediaItem'

const LowerCase = styled.span`
  text-transform: lowercase;
`

const MetaDataMedia = ({ feedListView, data, viewport, isInspector, account }) => {
  const isCategoryTypeOnline =
    data.news_source?.category?.category_type?.id === categoryTypes.CATEGORY_TYPE_ONLINE_MEDIA

  return (
    <>
      {((feedListView.visibleMediaView && !viewport.isMobile) || isInspector) && (
        <>
          {data.reach > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleReach"
              icon="people"
              label={
                <>
                  <Trans>reach</Trans> {format.humanizeNumber(data.reach)} <Trans>people</Trans>
                </>
              }
            />
          )}
          {data.adjusted_reach_pl > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleAdjustedReach"
              icon="people"
              label={
                <>
                  <Trans>Adjusted Reach</Trans> {format.humanizeNumber(data.adjusted_reach_pl)}
                </>
              }
            />
          )}
          {data.news_source?.listenership > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleListenership"
              icon="radio"
              label={
                <>
                  <Trans>Daily listenership</Trans>:{' '}
                  {format.humanizeNumber(data.news_source.listenership)}
                </>
              }
            />
          )}
          {data.news_source?.daily_ru > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleDailyUsers"
              icon="data_usage"
              label={
                <>
                  {format.humanizeNumber(
                    data.news_source.daily_ru +
                      sum(data.identical_articles.map((item) => item.news_source.daily_ru || 0)),
                  )}{' '}
                  <Trans>daily users</Trans>
                </>
              }
            />
          )}
          {data.news_source?.monthly_ru > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleMonthlyUsers"
              icon="data_usage"
              label={
                <>
                  {format.humanizeNumber(
                    data.news_source.monthly_ru +
                      sum(data.identical_articles.map((item) => item.news_source.monthly_ru || 0)),
                  )}{' '}
                  <Trans>monthly users</Trans>
                </>
              }
            />
          )}
          {data.news_source?.monthly_sessions > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleMonthlySessions"
              icon="data_usage"
              label={
                <>
                  {format.humanizeNumber(
                    data.news_source.monthly_sessions +
                      sum(
                        data.identical_articles.map(
                          (item) => item.news_source.monthly_sessions || 0,
                        ),
                      ),
                  )}{' '}
                  <Trans>monthly sessions</Trans>
                </>
              }
            />
          )}
          {data.social_shares > 0 && isCategoryTypeOnline && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleSocialShares"
              icon="share"
              hoverable
              label={
                <Plural
                  value={parseInt(data.social_shares)}
                  one="# interaction"
                  other="# interactions"
                />
              }
              popup={() => {
                return (
                  <Box px={2} py={1} color="lightGrey" width="250px" fontSize="13px">
                    <p>{socialShares()}</p>
                  </Box>
                )
              }}
              disabled={false}
            />
          )}
          {data.frontpage_promo && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleFrontpagePromo"
              icon="timer"
              label={
                <>
                  {data.frontpage_promo} <Trans>on frontpage</Trans>
                </>
              }
            />
          )}
          {data.news_source?.press_amount > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visiblePressAmount"
              icon="data_usage"
              label={
                <>
                  <Trans>Distribution amount</Trans>:{' '}
                  {format.formatPrintAmount(
                    data.news_source.press_amount +
                      sum(
                        data.identical_articles.map((item) => item.news_source.press_amount || 0),
                      ),
                  )}
                </>
              }
              tooltip={<Trans>Estimated number of distributed copies (print and digital).</Trans>}
            />
          )}
          {data.news_source?.sold_amount > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleSoldAmount"
              icon="data_usage"
              label={
                <>
                  <Trans>Sold amount (print+digital)</Trans>:{' '}
                  {format.formatPrintAmount(
                    data.news_source.sold_amount +
                      sum(data.identical_articles.map((item) => item.news_source.sold_amount || 0)),
                  )}
                </>
              }
            />
          )}
          {data.news_source?.readership > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleReadership"
              icon="data_usage"
              label={
                <>
                  <Trans>Readership</Trans>:{' '}
                  {format.humanizeNumber(
                    data.news_source.readership +
                      sum(data.identical_articles.map((item) => item.news_source.readership || 0)),
                  )}
                </>
              }
            />
          )}
          {data.area > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleArea"
              icon="receipt_long"
              label={
                <>
                  {data.area.toFixed(1)} <Trans>area</Trans>
                </>
              }
            />
          )}
          {data.print_ad_price_full_page > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visiblePrintAdPriceFullPage"
              icon="request_page"
              label={
                <>
                  {format.formatCurrency(data.print_ad_price_full_page, account.user.currency.text)}
                  /
                  <LowerCase>
                    <Trans>Page</Trans>
                  </LowerCase>
                </>
              }
            />
          )}
          {data.cpp_second > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleCPP"
              icon="assessment"
              label={
                <>
                  <Trans>CPP</Trans>:{' '}
                  {format.formatCurrency(data.cpp_second, account.user.currency.text)}
                </>
              }
              tooltip={
                <Trans>
                  Cost per Point (CCP) - how much does one second of advertising cost for each GRP
                  point (AVE = CPP * GRP * duration)
                </Trans>
              }
            />
          )}
          {data.AVE > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleAVE"
              icon="assessment"
              label={
                <>
                  <Trans>AVE</Trans>: {format.formatCurrency(data.AVE, account.user.currency.text)}
                </>
              }
              tooltip={<Trans>Advertising Value Equivalency</Trans>}
            />
          )}
          {data.GRP > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleGRP"
              icon="assessment"
              label={
                <>
                  <Trans>GRP</Trans>: {format.formatNumber(data.GRP)}
                </>
              }
              tooltip={<Trans>Gross Rating Point</Trans>}
            />
          )}
          {data.OTS > 0 && (
            <MetaDataMediaItem
              feedListView={feedListView}
              id="visibleOTS"
              icon="assessment"
              label={
                <>
                  <Trans>OTS</Trans>: {format.formatNumber(data.OTS)}
                </>
              }
              tooltip={<Trans>Opportunity to see</Trans>}
            />
          )}
          {data.article_metrics.length > 0 &&
            data.article_metrics.map((articleMetric, idx) => {
              return (
                <MetaDataMediaItem
                  key={idx}
                  feedListView={feedListView}
                  id="visibleArticleMetrics"
                  icon="receipt_long"
                  label={articleMetric.display_value}
                  tooltip={articleMetric.article_metric_definition.name}
                />
              )
            })}
        </>
      )}
    </>
  )
}

export default observer(MetaDataMedia)
