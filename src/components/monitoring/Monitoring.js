import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { useRouter } from 'next/router'
import { useEffect } from 'react'
import PageContent from '~/components/Content/Content'
import DashboardInspectorWrapper from '~/components/dashboards/DashboardInspectorWrapper/DashboardInspectorWrapper'
import MessageDirty from '~/components/layout/Header/MessageDirty/MessageDirty'
import MessageLimit from '~/components/layout/Header/MessageLimit/MessageLimit'
import MntrActiveFilters from '~/components/layout/MntrActiveFilters/MntrActiveFilters'
import MntrFiltersBar from '~/components/layout/MntrFiltersBar/MntrFiltersBar'
import ActionsBarSticky from '~/components/misc/ActionsBar/Sticky/Sticky'
import BannerMessage from '~/components/misc/BannerMessage/BannerMessage'
import Head from '~/components/misc/Head/Head'
import { Box } from '~/components/misc/Mntr'
import PortableExport from '~/components/misc/portable/PortableExport/PortableExport'
import PortableResend from '~/components/misc/portable/PortableResend/PortableResend'
import TopicsMultiSelector from '~/components/misc/TopicsMultiSelector/TopicsMultiSelector'
import FeedMapActionsBar from '~/components/monitoring/FeedActionsBar/FeedMapActionsBar'
import events from '~/constants/gtm'
import staticFeeds from '~/constants/staticFeeds'
import { getConfigMonitoringFeed } from '~/helpers/getActiveFiltersConfig'
import { useGTM } from '~/app/lib/use-gtm'
import { observer } from '~/helpers/mst'
import FeedChart from './FeedChart/FeedChart'
import FeedList from './FeedList/FeedList'

const Monitoring = ({
  appStore: {
    account,
    appNotifications,
    monitoring: { feedMap, activeFeedMapItem, isVisibleActiveFilters, tags },
    filter,
    topics,
    router: { redirectTo },
  },
}) => {
  const { pathname } = useRouter()
  const { pushEvent } = useGTM()

  const feedId = staticFeeds.MONITORING_FEED
  const customRoute = '/'
  const feed = feedMap.get(feedId)

  const { labels = {} } = filter

  const isLoading = feed?.loader.isLoading('feed-loading')

  useEffect(() => {
    if (!isLoading && !activeFeedMapItem && feed) {
      pushEvent(events.ARTICLES_LOADED, {
        articles_count: feed.chart.totalCount,
        topics_count: topics?.list?.length,
        tags_count: tags?.list?.length,
        topics_filtered: !!labels.topic_monitors,
        topics_filtered_count: labels.topic_monitors?.length,
        tags_filtered: !!labels.tags,
        tags_filtered_count: labels.tags?.length,
        date_filtered: !!labels.lower_date || !!labels.upper_date,
        source_filtered: !!labels.category_type,
        author_filtered: !!labels.author,
        country_filtered: !!labels.news_source_country,
        language_filtered: !!labels.language,
        sentiment_filtered: !!labels.sentiment,
        note_filtered: !!labels.note,
        type_filtered: !!labels.article_type,
        sort_used: !!labels.order_by,
        sort_type: labels ? labels?.order_by?.text : undefined,
        filter_string: labels ? JSON.stringify(labels) : undefined,
      })
    }
    // disable eslint to control pushEvent hit
    // eslint-disable-next-line
  }, [isLoading, activeFeedMapItem])

  if (!feed) {
    return
  }

  const filters = [
    'Date',
    'Source',
    'LanguageMultiselect',
    'CountryMultiselect',
    'Author',
    'Sentiment',
    'Notes',
    'ArticleType',
  ]

  if (filter.data.topic_monitors?.trim()) {
    const topicIds = filter.data?.topic_monitors
      ?.split(',')
      .map((id) => Number(id.trim()))
      .filter(Boolean)

    const hasAnyPrimeScore = topics.list.some(
      (item) => topicIds.includes(item.id) && item.data.has_prime_score,
    )

    if (hasAnyPrimeScore && account.enums.prime_score.prime_score_definitions?.length > 0) {
      filters.push('Prime')
    }
  }

  return (
    <PageContent>
      <Head
        title={
          appNotifications.unreadCountNumber > 0
            ? `(${appNotifications.unreadCount}) ${t`Monitoring`}`
            : t`Monitoring`
        }
      />

      {account.enums.notification && (
        <Box key="message-notification">
          <BannerMessage {...account.enums.notification} bg="beige" />
        </Box>
      )}

      <MntrFiltersBar filters={filters} />

      {isVisibleActiveFilters && <MntrActiveFilters config={getConfigMonitoringFeed()} />}

      {!isLoading && feed.isDirty && (
        <Box key="message-dirty">
          <MessageDirty />
        </Box>
      )}

      {!isLoading && feed.isLimitExceeded && (
        <Box key="message-limit">
          <MessageLimit />
        </Box>
      )}

      {/* Feed Chart */}
      {!feed.isVisibleTopicSelector &&
        (isLoading || feed.getFeedList.length > 0 || !filter.isEmpty) && (
          <FeedChart feedId={feedId} customRoute={customRoute} />
        )}

      <Box>
        {!feed.isVisibleTopicSelector && feed.getFeedList.length > 0 && feed.isVisibleActionBar && (
          <ActionsBarSticky>
            <FeedMapActionsBar
              actions={[
                'download',
                'send',
                'export',
                'delete',
                'add_sentiment',
                'add_tag',
                'remove_tag',
                'add_to_basket',
                'remove_from_basket',
              ]}
              feedId={feedId}
              items={feed.getFeedList}
              withRefineArticles
            />
          </ActionsBarSticky>
        )}
        {feed.isVisibleTopicSelector && (
          <TopicsMultiSelector
            header={<Trans>Monitoring</Trans>}
            subHeader={<Trans>Select Topic</Trans>}
            maxSelectedLimit={1}
            onSubmit={(model) => {
              redirectTo(`${pathname}?${filter.urlWithParam({ topic_monitors: model[0] }, true)}`)
            }}
          />
        )}

        {/* Feed List */}
        {!feed.isVisibleTopicSelector && <FeedList feedId={feedId} customRoute={customRoute} />}

        <PortableExport feedId={feedId} />
        <PortableResend feedId={feedId} />

        {/* Article Inspector */}
        {activeFeedMapItem && <DashboardInspectorWrapper isDashboard={false} feedId={feedId} />}
      </Box>
    </PageContent>
  )
}

export default observer(Monitoring)
