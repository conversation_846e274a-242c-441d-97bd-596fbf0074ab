import { useEffect, useState } from 'react'
import { styled } from 'styled-components'
import { Paper } from '~/components/layout/MntrPaper/MntrPaper'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrSnackbar from '~/components/misc/MntrSnackbar/MntrSnackbar'
import events from '~/constants/gtm'
import { pushEvent } from '~/helpers/gtm'

const NotificationPaper = styled(Paper)`
  background: ${({ theme, bg }) => theme.snackbar[bg]};
  color: ${({ theme }) => theme.snackbar.color};
  box-shadow: ${({ theme }) => theme.snackbar.boxShadow};
`

const NotificationItem = ({
  actions = [],
  anchorOrigin = { vertical: 'top', horizontal: 'center' },
  icon,
  isOpen = true,
  item: { id, autoHideDuration, message, type, remove },
  width = 320,
}) => {
  const [open, setOpen] = useState(isOpen)

  useEffect(() => {
    setOpen(isOpen)
    if (type === 'error') pushEvent(events.ERROR_MESSAGE_SHOWN, { error_text: message })
  }, [id, isOpen, message, type])

  const handleClose = (event, reason) => {
    if (reason === 'clickaway') {
      return
    }

    setOpen(false)

    setTimeout(function () {
      if (remove) {
        remove(id)
      }
    }, 150)
  }

  return (
    <MntrSnackbar
      anchorOrigin={anchorOrigin}
      autoHideDuration={autoHideDuration}
      onClose={handleClose}
      open={open}
    >
      <div data-e2e="notification">
        <NotificationPaper bg={type}>
          <Flex p={1} justifyContent="space-between" width={`${width}px`}>
            <Flex>
              {icon && (
                <Flex m={1}>
                  <Icon>{icon}</Icon>
                </Flex>
              )}
              <Box m={1}>{message}</Box>
            </Flex>
            <Box m="1px">
              {actions &&
                (actions.length ? (
                  actions.map(({ onClick, ...action }, index) => {
                    return (
                      <MntrButton
                        key={`notification-item-action-${index}`}
                        bg="flat"
                        icon="close"
                        iconColor="currentColor"
                        {...action}
                        onClick={() => {
                          onClick(handleClose)
                        }}
                      />
                    )
                  })
                ) : (
                  <MntrButton
                    bg="flat"
                    icon="close"
                    iconColor="currentColor"
                    onClick={handleClose}
                  />
                ))}
            </Box>
          </Flex>
        </NotificationPaper>
      </div>
    </MntrSnackbar>
  )
}

export default NotificationItem
