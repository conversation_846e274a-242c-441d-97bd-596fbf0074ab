name: E2E Tests (production)

on:
  schedule:
    # every day at 5:30 and 17:30 UTC+1
    - cron: '30 4,16 * * *'
  workflow_dispatch:

env:
  DOCKER_BUILD_CLOUD_PAT: ${{ secrets.DOCKER_BUILD_CLOUD_PAT }}
  DOCKER_BUILD_CLOUD_USER: ${{ vars.DOCKER_BUILD_CLOUD_USER }}
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  run_tests:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        shardIndex: [1]
        shardTotal: [1]
    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true

      - uses: ./.github/actions/setup/docker

      - name: Run E2E tests
        env:
          PLAYWRIGHT_CONFIG_BASE_URL: https://app.mediaboard.com
        run: |
          docker compose pull e2e

          docker compose run --no-deps e2e \
            --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }} \
        id: e2e

      - name: Upload blob report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: blob-report-${{ matrix.shardIndex }}
          path: blob-report
          retention-days: 1

      - name: Get snapshot changes
        if: always() && (steps.e2e.conclusion == 'failure' || steps.e2e.conclusion == 'success')
        run: |
          mktemp dummy.XXXXXX

          delimiter="$(cat /proc/sys/kernel/random/uuid)"

          echo "changes<<${delimiter}" >> $GITHUB_OUTPUT
          git ls-files --modified e2e/*.png >> $GITHUB_OUTPUT
          echo "${delimiter}" >> $GITHUB_OUTPUT
        id: get_snapshot_changes

      - name: Upload changed snapshots
        if: steps.get_snapshot_changes.outputs.changes != ''
        uses: actions/upload-artifact@v4
        with:
          name: changed_snapshots-${{ matrix.shardIndex }}
          path: |
            ${{ steps.get_snapshot_changes.outputs.changes }}
            dummy.*
          retention-days: 1
    # outputs: Don't rely on matrix job outputs
    # (https://github.com/community/community/discussions/17245)

  tests:
    needs: run_tests
    if: failure() || success()
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/setup

      - name: Download blob reports
        uses: actions/download-artifact@v4
        with:
          pattern: blob-report-*
          path: all-blob-reports
          merge-multiple: true

      - name: Merge into HTML report & report visual changes
        run: |
          pnpm playwright merge-reports \
            --reporter e2e/visual-changes-reporter.ts,html \
            ./all-blob-reports

      - name: Package & upload report
        run: |
          tar cf report.tar playwright-report

          echo report_url=$(
            curl \
              -F "id=${{ github.run_id }}-${{ github.run_attempt }}" \
              -F "report=@report.tar" \
              https://${{ secrets.PREVIEW_HTTP_AUTH }}@www.preview.monitora.cz/upload-report/
          ) >> $GITHUB_OUTPUT
        id: pw_report

      - name: Get visual changes
        run: |
          if [[ -f visual_changes.json ]]; then
            echo visual_changes=$(cat visual_changes.json) >> $GITHUB_OUTPUT
          fi
        id: get_visual_changes

      - name: Update summary
        run: |
          report_url='${{ steps.pw_report.outputs.report_url }}'

          echo '## 🎭 Playwright report' >> $GITHUB_STEP_SUMMARY
          echo '' >> $GITHUB_STEP_SUMMARY
          echo "$report_url" >> $GITHUB_STEP_SUMMARY
          echo '' >> $GITHUB_STEP_SUMMARY

          if [[ $(echo '${{ steps.get_visual_changes.outputs.visual_changes }}' | jq length) -gt 0 ]]; then
            echo '### 📸 Visual comparisons' >> $GITHUB_STEP_SUMMARY
            echo '' >> $GITHUB_STEP_SUMMARY
            echo '1. Review the report' >> $GITHUB_STEP_SUMMARY
            echo '2. Update screenshots [locally](https://github.com/monitora-media/monitora-frontend?tab=readme-ov-file#run-e2e-tests) or add the [`ci: update snapshots`](https://github.com/monitora-media/monitora-frontend/labels/ci%3A%20update%20snapshots) label to the PR' >> $GITHUB_STEP_SUMMARY
            echo '' >> $GITHUB_STEP_SUMMARY
            echo 'The following tests report visual changes:' >> $GITHUB_STEP_SUMMARY
            echo '' >> $GITHUB_STEP_SUMMARY
            echo '| Test | Change Type |' >> $GITHUB_STEP_SUMMARY
            echo '| - | - |' >> $GITHUB_STEP_SUMMARY

            echo '${{ steps.get_visual_changes.outputs.visual_changes }}' | jq -c '.[]' | while read -r t; do
              echo "| [$(echo "$t" | jq -r '.titlePath')]($report_url#\?$(echo "$t" | jq -r '.query')) | $(echo "$t" | jq -r '.changeType') |" >> $GITHUB_STEP_SUMMARY
            done
          else
            echo '### 📷 Visual comparisons' >> $GITHUB_STEP_SUMMARY
            echo '' >> $GITHUB_STEP_SUMMARY
            echo 'No visual changes detected.' >> $GITHUB_STEP_SUMMARY
          fi

      - name: Download changed snapshots
        uses: actions/download-artifact@v4
        with:
          pattern: changed_snapshots-*
          merge-multiple: true
        continue-on-error: true

      - name: Get snapshot changes
        run: |
          mktemp dummy.XXXXXX

          delimiter="$(cat /proc/sys/kernel/random/uuid)"

          echo "changes<<${delimiter}" >> $GITHUB_OUTPUT
          git ls-files --modified e2e/*.png >> $GITHUB_OUTPUT
          echo "${delimiter}" >> $GITHUB_OUTPUT
        id: get_snapshot_changes

      - name: Upload changed snapshots
        if: steps.get_snapshot_changes.outputs.changes != ''
        uses: actions/upload-artifact@v4
        with:
          name: changed_snapshots
          path: |
            ${{ steps.get_snapshot_changes.outputs.changes }}
            dummy.*
          retention-days: 1
